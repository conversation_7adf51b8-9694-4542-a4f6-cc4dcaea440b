.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/a;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/a;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
