.class public final synthetic LM31/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LM31/e;->a:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LM31/e;->a:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    check-cast p1, Landroid/content/res/TypedArray;

    invoke-static {v0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->e(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
