.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0001\u0018\u0000 K2\u00020\u0001:\u0001LB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0006\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0003J\u0017\u0010\t\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u0003J\u000f\u0010\u000c\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\u0003J\u000f\u0010\r\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\r\u0010\u0003J\u000f\u0010\u000e\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u0003J\u000f\u0010\u000f\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0003J\u0019\u0010\u0012\u001a\u00020\u00042\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0010H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0003J\u000f\u0010\u0015\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0003J\u000f\u0010\u0016\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0003R\u001b\u0010\u001c\u001a\u00020\u00178BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\"\u0010$\u001a\u00020\u001d8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!\"\u0004\u0008\"\u0010#R\"\u0010,\u001a\u00020%8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008&\u0010\'\u001a\u0004\u0008(\u0010)\"\u0004\u0008*\u0010+R\"\u00104\u001a\u00020-8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101\"\u0004\u00082\u00103R\"\u0010<\u001a\u0002058\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109\"\u0004\u0008:\u0010;R\"\u0010D\u001a\u00020=8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\"\u0004\u0008B\u0010CR\u001b\u0010J\u001a\u00020E8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008F\u0010G\u001a\u0004\u0008H\u0010I\u00a8\u0006M"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "T2",
        "Y2",
        "",
        "message",
        "f3",
        "(Ljava/lang/String;)V",
        "V2",
        "L2",
        "b3",
        "d3",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "x2",
        "v2",
        "onResume",
        "LOU0/g;",
        "i0",
        "LRc/c;",
        "N2",
        "()LOU0/g;",
        "binding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "j0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "S2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LTZ0/a;",
        "k0",
        "LTZ0/a;",
        "M2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "LzX0/k;",
        "l0",
        "LzX0/k;",
        "P2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "LAX0/b;",
        "m0",
        "LAX0/b;",
        "Q2",
        "()LAX0/b;",
        "setSuccessBetAlertManager",
        "(LAX0/b;)V",
        "successBetAlertManager",
        "Lck/a;",
        "n0",
        "Lck/a;",
        "O2",
        "()Lck/a;",
        "setChangeBalanceDialogProvider",
        "(Lck/a;)V",
        "changeBalanceDialogProvider",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;",
        "o0",
        "Lkotlin/j;",
        "R2",
        "()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;",
        "viewModel",
        "b1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic k1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final v1:I


# instance fields
.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public k0:LTZ0/a;

.field public l0:LzX0/k;

.field public m0:LAX0/b;

.field public n0:Lck/a;

.field public final o0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/toto_bet/impl/databinding/FragmentSimpleBetTotoBetBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->k1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->b1:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$a;

    .line 31
    .line 32
    const/16 v0, 0x8

    .line 33
    .line 34
    sput v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->v1:I

    .line 35
    .line 36
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LNU0/b;->fragment_simple_bet_toto_bet:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$binding$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/i;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/i;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 17
    .line 18
    .line 19
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$1;

    .line 20
    .line 21
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 22
    .line 23
    .line 24
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 25
    .line 26
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$2;

    .line 27
    .line 28
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    const-class v2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$4;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->o0:Lkotlin/j;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->W2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->c3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic C2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->Z2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->U2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public static synthetic E2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->X2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic F2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->a3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V

    return-void
.end method

.method public static final synthetic G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic H2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic I2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->b3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->d3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->f3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final T2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/m;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/m;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 8
    .line 9
    .line 10
    const-string v2, "REQUEST_CHANGE_BALANCE_DIALOG_KEY"

    .line 11
    .line 12
    invoke-virtual {v0, v2, p0, v1}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static final U2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 1

    .line 1
    const-string p1, "RESULT_ON_ITEM_SELECTED_LISTENER_KEY"

    .line 2
    .line 3
    invoke-virtual {p2, p1}, Landroid/os/BaseBundle;->containsKey(Ljava/lang/String;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p2, p1}, Landroid/os/Bundle;->getSerializable(Ljava/lang/String;)Ljava/io/Serializable;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    instance-of p2, p1, Lorg/xbet/balance/model/BalanceModel;

    .line 14
    .line 15
    if-eqz p2, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 22
    .line 23
    invoke-virtual {p2, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->x4(Lorg/xbet/balance/model/BalanceModel;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->L2()V

    .line 27
    .line 28
    .line 29
    :cond_0
    return-void
.end method

.method private final V2()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/n;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/n;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 4
    .line 5
    .line 6
    const-string v1, "REQUEST_INSUFFICIENT_FOUNDS_DIALOG_KEY"

    .line 7
    .line 8
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/o;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/o;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 14
    .line 15
    .line 16
    invoke-static {p0, v1, v0}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public static final W2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->s4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final X2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 6
    .line 7
    const/4 v0, 0x1

    .line 8
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setActionsEnabled(Z)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final Z2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->q4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final a3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->s4()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final b3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/g;->e:Landroid/widget/TextView;

    .line 6
    .line 7
    sget v1, Lpb/k;->change_balance_account:I

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LOU0/g;->e:Landroid/widget/TextView;

    .line 17
    .line 18
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/l;

    .line 19
    .line 20
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/l;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final c3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V
    .locals 13

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iget-object p1, p1, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroid/view/View;->clearFocus()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->O2()Lck/a;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->TEMPORARY:Lorg/xbet/balance/model/BalanceScreenType;

    .line 15
    .line 16
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 17
    .line 18
    .line 19
    move-result-object v5

    .line 20
    const/16 v11, 0x2ce

    .line 21
    .line 22
    const/4 v12, 0x0

    .line 23
    const/4 v2, 0x0

    .line 24
    const/4 v3, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    const/4 v8, 0x0

    .line 29
    const-string v9, "REQUEST_CHANGE_BALANCE_DIALOG_KEY"

    .line 30
    .line 31
    const/4 v10, 0x0

    .line 32
    invoke-static/range {v0 .. v12}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method private final d3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/g;->e:Landroid/widget/TextView;

    .line 6
    .line 7
    sget v1, Lpb/k;->refill_account:I

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LOU0/g;->e:Landroid/widget/TextView;

    .line 17
    .line 18
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/h;

    .line 19
    .line 20
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/h;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static final e3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->s4()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final f3(Ljava/lang/String;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->M2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->error:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->replenish:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    sget v4, Lpb/k;->cancel:I

    .line 22
    .line 23
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v6

    .line 27
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 28
    .line 29
    const/16 v15, 0xbd0

    .line 30
    .line 31
    const/16 v16, 0x0

    .line 32
    .line 33
    const/4 v7, 0x0

    .line 34
    const-string v8, "REQUEST_INSUFFICIENT_FOUNDS_DIALOG_KEY"

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v10, 0x0

    .line 38
    const/4 v11, 0x0

    .line 39
    const/4 v12, 0x0

    .line 40
    const/4 v14, 0x0

    .line 41
    move-object/from16 v4, p1

    .line 42
    .line 43
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public static final g3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->S2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->e3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic z2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->g3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final L2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    const/4 v2, 0x2

    .line 9
    const-string v3, ""

    .line 10
    .line 11
    invoke-static {v0, v3, v1, v2, v1}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setText$default(Lorg/xbet/ui_common/viewcomponents/views/StepInputView;Ljava/lang/String;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 19
    .line 20
    invoke-virtual {v0}, Landroid/view/View;->clearFocus()V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final M2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final N2()LOU0/g;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LOU0/g;

    .line 13
    .line 14
    return-object v0
.end method

.method public final O2()Lck/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->n0:Lck/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final P2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->l0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Q2()LAX0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->m0:LAX0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->o0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final S2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Y2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->a4()Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->v4()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v0, v0, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 16
    .line 17
    invoke-virtual {v0}, Landroid/view/View;->clearFocus()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->T2()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->V2()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    iget-object p1, p1, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 15
    .line 16
    const/4 v0, 0x0

    .line 17
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setVisibilityStepButtons(Z)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    iget-object p1, p1, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 25
    .line 26
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/j;

    .line 27
    .line 28
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/j;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setActionCLickListener(Lkotlin/jvm/functions/Function0;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    iget-object p1, p1, LOU0/g;->c:Landroid/widget/ImageView;

    .line 39
    .line 40
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/k;

    .line 41
    .line 42
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/k;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iget-object p1, p1, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 53
    .line 54
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/views/StepInputView$b;

    .line 55
    .line 56
    const/16 v1, 0xd

    .line 57
    .line 58
    const/4 v2, 0x2

    .line 59
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView$b;-><init>(II)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setFormatParams(Lorg/xbet/ui_common/viewcomponents/views/StepInputView$b;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->N2()LOU0/g;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    iget-object p1, p1, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 70
    .line 71
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onInitView$3;

    .line 72
    .line 73
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onInitView$3;-><init>(Ljava/lang/Object;)V

    .line 78
    .line 79
    .line 80
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setAfterTextChangedListener(Lkotlin/jvm/functions/Function1;)V

    .line 81
    .line 82
    .line 83
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LTU0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LTU0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LTU0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v2, v0}, LTU0/d;->a(LwX0/c;)LTU0/c;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0, p0}, LTU0/c;->d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    new-instance v2, Ljava/lang/StringBuilder;

    .line 70
    .line 71
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 72
    .line 73
    .line 74
    const-string v3, "Cannot create dependency "

    .line 75
    .line 76
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->g4()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->i4()Lkotlinx/coroutines/flow/e;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->R2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->b4()Lkotlinx/coroutines/flow/e;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$3;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v4

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v5, 0x3

    .line 108
    const/4 v6, 0x0

    .line 109
    const/4 v2, 0x0

    .line 110
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 111
    .line 112
    .line 113
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->Y2()V

    .line 114
    .line 115
    .line 116
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
