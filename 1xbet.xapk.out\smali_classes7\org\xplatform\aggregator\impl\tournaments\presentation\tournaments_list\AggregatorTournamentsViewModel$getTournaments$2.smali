.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_list.AggregatorTournamentsViewModel$getTournaments$2"
    f = "AggregatorTournamentsViewModel.kt"
    l = {
        0x16c,
        0x172
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->X4(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field L$6:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 11

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$6:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v1, Ljava/util/Collection;

    .line 18
    .line 19
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$5:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v3, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 22
    .line 23
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$4:Ljava/lang/Object;

    .line 24
    .line 25
    check-cast v4, Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 26
    .line 27
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$3:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast v5, LHX0/e;

    .line 30
    .line 31
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$2:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast v6, Ljava/util/Iterator;

    .line 34
    .line 35
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$1:Ljava/lang/Object;

    .line 36
    .line 37
    check-cast v7, Ljava/util/Collection;

    .line 38
    .line 39
    iget-object v8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$0:Ljava/lang/Object;

    .line 40
    .line 41
    check-cast v8, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 42
    .line 43
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    goto/16 :goto_3

    .line 47
    .line 48
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw p1

    .line 56
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 61
    .line 62
    .line 63
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 64
    .line 65
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->A4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lw81/b;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    invoke-interface {p1}, Lw81/b;->invoke()Lkotlinx/coroutines/flow/e;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iput v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->label:I

    .line 74
    .line 75
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    if-ne p1, v0, :cond_3

    .line 80
    .line 81
    goto :goto_2

    .line 82
    :cond_3
    :goto_0
    check-cast p1, Ljava/util/List;

    .line 83
    .line 84
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 85
    .line 86
    new-instance v3, Ljava/util/ArrayList;

    .line 87
    .line 88
    const/16 v4, 0xa

    .line 89
    .line 90
    invoke-static {p1, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 91
    .line 92
    .line 93
    move-result v4

    .line 94
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 95
    .line 96
    .line 97
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    move-object v6, p1

    .line 102
    move-object v8, v1

    .line 103
    move-object v1, v3

    .line 104
    :goto_1
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 105
    .line 106
    .line 107
    move-result p1

    .line 108
    if-eqz p1, :cond_5

    .line 109
    .line 110
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    move-object v3, p1

    .line 115
    check-cast v3, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 116
    .line 117
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->I4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Ljava/util/Map;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 122
    .line 123
    .line 124
    move-result-wide v4

    .line 125
    invoke-static {v4, v5}, LHc/a;->f(J)Ljava/lang/Long;

    .line 126
    .line 127
    .line 128
    move-result-object v4

    .line 129
    invoke-interface {p1, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 130
    .line 131
    .line 132
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->F4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;

    .line 133
    .line 134
    .line 135
    move-result-object v4

    .line 136
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 137
    .line 138
    .line 139
    move-result-object v5

    .line 140
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->y4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lgk/b;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    invoke-virtual {v3}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 145
    .line 146
    .line 147
    move-result-object v7

    .line 148
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->b()J

    .line 149
    .line 150
    .line 151
    move-result-wide v9

    .line 152
    iput-object v8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$0:Ljava/lang/Object;

    .line 153
    .line 154
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$1:Ljava/lang/Object;

    .line 155
    .line 156
    iput-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$2:Ljava/lang/Object;

    .line 157
    .line 158
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$3:Ljava/lang/Object;

    .line 159
    .line 160
    iput-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$4:Ljava/lang/Object;

    .line 161
    .line 162
    iput-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$5:Ljava/lang/Object;

    .line 163
    .line 164
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->L$6:Ljava/lang/Object;

    .line 165
    .line 166
    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->label:I

    .line 167
    .line 168
    invoke-interface {p1, v9, v10, p0}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 169
    .line 170
    .line 171
    move-result-object p1

    .line 172
    if-ne p1, v0, :cond_4

    .line 173
    .line 174
    :goto_2
    return-object v0

    .line 175
    :cond_4
    move-object v7, v1

    .line 176
    :goto_3
    check-cast p1, Lbk/a;

    .line 177
    .line 178
    invoke-virtual {p1}, Lbk/a;->o()Ljava/lang/String;

    .line 179
    .line 180
    .line 181
    move-result-object p1

    .line 182
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->w4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Ljava/util/Locale;

    .line 183
    .line 184
    .line 185
    move-result-object v9

    .line 186
    invoke-static {v3, v4, v5, p1, v9}, Ljb1/k;->a(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Lorg/xbet/uikit_aggregator/aggregatorTournamentsCardsNative/models/AggregatorTournamentCardsNativeDSStyleType;LHX0/e;Ljava/lang/String;Ljava/util/Locale;)Lv21/c;

    .line 187
    .line 188
    .line 189
    move-result-object p1

    .line 190
    invoke-interface {v1, p1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 191
    .line 192
    .line 193
    move-object v1, v7

    .line 194
    goto :goto_1

    .line 195
    :cond_5
    check-cast v1, Ljava/util/List;

    .line 196
    .line 197
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 198
    .line 199
    .line 200
    move-result p1

    .line 201
    if-nez p1, :cond_6

    .line 202
    .line 203
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 204
    .line 205
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->t4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 206
    .line 207
    .line 208
    move-result-object p1

    .line 209
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$d;

    .line 210
    .line 211
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$d;-><init>(Ljava/util/List;)V

    .line 212
    .line 213
    .line 214
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 215
    .line 216
    .line 217
    goto :goto_4

    .line 218
    :cond_6
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$getTournaments$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 219
    .line 220
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->M4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)V

    .line 221
    .line 222
    .line 223
    :goto_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 224
    .line 225
    return-object p1
.end method
