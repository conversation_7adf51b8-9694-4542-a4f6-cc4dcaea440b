.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a;\u0010\u000b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\n0\t0\u00082\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u0006H\u0000\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\u00a8\u0006\r"
    }
    d2 = {
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "LCb1/a;",
        "aggregatorPopularItemsClickListener",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
        "aggregatorPopularClassicCommonClickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "h",
        "(LUX0/k;LCb1/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/c;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->j(LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;LCb1/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->n(LB4/a;LCb1/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LCb1/a;Ljava/lang/String;ILN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->l(LCb1/a;Ljava/lang/String;ILN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->o(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LB4/a;LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->k(LB4/a;LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(LCb1/a;ILN21/k;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt;->m(LCb1/a;ILN21/k;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final h(LUX0/k;LCb1/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;
    .locals 2
    .param p0    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LCb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LUX0/k;",
            "LCb1/a;",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LOb1/h;

    .line 2
    .line 3
    invoke-direct {v0}, LOb1/h;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LOb1/i;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1, p3, p2}, LOb1/i;-><init>(LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt$popularGamesCategoryContainerDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt$popularGamesCategoryContainerDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt$popularGamesCategoryContainerDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularGamesCategoryContainerDelegateKt$popularGamesCategoryContainerDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/c;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/c;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/c;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final j(LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;LB4/a;)Lkotlin/Unit;
    .locals 8

    .line 1
    invoke-virtual {p4}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LIb1/c;

    .line 6
    .line 7
    iget-object v0, v0, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 8
    .line 9
    const/4 v1, 0x0

    .line 10
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 11
    .line 12
    .line 13
    new-instance v2, LOb1/j;

    .line 14
    .line 15
    move-object v4, p0

    .line 16
    move-object v5, p1

    .line 17
    move-object v6, p2

    .line 18
    move-object v7, p3

    .line 19
    move-object v3, p4

    .line 20
    invoke-direct/range {v2 .. v7}, LOb1/j;-><init>(LB4/a;LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v3, v2}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 24
    .line 25
    .line 26
    new-instance p0, LOb1/k;

    .line 27
    .line 28
    invoke-direct {p0, v4, v3}, LOb1/k;-><init>(LUX0/k;LB4/a;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {v3, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 32
    .line 33
    .line 34
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 35
    .line 36
    return-object p0
.end method

.method public static final k(LB4/a;LUX0/k;LCb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p5

    .line 5
    check-cast p5, LQb1/c;

    .line 6
    .line 7
    invoke-virtual {p5}, LQb1/c;->s()Z

    .line 8
    .line 9
    .line 10
    move-result p5

    .line 11
    if-eqz p5, :cond_0

    .line 12
    .line 13
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p5

    .line 17
    check-cast p5, LQb1/c;

    .line 18
    .line 19
    invoke-virtual {p5}, LQb1/c;->j()I

    .line 20
    .line 21
    .line 22
    move-result p5

    .line 23
    goto :goto_0

    .line 24
    :cond_0
    sget-object p5, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->Companion:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;

    .line 25
    .line 26
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LQb1/c;

    .line 31
    .line 32
    invoke-virtual {v0}, LQb1/c;->e()Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p5, v0}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;->a(Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;)I

    .line 37
    .line 38
    .line 39
    move-result p5

    .line 40
    invoke-static {p5}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/x;->b(I)I

    .line 41
    .line 42
    .line 43
    move-result p5

    .line 44
    :goto_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    check-cast v0, LIb1/c;

    .line 49
    .line 50
    iget-object v0, v0, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 51
    .line 52
    new-instance v1, LOb1/l;

    .line 53
    .line 54
    invoke-direct {v1, p2, p3, p5}, LOb1/l;-><init>(LCb1/a;Ljava/lang/String;I)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    check-cast v0, LIb1/c;

    .line 65
    .line 66
    iget-object v0, v0, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 67
    .line 68
    new-instance v1, LOb1/m;

    .line 69
    .line 70
    invoke-direct {v1, p2, p5}, LOb1/m;-><init>(LCb1/a;I)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setOnActionIconClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 77
    .line 78
    .line 79
    move-result-object p5

    .line 80
    check-cast p5, LIb1/c;

    .line 81
    .line 82
    iget-object p5, p5, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 83
    .line 84
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    check-cast v0, LQb1/c;

    .line 89
    .line 90
    invoke-virtual {v0}, LQb1/c;->f()I

    .line 91
    .line 92
    .line 93
    move-result v0

    .line 94
    invoke-virtual {p5, v0}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setStyle(I)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object p5

    .line 101
    check-cast p5, LQb1/c;

    .line 102
    .line 103
    invoke-virtual {p5}, LQb1/c;->o()Z

    .line 104
    .line 105
    .line 106
    move-result p5

    .line 107
    if-eqz p5, :cond_1

    .line 108
    .line 109
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 110
    .line 111
    .line 112
    move-result-object p2

    .line 113
    check-cast p2, LIb1/c;

    .line 114
    .line 115
    iget-object p2, p2, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 116
    .line 117
    invoke-virtual {p2}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->o()V

    .line 118
    .line 119
    .line 120
    goto :goto_2

    .line 121
    :cond_1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 122
    .line 123
    .line 124
    move-result-object v0

    .line 125
    check-cast v0, LIb1/c;

    .line 126
    .line 127
    iget-object v0, v0, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 128
    .line 129
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 130
    .line 131
    .line 132
    move-result-object v1

    .line 133
    check-cast v1, LQb1/c;

    .line 134
    .line 135
    invoke-virtual {v1}, LQb1/c;->d()Ljava/util/List;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;->setItems(Ljava/util/List;)V

    .line 140
    .line 141
    .line 142
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 143
    .line 144
    .line 145
    move-result-object v0

    .line 146
    check-cast v0, LIb1/c;

    .line 147
    .line 148
    iget-object v0, v0, LIb1/c;->b:Lorg/xbet/uikit/components/header/HeaderLarge;

    .line 149
    .line 150
    new-instance v1, LOb1/n;

    .line 151
    .line 152
    invoke-direct {v1, p0, p2, p4, p3}, LOb1/n;-><init>(LB4/a;LCb1/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)V

    .line 153
    .line 154
    .line 155
    const/4 p2, 0x1

    .line 156
    const/4 p3, 0x0

    .line 157
    invoke-static {p3, v1, p2, p3}, LN11/f;->k(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 158
    .line 159
    .line 160
    move-result-object p2

    .line 161
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/components/header/HeaderLarge;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 162
    .line 163
    .line 164
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 165
    .line 166
    .line 167
    move-result-object p2

    .line 168
    check-cast p2, LIb1/c;

    .line 169
    .line 170
    iget-object p2, p2, LIb1/c;->b:Lorg/xbet/uikit/components/header/HeaderLarge;

    .line 171
    .line 172
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 173
    .line 174
    .line 175
    move-result-object p3

    .line 176
    check-cast p3, LQb1/c;

    .line 177
    .line 178
    invoke-virtual {p3}, LQb1/c;->s()Z

    .line 179
    .line 180
    .line 181
    move-result p3

    .line 182
    if-eqz p3, :cond_2

    .line 183
    .line 184
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 185
    .line 186
    .line 187
    move-result-object p3

    .line 188
    check-cast p3, LQb1/c;

    .line 189
    .line 190
    invoke-virtual {p3}, LQb1/c;->getTitle()Ljava/lang/String;

    .line 191
    .line 192
    .line 193
    move-result-object p3

    .line 194
    goto :goto_1

    .line 195
    :cond_2
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 196
    .line 197
    .line 198
    move-result-object p3

    .line 199
    invoke-virtual {p3}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 200
    .line 201
    .line 202
    move-result-object p3

    .line 203
    sget-object p4, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->Companion:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;

    .line 204
    .line 205
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 206
    .line 207
    .line 208
    move-result-object v0

    .line 209
    check-cast v0, LQb1/c;

    .line 210
    .line 211
    invoke-virtual {v0}, LQb1/c;->e()Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 212
    .line 213
    .line 214
    move-result-object v0

    .line 215
    invoke-virtual {p4, v0}, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory$a;->a(Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;)I

    .line 216
    .line 217
    .line 218
    move-result p4

    .line 219
    invoke-virtual {p3, p4}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    .line 220
    .line 221
    .line 222
    move-result-object p3

    .line 223
    :goto_1
    invoke-virtual {p2, p3}, Lorg/xbet/uikit/components/header/HeaderLarge;->setTitle(Ljava/lang/CharSequence;)V

    .line 224
    .line 225
    .line 226
    :goto_2
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 227
    .line 228
    .line 229
    move-result-object p2

    .line 230
    check-cast p2, LIb1/c;

    .line 231
    .line 232
    iget-object p2, p2, LIb1/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 233
    .line 234
    const/16 p3, 0x8

    .line 235
    .line 236
    const/4 p4, 0x0

    .line 237
    if-eqz p5, :cond_3

    .line 238
    .line 239
    const/4 v0, 0x0

    .line 240
    goto :goto_3

    .line 241
    :cond_3
    const/16 v0, 0x8

    .line 242
    .line 243
    :goto_3
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    .line 244
    .line 245
    .line 246
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 247
    .line 248
    .line 249
    move-result-object p2

    .line 250
    check-cast p2, LIb1/c;

    .line 251
    .line 252
    iget-object p2, p2, LIb1/c;->b:Lorg/xbet/uikit/components/header/HeaderLarge;

    .line 253
    .line 254
    if-nez p5, :cond_4

    .line 255
    .line 256
    const/4 p3, 0x0

    .line 257
    :cond_4
    invoke-virtual {p2, p3}, Landroid/view/View;->setVisibility(I)V

    .line 258
    .line 259
    .line 260
    invoke-virtual {p0}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 261
    .line 262
    .line 263
    move-result p2

    .line 264
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 265
    .line 266
    .line 267
    move-result-object p2

    .line 268
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 269
    .line 270
    .line 271
    move-result-object p0

    .line 272
    check-cast p0, LIb1/c;

    .line 273
    .line 274
    iget-object p0, p0, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 275
    .line 276
    invoke-virtual {p1, p2, p0}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 277
    .line 278
    .line 279
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 280
    .line 281
    return-object p0
.end method

.method public static final l(LCb1/a;Ljava/lang/String;ILN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p3}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-interface {p0, p1, v0, v1, p2}, LCb1/a;->k0(Ljava/lang/String;JI)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final m(LCb1/a;ILN21/k;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p2}, LN21/k;->e()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-virtual {p2}, LN21/k;->c()LN21/m;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-virtual {p2}, LN21/m;->b()Z

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    invoke-interface {p0, v0, v1, p2, p1}, LCb1/a;->e0(JZI)V

    .line 14
    .line 15
    .line 16
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 17
    .line 18
    return-object p0
.end method

.method public static final n(LB4/a;LCb1/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    check-cast p4, LQb1/c;

    .line 6
    .line 7
    invoke-virtual {p4}, LQb1/c;->s()Z

    .line 8
    .line 9
    .line 10
    move-result p4

    .line 11
    if-eqz p4, :cond_0

    .line 12
    .line 13
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    check-cast p2, LQb1/c;

    .line 18
    .line 19
    invoke-virtual {p2}, LQb1/c;->getId()J

    .line 20
    .line 21
    .line 22
    move-result-wide p2

    .line 23
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p0

    .line 27
    check-cast p0, LQb1/c;

    .line 28
    .line 29
    invoke-virtual {p0}, LQb1/c;->getTitle()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    const/4 p4, 0x1

    .line 34
    invoke-interface {p1, p2, p3, p0, p4}, LCb1/a;->h0(JLjava/lang/String;Z)V

    .line 35
    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-interface {p2, p3, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;->l(Ljava/lang/String;Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 46
    .line 47
    return-object p0
.end method

.method public static final o(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LIb1/c;

    .line 14
    .line 15
    iget-object p1, p1, LIb1/c;->d:Lorg/xbet/uikit_aggregator/aggregatorgamecardcollection/AggregatorGameCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method
