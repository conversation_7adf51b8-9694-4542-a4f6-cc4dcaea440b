.class Lcom/google/common/collect/Lists$RandomAccessListWrapper;
.super Lcom/google/common/collect/Lists$AbstractListWrapper;
.source "SourceFile"

# interfaces
.implements Ljava/util/RandomAccess;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/common/collect/Lists;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "RandomAccessListWrapper"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/google/common/collect/Lists$AbstractListWrapper<",
        "TE;>;",
        "Ljava/util/RandomAccess;"
    }
.end annotation
