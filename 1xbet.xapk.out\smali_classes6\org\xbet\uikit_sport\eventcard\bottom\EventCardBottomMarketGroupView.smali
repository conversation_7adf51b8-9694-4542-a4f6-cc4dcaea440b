.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u0001B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0008\u0010\t\u001a\u0004\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000b\u001a\u00020\n2\u0008\u0008\u0001\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000b\u0010\u000fJ?\u0010\u0015\u001a\u00020\n2\u000c\u0010\u0012\u001a\u0008\u0012\u0004\u0012\u00020\u00110\u00102\u0010\u0008\u0002\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u00102\u0010\u0008\u0002\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0010\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0019\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0016\u0010\u001d\u001a\u00020\r8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u0014\u0010!\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 \u00a8\u0006\""
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "text",
        "",
        "setHeader",
        "(Ljava/lang/CharSequence;)V",
        "",
        "resId",
        "(I)V",
        "",
        "LL11/a;",
        "firstColumn",
        "secondColumn",
        "thirdColumn",
        "setMarketColumns",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V",
        "",
        "visible",
        "s",
        "(Z)V",
        "a",
        "I",
        "currentMaxLastIndexOfColumns",
        "LC31/i;",
        "b",
        "LC31/i;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public a:I

.field public final b:LC31/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/i;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/i;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic setMarketColumns$default(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;Ljava/util/List;Ljava/util/List;Ljava/util/List;ILjava/lang/Object;)V
    .locals 1

    .line 1
    and-int/lit8 p5, p4, 0x2

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    if-eqz p5, :cond_0

    .line 5
    .line 6
    move-object p2, v0

    .line 7
    :cond_0
    and-int/lit8 p4, p4, 0x4

    .line 8
    .line 9
    if-eqz p4, :cond_1

    .line 10
    .line 11
    move-object p3, v0

    .line 12
    :cond_1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->setMarketColumns(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method


# virtual methods
.method public final s(Z)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    .line 2
    .line 3
    iget-object v0, v0, LC31/i;->c:Landroid/widget/LinearLayout;

    .line 4
    .line 5
    invoke-static {v0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->a:I

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    add-int/2addr v1, v2

    .line 13
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->i0(Lkotlin/sequences/Sequence;I)Lkotlin/sequences/Sequence;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView$a;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView$a;

    .line 18
    .line 19
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->O(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-interface {v0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    const/4 v1, 0x0

    .line 28
    const/4 v3, 0x0

    .line 29
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 30
    .line 31
    .line 32
    move-result v4

    .line 33
    if-eqz v4, :cond_4

    .line 34
    .line 35
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    add-int/lit8 v5, v3, 0x1

    .line 40
    .line 41
    if-gez v3, :cond_0

    .line 42
    .line 43
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 44
    .line 45
    .line 46
    :cond_0
    check-cast v4, Lorg/xbet/uikit_sport/eventcard/bottom/BottomMarketLine;

    .line 47
    .line 48
    if-eqz v3, :cond_2

    .line 49
    .line 50
    if-eqz p1, :cond_1

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_1
    const/4 v3, 0x0

    .line 54
    goto :goto_2

    .line 55
    :cond_2
    :goto_1
    const/4 v3, 0x1

    .line 56
    :goto_2
    if-eqz v3, :cond_3

    .line 57
    .line 58
    const/4 v3, 0x0

    .line 59
    goto :goto_3

    .line 60
    :cond_3
    const/16 v3, 0x8

    .line 61
    .line 62
    :goto_3
    invoke-virtual {v4, v3}, Landroid/view/View;->setVisibility(I)V

    .line 63
    .line 64
    .line 65
    move v3, v5

    .line 66
    goto :goto_0

    .line 67
    :cond_4
    return-void
.end method

.method public final setHeader(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->setHeader(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setHeader(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    iget-object v0, v0, LC31/i;->b:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    goto :goto_1

    :cond_1
    const/16 v1, 0x8

    .line 2
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    iget-object v0, v0, LC31/i;->b:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setMarketColumns(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V
    .locals 11
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LL11/a;",
            ">;",
            "Ljava/util/List<",
            "LL11/a;",
            ">;",
            "Ljava/util/List<",
            "LL11/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    if-eqz v0, :cond_3

    .line 8
    .line 9
    if-eqz p2, :cond_3

    .line 10
    .line 11
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    goto :goto_1

    .line 18
    :cond_0
    if-eqz p3, :cond_1

    .line 19
    .line 20
    invoke-interface {p3}, Ljava/util/Collection;->isEmpty()Z

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    xor-int/2addr v0, v1

    .line 25
    goto :goto_0

    .line 26
    :cond_1
    const/4 v0, 0x0

    .line 27
    :goto_0
    if-eqz v0, :cond_2

    .line 28
    .line 29
    goto :goto_1

    .line 30
    :cond_2
    const/4 v0, 0x0

    .line 31
    goto :goto_2

    .line 32
    :cond_3
    :goto_1
    const/4 v0, 0x1

    .line 33
    :goto_2
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    .line 34
    .line 35
    iget-object v3, v3, LC31/i;->c:Landroid/widget/LinearLayout;

    .line 36
    .line 37
    if-eqz v0, :cond_4

    .line 38
    .line 39
    const/4 v4, 0x0

    .line 40
    goto :goto_3

    .line 41
    :cond_4
    const/16 v4, 0x8

    .line 42
    .line 43
    :goto_3
    invoke-virtual {v3, v4}, Landroid/view/View;->setVisibility(I)V

    .line 44
    .line 45
    .line 46
    if-nez v0, :cond_5

    .line 47
    .line 48
    return-void

    .line 49
    :cond_5
    invoke-static {p1}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    if-eqz p2, :cond_6

    .line 54
    .line 55
    invoke-static {p2}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 56
    .line 57
    .line 58
    move-result v3

    .line 59
    goto :goto_4

    .line 60
    :cond_6
    const/4 v3, 0x0

    .line 61
    :goto_4
    if-eqz p3, :cond_7

    .line 62
    .line 63
    invoke-static {p3}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 64
    .line 65
    .line 66
    move-result v4

    .line 67
    goto :goto_5

    .line 68
    :cond_7
    const/4 v4, 0x0

    .line 69
    :goto_5
    invoke-static {v3, v4}, Ljava/lang/Math;->max(II)I

    .line 70
    .line 71
    .line 72
    move-result v3

    .line 73
    invoke-static {v0, v3}, Ljava/lang/Math;->max(II)I

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    .line 78
    .line 79
    iget-object v3, v3, LC31/i;->c:Landroid/widget/LinearLayout;

    .line 80
    .line 81
    invoke-virtual {v3}, Landroid/view/ViewGroup;->getChildCount()I

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    const/4 v4, 0x2

    .line 86
    const/4 v5, 0x0

    .line 87
    if-gt v3, v0, :cond_8

    .line 88
    .line 89
    :goto_6
    iget-object v6, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    .line 90
    .line 91
    iget-object v6, v6, LC31/i;->c:Landroid/widget/LinearLayout;

    .line 92
    .line 93
    new-instance v7, Lorg/xbet/uikit_sport/eventcard/bottom/BottomMarketLine;

    .line 94
    .line 95
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 96
    .line 97
    .line 98
    move-result-object v8

    .line 99
    invoke-direct {v7, v8, v5, v4, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/BottomMarketLine;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 100
    .line 101
    .line 102
    invoke-virtual {v6, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 103
    .line 104
    .line 105
    if-eq v3, v0, :cond_8

    .line 106
    .line 107
    add-int/lit8 v3, v3, 0x1

    .line 108
    .line 109
    goto :goto_6

    .line 110
    :cond_8
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->b:LC31/i;

    .line 111
    .line 112
    iget-object v3, v3, LC31/i;->c:Landroid/widget/LinearLayout;

    .line 113
    .line 114
    invoke-static {v3}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 115
    .line 116
    .line 117
    move-result-object v3

    .line 118
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView$b;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView$b;

    .line 119
    .line 120
    invoke-static {v3, v6}, Lkotlin/sequences/SequencesKt___SequencesKt;->O(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 121
    .line 122
    .line 123
    move-result-object v3

    .line 124
    invoke-interface {v3}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 125
    .line 126
    .line 127
    move-result-object v3

    .line 128
    const/4 v6, 0x0

    .line 129
    :goto_7
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 130
    .line 131
    .line 132
    move-result v7

    .line 133
    if-eqz v7, :cond_d

    .line 134
    .line 135
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object v7

    .line 139
    add-int/lit8 v8, v6, 0x1

    .line 140
    .line 141
    if-gez v6, :cond_9

    .line 142
    .line 143
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 144
    .line 145
    .line 146
    :cond_9
    check-cast v7, Lorg/xbet/uikit_sport/eventcard/bottom/BottomMarketLine;

    .line 147
    .line 148
    const/4 v9, 0x3

    .line 149
    new-array v9, v9, [LL11/a;

    .line 150
    .line 151
    invoke-static {p1, v6}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 152
    .line 153
    .line 154
    move-result-object v10

    .line 155
    aput-object v10, v9, v2

    .line 156
    .line 157
    if-eqz p2, :cond_a

    .line 158
    .line 159
    invoke-static {p2, v6}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 160
    .line 161
    .line 162
    move-result-object v10

    .line 163
    check-cast v10, LL11/a;

    .line 164
    .line 165
    goto :goto_8

    .line 166
    :cond_a
    move-object v10, v5

    .line 167
    :goto_8
    aput-object v10, v9, v1

    .line 168
    .line 169
    if-eqz p3, :cond_b

    .line 170
    .line 171
    invoke-static {p3, v6}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 172
    .line 173
    .line 174
    move-result-object v10

    .line 175
    check-cast v10, LL11/a;

    .line 176
    .line 177
    goto :goto_9

    .line 178
    :cond_b
    move-object v10, v5

    .line 179
    :goto_9
    aput-object v10, v9, v4

    .line 180
    .line 181
    invoke-static {v9}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 182
    .line 183
    .line 184
    move-result-object v9

    .line 185
    if-gt v6, v0, :cond_c

    .line 186
    .line 187
    invoke-virtual {v7, v9}, Lorg/xbet/uikit_sport/eventcard/bottom/BottomMarketLine;->setMarkets(Ljava/util/List;)V

    .line 188
    .line 189
    .line 190
    :cond_c
    move v6, v8

    .line 191
    goto :goto_7

    .line 192
    :cond_d
    iput v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->a:I

    .line 193
    .line 194
    return-void
.end method
