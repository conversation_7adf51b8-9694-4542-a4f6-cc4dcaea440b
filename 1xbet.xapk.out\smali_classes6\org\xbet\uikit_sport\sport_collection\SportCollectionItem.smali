.class public final Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\r\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u0001B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0015\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\n2\u0008\u0010\u000e\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u000f\u001a\u00020\n2\u0008\u0008\u0001\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u000f\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\n2\u0008\u0010\u0015\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u0016\u001a\u00020\n2\u0008\u0008\u0001\u0010\u0015\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0016\u0010\u0013J\u0015\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u0016\u0010\u0019J\u0015\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u0016\u0010\u001bJ\u0015\u0010\u001d\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u001d\u0010\u000cJ\u000f\u0010\u001e\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fR\u001b\u0010%\u001a\u00020 8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010$R\u0014\u0010)\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010,\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+\u00a8\u0006-"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "show",
        "",
        "f",
        "(Z)V",
        "",
        "text",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "",
        "resId",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "icon",
        "setIcon",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "(Ljava/lang/String;)V",
        "LL11/c;",
        "(LL11/c;)V",
        "selected",
        "setItemSelected",
        "e",
        "()V",
        "LC31/P;",
        "a",
        "Lkotlin/j;",
        "getBinding",
        "()LC31/P;",
        "binding",
        "Lorg/xbet/uikit/components/badges/Badge;",
        "b",
        "Lorg/xbet/uikit/components/badges/Badge;",
        "couponBadge",
        "c",
        "I",
        "badgePadding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit/components/badges/Badge;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    new-instance p2, LN31/a;

    invoke-direct {p2, p1, p0}, LN31/a;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->a:Lkotlin/j;

    .line 5
    new-instance v0, Lorg/xbet/uikit/components/badges/Badge;

    new-instance v1, Lk/d;

    sget p2, LlZ0/n;->Widget_Badge_Market_Coupon:I

    invoke-direct {v1, p1, p2}, Lk/d;-><init>(Landroid/content/Context;I)V

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/badges/Badge;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 6
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    move-result-object p2

    iget-object p2, p2, LC31/P;->b:Landroid/widget/FrameLayout;

    new-instance v1, LN31/b;

    invoke-direct {v1, p0}, LN31/b;-><init>(Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)V

    invoke-virtual {v0, p2, v1}, Lorg/xbet/uikit/components/badges/Badge;->k(Landroid/view/View;Lkotlin/jvm/functions/Function0;)Lorg/xbet/uikit/components/badges/Badge;

    .line 7
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->b:Lorg/xbet/uikit/components/badges/Badge;

    .line 8
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_2:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->c:I

    const/4 p1, 0x1

    .line 9
    invoke-virtual {p0, p1}, Landroid/view/View;->setClickable(Z)V

    .line 10
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->e()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)LC31/P;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->c(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)LC31/P;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->d(Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)Landroid/view/View;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)LC31/P;
    .locals 1

    .line 1
    invoke-static {p0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const/4 v0, 0x1

    .line 6
    invoke-static {p0, p1, v0}, LC31/P;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/P;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method

.method public static final d(Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)Landroid/view/View;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, LC31/P;->b:Landroid/widget/FrameLayout;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-interface {p0}, Landroid/view/ViewParent;->getParent()Landroid/view/ViewParent;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    check-cast p0, Landroid/view/View;

    .line 16
    .line 17
    return-object p0
.end method

.method private final getBinding()LC31/P;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LC31/P;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final e()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->b:Lorg/xbet/uikit/components/badges/Badge;

    .line 9
    .line 10
    const v1, 0x800033

    .line 11
    .line 12
    .line 13
    iget v2, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->c:I

    .line 14
    .line 15
    invoke-virtual {v0, v1, v2, v2}, Lorg/xbet/uikit/components/badges/Badge;->setPosition(III)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->b:Lorg/xbet/uikit/components/badges/Badge;

    .line 20
    .line 21
    const v1, 0x800035

    .line 22
    .line 23
    .line 24
    iget v2, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->c:I

    .line 25
    .line 26
    invoke-virtual {v0, v1, v2, v2}, Lorg/xbet/uikit/components/badges/Badge;->setPosition(III)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final f(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->b:Lorg/xbet/uikit/components/badges/Badge;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x0

    .line 6
    goto :goto_0

    .line 7
    :cond_0
    const/16 p1, 0x8

    .line 8
    .line 9
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final setIcon(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setIcon(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setIcon(LL11/c;)V
    .locals 1
    .param p1    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 7
    instance-of v0, p1, LL11/c$c;

    if-eqz v0, :cond_0

    check-cast p1, LL11/c$c;

    invoke-virtual {p1}, LL11/c$c;->h()I

    move-result p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setIcon(I)V

    return-void

    .line 8
    :cond_0
    instance-of v0, p1, LL11/c$d;

    if-eqz v0, :cond_1

    check-cast p1, LL11/c$d;

    invoke-virtual {p1}, LL11/c$d;->i()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setIcon(Ljava/lang/String;)V

    return-void

    .line 9
    :cond_1
    instance-of v0, p1, LL11/c$b;

    if-eqz v0, :cond_2

    check-cast p1, LL11/c$b;

    invoke-virtual {p1}, LL11/c$b;->c()Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setIcon(Landroid/graphics/drawable/Drawable;)V

    return-void

    .line 10
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw p1
.end method

.method public final setIcon(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    move-result-object v0

    iget-object v0, v0, LC31/P;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setIcon(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/bumptech/glide/b;->t(Landroid/content/Context;)Lcom/bumptech/glide/i;

    move-result-object v0

    .line 4
    invoke-virtual {v0, p1}, Lcom/bumptech/glide/i;->x(Ljava/lang/String;)Lcom/bumptech/glide/h;

    move-result-object p1

    .line 5
    sget v0, LlZ0/h;->ic_glyph_category_new:I

    invoke-virtual {p1, v0}, Lcom/bumptech/glide/request/a;->d0(I)Lcom/bumptech/glide/request/a;

    move-result-object p1

    check-cast p1, Lcom/bumptech/glide/h;

    .line 6
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    move-result-object v0

    iget-object v0, v0, LC31/P;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    if-eqz v0, :cond_0

    invoke-virtual {p1, v0}, Lcom/bumptech/glide/h;->I0(Landroid/widget/ImageView;)LO3/j;

    return-void

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "Required value was null."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public final setItemSelected(Z)V
    .locals 5

    .line 1
    invoke-virtual {p0, p1}, Landroid/view/View;->setSelected(Z)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    sget v1, LlZ0/d;->uikitBackgroundContent:I

    .line 9
    .line 10
    const/4 v2, 0x0

    .line 11
    const/4 v3, 0x2

    .line 12
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    sget v4, LlZ0/d;->uikitPrimary:I

    .line 25
    .line 26
    invoke-static {v1, v4, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-static {v0, v1}, Lorg/xbet/uikit/utils/i;->g(Ljava/lang/Integer;Ljava/lang/Integer;)Landroid/content/res/ColorStateList;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-static {p0, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 39
    .line 40
    .line 41
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iget-object v0, v0, LC31/P;->c:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    if-eqz p1, :cond_0

    .line 52
    .line 53
    sget v4, LlZ0/d;->uikitStaticWhite:I

    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_0
    sget v4, LlZ0/d;->uikitSecondary:I

    .line 57
    .line 58
    :goto_0
    invoke-static {v1, v4, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 63
    .line 64
    .line 65
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    iget-object v0, v0, LC31/P;->d:Landroid/widget/TextView;

    .line 70
    .line 71
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    if-eqz p1, :cond_1

    .line 76
    .line 77
    sget v4, LlZ0/d;->uikitStaticWhite:I

    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_1
    sget v4, LlZ0/d;->uikitSecondary:I

    .line 81
    .line 82
    :goto_1
    invoke-static {v1, v4, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 83
    .line 84
    .line 85
    move-result v1

    .line 86
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 87
    .line 88
    .line 89
    if-eqz p1, :cond_2

    .line 90
    .line 91
    const/4 p1, 0x0

    .line 92
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->f(Z)V

    .line 93
    .line 94
    .line 95
    :cond_2
    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->getBinding()LC31/P;

    move-result-object v0

    iget-object v0, v0, LC31/P;->d:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
