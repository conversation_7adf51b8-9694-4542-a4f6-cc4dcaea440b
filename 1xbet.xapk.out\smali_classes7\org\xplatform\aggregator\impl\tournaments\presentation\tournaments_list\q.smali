.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/q;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/q;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/q;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/q;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->m4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
