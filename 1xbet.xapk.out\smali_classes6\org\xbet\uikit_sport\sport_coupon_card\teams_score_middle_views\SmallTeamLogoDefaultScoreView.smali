.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LZ31/a;


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0012\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001f\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J7\u0010\u0018\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001c\u001a\u00020\u000b2\u0006\u0010\u001b\u001a\u00020\u001aH\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dR\u0014\u0010 \u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u001fR\u0014\u0010\"\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010\u001fR\u0016\u0010%\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008#\u0010$R\u0016\u0010\'\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010$R\u0014\u0010)\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010$R\u0014\u0010+\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010$R\u0014\u0010-\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010$R\u0014\u0010/\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010$\u00a8\u00060"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;",
        "Landroid/widget/FrameLayout;",
        "LZ31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "a",
        "()V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
        "firstTeamScoreCellView",
        "b",
        "secondTeamScoreCellView",
        "c",
        "I",
        "maxScoreLength",
        "d",
        "scoreCellsWidth",
        "e",
        "minimalCellWidth",
        "f",
        "widthIncreaseStepPerDigit",
        "g",
        "scoreCellsBetweenMargin",
        "h",
        "verticalPadding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:I

.field public d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 6
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 7
    sget p2, Lm31/f;->Widget_ScoreCell_SmallTeamLogo:I

    .line 8
    invoke-direct {v1, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 10
    new-instance v1, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 11
    new-instance v2, Landroid/view/ContextThemeWrapper;

    .line 12
    sget p2, Lm31/f;->Widget_ScoreCell_SmallTeamLogo:I

    .line 13
    invoke-direct {v2, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    const/4 v5, 0x6

    const/4 v6, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    .line 14
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_24:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->e:I

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->f:I

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->g:I

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->h:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final a()V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->c:I

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    if-gt v0, v1, :cond_0

    .line 5
    .line 6
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->e:I

    .line 7
    .line 8
    goto :goto_0

    .line 9
    :cond_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->e:I

    .line 10
    .line 11
    sub-int/2addr v0, v1

    .line 12
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->f:I

    .line 13
    .line 14
    mul-int v0, v0, v1

    .line 15
    .line 16
    add-int/2addr v0, v2

    .line 17
    :goto_0
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->d:I

    .line 18
    .line 19
    return-void
.end method


# virtual methods
.method public onLayout(ZIIII)V
    .locals 10

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-lez p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-lez p1, :cond_0

    .line 12
    .line 13
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->h:I

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 16
    .line 17
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 18
    .line 19
    .line 20
    move-result v4

    .line 21
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 22
    .line 23
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    add-int v5, v3, p1

    .line 28
    .line 29
    const/4 v2, 0x0

    .line 30
    move-object v0, p0

    .line 31
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 32
    .line 33
    .line 34
    iget-object p1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 35
    .line 36
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 37
    .line 38
    .line 39
    move-result p1

    .line 40
    add-int/2addr v3, p1

    .line 41
    iget p1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->g:I

    .line 42
    .line 43
    add-int v7, v3, p1

    .line 44
    .line 45
    iget-object v4, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 46
    .line 47
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 48
    .line 49
    .line 50
    move-result v8

    .line 51
    iget-object p1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 52
    .line 53
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    add-int v9, v7, p1

    .line 58
    .line 59
    const/4 v6, 0x0

    .line 60
    move-object v5, v4

    .line 61
    invoke-static/range {v4 .. v9}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 62
    .line 63
    .line 64
    return-void

    .line 65
    :cond_0
    move-object v0, p0

    .line 66
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a()V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 5
    .line 6
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->d:I

    .line 7
    .line 8
    const/high16 v1, 0x40000000    # 2.0f

    .line 9
    .line 10
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    invoke-virtual {p1, v0, p2}, Landroid/view/View;->measure(II)V

    .line 15
    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 18
    .line 19
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->d:I

    .line 20
    .line 21
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    invoke-virtual {p1, v0, p2}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 29
    .line 30
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 35
    .line 36
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 37
    .line 38
    .line 39
    move-result p2

    .line 40
    add-int/2addr p1, p2

    .line 41
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->g:I

    .line 42
    .line 43
    add-int/2addr p1, p2

    .line 44
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->h:I

    .line 45
    .line 46
    mul-int/lit8 p2, p2, 0x2

    .line 47
    .line 48
    add-int/2addr p1, p2

    .line 49
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 54
    .line 55
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 56
    .line 57
    .line 58
    move-result p2

    .line 59
    if-gtz p2, :cond_1

    .line 60
    .line 61
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 62
    .line 63
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 64
    .line 65
    .line 66
    move-result p2

    .line 67
    if-lez p2, :cond_0

    .line 68
    .line 69
    goto :goto_0

    .line 70
    :cond_0
    const/4 p1, 0x0

    .line 71
    :cond_1
    :goto_0
    if-eqz p1, :cond_2

    .line 72
    .line 73
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 74
    .line 75
    .line 76
    move-result p1

    .line 77
    goto :goto_1

    .line 78
    :cond_2
    const/4 p1, 0x0

    .line 79
    :goto_1
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->d:I

    .line 80
    .line 81
    invoke-static {p2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 82
    .line 83
    .line 84
    move-result p2

    .line 85
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 90
    .line 91
    .line 92
    return-void
.end method

.method public setScoreUiModel(LX31/a;)V
    .locals 2
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/a$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LX31/a$c;

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    :goto_0
    if-nez p1, :cond_1

    .line 10
    .line 11
    return-void

    .line 12
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 13
    .line 14
    invoke-virtual {p1}, LX31/a$c;->a()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScore(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 22
    .line 23
    invoke-virtual {p1}, LX31/a$c;->c()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScore(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p1}, LX31/a$c;->a()Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    invoke-virtual {p1}, LX31/a$c;->c()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->c:I

    .line 51
    .line 52
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 53
    .line 54
    invoke-virtual {p1}, LX31/a$c;->b()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V

    .line 59
    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 62
    .line 63
    invoke-virtual {p1}, LX31/a$c;->d()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V

    .line 68
    .line 69
    .line 70
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 71
    .line 72
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    if-nez p1, :cond_2

    .line 77
    .line 78
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 79
    .line 80
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 81
    .line 82
    .line 83
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 84
    .line 85
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    if-nez p1, :cond_3

    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoDefaultScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 92
    .line 93
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 94
    .line 95
    .line 96
    :cond_3
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 97
    .line 98
    .line 99
    return-void
.end method
