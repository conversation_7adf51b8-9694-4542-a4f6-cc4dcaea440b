.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\n\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\'\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0010\u0010\n\u001a\u00020\u0002H\u00d6\u0001\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0010\u0010\r\u001a\u00020\u000cH\u00d6\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001a\u0010\u0012\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u000fH\u00d6\u0003\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0015\u001a\u0004\u0008\u0016\u0010\u000bR\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0015\u001a\u0004\u0008\u0018\u0010\u000bR\u0017\u0010\u0005\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0015\u001a\u0004\u0008\u0017\u0010\u000bR\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0019\u001a\u0004\u0008\u0014\u0010\u001a\u00a8\u0006\u001b"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b;",
        "",
        "title",
        "text",
        "positiveButtonText",
        "Lorg/xbet/uikit/components/dialog/AlertType;",
        "alertType",
        "<init>",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/lang/String;",
        "d",
        "b",
        "c",
        "Lorg/xbet/uikit/components/dialog/AlertType;",
        "()Lorg/xbet/uikit/components/dialog/AlertType;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/uikit/components/dialog/AlertType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/uikit/components/dialog/AlertType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->b:Ljava/lang/String;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->c:Ljava/lang/String;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->d:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/uikit/components/dialog/AlertType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->d:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->c:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->a:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->b:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->b:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->c:Ljava/lang/String;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->c:Ljava/lang/String;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->d:Lorg/xbet/uikit/components/dialog/AlertType;

    iget-object p1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->d:Lorg/xbet/uikit/components/dialog/AlertType;

    if-eq v1, p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->c:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->d:Lorg/xbet/uikit/components/dialog/AlertType;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->c:Ljava/lang/String;

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$b$a;->d:Lorg/xbet/uikit/components/dialog/AlertType;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "ShowDialog(title="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", text="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", positiveButtonText="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ", alertType="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
