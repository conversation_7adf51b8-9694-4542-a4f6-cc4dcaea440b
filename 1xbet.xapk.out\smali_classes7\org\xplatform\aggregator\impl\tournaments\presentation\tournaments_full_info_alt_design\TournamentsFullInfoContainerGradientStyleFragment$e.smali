.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment$e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/core/view/K;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;->s2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;


# direct methods
.method public constructor <init>(ZLorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;)V
    .locals 0

    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment$e;->a:Z

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment$e;->b:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onApplyWindowInsets(Landroid/view/View;Landroidx/core/view/F0;)Landroidx/core/view/F0;
    .locals 1

    .line 1
    invoke-static {}, Landroidx/core/view/F0$o;->h()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p2, p1}, Landroidx/core/view/F0;->f(I)LI0/d;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    iget p1, p1, LI0/d;->b:I

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment$e;->b:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;

    .line 12
    .line 13
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;->X2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment;I)V

    .line 14
    .line 15
    .line 16
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerGradientStyleFragment$e;->a:Z

    .line 17
    .line 18
    if-eqz p1, :cond_0

    .line 19
    .line 20
    sget-object p1, Landroidx/core/view/F0;->b:Landroidx/core/view/F0;

    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    return-object p2
.end method
