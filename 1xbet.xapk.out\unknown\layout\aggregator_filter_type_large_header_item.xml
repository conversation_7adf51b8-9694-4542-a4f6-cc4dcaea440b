<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <org.xbet.uikit.components.header.DSHeader
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:horizontalMargin="@dimen/space_8" />

    <FrameLayout
        android:id="@+id/fmChipGroupBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_background_16_content">

        <org.xbet.uikit.components.chips.ChipGroup
            android:id="@+id/chipsGroupFilters"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_16"
            android:paddingTop="@dimen/space_8"
            android:paddingBottom="@dimen/space_8"
            app:chipSpacingHorizontal="@dimen/space_8"
            app:chipSpacingVertical="@dimen/space_0"
            app:singleSelection="true" />

    </FrameLayout>
</LinearLayout>