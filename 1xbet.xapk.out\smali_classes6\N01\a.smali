.class public final synthetic LN01/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/a;->a:Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LN01/a;->a:Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;

    invoke-static {v0}, Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;->w(Lorg/xbet/uikit/components/toolbar/base/styles/PreTitleNavigationBar;)V

    return-void
.end method
