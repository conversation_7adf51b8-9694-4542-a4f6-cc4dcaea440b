.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;
.super LAb1/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 >2\u00020\u0001:\u0001?B9\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001f\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u001f\u0010\u001d\u001a\u00020\u00142\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010\u001f\u001a\u00020\u0014H\u0016\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010!\u001a\u00020\u00142\u0006\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008!\u0010\"J\'\u0010)\u001a\u00020\u00142\u0006\u0010$\u001a\u00020#2\u0006\u0010&\u001a\u00020%2\u0006\u0010(\u001a\u00020\'H\u0016\u00a2\u0006\u0004\u0008)\u0010*J\u001f\u0010-\u001a\u00020\u00142\u0006\u0010+\u001a\u00020%2\u0006\u0010\u0011\u001a\u00020,H\u0016\u00a2\u0006\u0004\u0008-\u0010.R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010/R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u00102R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u00108R\u001a\u0010=\u001a\u0008\u0012\u0004\u0012\u00020:098\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010<\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;",
        "LAb1/b;",
        "LwX0/C;",
        "rootRouterHolder",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "aggregatorScreenFactory",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lfk/m;",
        "getPrimaryBalanceUseCase",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "popularClassicAggregatorDelegate",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "<init>",
        "(LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/ui_common/utils/M;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lek/f;)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "",
        "subcategoryId",
        "",
        "C",
        "(Lorg/xplatform/aggregator/api/model/Game;I)V",
        "",
        "throwable",
        "v",
        "(Ljava/lang/Throwable;)V",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "f",
        "(Lorg/xbet/balance/model/BalanceModel;Lorg/xplatform/aggregator/api/model/Game;)V",
        "b0",
        "()V",
        "d",
        "(Lorg/xplatform/aggregator/api/model/Game;)V",
        "",
        "id",
        "",
        "title",
        "",
        "isVirtual",
        "h0",
        "(JLjava/lang/String;Z)V",
        "screenName",
        "LCb1/b;",
        "V1",
        "(Ljava/lang/String;LCb1/b;)V",
        "LwX0/C;",
        "e",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "Lorg/xbet/ui_common/utils/M;",
        "g",
        "Lfk/m;",
        "h",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "i",
        "Lek/f;",
        "Lkotlinx/coroutines/flow/U;",
        "Lb81/a;",
        "j",
        "Lkotlinx/coroutines/flow/U;",
        "singleEventState",
        "k",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final d:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xplatform/aggregator/api/navigation/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lfk/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Lb81/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->k:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$a;

    return-void
.end method

.method public constructor <init>(LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/ui_common/utils/M;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lek/f;)V
    .locals 0
    .param p1    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/navigation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lfk/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, LAb1/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->d:LwX0/C;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->e:Lorg/xplatform/aggregator/api/navigation/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->f:Lorg/xbet/ui_common/utils/M;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->g:Lfk/m;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->h:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->i:Lek/f;

    .line 15
    .line 16
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->j:Lkotlinx/coroutines/flow/U;

    .line 21
    .line 22
    return-void
.end method

.method private final C(Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->h:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-static {v1}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;

    .line 12
    .line 13
    invoke-direct {v2, p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0, p1, p2, v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public static final E(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    instance-of v0, p3, Lcom/xbet/onexuser/domain/exceptions/UnauthorizedException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->d:LwX0/C;

    .line 6
    .line 7
    invoke-virtual {p3}, LwX0/D;->a()LwX0/c;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    if-eqz p3, :cond_1

    .line 12
    .line 13
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;

    .line 14
    .line 15
    invoke-direct {v0, p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p3, v0}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    invoke-direct {p0, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->v(Ljava/lang/Throwable;)V

    .line 23
    .line 24
    .line 25
    :cond_1
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 26
    .line 27
    return-object p0
.end method

.method public static final F(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->C(Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic k(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->E(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic l(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->F(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n(Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->x(Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic o(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;)Lfk/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->g:Lfk/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->h:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->i:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->v(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final v(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->f:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/d;

    .line 4
    .line 5
    invoke-direct {v1, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/d;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private static final x(Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public V1(Ljava/lang/String;LCb1/b;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LCb1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p2}, LPb1/c;->a(LCb1/b;)Lorg/xplatform/aggregator/api/model/Game;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    const/4 p2, 0x0

    .line 6
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->C(Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public b0()V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->d:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->e:Lorg/xplatform/aggregator/api/navigation/a;

    .line 10
    .line 11
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Promo;

    .line 12
    .line 13
    new-instance v3, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Tournaments;

    .line 14
    .line 15
    const-wide/16 v4, 0x0

    .line 16
    .line 17
    invoke-direct {v3, v4, v5}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Tournaments;-><init>(J)V

    .line 18
    .line 19
    .line 20
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Promo;-><init>(Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;)V

    .line 21
    .line 22
    .line 23
    const/4 v3, 0x0

    .line 24
    invoke-interface {v1, v3, v2}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 29
    .line 30
    .line 31
    :cond_0
    return-void
.end method

.method public d(Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 9
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xbet/ui_common/viewmodel/core/k;->b()Landroidx/lifecycle/b0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$changeBalanceToPrimary$1;

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->f:Lorg/xbet/ui_common/utils/M;

    .line 12
    .line 13
    invoke-direct {v2, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$changeBalanceToPrimary$1;-><init>(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    new-instance v6, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$changeBalanceToPrimary$2;

    .line 17
    .line 18
    const/4 v0, 0x0

    .line 19
    invoke-direct {v6, p0, p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$changeBalanceToPrimary$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v7, 0xe

    .line 23
    .line 24
    const/4 v8, 0x0

    .line 25
    const/4 v3, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    invoke-static/range {v1 .. v8}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public f(Lorg/xbet/balance/model/BalanceModel;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 3
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->h:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$onBalanceChosen$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl$onBalanceChosen$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    invoke-virtual {v0, p2, p1, v2, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->q(Lorg/xplatform/aggregator/api/model/Game;Lorg/xbet/balance/model/BalanceModel;ILkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public h0(JLjava/lang/String;Z)V
    .locals 14
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move/from16 v0, p4

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->d:LwX0/C;

    .line 4
    .line 5
    invoke-virtual {v1}, LwX0/D;->a()LwX0/c;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->e:Lorg/xplatform/aggregator/api/navigation/a;

    .line 12
    .line 13
    new-instance v3, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;

    .line 14
    .line 15
    new-instance v4, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;

    .line 16
    .line 17
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 18
    .line 19
    .line 20
    move-result-object v8

    .line 21
    const/16 v12, 0x18

    .line 22
    .line 23
    const/4 v13, 0x0

    .line 24
    const/4 v9, 0x0

    .line 25
    const-wide/16 v10, 0x0

    .line 26
    .line 27
    move-wide v6, p1

    .line 28
    move-object/from16 v5, p3

    .line 29
    .line 30
    invoke-direct/range {v4 .. v13}, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;-><init>(Ljava/lang/String;JLjava/util/List;Ljava/util/List;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    invoke-direct {v3, v4, v0}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;Z)V

    .line 34
    .line 35
    .line 36
    invoke-interface {v2, v0, v3}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {v1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 41
    .line 42
    .line 43
    :cond_0
    return-void
.end method
