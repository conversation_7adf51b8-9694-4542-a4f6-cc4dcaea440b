.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u0008\u00c7\n\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0010\u0010\u0005\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0005\u0010\u0006J\u0010\u0010\u0008\u001a\u00020\u0007H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001a\u0010\r\u001a\u00020\u000c2\u0008\u0010\u000b\u001a\u0004\u0018\u00010\nH\u00d6\u0003\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
        "<init>",
        "()V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;

    invoke-direct {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;-><init>()V

    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 1

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of p1, p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;

    if-nez p1, :cond_1

    const/4 p1, 0x0

    return p1

    :cond_1
    return v0
.end method

.method public hashCode()I
    .locals 1

    const v0, -0x570029e7

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    const-string v0, "NotFoundPromoCode"

    return-object v0
.end method
