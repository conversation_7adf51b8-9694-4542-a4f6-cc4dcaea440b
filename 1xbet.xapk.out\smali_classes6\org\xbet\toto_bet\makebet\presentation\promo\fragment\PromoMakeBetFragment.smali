.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0001\u0018\u0000 02\u00020\u0001:\u00011B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u000f\u0010\u000b\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u000b\u0010\u0003R\u001b\u0010\u0011\u001a\u00020\u000c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\u000e\u001a\u0004\u0008\u000f\u0010\u0010R\"\u0010\u0019\u001a\u00020\u00128\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0013\u0010\u0014\u001a\u0004\u0008\u0015\u0010\u0016\"\u0004\u0008\u0017\u0010\u0018R\"\u0010!\u001a\u00020\u001a8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001b\u0010\u001c\u001a\u0004\u0008\u001d\u0010\u001e\"\u0004\u0008\u001f\u0010 R\"\u0010)\u001a\u00020\"8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008#\u0010$\u001a\u0004\u0008%\u0010&\"\u0004\u0008\'\u0010(R\u001b\u0010/\u001a\u00020*8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008+\u0010,\u001a\u0004\u0008-\u0010.\u00a8\u00062"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "x2",
        "v2",
        "LOU0/f;",
        "i0",
        "LRc/c;",
        "C2",
        "()LOU0/f;",
        "binding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "j0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "G2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LzX0/k;",
        "k0",
        "LzX0/k;",
        "D2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "LAX0/b;",
        "l0",
        "LAX0/b;",
        "E2",
        "()LAX0/b;",
        "setSuccessBetAlertManager",
        "(LAX0/b;)V",
        "successBetAlertManager",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;",
        "m0",
        "Lkotlin/j;",
        "F2",
        "()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;",
        "viewModel",
        "n0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:I

.field public static final n0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic o0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public k0:LzX0/k;

.field public l0:LAX0/b;

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/toto_bet/impl/databinding/FragmentPromoBetTotoBetBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->o0:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->n0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$a;

    .line 31
    .line 32
    const/16 v0, 0x8

    .line 33
    .line 34
    sput v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->b1:I

    .line 35
    .line 36
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LNU0/b;->fragment_promo_bet_toto_bet:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/e;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/e;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V

    .line 17
    .line 18
    .line 19
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$1;

    .line 20
    .line 21
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 22
    .line 23
    .line 24
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 25
    .line 26
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$2;

    .line 27
    .line 28
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    const-class v2, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$4;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->m0:Lkotlin/j;

    .line 57
    .line 58
    return-void
.end method

.method public static final synthetic A2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)LOU0/f;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->C2()LOU0/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->F2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final H2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->F2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->C2()LOU0/f;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    iget-object p0, p0, LOU0/f;->e:Landroidx/appcompat/widget/AppCompatEditText;

    .line 10
    .line 11
    invoke-virtual {p0}, Landroidx/appcompat/widget/AppCompatEditText;->getText()Landroid/text/Editable;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    invoke-static {p0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    invoke-virtual {p1, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->y3(Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 23
    .line 24
    return-object p0
.end method

.method public static final I2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->G2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->H2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->I2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final C2()LOU0/f;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->o0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LOU0/f;

    .line 13
    .line 14
    return-object v0
.end method

.method public final D2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->k0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final E2()LAX0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->l0:LAX0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final F2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final G2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->C2()LOU0/f;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LOU0/f;->e:Landroidx/appcompat/widget/AppCompatEditText;

    .line 9
    .line 10
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$b;

    .line 11
    .line 12
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->addTextChangedListener(Landroid/text/TextWatcher;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->C2()LOU0/f;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    iget-object p1, p1, LOU0/f;->c:Lcom/google/android/material/button/MaterialButton;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/f;

    .line 25
    .line 26
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/f;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V

    .line 27
    .line 28
    .line 29
    const/4 v1, 0x1

    .line 30
    const/4 v2, 0x0

    .line 31
    invoke-static {p1, v2, v0, v1, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LTU0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LTU0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LTU0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v2, v0}, LTU0/d;->a(LwX0/c;)LTU0/c;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0, p0}, LTU0/c;->b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    new-instance v2, Ljava/lang/StringBuilder;

    .line 70
    .line 71
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 72
    .line 73
    .line 74
    const-string v3, "Cannot create dependency "

    .line 75
    .line 76
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw v0
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->F2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->w3()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v8

    .line 30
    new-instance v2, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    invoke-direct/range {v2 .. v7}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 34
    .line 35
    .line 36
    const/4 v11, 0x3

    .line 37
    const/4 v12, 0x0

    .line 38
    move-object v7, v8

    .line 39
    const/4 v8, 0x0

    .line 40
    const/4 v9, 0x0

    .line 41
    move-object v10, v2

    .line 42
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->F2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->v3()Lkotlinx/coroutines/flow/e;

    .line 50
    .line 51
    .line 52
    move-result-object v8

    .line 53
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;

    .line 54
    .line 55
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Lkotlin/coroutines/e;)V

    .line 56
    .line 57
    .line 58
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 59
    .line 60
    .line 61
    move-result-object v9

    .line 62
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    new-instance v15, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 67
    .line 68
    move-object v10, v5

    .line 69
    move-object v7, v15

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/16 v16, 0x3

    .line 74
    .line 75
    const/16 v17, 0x0

    .line 76
    .line 77
    const/4 v13, 0x0

    .line 78
    const/4 v14, 0x0

    .line 79
    move-object v12, v1

    .line 80
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 81
    .line 82
    .line 83
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
