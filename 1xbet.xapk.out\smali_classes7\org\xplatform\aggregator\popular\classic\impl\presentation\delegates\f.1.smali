.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;->b:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;->c:I

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;->b:Lorg/xplatform/aggregator/api/model/Game;

    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/f;->c:I

    invoke-static {v0, v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->l(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
