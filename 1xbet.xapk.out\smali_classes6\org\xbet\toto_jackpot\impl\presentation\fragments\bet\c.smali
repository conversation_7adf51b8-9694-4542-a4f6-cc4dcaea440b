.class public final Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->l0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;LtW0/q$b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->k0:LtW0/q$b;

    .line 2
    .line 3
    return-void
.end method
