.class public LmY0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LmY0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Position:",
        "LmY0/d;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\u0007\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\r\n\u0002\u0008\u0008\u0008\u0017\u0018\u0000*\u0008\u0008\u0001\u0010\u0002*\u00020\u00012\u00020\u0003B\u0019\u0012\u0010\u0008\u0002\u0010\u0004\u001a\n\u0012\u0004\u0012\u00028\u0001\u0018\u00010\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006R$\u0010\u000e\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0008\u0010\t\u001a\u0004\u0008\n\u0010\u000b\"\u0004\u0008\u000c\u0010\rR$\u0010\u0015\u001a\u0004\u0018\u00010\u000f8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0008\u0010\u0012\"\u0004\u0008\u0013\u0010\u0014R$\u0010\u0018\u001a\u0004\u0018\u00010\u000f8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\n\u0010\u0011\u001a\u0004\u0008\u0016\u0010\u0012\"\u0004\u0008\u0017\u0010\u0014R\"\u0010 \u001a\u00020\u00198\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001d\"\u0004\u0008\u001e\u0010\u001fR$\u0010#\u001a\u0004\u0018\u00010\u000f8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008!\u0010\u0011\u001a\u0004\u0008\u0010\u0010\u0012\"\u0004\u0008\"\u0010\u0014R(\u0010*\u001a\u0008\u0012\u0004\u0012\u00028\u00010$8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0016\u0010%\u001a\u0004\u0008&\u0010\'\"\u0004\u0008(\u0010)R\"\u00100\u001a\u00020+8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u001c\u0010,\u001a\u0004\u0008!\u0010-\"\u0004\u0008.\u0010/R$\u00104\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00081\u0010\t\u001a\u0004\u00082\u0010\u000b\"\u0004\u00083\u0010\rR$\u0010:\u001a\u0004\u0018\u0001058\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00082\u00106\u001a\u0004\u00081\u00107\"\u0004\u00088\u00109R\"\u0010<\u001a\u00020\u00198\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008&\u0010\u001b\u001a\u0004\u0008\u001a\u0010\u001d\"\u0004\u0008;\u0010\u001f\u00a8\u0006="
    }
    d2 = {
        "LmY0/a$a;",
        "LmY0/d;",
        "Position",
        "",
        "builder",
        "<init>",
        "(LmY0/a$a;)V",
        "Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "a",
        "Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "c",
        "()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "m",
        "(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V",
        "label",
        "LDY0/a;",
        "b",
        "LDY0/a;",
        "()LDY0/a;",
        "k",
        "(LDY0/a;)V",
        "axis",
        "f",
        "o",
        "tick",
        "",
        "d",
        "F",
        "g",
        "()F",
        "p",
        "(F)V",
        "tickLengthDp",
        "e",
        "l",
        "guideline",
        "LnY0/a;",
        "LnY0/a;",
        "j",
        "()LnY0/a;",
        "setValueFormatter",
        "(LnY0/a;)V",
        "valueFormatter",
        "LmY0/a$b;",
        "LmY0/a$b;",
        "()LmY0/a$b;",
        "setSizeConstraint",
        "(LmY0/a$b;)V",
        "sizeConstraint",
        "h",
        "i",
        "r",
        "titleComponent",
        "",
        "Ljava/lang/CharSequence;",
        "()Ljava/lang/CharSequence;",
        "q",
        "(Ljava/lang/CharSequence;)V",
        "title",
        "n",
        "labelRotationDegrees",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

.field public b:LDY0/a;

.field public c:LDY0/a;

.field public d:F

.field public e:LDY0/a;

.field public f:LnY0/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LnY0/a<",
            "TPosition;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:LmY0/a$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public h:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

.field public i:Ljava/lang/CharSequence;

.field public j:F


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 1
    invoke-direct {p0, v0, v1, v0}, LmY0/a$a;-><init>(LmY0/a$a;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(LmY0/a$a;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/a$a<",
            "TPosition;>;)V"
        }
    .end annotation

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    if-eqz p1, :cond_0

    .line 3
    iget-object v1, p1, LmY0/a$a;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    goto :goto_0

    :cond_0
    move-object v1, v0

    :goto_0
    iput-object v1, p0, LmY0/a$a;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    if-eqz p1, :cond_1

    .line 4
    iget-object v1, p1, LmY0/a$a;->b:LDY0/a;

    goto :goto_1

    :cond_1
    move-object v1, v0

    :goto_1
    iput-object v1, p0, LmY0/a$a;->b:LDY0/a;

    if-eqz p1, :cond_2

    .line 5
    iget-object v1, p1, LmY0/a$a;->c:LDY0/a;

    goto :goto_2

    :cond_2
    move-object v1, v0

    :goto_2
    iput-object v1, p0, LmY0/a$a;->c:LDY0/a;

    if-eqz p1, :cond_3

    .line 6
    iget v1, p1, LmY0/a$a;->d:F

    goto :goto_3

    :cond_3
    const/high16 v1, 0x40800000    # 4.0f

    :goto_3
    iput v1, p0, LmY0/a$a;->d:F

    if-eqz p1, :cond_4

    .line 7
    iget-object v1, p1, LmY0/a$a;->e:LDY0/a;

    goto :goto_4

    :cond_4
    move-object v1, v0

    :goto_4
    iput-object v1, p0, LmY0/a$a;->e:LDY0/a;

    if-eqz p1, :cond_5

    .line 8
    iget-object v1, p1, LmY0/a$a;->f:LnY0/a;

    if-nez v1, :cond_6

    :cond_5
    new-instance v1, LnY0/b;

    invoke-direct {v1}, LnY0/b;-><init>()V

    :cond_6
    iput-object v1, p0, LmY0/a$a;->f:LnY0/a;

    .line 9
    new-instance v1, LmY0/a$b$a;

    const/4 v2, 0x3

    const/4 v3, 0x0

    invoke-direct {v1, v3, v3, v2, v0}, LmY0/a$b$a;-><init>(FFILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, p0, LmY0/a$a;->g:LmY0/a$b;

    if-eqz p1, :cond_7

    .line 10
    iget-object v1, p1, LmY0/a$a;->h:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    goto :goto_5

    :cond_7
    move-object v1, v0

    :goto_5
    iput-object v1, p0, LmY0/a$a;->h:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    if-eqz p1, :cond_8

    .line 11
    iget-object v0, p1, LmY0/a$a;->i:Ljava/lang/CharSequence;

    :cond_8
    iput-object v0, p0, LmY0/a$a;->i:Ljava/lang/CharSequence;

    if-eqz p1, :cond_9

    .line 12
    iget v3, p1, LmY0/a$a;->j:F

    :cond_9
    iput v3, p0, LmY0/a$a;->j:F

    return-void
.end method

.method public synthetic constructor <init>(LmY0/a$a;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p2, p2, 0x1

    if-eqz p2, :cond_0

    const/4 p1, 0x0

    .line 13
    :cond_0
    invoke-direct {p0, p1}, LmY0/a$a;-><init>(LmY0/a$a;)V

    return-void
.end method


# virtual methods
.method public final a()LDY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a$a;->b:LDY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()LDY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a$a;->e:LDY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a$a;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()F
    .locals 1

    .line 1
    iget v0, p0, LmY0/a$a;->j:F

    .line 2
    .line 3
    return v0
.end method

.method public final e()LmY0/a$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/a$a;->g:LmY0/a$b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final f()LDY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a$a;->c:LDY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()F
    .locals 1

    .line 1
    iget v0, p0, LmY0/a$a;->d:F

    .line 2
    .line 3
    return v0
.end method

.method public final h()Ljava/lang/CharSequence;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a$a;->i:Ljava/lang/CharSequence;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a$a;->h:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()LnY0/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LnY0/a<",
            "TPosition;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/a$a;->f:LnY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k(LDY0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a$a;->b:LDY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public final l(LDY0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a$a;->e:LDY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public final m(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a$a;->a:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-void
.end method

.method public final n(F)V
    .locals 0

    .line 1
    iput p1, p0, LmY0/a$a;->j:F

    .line 2
    .line 3
    return-void
.end method

.method public final o(LDY0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a$a;->c:LDY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public final p(F)V
    .locals 0

    .line 1
    iput p1, p0, LmY0/a$a;->d:F

    .line 2
    .line 3
    return-void
.end method

.method public final q(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a$a;->i:Ljava/lang/CharSequence;

    .line 2
    .line 3
    return-void
.end method

.method public final r(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a$a;->h:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-void
.end method
