.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;
.super Landroidx/recyclerview/widget/RecyclerView;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0088\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 G2\u00020\u0001:\u0001#B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0015\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001b\u0010\u0010\u001a\u00020\n2\u000c\u0010\u000f\u001a\u0008\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0015\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\r\u0010\u0016\u001a\u00020\n\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u001b\u0010\u001a\u001a\u00020\n2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\n0\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001d\u001a\u0004\u0018\u00010\u001c\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010 \u001a\u0004\u0018\u00010\u001f\u00a2\u0006\u0004\u0008 \u0010!R\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$R\u001b\u0010+\u001a\u00020&8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\'\u0010(\u001a\u0004\u0008)\u0010*R\u001b\u0010.\u001a\u00020&8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008,\u0010(\u001a\u0004\u0008-\u0010*R\u001b\u00102\u001a\u00020/8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010(\u001a\u0004\u00080\u00101R\u001b\u00107\u001a\u0002038BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00084\u0010(\u001a\u0004\u00085\u00106R\u0016\u0010;\u001a\u0002088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u0014\u0010?\u001a\u00020<8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u001c\u0010B\u001a\u0008\u0012\u0004\u0012\u00020\n0\u00188\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0016\u0010F\u001a\u00020C8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008D\u0010E\u00a8\u0006H"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;",
        "type",
        "",
        "setType",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;)V",
        "",
        "Lj31/a;",
        "statuses",
        "setData",
        "(Ljava/util/List;)V",
        "",
        "text",
        "setHeaderText",
        "(Ljava/lang/String;)V",
        "d",
        "()V",
        "Lkotlin/Function0;",
        "listener",
        "setOnHelpClickListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "getHeader",
        "()Lorg/xbet/uikit/components/header/DSHeader;",
        "Lorg/xbet/uikit/components/buttons/DSButton;",
        "getButtonHelp",
        "()Lorg/xbet/uikit/components/buttons/DSButton;",
        "",
        "a",
        "I",
        "space8",
        "Landroidx/recyclerview/widget/RecyclerView$o;",
        "b",
        "Lkotlin/j;",
        "getLinearItemDecoration",
        "()Landroidx/recyclerview/widget/RecyclerView$o;",
        "linearItemDecoration",
        "c",
        "getGridItemDecoration",
        "gridItemDecoration",
        "Landroidx/recyclerview/widget/LinearLayoutManager;",
        "getLinearLayoutManager",
        "()Landroidx/recyclerview/widget/LinearLayoutManager;",
        "linearLayoutManager",
        "Landroidx/recyclerview/widget/GridLayoutManager;",
        "e",
        "getGridLayoutManager",
        "()Landroidx/recyclerview/widget/GridLayoutManager;",
        "gridLayoutManager",
        "Lj31/f;",
        "f",
        "Lj31/f;",
        "headerItemModel",
        "Lj31/g;",
        "g",
        "Lj31/g;",
        "headerShimmerItemModel",
        "h",
        "Lkotlin/jvm/functions/Function0;",
        "onHelpClickListener",
        "Li31/g;",
        "i",
        "Li31/g;",
        "statusesAdapter",
        "j",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final j:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final k:I


# instance fields
.field public final a:I

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Lj31/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lj31/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public h:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i:Li31/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->j:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->k:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 4
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroidx/recyclerview/widget/RecyclerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    sget v1, LlZ0/g;->space_8:I

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->a:I

    .line 15
    .line 16
    new-instance v0, Li31/a;

    .line 17
    .line 18
    invoke-direct {v0, p0}, Li31/a;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->b:Lkotlin/j;

    .line 26
    .line 27
    new-instance v0, Li31/b;

    .line 28
    .line 29
    invoke-direct {v0, p0}, Li31/b;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)V

    .line 30
    .line 31
    .line 32
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->c:Lkotlin/j;

    .line 37
    .line 38
    new-instance v0, Li31/c;

    .line 39
    .line 40
    invoke-direct {v0, p1}, Li31/c;-><init>(Landroid/content/Context;)V

    .line 41
    .line 42
    .line 43
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->d:Lkotlin/j;

    .line 48
    .line 49
    new-instance v0, Li31/d;

    .line 50
    .line 51
    invoke-direct {v0, p1}, Li31/d;-><init>(Landroid/content/Context;)V

    .line 52
    .line 53
    .line 54
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->e:Lkotlin/j;

    .line 59
    .line 60
    new-instance v0, Lj31/f;

    .line 61
    .line 62
    const-string v1, ""

    .line 63
    .line 64
    invoke-direct {v0, v1}, Lj31/f;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->f:Lj31/f;

    .line 68
    .line 69
    new-instance v0, Lj31/g;

    .line 70
    .line 71
    invoke-direct {v0}, Lj31/g;-><init>()V

    .line 72
    .line 73
    .line 74
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->g:Lj31/g;

    .line 75
    .line 76
    new-instance v0, Li31/e;

    .line 77
    .line 78
    invoke-direct {v0}, Li31/e;-><init>()V

    .line 79
    .line 80
    .line 81
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->h:Lkotlin/jvm/functions/Function0;

    .line 82
    .line 83
    new-instance v0, Li31/g;

    .line 84
    .line 85
    sget-object v2, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->LARGE_ICON:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 86
    .line 87
    new-instance v3, Li31/f;

    .line 88
    .line 89
    invoke-direct {v3, p0}, Li31/f;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)V

    .line 90
    .line 91
    .line 92
    invoke-direct {v0, v2, v3}, Li31/g;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;Lkotlin/jvm/functions/Function0;)V

    .line 93
    .line 94
    .line 95
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->i:Li31/g;

    .line 96
    .line 97
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 98
    .line 99
    invoke-direct {v0, p1}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 100
    .line 101
    .line 102
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 103
    .line 104
    .line 105
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->i:Li31/g;

    .line 106
    .line 107
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 108
    .line 109
    .line 110
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getLinearItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 115
    .line 116
    .line 117
    const/4 v0, 0x0

    .line 118
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setClipToPadding(Z)V

    .line 119
    .line 120
    .line 121
    sget-object v2, LS11/h;->DSAggregatorVipCashbackStatuses:[I

    .line 122
    .line 123
    invoke-virtual {p1, p2, v2, v0, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    .line 124
    .line 125
    .line 126
    move-result-object p2

    .line 127
    sget v0, LS11/h;->DSAggregatorVipCashbackStatuses_headerText:I

    .line 128
    .line 129
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    if-eqz p1, :cond_0

    .line 138
    .line 139
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 140
    .line 141
    .line 142
    move-result-object p1

    .line 143
    if-eqz p1, :cond_0

    .line 144
    .line 145
    move-object v1, p1

    .line 146
    :cond_0
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->setHeaderText(Ljava/lang/String;)V

    .line 147
    .line 148
    .line 149
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    .line 150
    .line 151
    .line 152
    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)LR11/c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->m(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)LR11/c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)Li31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->k(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)Li31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Landroid/content/Context;)Landroidx/recyclerview/widget/GridLayoutManager;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->l(Landroid/content/Context;)Landroidx/recyclerview/widget/GridLayoutManager;

    move-result-object p0

    return-object p0
.end method

.method private final getGridItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/RecyclerView$o;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getGridLayoutManager()Landroidx/recyclerview/widget/GridLayoutManager;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/GridLayoutManager;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getLinearItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/RecyclerView$o;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getLinearLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic h(Landroid/content/Context;)Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->n(Landroid/content/Context;)Landroidx/recyclerview/widget/LinearLayoutManager;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->o()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic j(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->p(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final k(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)Li31/h;
    .locals 3

    .line 1
    new-instance v0, Li31/h;

    .line 2
    .line 3
    iget p0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->a:I

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x2

    .line 7
    invoke-direct {v0, v2, p0, p0, v1}, Li31/h;-><init>(IIII)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public static final l(Landroid/content/Context;)Landroidx/recyclerview/widget/GridLayoutManager;
    .locals 4

    .line 1
    new-instance v0, Landroidx/recyclerview/widget/GridLayoutManager;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    const/4 v3, 0x2

    .line 6
    invoke-direct {v0, p0, v3, v1, v2}, Landroidx/recyclerview/widget/GridLayoutManager;-><init>(Landroid/content/Context;IIZ)V

    .line 7
    .line 8
    .line 9
    new-instance p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$d;

    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$d;-><init>()V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, p0}, Landroidx/recyclerview/widget/GridLayoutManager;->Q(Landroidx/recyclerview/widget/GridLayoutManager$c;)V

    .line 15
    .line 16
    .line 17
    return-object v0
.end method

.method public static final m(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)LR11/c;
    .locals 7

    .line 1
    new-instance v0, LR11/c;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->a:I

    .line 4
    .line 5
    const/16 v5, 0xa

    .line 6
    .line 7
    const/4 v6, 0x0

    .line 8
    const/4 v2, 0x0

    .line 9
    const/4 v3, 0x1

    .line 10
    const/4 v4, 0x0

    .line 11
    invoke-direct/range {v0 .. v6}, LR11/c;-><init>(IIIZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public static final n(Landroid/content/Context;)Landroidx/recyclerview/widget/LinearLayoutManager;
    .locals 1

    .line 1
    new-instance v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final o()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final p(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->h:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method


# virtual methods
.method public final d()V
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->g:Lj31/g;

    .line 6
    .line 7
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    :goto_0
    const/16 v2, 0x8

    .line 12
    .line 13
    if-ge v1, v2, :cond_0

    .line 14
    .line 15
    new-instance v2, Lj31/b;

    .line 16
    .line 17
    invoke-direct {v2, v1}, Lj31/b;-><init>(I)V

    .line 18
    .line 19
    .line 20
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 21
    .line 22
    .line 23
    add-int/lit8 v1, v1, 0x1

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->i:Li31/g;

    .line 31
    .line 32
    invoke-virtual {v1, v0}, Landroidx/recyclerview/widget/s;->q(Ljava/util/List;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final getButtonHelp()Lorg/xbet/uikit/components/buttons/DSButton;
    .locals 2

    .line 1
    invoke-static {p0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$b;

    .line 6
    .line 7
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->O(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lkotlin/sequences/SequencesKt___SequencesKt;->S(Lkotlin/sequences/Sequence;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->getBtnHelp()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    return-object v0

    .line 24
    :cond_0
    const/4 v0, 0x0

    .line 25
    return-object v0
.end method

.method public final getHeader()Lorg/xbet/uikit/components/header/DSHeader;
    .locals 2

    .line 1
    invoke-static {p0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$c;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses$c;

    .line 6
    .line 7
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->O(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-static {v0}, Lkotlin/sequences/SequencesKt___SequencesKt;->S(Lkotlin/sequences/Sequence;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackItemHeader;->getHeaderCashbackStatuses()Lorg/xbet/uikit/components/header/DSHeader;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    return-object v0

    .line 24
    :cond_0
    const/4 v0, 0x0

    .line 25
    return-object v0
.end method

.method public final setData(Ljava/util/List;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lj31/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->i:Li31/g;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->f:Lj31/f;

    .line 4
    .line 5
    invoke-static {v1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {v1, p1}, Lkotlin/collections/CollectionsKt;->Z0(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-virtual {v0, p1}, Landroidx/recyclerview/widget/s;->q(Ljava/util/List;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setHeaderText(Ljava/lang/String;)V
    .locals 3
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lj31/f;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Lj31/f;-><init>(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->f:Lj31/f;

    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->i:Li31/g;

    .line 9
    .line 10
    invoke-virtual {p1}, Landroidx/recyclerview/widget/s;->n()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    new-instance v0, Ljava/util/ArrayList;

    .line 15
    .line 16
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 17
    .line 18
    .line 19
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    if-eqz v1, :cond_1

    .line 28
    .line 29
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    instance-of v2, v1, Lj31/a;

    .line 34
    .line 35
    if-eqz v2, :cond_0

    .line 36
    .line 37
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->setData(Ljava/util/List;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public final setOnHelpClickListener(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->h:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method

.method public final setType(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;)V
    .locals 2
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getLinearItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->removeItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getGridItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->removeItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 13
    .line 14
    .line 15
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;->COMPACT:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;

    .line 16
    .line 17
    if-ne p1, v0, :cond_0

    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getGridLayoutManager()Landroidx/recyclerview/widget/GridLayoutManager;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 24
    .line 25
    .line 26
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getGridItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 31
    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getLinearLayoutManager()Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 39
    .line 40
    .line 41
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->getLinearItemDecoration()Landroidx/recyclerview/widget/RecyclerView$o;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 46
    .line 47
    .line 48
    :goto_0
    new-instance v0, Li31/g;

    .line 49
    .line 50
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->h:Lkotlin/jvm/functions/Function0;

    .line 51
    .line 52
    invoke-direct {v0, p1, v1}, Li31/g;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/models/DSAggregatorVipCashbackStatusesType;Lkotlin/jvm/functions/Function0;)V

    .line 53
    .line 54
    .line 55
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/DSAggregatorVipCashbackStatuses;->i:Li31/g;

    .line 56
    .line 57
    invoke-virtual {p0, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 58
    .line 59
    .line 60
    return-void
.end method
