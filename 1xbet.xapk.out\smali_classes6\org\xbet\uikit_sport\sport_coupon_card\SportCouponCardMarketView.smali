.class public final Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$a;,
        Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0010\u0007\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\u0014\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008/\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 \u008f\u00012\u00020\u0001:\u0001GB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ7\u0010\u0015\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0019\u001a\u00020\u000c2\u0006\u0010\u0018\u001a\u00020\u0017H\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0015\u0010\u001d\u001a\u00020\u000c2\u0006\u0010\u001c\u001a\u00020\u001b\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010 \u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u001f\u001a\u00020\u0006\u00a2\u0006\u0004\u0008 \u0010!J\u0017\u0010\"\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u001f\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\"\u0010!J\u0017\u0010#\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u001f\u001a\u00020\u0006\u00a2\u0006\u0004\u0008#\u0010!J\u0015\u0010%\u001a\u00020\u000c2\u0006\u0010$\u001a\u00020\u0006\u00a2\u0006\u0004\u0008%\u0010!J\u0015\u0010&\u001a\u00020\u000c2\u0006\u0010$\u001a\u00020\u0006\u00a2\u0006\u0004\u0008&\u0010!J\u0015\u0010)\u001a\u00020\u000c2\u0006\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008)\u0010*J\u0015\u0010+\u001a\u00020\u000c2\u0006\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008+\u0010*J\u0017\u0010-\u001a\u00020\u000c2\u0008\u0008\u0001\u0010,\u001a\u00020\u0006\u00a2\u0006\u0004\u0008-\u0010!J\u0015\u00100\u001a\u00020\u000c2\u0006\u0010/\u001a\u00020.\u00a2\u0006\u0004\u00080\u00101J\u0019\u00103\u001a\u00020\u000c2\n\u0008\u0001\u00102\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u00083\u00104J\u0017\u00107\u001a\u00020\u000c2\u0008\u00106\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u00087\u00108J\u0017\u0010:\u001a\u00020\u000c2\u0008\u00109\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u0008:\u00108J\u0017\u0010<\u001a\u00020\u000c2\u0008\u0010;\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u0008<\u00108J\u0017\u0010>\u001a\u00020\u00062\u0006\u0010=\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008>\u0010?J\u0017\u0010A\u001a\u00020\u000c2\u0006\u0010@\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008A\u0010!J\u001f\u0010C\u001a\u00020\u000c2\u0006\u0010@\u001a\u00020\u00062\u0006\u0010B\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008C\u0010\u000eJ\u000f\u0010D\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008D\u0010EJ\u0017\u0010F\u001a\u00020\u000c2\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008F\u0010\u001aJ\u0017\u0010G\u001a\u00020\u000c2\u0006\u0010\u0018\u001a\u00020\u0017H\u0002\u00a2\u0006\u0004\u0008G\u0010\u001aJ1\u0010L\u001a\u00020\u000c2\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010H\u001a\u00020\'2\u0006\u0010I\u001a\u00020\'2\u0008\u0010K\u001a\u0004\u0018\u00010JH\u0002\u00a2\u0006\u0004\u0008L\u0010MJ1\u0010R\u001a\u0004\u0018\u00010J2\u0006\u0010O\u001a\u00020N2\u0006\u0010Q\u001a\u00020P2\u0006\u0010=\u001a\u00020\u00062\u0006\u0010$\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008R\u0010SR\u0016\u0010U\u001a\u00020\u001b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008G\u0010TR\u0016\u0010W\u001a\u00020\u00068\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0006\n\u0004\u0008F\u0010VR\u0016\u0010X\u001a\u00020\u00068\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0006\n\u0004\u0008L\u0010VR\u0016\u0010Y\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008>\u0010VR\u0016\u0010Z\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008R\u0010VR\u0016\u0010[\u001a\u00020\u00068\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0006\n\u0004\u0008A\u0010VR\u0016\u0010\\\u001a\u00020\u00068\u0002@\u0002X\u0083\u000e\u00a2\u0006\u0006\n\u0004\u0008C\u0010VR\u0016\u0010^\u001a\u00020\'8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008D\u0010]R\u0016\u0010`\u001a\u00020\'8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008_\u0010]R\u0016\u0010c\u001a\u00020P8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0016\u0010e\u001a\u00020P8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008d\u0010bR\u0016\u0010g\u001a\u00020P8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008f\u0010bR\u0016\u0010B\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008h\u0010VR\u0016\u0010j\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008i\u0010VR\u0014\u0010l\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010VR\u0014\u0010n\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010VR\u0014\u0010p\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010VR\u0014\u0010r\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010VR\u0014\u0010t\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010VR\u0016\u0010v\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008u\u0010VR\u0016\u0010x\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008w\u0010VR\u0018\u0010{\u001a\u0004\u0018\u00010J8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0018\u0010}\u001a\u0004\u0018\u00010J8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008|\u0010zR\u0018\u0010\u007f\u001a\u0004\u0018\u00010J8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008~\u0010zR\u0018\u0010\u0083\u0001\u001a\u00030\u0080\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0017\u0010\u0086\u0001\u001a\u00020N8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0001\u0010\u0085\u0001R\u0017\u0010\u0088\u0001\u001a\u00020N8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0085\u0001R\u0017\u0010\u008a\u0001\u001a\u00020N8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u0085\u0001R\u0018\u0010\u008e\u0001\u001a\u00030\u008b\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008c\u0001\u0010\u008d\u0001\u00a8\u0006\u0090\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;",
        "blockedIconPosition",
        "setMarketBlockedIconPosition",
        "(Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;)V",
        "style",
        "setDescriptionTextStyle",
        "(I)V",
        "setHeaderTextStyle",
        "setCoefficientTextStyle",
        "maxLines",
        "setHeaderMaxLines",
        "setDescriptionMaxLines",
        "",
        "textSize",
        "setCoefficientMaxTextSize",
        "(F)V",
        "setCoefficientMinTextSize",
        "backgroundTint",
        "setBackgroundTintAttr",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "setMarketCoefficientState",
        "(Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "styleResId",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "",
        "marketDescription",
        "setMarketDescription",
        "(Ljava/lang/CharSequence;)V",
        "marketCoefficient",
        "setMarketCoefficient",
        "header",
        "setMarketHeader",
        "width",
        "d",
        "(I)I",
        "marketTextTotalWidth",
        "f",
        "coefficientWidth",
        "g",
        "h",
        "()V",
        "b",
        "a",
        "startPosition",
        "topPosition",
        "Landroid/text/StaticLayout;",
        "staticLayout",
        "c",
        "(Landroid/graphics/Canvas;FFLandroid/text/StaticLayout;)V",
        "Landroid/text/TextPaint;",
        "textPaint",
        "",
        "text",
        "e",
        "(Landroid/text/TextPaint;Ljava/lang/String;II)Landroid/text/StaticLayout;",
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;",
        "marketBlockedIconPosition",
        "I",
        "descriptionTextStyle",
        "headerTextStyle",
        "headerMaxLines",
        "descriptionMaxLines",
        "coefficientTextStyle",
        "backgroundTintAttr",
        "F",
        "maxCoefficientTextSize",
        "i",
        "minCoefficientTextSize",
        "j",
        "Ljava/lang/String;",
        "marketDescriptionText",
        "k",
        "marketHeaderText",
        "l",
        "marketCoefficientText",
        "m",
        "n",
        "marketTotalHeight",
        "o",
        "iconSize",
        "p",
        "buttonHorizontalMargin",
        "q",
        "marketHeaderBottomMargin",
        "r",
        "marketAndCoefBetweenMargin",
        "s",
        "endIconHorizontalMargin",
        "t",
        "verticalPadding",
        "u",
        "horizontalPadding",
        "v",
        "Landroid/text/StaticLayout;",
        "marketDescriptionStaticLayout",
        "w",
        "marketHeaderStaticLayout",
        "x",
        "marketCoefficientStaticLayout",
        "Landroid/widget/ImageView;",
        "y",
        "Landroid/widget/ImageView;",
        "marketImageView",
        "z",
        "Landroid/text/TextPaint;",
        "marketDescriptionTextPaint",
        "A",
        "marketHeaderTextPaint",
        "B",
        "marketCoefficientTextPaint",
        "Landroid/graphics/Paint;",
        "C",
        "Landroid/graphics/Paint;",
        "backgroundPaint",
        "D",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final D:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final E:I


# instance fields
.field public final A:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:I

.field public c:I

.field public d:I

.field public e:I

.field public f:I

.field public g:I

.field public h:F

.field public i:F

.field public j:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public l:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public m:I

.field public n:I

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:I

.field public final s:I

.field public t:I

.field public u:I

.field public v:Landroid/text/StaticLayout;

.field public w:Landroid/text/StaticLayout;

.field public x:Landroid/text/StaticLayout;

.field public final y:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->D:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->E:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    sget-object p2, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 6
    const-string p2, ""

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->j:Ljava/lang/String;

    .line 7
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->k:Ljava/lang/String;

    .line 8
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->l:Ljava/lang/String;

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_16:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_8:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->p:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_2:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->q:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_8:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->r:I

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_4:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->s:I

    .line 14
    new-instance p2, Landroid/widget/ImageView;

    invoke-direct {p2, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 15
    sget p3, LlZ0/d;->uikitSecondary:I

    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-static {p1, p3, v0, v1, v0}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p3

    invoke-virtual {p2, p3}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 16
    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 17
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 18
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2}, Landroid/text/TextPaint;-><init>()V

    .line 19
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->b:I

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    const/4 p3, 0x1

    .line 20
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 21
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->z:Landroid/text/TextPaint;

    .line 22
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2}, Landroid/text/TextPaint;-><init>()V

    .line 23
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c:I

    invoke-static {p2, p1, v2}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 24
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 25
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->A:Landroid/text/TextPaint;

    .line 26
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2}, Landroid/text/TextPaint;-><init>()V

    .line 27
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->f:I

    invoke-static {p2, p1, v2}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 28
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 29
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 30
    new-instance p2, Landroid/graphics/Paint;

    invoke-direct {p2}, Landroid/graphics/Paint;-><init>()V

    .line 31
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 32
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g:I

    invoke-static {p1, p3, v0, v1, v0}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result p1

    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 33
    sget-object p1, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 34
    new-instance p1, Landroid/graphics/CornerPathEffect;

    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->radius_10:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    int-to-float p3, p3

    invoke-direct {p1, p3}, Landroid/graphics/CornerPathEffect;-><init>(F)V

    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setPathEffect(Landroid/graphics/PathEffect;)Landroid/graphics/PathEffect;

    .line 35
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->C:Landroid/graphics/Paint;

    const/4 p1, 0x0

    .line 36
    invoke-virtual {p0, p1}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final a(Landroid/graphics/Canvas;)V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->n:I

    .line 2
    .line 3
    div-int/lit8 v0, v0, 0x2

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->x:Landroid/text/StaticLayout;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {v1}, Landroid/text/Layout;->getHeight()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    div-int/lit8 v1, v1, 0x2

    .line 14
    .line 15
    sub-int/2addr v0, v1

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->m:I

    .line 21
    .line 22
    sub-int/2addr v1, v2

    .line 23
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->u:I

    .line 24
    .line 25
    sub-int/2addr v1, v2

    .line 26
    int-to-float v1, v1

    .line 27
    int-to-float v0, v0

    .line 28
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->x:Landroid/text/StaticLayout;

    .line 29
    .line 30
    invoke-virtual {p0, p1, v1, v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c(Landroid/graphics/Canvas;FFLandroid/text/StaticLayout;)V

    .line 31
    .line 32
    .line 33
    :cond_0
    return-void
.end method

.method public final b(Landroid/graphics/Canvas;)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-nez v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 11
    .line 12
    sget-object v2, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 13
    .line 14
    if-ne v0, v2, :cond_0

    .line 15
    .line 16
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 17
    .line 18
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->p:I

    .line 19
    .line 20
    add-int/2addr v0, v2

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    const/4 v0, 0x0

    .line 23
    :goto_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->u:I

    .line 24
    .line 25
    add-int/2addr v0, v2

    .line 26
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->k:Ljava/lang/String;

    .line 27
    .line 28
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    if-lez v2, :cond_2

    .line 33
    .line 34
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c:I

    .line 35
    .line 36
    if-eqz v2, :cond_2

    .line 37
    .line 38
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->w:Landroid/text/StaticLayout;

    .line 39
    .line 40
    if-eqz v2, :cond_1

    .line 41
    .line 42
    invoke-virtual {v2}, Landroid/text/Layout;->getHeight()I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    goto :goto_1

    .line 47
    :cond_1
    const/4 v2, 0x0

    .line 48
    :goto_1
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->q:I

    .line 49
    .line 50
    add-int/2addr v2, v3

    .line 51
    goto :goto_2

    .line 52
    :cond_2
    const/4 v2, 0x0

    .line 53
    :goto_2
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->v:Landroid/text/StaticLayout;

    .line 54
    .line 55
    if-eqz v3, :cond_3

    .line 56
    .line 57
    invoke-virtual {v3}, Landroid/text/Layout;->getHeight()I

    .line 58
    .line 59
    .line 60
    move-result v1

    .line 61
    :cond_3
    if-nez v2, :cond_4

    .line 62
    .line 63
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->n:I

    .line 64
    .line 65
    div-int/lit8 v3, v3, 0x2

    .line 66
    .line 67
    div-int/lit8 v4, v1, 0x2

    .line 68
    .line 69
    sub-int/2addr v3, v4

    .line 70
    goto :goto_3

    .line 71
    :cond_4
    move v3, v2

    .line 72
    :goto_3
    int-to-float v3, v3

    .line 73
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->n:I

    .line 74
    .line 75
    div-int/lit8 v4, v4, 0x2

    .line 76
    .line 77
    div-int/lit8 v2, v2, 0x2

    .line 78
    .line 79
    sub-int/2addr v4, v2

    .line 80
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    if-nez v1, :cond_5

    .line 85
    .line 86
    goto :goto_4

    .line 87
    :cond_5
    const/4 v2, 0x0

    .line 88
    :goto_4
    if-eqz v2, :cond_6

    .line 89
    .line 90
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 91
    .line 92
    .line 93
    move-result v1

    .line 94
    int-to-float v1, v1

    .line 95
    goto :goto_5

    .line 96
    :cond_6
    const/4 v1, 0x0

    .line 97
    :goto_5
    int-to-float v0, v0

    .line 98
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->v:Landroid/text/StaticLayout;

    .line 99
    .line 100
    invoke-virtual {p0, p1, v0, v3, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c(Landroid/graphics/Canvas;FFLandroid/text/StaticLayout;)V

    .line 101
    .line 102
    .line 103
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->w:Landroid/text/StaticLayout;

    .line 104
    .line 105
    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c(Landroid/graphics/Canvas;FFLandroid/text/StaticLayout;)V

    .line 106
    .line 107
    .line 108
    return-void
.end method

.method public final c(Landroid/graphics/Canvas;FFLandroid/text/StaticLayout;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-ne v0, v1, :cond_1

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    int-to-float v0, v0

    .line 13
    sub-float/2addr v0, p2

    .line 14
    if-eqz p4, :cond_0

    .line 15
    .line 16
    invoke-virtual {p4}, Landroid/text/Layout;->getWidth()I

    .line 17
    .line 18
    .line 19
    move-result p2

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 p2, 0x0

    .line 22
    :goto_0
    int-to-float p2, p2

    .line 23
    sub-float p2, v0, p2

    .line 24
    .line 25
    :cond_1
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 26
    .line 27
    .line 28
    invoke-virtual {p1, p2, p3}, Landroid/graphics/Canvas;->translate(FF)V

    .line 29
    .line 30
    .line 31
    if-eqz p4, :cond_2

    .line 32
    .line 33
    invoke-virtual {p4, p1}, Landroid/text/Layout;->draw(Landroid/graphics/Canvas;)V

    .line 34
    .line 35
    .line 36
    :cond_2
    invoke-virtual {p1}, Landroid/graphics/Canvas;->restore()V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method public final d(I)I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 10
    .line 11
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 12
    .line 13
    if-ne v0, v1, :cond_0

    .line 14
    .line 15
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 16
    .line 17
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->p:I

    .line 18
    .line 19
    :goto_0
    add-int/2addr v0, v1

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-nez v0, :cond_1

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 30
    .line 31
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->END:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 32
    .line 33
    if-ne v0, v1, :cond_1

    .line 34
    .line 35
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 36
    .line 37
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->s:I

    .line 38
    .line 39
    mul-int/lit8 v1, v1, 0x2

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_1
    const/4 v0, 0x0

    .line 43
    :goto_1
    sub-int/2addr p1, v0

    .line 44
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->u:I

    .line 45
    .line 46
    mul-int/lit8 v0, v0, 0x2

    .line 47
    .line 48
    sub-int/2addr p1, v0

    .line 49
    return p1
.end method

.method public final e(Landroid/text/TextPaint;Ljava/lang/String;II)Landroid/text/StaticLayout;
    .locals 14

    .line 1
    invoke-static/range {p2 .. p2}, Lorg/xbet/uikit/utils/g;->a(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v9, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 6
    .line 7
    const/16 v12, 0xdf0

    .line 8
    .line 9
    const/4 v13, 0x0

    .line 10
    const/4 v4, 0x0

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v6, 0x0

    .line 13
    const/4 v7, 0x0

    .line 14
    const/4 v8, 0x0

    .line 15
    const/4 v10, 0x0

    .line 16
    const/4 v11, 0x0

    .line 17
    move-object v1, p1

    .line 18
    move/from16 v2, p3

    .line 19
    .line 20
    move/from16 v3, p4

    .line 21
    .line 22
    invoke-static/range {v0 .. v13}, Lorg/xbet/uikit/utils/H;->d(Ljava/lang/CharSequence;Landroid/text/TextPaint;IIIIFFZLandroid/text/TextUtils$TruncateAt;ILandroid/text/Layout$Alignment;ILjava/lang/Object;)Landroid/text/StaticLayout;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-interface/range {p2 .. p2}, Ljava/lang/CharSequence;->length()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    if-lez v0, :cond_0

    .line 31
    .line 32
    return-object p1

    .line 33
    :cond_0
    const/4 p1, 0x0

    .line 34
    return-object p1
.end method

.method public final f(I)V
    .locals 4

    .line 1
    int-to-double v0, p1

    .line 2
    const-wide v2, 0x3fd999999999999aL    # 0.4

    .line 3
    .line 4
    .line 5
    .line 6
    .line 7
    mul-double v0, v0, v2

    .line 8
    .line 9
    double-to-int p1, v0

    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->h:F

    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->l:Ljava/lang/String;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 26
    .line 27
    int-to-float v2, p1

    .line 28
    cmpl-float v2, v0, v2

    .line 29
    .line 30
    if-lez v2, :cond_0

    .line 31
    .line 32
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->i:F

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->h:F

    .line 36
    .line 37
    :goto_0
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 38
    .line 39
    .line 40
    float-to-int v0, v0

    .line 41
    invoke-static {p1, v0}, Ljava/lang/Math;->min(II)I

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 46
    .line 47
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->l:Ljava/lang/String;

    .line 48
    .line 49
    const/4 v2, 0x1

    .line 50
    invoke-virtual {p0, v0, v1, p1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->e(Landroid/text/TextPaint;Ljava/lang/String;II)Landroid/text/StaticLayout;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->x:Landroid/text/StaticLayout;

    .line 55
    .line 56
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->m:I

    .line 57
    .line 58
    return-void
.end method

.method public final g(II)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 10
    .line 11
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->END:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 12
    .line 13
    if-ne v0, v1, :cond_0

    .line 14
    .line 15
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 16
    .line 17
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->s:I

    .line 18
    .line 19
    mul-int/lit8 v1, v1, 0x2

    .line 20
    .line 21
    add-int/2addr v0, v1

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->r:I

    .line 24
    .line 25
    :goto_0
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c:I

    .line 26
    .line 27
    if-eqz v1, :cond_1

    .line 28
    .line 29
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->A:Landroid/text/TextPaint;

    .line 30
    .line 31
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->k:Ljava/lang/String;

    .line 32
    .line 33
    sub-int v3, p1, p2

    .line 34
    .line 35
    sub-int/2addr v3, v0

    .line 36
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->d:I

    .line 37
    .line 38
    invoke-virtual {p0, v1, v2, v3, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->e(Landroid/text/TextPaint;Ljava/lang/String;II)Landroid/text/StaticLayout;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->w:Landroid/text/StaticLayout;

    .line 43
    .line 44
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->z:Landroid/text/TextPaint;

    .line 45
    .line 46
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->j:Ljava/lang/String;

    .line 47
    .line 48
    sub-int/2addr p1, p2

    .line 49
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->r:I

    .line 50
    .line 51
    sub-int/2addr p1, p2

    .line 52
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->e:I

    .line 53
    .line 54
    invoke-virtual {p0, v0, v1, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->e(Landroid/text/TextPaint;Ljava/lang/String;II)Landroid/text/StaticLayout;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->v:Landroid/text/StaticLayout;

    .line 59
    .line 60
    return-void
.end method

.method public final h()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->k:Ljava/lang/String;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-lez v0, :cond_1

    .line 9
    .line 10
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c:I

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->w:Landroid/text/StaticLayout;

    .line 15
    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    invoke-virtual {v0}, Landroid/text/Layout;->getHeight()I

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 v0, 0x0

    .line 24
    :goto_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->q:I

    .line 25
    .line 26
    add-int/2addr v0, v2

    .line 27
    goto :goto_1

    .line 28
    :cond_1
    const/4 v0, 0x0

    .line 29
    :goto_1
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->v:Landroid/text/StaticLayout;

    .line 30
    .line 31
    if-eqz v2, :cond_2

    .line 32
    .line 33
    invoke-virtual {v2}, Landroid/text/Layout;->getHeight()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    :cond_2
    add-int/2addr v1, v0

    .line 38
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->t:I

    .line 39
    .line 40
    mul-int/lit8 v0, v0, 0x2

    .line 41
    .line 42
    add-int/2addr v1, v0

    .line 43
    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->n:I

    .line 44
    .line 45
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 7
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g:I

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    int-to-float v4, v0

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    int-to-float v5, v0

    .line 15
    iget-object v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->C:Landroid/graphics/Paint;

    .line 16
    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    move-object v1, p1

    .line 20
    invoke-virtual/range {v1 .. v6}, Landroid/graphics/Canvas;->drawRect(FFFFLandroid/graphics/Paint;)V

    .line 21
    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    move-object v1, p1

    .line 25
    :goto_0
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->b(Landroid/graphics/Canvas;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a(Landroid/graphics/Canvas;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 6

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-nez p1, :cond_1

    .line 8
    .line 9
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->n:I

    .line 10
    .line 11
    div-int/lit8 p1, p1, 0x2

    .line 12
    .line 13
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 14
    .line 15
    div-int/lit8 p3, p2, 0x2

    .line 16
    .line 17
    sub-int v3, p1, p3

    .line 18
    .line 19
    div-int/lit8 p2, p2, 0x2

    .line 20
    .line 21
    add-int v5, p1, p2

    .line 22
    .line 23
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 24
    .line 25
    sget-object p2, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 26
    .line 27
    if-ne p1, p2, :cond_0

    .line 28
    .line 29
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->u:I

    .line 30
    .line 31
    :goto_0
    move v2, p1

    .line 32
    goto :goto_1

    .line 33
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->m:I

    .line 38
    .line 39
    sub-int/2addr p1, p2

    .line 40
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->s:I

    .line 41
    .line 42
    sub-int/2addr p1, p2

    .line 43
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 44
    .line 45
    sub-int/2addr p1, p2

    .line 46
    goto :goto_0

    .line 47
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 48
    .line 49
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 50
    .line 51
    add-int v4, v2, p1

    .line 52
    .line 53
    move-object v0, p0

    .line 54
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 55
    .line 56
    .line 57
    :cond_1
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->d(I)I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->f(I)V

    .line 10
    .line 11
    .line 12
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->m:I

    .line 13
    .line 14
    invoke-virtual {p0, p2, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g(II)V

    .line 15
    .line 16
    .line 17
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 18
    .line 19
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 20
    .line 21
    .line 22
    move-result p2

    .line 23
    if-nez p2, :cond_0

    .line 24
    .line 25
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 26
    .line 27
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 28
    .line 29
    const/high16 v1, 0x40000000    # 2.0f

    .line 30
    .line 31
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->o:I

    .line 36
    .line 37
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    invoke-virtual {p2, v0, v1}, Landroid/view/View;->measure(II)V

    .line 42
    .line 43
    .line 44
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->h()V

    .line 45
    .line 46
    .line 47
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->n:I

    .line 48
    .line 49
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 50
    .line 51
    .line 52
    return-void
.end method

.method public final setBackgroundTintAttr(I)V
    .locals 4

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g:I

    .line 2
    .line 3
    const/4 v0, 0x0

    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    sget v1, LlZ0/g;->space_10:I

    .line 11
    .line 12
    invoke-virtual {p1, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 p1, 0x0

    .line 18
    :goto_0
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->t:I

    .line 19
    .line 20
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g:I

    .line 21
    .line 22
    if-eqz p1, :cond_1

    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    sget v0, LlZ0/g;->space_8:I

    .line 29
    .line 30
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    :cond_1
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->u:I

    .line 35
    .line 36
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->C:Landroid/graphics/Paint;

    .line 37
    .line 38
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g:I

    .line 43
    .line 44
    const/4 v2, 0x2

    .line 45
    const/4 v3, 0x0

    .line 46
    invoke-static {v0, v1, v3, v2, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    invoke-virtual {p1, v0}, Landroid/graphics/Paint;->setColor(I)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public final setCoefficientMaxTextSize(F)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->h:F

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setCoefficientMinTextSize(F)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->i:F

    .line 2
    .line 3
    return-void
.end method

.method public final setCoefficientTextStyle(I)V
    .locals 2

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->f:I

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->f:I

    .line 10
    .line 11
    invoke-static {p1, v0, v1}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final setDescriptionMaxLines(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->e:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setDescriptionTextStyle(I)V
    .locals 2

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->b:I

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->z:Landroid/text/TextPaint;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->b:I

    .line 10
    .line 11
    invoke-static {p1, v0, v1}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final setHeaderMaxLines(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->d:I

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setHeaderTextStyle(I)V
    .locals 2

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c:I

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->A:Landroid/text/TextPaint;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->c:I

    .line 10
    .line 11
    invoke-static {p1, v0, v1}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final setMarketBlockedIconPosition(Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    .line 2
    .line 3
    return-void
.end method

.method public final setMarketCoefficient(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    if-nez p1, :cond_1

    .line 8
    .line 9
    :cond_0
    const-string p1, ""

    .line 10
    .line 11
    :cond_1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->l:Ljava/lang/String;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 5
    .param p1    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/graphics/Paint;->getAlpha()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    sget-object v3, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView$b;->a:[I

    .line 14
    .line 15
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    aget p1, v3, p1

    .line 20
    .line 21
    const/4 v3, 0x1

    .line 22
    const/4 v4, 0x2

    .line 23
    if-eq p1, v3, :cond_2

    .line 24
    .line 25
    if-eq p1, v4, :cond_1

    .line 26
    .line 27
    const/4 v3, 0x3

    .line 28
    if-ne p1, v3, :cond_0

    .line 29
    .line 30
    sget p1, LlZ0/d;->uikitTextPrimary:I

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 34
    .line 35
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 36
    .line 37
    .line 38
    throw p1

    .line 39
    :cond_1
    sget p1, LlZ0/d;->uikitStaticGreen:I

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_2
    sget p1, LlZ0/d;->uikitStaticRed:I

    .line 43
    .line 44
    :goto_0
    const/4 v3, 0x0

    .line 45
    invoke-static {v2, p1, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 46
    .line 47
    .line 48
    move-result p1

    .line 49
    invoke-virtual {v1, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 50
    .line 51
    .line 52
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 53
    .line 54
    invoke-virtual {p1, v0}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 58
    .line 59
    .line 60
    return-void
.end method

.method public final setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    if-nez p1, :cond_1

    .line 8
    .line 9
    :cond_0
    const-string p1, ""

    .line 10
    .line 11
    :cond_1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->j:Ljava/lang/String;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setMarketHeader(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    if-nez p1, :cond_1

    .line 8
    .line 9
    :cond_0
    const-string p1, ""

    .line 10
    .line 11
    :cond_1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->k:Ljava/lang/String;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setMarketStyle(Ljava/lang/Integer;)V
    .locals 4

    .line 1
    if-eqz p1, :cond_3

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    sget-object v1, LlZ0/o;->Market:[I

    .line 12
    .line 13
    invoke-virtual {v0, p1, v1}, Landroid/content/Context;->obtainStyledAttributes(I[I)Landroid/content/res/TypedArray;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    sget v0, LlZ0/o;->Market_alpha:I

    .line 18
    .line 19
    const/high16 v1, 0x3f800000    # 1.0f

    .line 20
    .line 21
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getFloat(IF)F

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    const/16 v1, 0xff

    .line 26
    .line 27
    int-to-float v1, v1

    .line 28
    mul-float v0, v0, v1

    .line 29
    .line 30
    float-to-int v0, v0

    .line 31
    sget v1, LlZ0/o;->Market_marketIcon:I

    .line 32
    .line 33
    invoke-virtual {p1, v1}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->z:Landroid/text/TextPaint;

    .line 38
    .line 39
    invoke-virtual {v2, v0}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 40
    .line 41
    .line 42
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->B:Landroid/text/TextPaint;

    .line 43
    .line 44
    invoke-virtual {v2, v0}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 45
    .line 46
    .line 47
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->A:Landroid/text/TextPaint;

    .line 48
    .line 49
    invoke-virtual {v2, v0}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 50
    .line 51
    .line 52
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->g:I

    .line 53
    .line 54
    if-eqz v2, :cond_0

    .line 55
    .line 56
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->C:Landroid/graphics/Paint;

    .line 57
    .line 58
    invoke-virtual {v2, v0}, Landroid/graphics/Paint;->setAlpha(I)V

    .line 59
    .line 60
    .line 61
    :cond_0
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 62
    .line 63
    int-to-float v0, v0

    .line 64
    const/high16 v3, 0x437f0000    # 255.0f

    .line 65
    .line 66
    div-float/2addr v0, v3

    .line 67
    invoke-virtual {v2, v0}, Landroid/view/View;->setAlpha(F)V

    .line 68
    .line 69
    .line 70
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 71
    .line 72
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 73
    .line 74
    .line 75
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->y:Landroid/widget/ImageView;

    .line 76
    .line 77
    const/4 v2, 0x0

    .line 78
    if-eqz v1, :cond_1

    .line 79
    .line 80
    const/4 v1, 0x1

    .line 81
    goto :goto_0

    .line 82
    :cond_1
    const/4 v1, 0x0

    .line 83
    :goto_0
    if-eqz v1, :cond_2

    .line 84
    .line 85
    goto :goto_1

    .line 86
    :cond_2
    const/16 v2, 0x8

    .line 87
    .line 88
    :goto_1
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 89
    .line 90
    .line 91
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 95
    .line 96
    .line 97
    :cond_3
    return-void
.end method
