.class final Lcom/google/android/gms/internal/fido/zzan;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final synthetic zza:I

.field private static final zzb:Lcom/google/android/gms/internal/fido/zzam;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/google/android/gms/internal/fido/zzam;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lcom/google/android/gms/internal/fido/zzam;-><init>(Lcom/google/android/gms/internal/fido/zzal;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lcom/google/android/gms/internal/fido/zzan;->zzb:Lcom/google/android/gms/internal/fido/zzam;

    .line 8
    .line 9
    return-void
.end method
