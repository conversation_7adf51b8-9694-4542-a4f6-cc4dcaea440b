.class public final Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0008\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J-\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0008\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u000e\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0010\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u000fR\u0014\u0010\u0011\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u000fR\u0014\u0010\u0012\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u000f\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;",
        "",
        "<init>",
        "()V",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "",
        "totoName",
        "outcomeCount",
        "",
        "isPromoEnable",
        "",
        "a",
        "(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;Z)V",
        "TOTO_NAME",
        "Ljava/lang/String;",
        "OUTCOME_COUNT",
        "IS_PROMO_ENABLE",
        "TAG",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 2
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const-string v0, "TotoBetMakeBetBottomSheetDialog"

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Landroidx/fragment/app/FragmentManager;->r0(Ljava/lang/String;)Landroidx/fragment/app/Fragment;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 11
    .line 12
    invoke-direct {v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;-><init>()V

    .line 13
    .line 14
    .line 15
    invoke-static {v1, p2}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->O2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v1, p3}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->M2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v1, p4}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->N2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;Z)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {v1, p1, v0}, Landroidx/fragment/app/l;->show(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method
