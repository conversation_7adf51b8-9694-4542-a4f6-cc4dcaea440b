.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;
.super Lorg/xbet/uikit_sport/sport_cell/DsSportCell;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001d\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000c\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u001d\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000c\u001a\u00020\n\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0014\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015R\u0018\u0010\u0011\u001a\u0004\u0018\u00010\u00108\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "showListCheckBox",
        "showBadge",
        "",
        "setChampionshipStyle",
        "(ZZ)V",
        "Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;",
        "componentStyle",
        "setComponentStyle",
        "(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;Z)V",
        "j",
        "(Z)V",
        "l",
        "Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final j(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;->l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    goto/16 :goto_0

    .line 6
    .line 7
    :cond_0
    sget-object v1, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall$a;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    aget v0, v1, v0

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    if-eq v0, v1, :cond_7

    .line 17
    .line 18
    const/4 v1, 0x2

    .line 19
    if-eq v0, v1, :cond_4

    .line 20
    .line 21
    const/4 v1, 0x3

    .line 22
    if-ne v0, v1, :cond_3

    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    if-eqz v0, :cond_1

    .line 29
    .line 30
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 31
    .line 32
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)V

    .line 33
    .line 34
    .line 35
    :cond_1
    if-eqz p1, :cond_2

    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    if-eqz p1, :cond_a

    .line 42
    .line 43
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->BADGE_WITH_COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 44
    .line 45
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 46
    .line 47
    .line 48
    return-void

    .line 49
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    if-eqz p1, :cond_a

    .line 54
    .line 55
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 56
    .line 57
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 58
    .line 59
    .line 60
    return-void

    .line 61
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 62
    .line 63
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 64
    .line 65
    .line 66
    throw p1

    .line 67
    :cond_4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    if-eqz v0, :cond_5

    .line 72
    .line 73
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->ACTION_ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 74
    .line 75
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)V

    .line 76
    .line 77
    .line 78
    :cond_5
    if-eqz p1, :cond_6

    .line 79
    .line 80
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    if-eqz p1, :cond_a

    .line 85
    .line 86
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->BADGE_WITH_COUNTER:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 87
    .line 88
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 89
    .line 90
    .line 91
    return-void

    .line 92
    :cond_6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    if-eqz p1, :cond_a

    .line 97
    .line 98
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->COUNTER:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 99
    .line 100
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 101
    .line 102
    .line 103
    return-void

    .line 104
    :cond_7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    if-eqz v0, :cond_8

    .line 109
    .line 110
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;->ICON:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;

    .line 111
    .line 112
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftType;)V

    .line 113
    .line 114
    .line 115
    :cond_8
    if-eqz p1, :cond_9

    .line 116
    .line 117
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    if-eqz p1, :cond_a

    .line 122
    .line 123
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->BADGE_WITH_COUNTER_WITH_ACCORDION:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 124
    .line 125
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 126
    .line 127
    .line 128
    return-void

    .line 129
    :cond_9
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    if-eqz p1, :cond_a

    .line 134
    .line 135
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->COUNTER_WITH_ACCORDION:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 136
    .line 137
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 138
    .line 139
    .line 140
    :cond_a
    :goto_0
    return-void
.end method

.method public final setChampionshipStyle(ZZ)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;->CHAMPIONSHIP_COUNTER_WITH_LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget-object p1, Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;->CHAMPIONSHIP_COUNTER_WITH_ACTION_ICON:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 7
    .line 8
    :goto_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;Z)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;->l:Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 2
    .line 3
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipSmall;->j(Z)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
