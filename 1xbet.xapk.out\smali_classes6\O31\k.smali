.class public final synthetic LO31/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LO31/n;

.field public final synthetic b:Lorg/xbet/uikit_sport/sport_collection/b;


# direct methods
.method public synthetic constructor <init>(LO31/n;Lorg/xbet/uikit_sport/sport_collection/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO31/k;->a:LO31/n;

    iput-object p2, p0, LO31/k;->b:Lorg/xbet/uikit_sport/sport_collection/b;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LO31/k;->a:LO31/n;

    iget-object v1, p0, LO31/k;->b:Lorg/xbet/uikit_sport/sport_collection/b;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, LO31/n;->s(LO31/n;Lorg/xbet/uikit_sport/sport_collection/b;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
