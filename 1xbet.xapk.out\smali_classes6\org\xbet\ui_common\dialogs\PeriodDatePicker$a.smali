.class public final Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/ui_common/dialogs/PeriodDatePicker;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0016\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J9\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00062\u0008\u0008\u0002\u0010\n\u001a\u00020\t2\u0006\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0010\u001a\u00020\u000b8\u0006X\u0086T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u0011R\u0014\u0010\u0012\u001a\u00020\u000b8\u0006X\u0086T\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0011R\u0014\u0010\u0013\u001a\u00020\u000b8\u0006X\u0086T\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0011R\u0014\u0010\u0014\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010\u0011R\u0014\u0010\u0015\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0011R\u0014\u0010\u0016\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0011R\u0014\u0010\u0017\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u0011R\u0014\u0010\u0018\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0011R\u0014\u0010\u0019\u001a\u00020\u000b8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010\u0011R\u0014\u0010\u001a\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u001c\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u001e\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001bR\u0014\u0010\u001f\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010\u001bR\u0014\u0010 \u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008 \u0010\u001bR\u0014\u0010!\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008!\u0010\u001bR\u0014\u0010\"\u001a\u00020\t8\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\"\u0010\u001b\u00a8\u0006#"
    }
    d2 = {
        "Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;",
        "",
        "<init>",
        "()V",
        "Landroidx/fragment/app/FragmentManager;",
        "manager",
        "",
        "startTimeSec",
        "defaultFromTimeSec",
        "",
        "maxPeriod",
        "",
        "requestKey",
        "",
        "a",
        "(Landroidx/fragment/app/FragmentManager;JJILjava/lang/String;)V",
        "BUNDLE_RESULT_START_TIME_SEC",
        "Ljava/lang/String;",
        "BUNDLE_RESULT_END_TIME_SEC",
        "BUNDLE_RESULT_CANCELED",
        "BUNDLE_START_DATE",
        "BUNDLE_DEFAULT_FROM_DATE",
        "BUNDLE_END_DATE",
        "BUNDLE_MAX_PERIOD",
        "BUNDLE_REQUEST_KEY",
        "BUNDLE_FROM_START_DATE",
        "DEFAULT_PERIOD",
        "I",
        "ONE_DAY",
        "J",
        "SIZE",
        "ONE_DAY_FOR_CALENDAR",
        "MIN_DAY",
        "MIN_MONTH",
        "MIN_YEAR",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;-><init>()V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;Landroidx/fragment/app/FragmentManager;JJILjava/lang/String;ILjava/lang/Object;)V
    .locals 8

    .line 1
    and-int/lit8 v0, p8, 0x4

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const-wide/16 p4, 0x0

    .line 6
    .line 7
    :cond_0
    move-wide v4, p4

    .line 8
    and-int/lit8 p4, p8, 0x8

    .line 9
    .line 10
    if-eqz p4, :cond_1

    .line 11
    .line 12
    const/4 p4, 0x0

    .line 13
    const/4 v6, 0x0

    .line 14
    :goto_0
    move-object v0, p0

    .line 15
    move-object v1, p1

    .line 16
    move-wide v2, p2

    .line 17
    move-object v7, p7

    .line 18
    goto :goto_1

    .line 19
    :cond_1
    move v6, p6

    .line 20
    goto :goto_0

    .line 21
    :goto_1
    invoke-virtual/range {v0 .. v7}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker$a;->a(Landroidx/fragment/app/FragmentManager;JJILjava/lang/String;)V

    .line 22
    .line 23
    .line 24
    return-void
.end method


# virtual methods
.method public final a(Landroidx/fragment/app/FragmentManager;JJILjava/lang/String;)V
    .locals 3
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    new-instance v0, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p7}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->h3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Ljava/lang/String;)V

    .line 7
    .line 8
    .line 9
    const-wide/16 v1, 0x0

    .line 10
    .line 11
    cmp-long p7, p2, v1

    .line 12
    .line 13
    if-nez p7, :cond_0

    .line 14
    .line 15
    const/4 p7, 0x1

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 p7, 0x0

    .line 18
    :goto_0
    invoke-static {v0, p7}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->f3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Z)V

    .line 19
    .line 20
    .line 21
    invoke-static {v0, p4, p5}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->e3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;J)V

    .line 22
    .line 23
    .line 24
    invoke-static {v0, p2, p3}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->i3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;J)V

    .line 25
    .line 26
    .line 27
    invoke-static {v0, p6}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->g3(Lorg/xbet/ui_common/dialogs/PeriodDatePicker;I)V

    .line 28
    .line 29
    .line 30
    const-class p2, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;

    .line 31
    .line 32
    invoke-virtual {p2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p2

    .line 36
    invoke-virtual {v0, p1, p2}, Landroidx/fragment/app/l;->show(Landroidx/fragment/app/FragmentManager;Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method
