.class final Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tvbet.presentation.TvBetJackpotTableViewModel$update$2"
    f = "TvBetJackpotTableViewModel.kt"
    l = {
        0x6b,
        0x6c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->Q3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->L$0:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Lorg/xbet/balance/model/BalanceModel;

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto :goto_3

    .line 23
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 24
    .line 25
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 26
    .line 27
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 32
    .line 33
    .line 34
    goto :goto_1

    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->u3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Z

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    if-eqz v1, :cond_3

    .line 45
    .line 46
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_3
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->MULTI:Lorg/xbet/balance/model/BalanceScreenType;

    .line 50
    .line 51
    :goto_0
    invoke-static {p1, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->B3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lorg/xbet/balance/model/BalanceScreenType;)V

    .line 52
    .line 53
    .line 54
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 55
    .line 56
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lfk/l;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 61
    .line 62
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->r3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lorg/xbet/balance/model/BalanceScreenType;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    iput v3, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->label:I

    .line 67
    .line 68
    invoke-interface {p1, v1, p0}, Lfk/l;->a(Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    if-ne p1, v0, :cond_4

    .line 73
    .line 74
    goto :goto_2

    .line 75
    :cond_4
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 76
    .line 77
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 78
    .line 79
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->L$0:Ljava/lang/Object;

    .line 80
    .line 81
    iput v2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->label:I

    .line 82
    .line 83
    invoke-static {v1, p1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->w3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    if-ne v1, v0, :cond_5

    .line 88
    .line 89
    :goto_2
    return-object v0

    .line 90
    :cond_5
    move-object v0, p1

    .line 91
    move-object p1, v1

    .line 92
    :goto_3
    check-cast p1, Lvb1/a;

    .line 93
    .line 94
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 95
    .line 96
    invoke-static {v1, v3}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->C3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Z)V

    .line 97
    .line 98
    .line 99
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 100
    .line 101
    invoke-virtual {v0}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    invoke-static {v1, p1, v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->D3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lvb1/a;Ljava/lang/String;)V

    .line 106
    .line 107
    .line 108
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 109
    .line 110
    return-object p1
.end method
