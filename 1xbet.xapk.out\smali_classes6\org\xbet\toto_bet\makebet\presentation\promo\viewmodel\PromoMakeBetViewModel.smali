.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;,
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 ,2\u00020\u0001:\u0002-.B)\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0013\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0013\u0010\u0016\u001a\u0008\u0012\u0004\u0012\u00020\u00150\u0011\u00a2\u0006\u0004\u0008\u0016\u0010\u0014J\u0015\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0015\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\r\u0010\u001c\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u001a\u0010)\u001a\u0008\u0012\u0004\u0012\u00020\u00150&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u001a\u0010+\u001a\u0008\u0012\u0004\u0012\u00020\u00120&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010(\u00a8\u0006/"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/l;",
        "makeBetWithPromoScenario",
        "LHX0/e;",
        "resourceManager",
        "Lfk/m;",
        "getPrimaryBalanceUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "getSelectedTotoUseCase",
        "<init>",
        "(Lorg/xbet/toto_bet/makebet/domain/usecase/l;LHX0/e;Lfk/m;Lorg/xbet/toto_bet/makebet/domain/usecase/e;)V",
        "Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;",
        "throwable",
        "",
        "x3",
        "(Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;)V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "w3",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;",
        "v3",
        "",
        "promoCode",
        "A3",
        "(Ljava/lang/String;)V",
        "y3",
        "B3",
        "()V",
        "v1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/l;",
        "x1",
        "LHX0/e;",
        "y1",
        "Lfk/m;",
        "F1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "Lkotlinx/coroutines/flow/V;",
        "H1",
        "Lkotlinx/coroutines/flow/V;",
        "actionStreamState",
        "I1",
        "promoCodeBetEnableStreamState",
        "P1",
        "a",
        "b",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final P1:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final S1:I


# instance fields
.field public final F1:Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lorg/xbet/toto_bet/makebet/domain/usecase/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lfk/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->P1:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$b;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->S1:I

    return-void
.end method

.method public constructor <init>(Lorg/xbet/toto_bet/makebet/domain/usecase/l;LHX0/e;Lfk/m;Lorg/xbet/toto_bet/makebet/domain/usecase/e;)V
    .locals 0
    .param p1    # Lorg/xbet/toto_bet/makebet/domain/usecase/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lfk/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/toto_bet/makebet/domain/usecase/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->v1:Lorg/xbet/toto_bet/makebet/domain/usecase/l;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->x1:LHX0/e;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->y1:Lfk/m;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->F1:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 11
    .line 12
    sget-object p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$a;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$a;

    .line 13
    .line 14
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 21
    .line 22
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->I1:Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->z3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;)Lfk/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->y1:Lfk/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->F1:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->v1:Lorg/xbet/toto_bet/makebet/domain/usecase/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->x1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method private final x3(Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;->getErrorResponse()Lh8/a;

    .line 2
    .line 3
    .line 4
    const/4 p1, 0x0

    .line 5
    throw p1
.end method

.method public static final z3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 3

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;

    .line 6
    .line 7
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->x3(Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;)V

    .line 8
    .line 9
    .line 10
    goto :goto_1

    .line 11
    :cond_0
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 12
    .line 13
    if-nez v0, :cond_3

    .line 14
    .line 15
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 21
    .line 22
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$e;

    .line 23
    .line 24
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-nez p1, :cond_2

    .line 29
    .line 30
    const-string p1, ""

    .line 31
    .line 32
    :cond_2
    invoke-direct {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$e;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_3
    :goto_0
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$d;

    .line 42
    .line 43
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->x1:LHX0/e;

    .line 44
    .line 45
    sget v1, Lpb/k;->no_connection_check_network:I

    .line 46
    .line 47
    const/4 v2, 0x0

    .line 48
    new-array v2, v2, [Ljava/lang/Object;

    .line 49
    .line 50
    invoke-interface {p0, v1, v2}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object p0

    .line 54
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$d;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    :goto_1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 61
    .line 62
    return-object p0
.end method


# virtual methods
.method public final A3(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->I1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {p1}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    xor-int/lit8 p1, p1, 0x1

    .line 8
    .line 9
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final B3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$a;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$a;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final v3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->I1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y3(Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->H1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$b;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$b;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/d;

    .line 13
    .line 14
    invoke-direct {v3, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/d;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;)V

    .line 15
    .line 16
    .line 17
    new-instance v7, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$onMakeBet$2;

    .line 18
    .line 19
    const/4 v0, 0x0

    .line 20
    invoke-direct {v7, p0, p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$onMakeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 21
    .line 22
    .line 23
    const/16 v8, 0xe

    .line 24
    .line 25
    const/4 v9, 0x0

    .line 26
    const/4 v4, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    const/4 v6, 0x0

    .line 29
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 30
    .line 31
    .line 32
    return-void
.end method
