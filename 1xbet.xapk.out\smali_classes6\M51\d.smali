.class public final LM51/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LM51/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u0096\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LM51/d;",
        "LM51/c;",
        "Lz41/a;",
        "baseVerificationFeature",
        "<init>",
        "(Lz41/a;)V",
        "LL51/a;",
        "a",
        "()LL51/a;",
        "b",
        "Lz41/a;",
        "impl_stubRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LM51/c;

.field public final b:Lz41/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lz41/a;)V
    .locals 1
    .param p1    # Lz41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, LM51/a;->a()LM51/c$a;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-interface {v0, p1}, LM51/c$a;->a(Lz41/a;)LM51/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, LM51/d;->a:LM51/c;

    .line 13
    .line 14
    iput-object p1, p0, LM51/d;->b:Lz41/a;

    .line 15
    .line 16
    return-void
.end method


# virtual methods
.method public a()LL51/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LM51/d;->a:LM51/c;

    .line 2
    .line 3
    invoke-interface {v0}, LK51/a;->a()LL51/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
