.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0008\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\'\u0010\n\u001a\u00020\t2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\n\u0010\u000bR\u0014\u0010\u000c\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\rR\u0014\u0010\u000e\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\rR\u0014\u0010\u000f\u001a\u00020\u00068\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\rR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment$a;",
        "",
        "<init>",
        "()V",
        "",
        "tournamentId",
        "",
        "tournamentTitle",
        "stageTournamentID",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;",
        "a",
        "(JLjava/lang/String;J)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;",
        "STAGE_TOURNAMENT_ID",
        "Ljava/lang/String;",
        "TOURNAMENT_TITLE",
        "TOURNAMENT_ITEM",
        "",
        "SINGLE_TAB_SIZE",
        "I",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(JLjava/lang/String;J)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;
    .locals 1
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;->F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;J)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;->G2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    invoke-static {v0, p4, p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;->E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;J)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method
