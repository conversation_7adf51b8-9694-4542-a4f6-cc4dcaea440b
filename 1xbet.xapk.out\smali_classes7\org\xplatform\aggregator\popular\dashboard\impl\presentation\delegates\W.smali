.class public final synthetic Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/W;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/W;->a:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/W;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/W;->a:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/W;->b:Ljava/lang/String;

    check-cast p1, LrZ0/b;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result p2

    invoke-static {v0, v1, p1, p2}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularSimpleBannersContainerDelegateKt;->c(Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;LrZ0/b;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
