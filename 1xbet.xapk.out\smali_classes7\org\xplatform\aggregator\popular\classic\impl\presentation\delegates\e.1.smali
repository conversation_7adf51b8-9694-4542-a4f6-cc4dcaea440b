.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;->b:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;->b:Lorg/xplatform/aggregator/api/model/Game;

    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/e;->c:I

    check-cast p1, Ljava/lang/Throwable;

    invoke-static {v0, v1, v2, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->k(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
