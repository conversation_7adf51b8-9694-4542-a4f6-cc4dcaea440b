.class public final synthetic Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/b;->a:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/b;->a:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-static {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->N2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
