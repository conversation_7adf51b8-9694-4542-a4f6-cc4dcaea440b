.class public final LO31/b;
.super LA4/e;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LO31/b$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LA4/e<",
        "LP31/i;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\n\u0008\u0001\u0018\u0000 \u000f2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0010B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004R0\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u00058\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008\u0008\u0010\t\u001a\u0004\u0008\n\u0010\u000b\"\u0004\u0008\u000c\u0010\r\u00a8\u0006\u0011"
    }
    d2 = {
        "LO31/b;",
        "LA4/e;",
        "LP31/i;",
        "<init>",
        "()V",
        "Lkotlin/Function1;",
        "LP31/g;",
        "",
        "f",
        "Lkotlin/jvm/functions/Function1;",
        "getClickListener",
        "()Lkotlin/jvm/functions/Function1;",
        "q",
        "(Lkotlin/jvm/functions/Function1;)V",
        "clickListener",
        "g",
        "a",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final g:LO31/b$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final h:I


# instance fields
.field public f:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LP31/g;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LO31/b$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LO31/b$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LO31/b;->g:LO31/b$a;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LO31/b;->h:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    sget-object v0, LO31/b;->g:LO31/b$a;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LA4/e;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 7
    .line 8
    new-instance v1, LO31/a;

    .line 9
    .line 10
    invoke-direct {v1, p0}, LO31/a;-><init>(LO31/b;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v1}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;->e(Lkotlin/jvm/functions/Function1;)LA4/c;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, LA4/e;->d:LA4/d;

    .line 21
    .line 22
    invoke-static {}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionShimmerViewHolderKt;->e()LA4/c;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-virtual {v0, v1}, LA4/d;->c(LA4/c;)LA4/d;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public static synthetic o(LO31/b;LP31/g;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LO31/b;->p(LO31/b;LP31/g;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final p(LO31/b;LP31/g;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, LO31/b;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final q(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "LP31/g;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LO31/b;->f:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-void
.end method
