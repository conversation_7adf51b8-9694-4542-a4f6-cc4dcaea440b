.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
        "aggregatorPopularClassicCommonClickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/d;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/d;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt;->g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt;->h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;
    .locals 3
    .param p0    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/J;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/J;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/K;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/K;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt$promoProductBannerDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt$promoProductBannerDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt$promoProductBannerDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoProductBannerDelegateKt$promoProductBannerDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/d;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/d;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/d;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/L;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/L;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)V

    .line 6
    .line 7
    .line 8
    const/4 p0, 0x1

    .line 9
    const/4 p1, 0x0

    .line 10
    invoke-static {v0, p1, v1, p0, p1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 11
    .line 12
    .line 13
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    check-cast p0, LIb1/d;

    .line 18
    .line 19
    iget-object p0, p0, LIb1/d;->d:Landroid/widget/TextView;

    .line 20
    .line 21
    const/4 p1, 0x0

    .line 22
    invoke-virtual {p0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/M;

    .line 26
    .line 27
    invoke-direct {p0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/M;-><init>(LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 31
    .line 32
    .line 33
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 34
    .line 35
    return-object p0
.end method

.method public static final h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-interface {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;->l(Ljava/lang/String;Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 12

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LIb1/d;

    .line 6
    .line 7
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 8
    .line 9
    iget-object v1, p1, LIb1/d;->b:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    check-cast v2, LQb1/d;

    .line 16
    .line 17
    invoke-virtual {v2}, LQb1/d;->e()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    sget v3, Lpb/g;->ic_aggregator_placeholder:I

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    new-array v6, v4, [LYW0/d;

    .line 25
    .line 26
    const/16 v10, 0xec

    .line 27
    .line 28
    const/4 v11, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    const/4 v9, 0x0

    .line 33
    invoke-static/range {v0 .. v11}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    iget-object p1, p1, LIb1/d;->c:Landroid/widget/TextView;

    .line 37
    .line 38
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    check-cast p0, LQb1/d;

    .line 43
    .line 44
    invoke-virtual {p0}, LQb1/d;->o()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p0

    .line 48
    invoke-virtual {p1, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 49
    .line 50
    .line 51
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 52
    .line 53
    return-object p0
.end method
