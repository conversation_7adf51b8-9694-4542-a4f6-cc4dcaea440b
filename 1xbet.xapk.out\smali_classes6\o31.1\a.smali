.class public final synthetic Lo31/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lo31/d;

.field public final synthetic b:Lq31/c;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lo31/d;Lq31/c;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo31/a;->a:Lo31/d;

    iput-object p2, p0, Lo31/a;->b:Lq31/c;

    iput p3, p0, Lo31/a;->c:I

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lo31/a;->a:Lo31/d;

    iget-object v1, p0, Lo31/a;->b:Lq31/c;

    iget v2, p0, Lo31/a;->c:I

    invoke-static {v0, v1, v2, p1}, Lo31/d;->u(Lo31/d;Lq31/c;ILandroid/view/View;)V

    return-void
.end method
