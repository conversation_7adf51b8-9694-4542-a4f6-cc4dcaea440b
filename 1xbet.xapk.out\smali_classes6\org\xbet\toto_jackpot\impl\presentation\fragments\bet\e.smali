.class public final Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwW0/k;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LwW0/k;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;->a:LBc/a;

    .line 5
    .line 6
    return-void
.end method

.method public static a(LBc/a;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LwW0/k;",
            ">;)",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;-><init>(LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LwW0/k;LwX0/c;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;-><init>(LwW0/k;LwX0/c;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b(LwX0/c;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LwW0/k;

    .line 8
    .line 9
    invoke-static {v0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/e;->c(LwW0/k;LwX0/c;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    return-object p1
.end method
