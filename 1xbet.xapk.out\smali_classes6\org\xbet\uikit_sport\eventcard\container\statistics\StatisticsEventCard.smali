.class public final Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;
.super Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0011\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001f\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u000c\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J7\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u0019\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ+\u0010\u001f\u001a\u00020\u000e2\u0008\u0010\u001c\u001a\u0004\u0018\u00010\u001b2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u001b2\u0008\u0008\u0002\u0010\u001e\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010\"\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u0017\u0010%\u001a\u00020\u000e2\u0006\u0010$\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008%\u0010&R\u0014\u0010(\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010\'R\u0014\u0010)\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u0010\'R\u0014\u0010,\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010.\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010+R\u0014\u00101\u001a\u00020\u00118BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008/\u00100\u00a8\u00062"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;",
        "Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "getEventCardTopHeight",
        "()I",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "onFinishInflate",
        "()V",
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;",
        "firstTeam",
        "secondTeam",
        "topDependent",
        "setIndication",
        "(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V",
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;",
        "i",
        "()Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;",
        "totalHeight",
        "j",
        "(I)V",
        "I",
        "indicatorWidth",
        "space8",
        "k",
        "Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;",
        "leftIndicator",
        "l",
        "rightIndicator",
        "getDoubleIndication",
        "()Z",
        "doubleIndication",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final i:I

.field public final j:I

.field public final k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->i:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j:I

    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->i()Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->i()Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    const/4 p1, 0x0

    .line 10
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->setClipToPadding(Z)V

    const/4 p1, 0x1

    .line 11
    invoke-virtual {p0, p1}, Landroid/view/View;->setClipToOutline(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardStatisticsStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getDoubleIndication()Z
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardMiddle()Landroid/view/View;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCricket;

    .line 6
    .line 7
    if-nez v1, :cond_1

    .line 8
    .line 9
    instance-of v1, v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;

    .line 10
    .line 11
    if-nez v1, :cond_1

    .line 12
    .line 13
    instance-of v0, v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleScore;

    .line 14
    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v0, 0x0

    .line 19
    return v0

    .line 20
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 21
    return v0
.end method

.method private final getEventCardTopHeight()I
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardTop()Landroid/view/View;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-lez v0, :cond_1

    .line 14
    .line 15
    return v0

    .line 16
    :cond_1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j:I

    .line 17
    .line 18
    return v0
.end method

.method public static synthetic setIndication$default(Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;ZILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p4, p4, 0x4

    .line 2
    .line 3
    if-eqz p4, :cond_0

    .line 4
    .line 5
    const/4 p3, 0x1

    .line 6
    :cond_0
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final i()Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x2

    .line 9
    invoke-direct {v0, v1, v2, v3, v2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    const/4 v2, -0x1

    .line 17
    if-ne v1, v2, :cond_0

    .line 18
    .line 19
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    invoke-virtual {v0, v1}, Landroid/view/View;->setId(I)V

    .line 24
    .line 25
    .line 26
    :cond_0
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    .line 27
    .line 28
    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->i:I

    .line 29
    .line 30
    invoke-direct {v1, v3, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p0, v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 34
    .line 35
    .line 36
    return-object v0
.end method

.method public final j(I)V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->i:I

    .line 2
    .line 3
    const/high16 v1, 0x40000000    # 2.0f

    .line 4
    .line 5
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->getDoubleIndication()Z

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    if-eqz v2, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardMiddle()Landroid/view/View;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    if-eqz p1, :cond_0

    .line 20
    .line 21
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    goto :goto_0

    .line 26
    :cond_0
    const/4 p1, 0x0

    .line 27
    :goto_0
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 32
    .line 33
    invoke-virtual {p0, v1, v0, p1}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 34
    .line 35
    .line 36
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 37
    .line 38
    invoke-virtual {p0, v1, v0, p1}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 39
    .line 40
    .line 41
    return-void

    .line 42
    :cond_1
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 47
    .line 48
    invoke-virtual {p0, v1, v0, p1}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 49
    .line 50
    .line 51
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 52
    .line 53
    invoke-virtual {p0, v1, v0, p1}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public onFinishInflate()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroid/widget/FrameLayout;->onFinishInflate()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->getDoubleIndication()Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 11
    .line 12
    const/4 v1, 0x1

    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->s(Z)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->s(Z)V

    .line 20
    .line 21
    .line 22
    :cond_0
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->getEventCardTopHeight()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardInfo()Landroid/view/View;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    const/4 p3, 0x0

    .line 10
    if-eqz p2, :cond_0

    .line 11
    .line 12
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 13
    .line 14
    .line 15
    move-result p2

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 p2, 0x0

    .line 18
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardMiddle()Landroid/view/View;

    .line 19
    .line 20
    .line 21
    move-result-object p4

    .line 22
    if-eqz p4, :cond_1

    .line 23
    .line 24
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 25
    .line 26
    .line 27
    move-result p4

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/4 p4, 0x0

    .line 30
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 31
    .line 32
    .line 33
    move-result p5

    .line 34
    sub-int/2addr p5, p1

    .line 35
    sub-int/2addr p5, p2

    .line 36
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j:I

    .line 37
    .line 38
    sub-int/2addr p5, v0

    .line 39
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 40
    .line 41
    .line 42
    move-result v0

    .line 43
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j:I

    .line 44
    .line 45
    sub-int/2addr v0, v1

    .line 46
    sub-int p2, v0, p2

    .line 47
    .line 48
    div-int/lit8 p5, p5, 0x2

    .line 49
    .line 50
    div-int/lit8 v1, p4, 0x2

    .line 51
    .line 52
    sub-int/2addr p5, v1

    .line 53
    add-int/2addr p5, p1

    .line 54
    add-int/2addr p4, p5

    .line 55
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardTop()Landroid/view/View;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    if-eqz v1, :cond_2

    .line 60
    .line 61
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 62
    .line 63
    .line 64
    move-result v2

    .line 65
    invoke-virtual {v1, p3, p3, v2, p1}, Landroid/view/View;->layout(IIII)V

    .line 66
    .line 67
    .line 68
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardMiddle()Landroid/view/View;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    if-eqz p1, :cond_3

    .line 73
    .line 74
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    invoke-virtual {p1, p3, p5, v1, p4}, Landroid/view/View;->layout(IIII)V

    .line 79
    .line 80
    .line 81
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardInfo()Landroid/view/View;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    if-eqz p1, :cond_4

    .line 86
    .line 87
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    invoke-virtual {p1, p3, p2, v1, v0}, Landroid/view/View;->layout(IIII)V

    .line 92
    .line 93
    .line 94
    :cond_4
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 99
    .line 100
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 101
    .line 102
    .line 103
    move-result p2

    .line 104
    sub-int/2addr p1, p2

    .line 105
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->getDoubleIndication()Z

    .line 106
    .line 107
    .line 108
    move-result p2

    .line 109
    if-eqz p2, :cond_5

    .line 110
    .line 111
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 112
    .line 113
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 114
    .line 115
    .line 116
    move-result v0

    .line 117
    invoke-virtual {p2, p3, p5, v0, p4}, Landroid/view/View;->layout(IIII)V

    .line 118
    .line 119
    .line 120
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 121
    .line 122
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 123
    .line 124
    .line 125
    move-result p3

    .line 126
    invoke-virtual {p2, p1, p5, p3, p4}, Landroid/view/View;->layout(IIII)V

    .line 127
    .line 128
    .line 129
    return-void

    .line 130
    :cond_5
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 131
    .line 132
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 133
    .line 134
    .line 135
    move-result p4

    .line 136
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 137
    .line 138
    .line 139
    move-result p5

    .line 140
    invoke-virtual {p2, p3, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 141
    .line 142
    .line 143
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 144
    .line 145
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 146
    .line 147
    .line 148
    move-result p4

    .line 149
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 150
    .line 151
    .line 152
    move-result p5

    .line 153
    invoke-virtual {p2, p1, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 154
    .line 155
    .line 156
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardTop()Landroid/view/View;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-nez v1, :cond_0

    .line 12
    .line 13
    invoke-virtual {p0, v0, p1, p2}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j:I

    .line 22
    .line 23
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardMiddle()Landroid/view/View;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const/16 v2, 0x8

    .line 28
    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 32
    .line 33
    .line 34
    move-result v3

    .line 35
    if-eq v3, v2, :cond_1

    .line 36
    .line 37
    invoke-virtual {p0, v1, p1, p2}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 41
    .line 42
    .line 43
    move-result v1

    .line 44
    add-int/2addr v0, v1

    .line 45
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardInfo()Landroid/view/View;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    if-eqz v1, :cond_2

    .line 50
    .line 51
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 52
    .line 53
    .line 54
    move-result v3

    .line 55
    if-eq v3, v2, :cond_2

    .line 56
    .line 57
    invoke-virtual {p0, v1, p1, p2}, Landroid/view/ViewGroup;->measureChild(Landroid/view/View;II)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 61
    .line 62
    .line 63
    move-result v1

    .line 64
    add-int/2addr v0, v1

    .line 65
    :cond_2
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j:I

    .line 66
    .line 67
    add-int/2addr v0, v1

    .line 68
    invoke-virtual {p0}, Landroid/view/View;->getSuggestedMinimumHeight()I

    .line 69
    .line 70
    .line 71
    move-result v1

    .line 72
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 73
    .line 74
    .line 75
    move-result v0

    .line 76
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 77
    .line 78
    .line 79
    move-result v1

    .line 80
    invoke-static {v1, p1}, Landroid/view/View;->resolveSize(II)I

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    invoke-static {v0, p2}, Landroid/view/View;->resolveSize(II)I

    .line 85
    .line 86
    .line 87
    move-result p2

    .line 88
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 89
    .line 90
    .line 91
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->j(I)V

    .line 92
    .line 93
    .line 94
    return-void
.end method

.method public final setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/container/BaseEventCard;->getEventCardTop()Landroid/view/View;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    if-eqz p3, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->getDoubleIndication()Z

    .line 11
    .line 12
    .line 13
    move-result p3

    .line 14
    if-eqz p3, :cond_1

    .line 15
    .line 16
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 17
    .line 18
    invoke-virtual {p3, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setTopIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 19
    .line 20
    .line 21
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 22
    .line 23
    invoke-virtual {p3, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setTopIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 24
    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 27
    .line 28
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setBotIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 29
    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 32
    .line 33
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setBotIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_1
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->k:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 38
    .line 39
    invoke-virtual {p3, p1}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/container/statistics/StatisticsEventCard;->l:Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;

    .line 43
    .line 44
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndicator;->setIndication(Lorg/xbet/uikit_sport/eventcard/container/statistics/EventCardIndication;)V

    .line 45
    .line 46
    .line 47
    return-void
.end method
