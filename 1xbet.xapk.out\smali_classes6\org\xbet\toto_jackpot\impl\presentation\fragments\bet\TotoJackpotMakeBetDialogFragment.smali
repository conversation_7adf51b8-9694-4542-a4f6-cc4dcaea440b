.class public final Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;
.super Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment<",
        "LrW0/a;",
        ">;",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/a;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0018\u0000 C2\u0008\u0012\u0004\u0012\u00020\u00020\u00012\u00020\u0003:\u0001DB\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u000f\u0010\u0007\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0005J\u0017\u0010\n\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000e\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0012J\u000f\u0010\u0014\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0005J\u000f\u0010\u0015\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0005J!\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u00162\u0008\u0010\u0019\u001a\u0004\u0018\u00010\u0018H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001c\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u0005J\u000f\u0010\u001d\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u0005J\u000f\u0010\u001e\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u0005J\u0017\u0010!\u001a\u00020\u00062\u0006\u0010 \u001a\u00020\u001fH\u0016\u00a2\u0006\u0004\u0008!\u0010\"J\u000f\u0010#\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008#\u0010\u0005R\"\u0010+\u001a\u00020$8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(\"\u0004\u0008)\u0010*R\"\u00103\u001a\u00020,8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008-\u0010.\u001a\u0004\u0008/\u00100\"\u0004\u00081\u00102R\u001b\u00109\u001a\u0002048BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00085\u00106\u001a\u0004\u00087\u00108R\u0018\u0010=\u001a\u0004\u0018\u00010:8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008;\u0010<R\u001b\u0010B\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\u00a8\u0006E"
    }
    d2 = {
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;",
        "Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;",
        "LrW0/a;",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/a;",
        "<init>",
        "()V",
        "",
        "Y2",
        "",
        "show",
        "C",
        "(Z)V",
        "",
        "variantsAmount",
        "Z2",
        "(Ljava/lang/String;)V",
        "",
        "D2",
        "()I",
        "q2",
        "z2",
        "onStart",
        "Landroid/view/View;",
        "view",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onViewCreated",
        "(Landroid/view/View;Landroid/os/Bundle;)V",
        "dismiss",
        "q0",
        "p0",
        "",
        "message",
        "E",
        "(Ljava/lang/CharSequence;)V",
        "T2",
        "LtW0/q$b;",
        "k0",
        "LtW0/q$b;",
        "W2",
        "()LtW0/q$b;",
        "setTotoJackpotMakeBetDialogViewModelFactory",
        "(LtW0/q$b;)V",
        "totoJackpotMakeBetDialogViewModelFactory",
        "LzX0/k;",
        "l0",
        "LzX0/k;",
        "V2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;",
        "m0",
        "Lkotlin/j;",
        "X2",
        "()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;",
        "viewModel",
        "Ly01/d;",
        "n0",
        "Ly01/d;",
        "snackBar",
        "o0",
        "LRc/c;",
        "U2",
        "()LrW0/a;",
        "binding",
        "b1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic k1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final v1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public k0:LtW0/q$b;

.field public l0:LzX0/k;

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n0:Ly01/d;

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/toto_jackpot/impl/databinding/FragmentDialogMakeBetBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->k1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->b1:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$a;

    .line 31
    .line 32
    invoke-virtual {v3}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    sput-object v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->v1:Ljava/lang/String;

    .line 37
    .line 38
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/b;

    .line 5
    .line 6
    invoke-direct {v0, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/b;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)V

    .line 7
    .line 8
    .line 9
    new-instance v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$1;

    .line 10
    .line 11
    invoke-direct {v1, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 12
    .line 13
    .line 14
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 15
    .line 16
    new-instance v3, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$2;

    .line 17
    .line 18
    invoke-direct {v3, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    const-class v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 26
    .line 27
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    new-instance v3, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$3;

    .line 32
    .line 33
    invoke-direct {v3, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 34
    .line 35
    .line 36
    new-instance v4, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$4;

    .line 37
    .line 38
    const/4 v5, 0x0

    .line 39
    invoke-direct {v4, v5, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 40
    .line 41
    .line 42
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iput-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->m0:Lkotlin/j;

    .line 47
    .line 48
    sget-object v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$binding$2;->INSTANCE:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$binding$2;

    .line 49
    .line 50
    invoke-static {p0, v0}, LLX0/j;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    iput-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->o0:LRc/c;

    .line 55
    .line 56
    return-void
.end method

.method private final C(Z)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget-object p1, Lorg/xbet/ui_common/viewcomponents/dialogs/y;->e0:Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;->c(Landroidx/fragment/app/FragmentManager;)V

    .line 10
    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object p1, Lorg/xbet/ui_common/viewcomponents/dialogs/y;->e0:Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;

    .line 14
    .line 15
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/dialogs/y$a;->a(Landroidx/fragment/app/FragmentManager;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static synthetic N2(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->a3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic O2(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->T2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic P2()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->v1:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic Q2(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic R2(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->Z2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic S2(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->C(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final Y2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->q3()Lkotlinx/coroutines/flow/f0;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$1;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v11

    .line 27
    new-instance v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    move-object v5, v10

    .line 31
    invoke-direct/range {v2 .. v7}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v15, 0x3

    .line 35
    const/16 v16, 0x0

    .line 36
    .line 37
    const/4 v12, 0x0

    .line 38
    const/4 v13, 0x0

    .line 39
    move-object v14, v2

    .line 40
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-virtual {v2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->r3()Lkotlinx/coroutines/flow/Z;

    .line 48
    .line 49
    .line 50
    move-result-object v8

    .line 51
    new-instance v11, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$2;

    .line 52
    .line 53
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$2;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 57
    .line 58
    .line 59
    move-result-object v9

    .line 60
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    new-instance v5, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 65
    .line 66
    move-object v7, v5

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/4 v6, 0x3

    .line 71
    const/4 v7, 0x0

    .line 72
    const/4 v3, 0x0

    .line 73
    const/4 v4, 0x0

    .line 74
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 75
    .line 76
    .line 77
    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 78
    .line 79
    .line 80
    move-result-object v2

    .line 81
    invoke-virtual {v2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->s3()Lkotlinx/coroutines/flow/f0;

    .line 82
    .line 83
    .line 84
    move-result-object v8

    .line 85
    new-instance v11, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$3;

    .line 86
    .line 87
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$3;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;Lkotlin/coroutines/e;)V

    .line 88
    .line 89
    .line 90
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 91
    .line 92
    .line 93
    move-result-object v9

    .line 94
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    new-instance v4, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 99
    .line 100
    move-object v7, v4

    .line 101
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 102
    .line 103
    .line 104
    const/4 v5, 0x3

    .line 105
    const/4 v6, 0x0

    .line 106
    const/4 v2, 0x0

    .line 107
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 108
    .line 109
    .line 110
    return-void
.end method

.method private final Z2(Ljava/lang/String;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->U2()LrW0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LrW0/a;->h:Landroid/widget/TextView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final a3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)Landroidx/lifecycle/e0$c;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/a;

    .line 2
    .line 3
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->W2()LtW0/q$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-direct {v0, v1, p0}, Lorg/xbet/ui_common/viewmodel/core/a;-><init>(LwX0/c;LQW0/i;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method


# virtual methods
.method public D2()I
    .locals 1

    .line 1
    sget v0, LmW0/a;->clParent:I

    .line 2
    .line 3
    return v0
.end method

.method public E(Ljava/lang/CharSequence;)V
    .locals 11
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/l;->getDialog()Landroid/app/Dialog;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->n0:Ly01/d;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {v0}, Lcom/google/android/material/snackbar/BaseTransientBottomBar;->dismiss()V

    .line 12
    .line 13
    .line 14
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->V2()LzX0/k;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    new-instance v2, Ly01/g;

    .line 19
    .line 20
    sget-object v3, Ly01/i$c;->a:Ly01/i$c;

    .line 21
    .line 22
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    const/16 v9, 0x3c

    .line 27
    .line 28
    const/4 v10, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v6, 0x0

    .line 31
    const/4 v7, 0x0

    .line 32
    const/4 v8, 0x0

    .line 33
    invoke-direct/range {v2 .. v10}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->U2()LrW0/a;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iget-object v4, p1, LrW0/a;->c:Landroidx/coordinatorlayout/widget/CoordinatorLayout;

    .line 41
    .line 42
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 43
    .line 44
    .line 45
    move-result-object v3

    .line 46
    const/16 v8, 0x38

    .line 47
    .line 48
    const/4 v9, 0x0

    .line 49
    const/4 v6, 0x0

    .line 50
    invoke-static/range {v1 .. v9}, LzX0/k;->y(LzX0/k;Ly01/g;Landroidx/fragment/app/FragmentActivity;Landroid/view/View;Lkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->n0:Ly01/d;

    .line 55
    .line 56
    :cond_1
    return-void
.end method

.method public final T2()V
    .locals 6

    .line 1
    new-instance v0, LEW0/a$a;

    .line 2
    .line 3
    invoke-direct {v0}, LEW0/a$a;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v1, 0x1

    .line 7
    new-array v1, v1, [LEW0/a;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    aput-object v0, v1, v2

    .line 11
    .line 12
    invoke-static {v1}, Lkotlin/collections/v;->t([Ljava/lang/Object;)Ljava/util/List;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->U2()LrW0/a;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    iget-object v1, v1, LrW0/a;->i:Landroidx/viewpager2/widget/ViewPager2;

    .line 21
    .line 22
    new-instance v3, LEW0/b;

    .line 23
    .line 24
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 25
    .line 26
    .line 27
    move-result-object v4

    .line 28
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getViewLifecycleOwner()Landroidx/lifecycle/w;

    .line 29
    .line 30
    .line 31
    move-result-object v5

    .line 32
    invoke-interface {v5}, Landroidx/lifecycle/w;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    .line 33
    .line 34
    .line 35
    move-result-object v5

    .line 36
    invoke-direct {v3, v4, v5, v0}, LEW0/b;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v1, v3}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->U2()LrW0/a;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iget-object v0, v0, LrW0/a;->i:Landroidx/viewpager2/widget/ViewPager2;

    .line 47
    .line 48
    invoke-virtual {v0, v2}, Landroidx/viewpager2/widget/ViewPager2;->setUserInputEnabled(Z)V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public U2()LrW0/a;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LrW0/a;

    .line 13
    .line 14
    return-object v0
.end method

.method public final V2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->l0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final W2()LtW0/q$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->k0:LtW0/q$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 8
    .line 9
    return-object v0
.end method

.method public dismiss()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/l;->getDialog()Landroid/app/Dialog;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/app/Dialog;->dismiss()V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public onStart()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroidx/fragment/app/l;->onStart()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->v2()Lcom/google/android/material/bottomsheet/BottomSheetBehavior;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    const/4 v1, 0x1

    .line 11
    invoke-virtual {v0, v1}, Lcom/google/android/material/bottomsheet/BottomSheetBehavior;->setSkipCollapsed(Z)V

    .line 12
    .line 13
    .line 14
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->t2()V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 0
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->Y2()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->v3()V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public p0()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->u3()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public q0()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->X2()Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->t3()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public q2()I
    .locals 1

    .line 1
    sget v0, Lpb/c;->contentBackground:I

    .line 2
    .line 3
    return v0
.end method

.method public bridge synthetic u2()LL2/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;->U2()LrW0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public z2()V
    .locals 4

    .line 1
    invoke-super {p0}, Lorg/xbet/ui_common/moxy/dialogs/BaseBottomSheetDialogFragment;->z2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LtW0/r;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LtW0/r;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LtW0/r;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-virtual {v2}, LtW0/r;->a()LtW0/q;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-interface {v0, p0}, LtW0/q;->a(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/TotoJackpotMakeBetDialogFragment;)V

    .line 60
    .line 61
    .line 62
    return-void

    .line 63
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 64
    .line 65
    new-instance v2, Ljava/lang/StringBuilder;

    .line 66
    .line 67
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 68
    .line 69
    .line 70
    const-string v3, "Cannot create dependency "

    .line 71
    .line 72
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 73
    .line 74
    .line 75
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 76
    .line 77
    .line 78
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 87
    .line 88
    .line 89
    throw v0
.end method
