.class public final Lorg/xbet/uikit_sport/compose/sport_game_events/t;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002H\u0001\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "",
        "title",
        "Landroidx/compose/ui/l;",
        "modifier",
        "",
        "b",
        "(Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xbet/uikit_sport/compose/sport_game_events/t;->c(Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 26
    .param p0    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    const v1, 0x5a7246b3

    .line 4
    .line 5
    .line 6
    move-object/from16 v2, p2

    .line 7
    .line 8
    invoke-interface {v2, v1}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    and-int/lit8 v3, p4, 0x1

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    or-int/lit8 v3, p3, 0x6

    .line 17
    .line 18
    goto :goto_1

    .line 19
    :cond_0
    and-int/lit8 v3, p3, 0x6

    .line 20
    .line 21
    if-nez v3, :cond_2

    .line 22
    .line 23
    invoke-interface {v2, v0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    if-eqz v3, :cond_1

    .line 28
    .line 29
    const/4 v3, 0x4

    .line 30
    goto :goto_0

    .line 31
    :cond_1
    const/4 v3, 0x2

    .line 32
    :goto_0
    or-int v3, p3, v3

    .line 33
    .line 34
    goto :goto_1

    .line 35
    :cond_2
    move/from16 v3, p3

    .line 36
    .line 37
    :goto_1
    and-int/lit8 v4, p4, 0x2

    .line 38
    .line 39
    if-eqz v4, :cond_4

    .line 40
    .line 41
    or-int/lit8 v3, v3, 0x30

    .line 42
    .line 43
    :cond_3
    move-object/from16 v5, p1

    .line 44
    .line 45
    goto :goto_3

    .line 46
    :cond_4
    and-int/lit8 v5, p3, 0x30

    .line 47
    .line 48
    if-nez v5, :cond_3

    .line 49
    .line 50
    move-object/from16 v5, p1

    .line 51
    .line 52
    invoke-interface {v2, v5}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 53
    .line 54
    .line 55
    move-result v6

    .line 56
    if-eqz v6, :cond_5

    .line 57
    .line 58
    const/16 v6, 0x20

    .line 59
    .line 60
    goto :goto_2

    .line 61
    :cond_5
    const/16 v6, 0x10

    .line 62
    .line 63
    :goto_2
    or-int/2addr v3, v6

    .line 64
    :goto_3
    and-int/lit8 v6, v3, 0x13

    .line 65
    .line 66
    const/16 v7, 0x12

    .line 67
    .line 68
    if-ne v6, v7, :cond_7

    .line 69
    .line 70
    invoke-interface {v2}, Landroidx/compose/runtime/j;->c()Z

    .line 71
    .line 72
    .line 73
    move-result v6

    .line 74
    if-nez v6, :cond_6

    .line 75
    .line 76
    goto :goto_4

    .line 77
    :cond_6
    invoke-interface {v2}, Landroidx/compose/runtime/j;->n()V

    .line 78
    .line 79
    .line 80
    move-object/from16 v21, v2

    .line 81
    .line 82
    goto :goto_5

    .line 83
    :cond_7
    :goto_4
    if-eqz v4, :cond_8

    .line 84
    .line 85
    sget-object v4, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 86
    .line 87
    move-object v5, v4

    .line 88
    :cond_8
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 89
    .line 90
    .line 91
    move-result v4

    .line 92
    if-eqz v4, :cond_9

    .line 93
    .line 94
    const/4 v4, -0x1

    .line 95
    const-string v6, "org.xbet.uikit_sport.compose.sport_game_events.TitleComponent (TitleComponent.kt:21)"

    .line 96
    .line 97
    invoke-static {v1, v3, v4, v6}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 98
    .line 99
    .line 100
    :cond_9
    sget-object v1, LA11/a;->a:LA11/a;

    .line 101
    .line 102
    invoke-virtual {v1}, LA11/a;->l1()F

    .line 103
    .line 104
    .line 105
    move-result v7

    .line 106
    const/16 v10, 0xd

    .line 107
    .line 108
    const/4 v11, 0x0

    .line 109
    const/4 v6, 0x0

    .line 110
    const/4 v8, 0x0

    .line 111
    const/4 v9, 0x0

    .line 112
    invoke-static/range {v5 .. v11}, Landroidx/compose/foundation/layout/PaddingKt;->m(Landroidx/compose/ui/l;FFFFILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    move-object/from16 v25, v5

    .line 117
    .line 118
    const/4 v4, 0x0

    .line 119
    const/4 v5, 0x0

    .line 120
    const/4 v6, 0x1

    .line 121
    invoke-static {v1, v4, v6, v5}, Landroidx/compose/foundation/layout/SizeKt;->h(Landroidx/compose/ui/l;FILjava/lang/Object;)Landroidx/compose/ui/l;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    sget-object v4, Landroidx/compose/ui/text/style/i;->b:Landroidx/compose/ui/text/style/i$a;

    .line 126
    .line 127
    invoke-virtual {v4}, Landroidx/compose/ui/text/style/i$a;->a()I

    .line 128
    .line 129
    .line 130
    move-result v4

    .line 131
    sget-object v5, LC11/a;->a:LC11/a;

    .line 132
    .line 133
    invoke-virtual {v5}, LC11/a;->n()Landroidx/compose/ui/text/a0;

    .line 134
    .line 135
    .line 136
    move-result-object v5

    .line 137
    const/4 v6, 0x0

    .line 138
    invoke-static {v5, v2, v6}, LB11/a;->a(Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;I)Landroidx/compose/ui/text/a0;

    .line 139
    .line 140
    .line 141
    move-result-object v20

    .line 142
    invoke-static {v4}, Landroidx/compose/ui/text/style/i;->h(I)Landroidx/compose/ui/text/style/i;

    .line 143
    .line 144
    .line 145
    move-result-object v12

    .line 146
    and-int/lit8 v22, v3, 0xe

    .line 147
    .line 148
    const/16 v23, 0x0

    .line 149
    .line 150
    const v24, 0xfdfc

    .line 151
    .line 152
    .line 153
    move-object/from16 v21, v2

    .line 154
    .line 155
    const-wide/16 v2, 0x0

    .line 156
    .line 157
    const-wide/16 v4, 0x0

    .line 158
    .line 159
    const/4 v6, 0x0

    .line 160
    const/4 v7, 0x0

    .line 161
    const/4 v8, 0x0

    .line 162
    const-wide/16 v9, 0x0

    .line 163
    .line 164
    const-wide/16 v13, 0x0

    .line 165
    .line 166
    const/4 v15, 0x0

    .line 167
    const/16 v16, 0x0

    .line 168
    .line 169
    const/16 v17, 0x0

    .line 170
    .line 171
    const/16 v18, 0x0

    .line 172
    .line 173
    const/16 v19, 0x0

    .line 174
    .line 175
    invoke-static/range {v0 .. v24}, Landroidx/compose/material3/TextKt;->c(Ljava/lang/String;Landroidx/compose/ui/l;JJLandroidx/compose/ui/text/font/t;Landroidx/compose/ui/text/font/y;Landroidx/compose/ui/text/font/j;JLandroidx/compose/ui/text/style/j;Landroidx/compose/ui/text/style/i;JIZIILkotlin/jvm/functions/Function1;Landroidx/compose/ui/text/a0;Landroidx/compose/runtime/j;III)V

    .line 176
    .line 177
    .line 178
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 179
    .line 180
    .line 181
    move-result v1

    .line 182
    if-eqz v1, :cond_a

    .line 183
    .line 184
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 185
    .line 186
    .line 187
    :cond_a
    move-object/from16 v5, v25

    .line 188
    .line 189
    :goto_5
    invoke-interface/range {v21 .. v21}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 190
    .line 191
    .line 192
    move-result-object v1

    .line 193
    if-eqz v1, :cond_b

    .line 194
    .line 195
    new-instance v2, Lorg/xbet/uikit_sport/compose/sport_game_events/s;

    .line 196
    .line 197
    move/from16 v3, p3

    .line 198
    .line 199
    move/from16 v4, p4

    .line 200
    .line 201
    invoke-direct {v2, v0, v5, v3, v4}, Lorg/xbet/uikit_sport/compose/sport_game_events/s;-><init>(Ljava/lang/String;Landroidx/compose/ui/l;II)V

    .line 202
    .line 203
    .line 204
    invoke-interface {v1, v2}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 205
    .line 206
    .line 207
    :cond_b
    return-void
.end method

.method public static final c(Ljava/lang/String;Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p2, p2, 0x1

    invoke-static {p2}, Landroidx/compose/runtime/A0;->a(I)I

    move-result p2

    invoke-static {p0, p1, p4, p2, p3}, Lorg/xbet/uikit_sport/compose/sport_game_events/t;->b(Ljava/lang/String;Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    return-object p0
.end method
