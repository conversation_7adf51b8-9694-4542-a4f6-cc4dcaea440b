.class public final LN81/g$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN81/t;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN81/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LN81/g$a$a;
    }
.end annotation


# instance fields
.field public final a:LzX0/k;

.field public final b:LN81/g$a;

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LRf0/l;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/u;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJ81/c;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJ81/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/z;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/n;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/E;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/f;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialogViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;Lorg/xbet/analytics/domain/scope/E;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LN81/g$a;->b:LN81/g$a;

    .line 4
    iput-object p10, p0, LN81/g$a;->a:LzX0/k;

    .line 5
    invoke-virtual/range {p0 .. p11}, LN81/g$a;->b(LQW0/c;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;Lorg/xbet/analytics/domain/scope/E;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;Lorg/xbet/analytics/domain/scope/E;LN81/h;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p11}, LN81/g$a;-><init>(LQW0/c;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;Lorg/xbet/analytics/domain/scope/E;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LN81/g$a;->c(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b(LQW0/c;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lf8/g;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lorg/xbet/remoteconfig/domain/usecases/i;LzX0/k;Lorg/xbet/analytics/domain/scope/E;)V
    .locals 0

    .line 1
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p4

    .line 5
    iput-object p4, p0, LN81/g$a;->c:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p4

    .line 11
    iput-object p4, p0, LN81/g$a;->d:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p4}, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/c;->a(LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/c;

    .line 14
    .line 15
    .line 16
    move-result-object p4

    .line 17
    iput-object p4, p0, LN81/g$a;->e:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p4}, LR81/v;->a(LBc/a;)LR81/v;

    .line 20
    .line 21
    .line 22
    move-result-object p4

    .line 23
    iput-object p4, p0, LN81/g$a;->f:Ldagger/internal/h;

    .line 24
    .line 25
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 26
    .line 27
    .line 28
    move-result-object p3

    .line 29
    iput-object p3, p0, LN81/g$a;->g:Ldagger/internal/h;

    .line 30
    .line 31
    new-instance p3, LN81/g$a$a;

    .line 32
    .line 33
    invoke-direct {p3, p1}, LN81/g$a$a;-><init>(LQW0/c;)V

    .line 34
    .line 35
    .line 36
    iput-object p3, p0, LN81/g$a;->h:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LN81/g$a;->i:Ldagger/internal/h;

    .line 43
    .line 44
    invoke-static {p1}, LJ81/d;->a(LBc/a;)LJ81/d;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, LN81/g$a;->j:Ldagger/internal/h;

    .line 49
    .line 50
    invoke-static {p2}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, LN81/g$a;->k:Ldagger/internal/h;

    .line 55
    .line 56
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iput-object p1, p0, LN81/g$a;->l:Ldagger/internal/h;

    .line 61
    .line 62
    iget-object p2, p0, LN81/g$a;->j:Ldagger/internal/h;

    .line 63
    .line 64
    iget-object p3, p0, LN81/g$a;->k:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p4, p0, LN81/g$a;->h:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p2, p3, p1, p4}, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/a;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, LN81/g$a;->m:Ldagger/internal/h;

    .line 73
    .line 74
    invoke-static {p1}, LR81/A;->a(LBc/a;)LR81/A;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iput-object p1, p0, LN81/g$a;->n:Ldagger/internal/h;

    .line 79
    .line 80
    iget-object p1, p0, LN81/g$a;->m:Ldagger/internal/h;

    .line 81
    .line 82
    invoke-static {p1}, LR81/o;->a(LBc/a;)LR81/o;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    iput-object p1, p0, LN81/g$a;->o:Ldagger/internal/h;

    .line 87
    .line 88
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iput-object p1, p0, LN81/g$a;->p:Ldagger/internal/h;

    .line 93
    .line 94
    iget-object p1, p0, LN81/g$a;->m:Ldagger/internal/h;

    .line 95
    .line 96
    invoke-static {p1}, LR81/g;->a(LBc/a;)LR81/g;

    .line 97
    .line 98
    .line 99
    move-result-object p9

    .line 100
    iput-object p9, p0, LN81/g$a;->q:Ldagger/internal/h;

    .line 101
    .line 102
    iget-object p2, p0, LN81/g$a;->c:Ldagger/internal/h;

    .line 103
    .line 104
    iget-object p3, p0, LN81/g$a;->f:Ldagger/internal/h;

    .line 105
    .line 106
    iget-object p4, p0, LN81/g$a;->g:Ldagger/internal/h;

    .line 107
    .line 108
    iget-object p5, p0, LN81/g$a;->h:Ldagger/internal/h;

    .line 109
    .line 110
    iget-object p6, p0, LN81/g$a;->n:Ldagger/internal/h;

    .line 111
    .line 112
    iget-object p7, p0, LN81/g$a;->o:Ldagger/internal/h;

    .line 113
    .line 114
    iget-object p8, p0, LN81/g$a;->p:Ldagger/internal/h;

    .line 115
    .line 116
    invoke-static/range {p2 .. p9}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/l;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/l;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    iput-object p1, p0, LN81/g$a;->r:Ldagger/internal/h;

    .line 121
    .line 122
    return-void
.end method

.method public final c(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LN81/g$a;->a:LzX0/k;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/m;->a(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;LzX0/k;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, LN81/g$a;->e()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/m;->b(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;Landroidx/lifecycle/e0$c;)V

    .line 11
    .line 12
    .line 13
    return-object p1
.end method

.method public final d()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialogViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LN81/g$a;->r:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final e()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/g$a;->d()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
