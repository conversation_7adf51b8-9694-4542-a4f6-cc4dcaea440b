.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/main_info/enum/TitleUiType;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
