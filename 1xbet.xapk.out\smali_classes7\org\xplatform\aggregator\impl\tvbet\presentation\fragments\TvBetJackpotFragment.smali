.class public final Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\n\u0018\u0000 ,2\u00020\u0001:\u0001-B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u000f\u0010\u0006\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0006\u0010\u0003J\u0019\u0010\t\u001a\u00020\u00042\u0008\u0010\u0008\u001a\u0004\u0018\u00010\u0007H\u0014\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\r\u0010\u000eR\"\u0010\u0016\u001a\u00020\u000f8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0012\u0010\u0013\"\u0004\u0008\u0014\u0010\u0015R+\u0010\u001f\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00178B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u001b\u0010\u001c\"\u0004\u0008\u001d\u0010\u001eR+\u0010#\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00178B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008 \u0010\u001a\u001a\u0004\u0008!\u0010\u001c\"\u0004\u0008\"\u0010\u001eR\u001b\u0010)\u001a\u00020$8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010&\u001a\u0004\u0008\'\u0010(R\u0014\u0010+\u001a\u00020\u00178VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008*\u0010\u001c\u00a8\u0006."
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "G2",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "",
        "amount",
        "L2",
        "(Ljava/lang/String;)V",
        "LMl0/a;",
        "i0",
        "LMl0/a;",
        "E2",
        "()LMl0/a;",
        "setRulesFeature",
        "(LMl0/a;)V",
        "rulesFeature",
        "",
        "<set-?>",
        "j0",
        "LeX0/a;",
        "D2",
        "()Z",
        "K2",
        "(Z)V",
        "bundleShowNavBar",
        "k0",
        "C2",
        "J2",
        "bundleFromAggregator",
        "LS91/k0;",
        "l0",
        "LRc/c;",
        "F2",
        "()LS91/k0;",
        "viewBinding",
        "r2",
        "showNavBar",
        "m0",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final m0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic n0:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:LMl0/a;

.field public final j0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;

    .line 4
    .line 5
    const-string v2, "bundleShowNavBar"

    .line 6
    .line 7
    const-string v3, "getBundleShowNavBar()Z"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bundleFromAggregator"

    .line 20
    .line 21
    const-string v5, "getBundleFromAggregator()Z"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "viewBinding"

    .line 33
    .line 34
    const-string v6, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTvBetAllBinding;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    const/4 v3, 0x3

    .line 44
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v3, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v3, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v1, v3, v0

    .line 53
    .line 54
    sput-object v3, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->m0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .line 1
    sget v0, Lu91/c;->fragment_tv_bet_all:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LeX0/a;

    .line 7
    .line 8
    const-string v1, "SHOW_NAVBAR"

    .line 9
    .line 10
    const/4 v2, 0x1

    .line 11
    invoke-direct {v0, v1, v2}, LeX0/a;-><init>(Ljava/lang/String;Z)V

    .line 12
    .line 13
    .line 14
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->j0:LeX0/a;

    .line 15
    .line 16
    new-instance v0, LeX0/a;

    .line 17
    .line 18
    const-string v1, "FROM_AGGREGATOR"

    .line 19
    .line 20
    invoke-direct {v0, v1, v2}, LeX0/a;-><init>(Ljava/lang/String;Z)V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->k0:LeX0/a;

    .line 24
    .line 25
    sget-object v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$viewBinding$2;

    .line 26
    .line 27
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->l0:LRc/c;

    .line 32
    .line 33
    return-void
.end method

.method public static final synthetic A2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->J2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->K2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final C2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->k0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final D2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->j0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final G2()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->F2()LS91/k0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/k0;->h:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/b;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/b;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public static final H2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    if-eqz p0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/activity/ComponentActivity;->onBackPressed()V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public static final I2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;I)Ljava/lang/CharSequence;
    .locals 0

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    sget p1, Lpb/k;->results:I

    .line 4
    .line 5
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0

    .line 10
    :cond_0
    sget p1, Lpb/k;->rules:I

    .line 11
    .line 12
    invoke-virtual {p0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p0

    .line 16
    return-object p0
.end method

.method private final J2(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->k0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final K2(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->j0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;I)Ljava/lang/CharSequence;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->I2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;I)Ljava/lang/CharSequence;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->H2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Landroid/view/View;)V

    return-void
.end method


# virtual methods
.method public final E2()LMl0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->i0:LMl0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final F2()LS91/k0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->n0:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/k0;

    .line 13
    .line 14
    return-object v0
.end method

.method public final L2(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->F2()LS91/k0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/k0;->d:Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a(Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->D2()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 14

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->G2()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    const/4 v0, 0x0

    .line 12
    const-string v1, ""

    .line 13
    .line 14
    if-eqz p1, :cond_0

    .line 15
    .line 16
    const-string v2, "BANNER_URL"

    .line 17
    .line 18
    invoke-virtual {p1, v2, v1}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    move-object p1, v0

    .line 24
    :goto_0
    if-nez p1, :cond_1

    .line 25
    .line 26
    move-object v4, v1

    .line 27
    goto :goto_1

    .line 28
    :cond_1
    move-object v4, p1

    .line 29
    :goto_1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    if-eqz p1, :cond_2

    .line 34
    .line 35
    const-string v0, "BANNER_TRANSLATE_ID"

    .line 36
    .line 37
    invoke-virtual {p1, v0, v1}, Landroid/os/BaseBundle;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    :cond_2
    if-nez v0, :cond_3

    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_3
    move-object v1, v0

    .line 45
    :goto_2
    sget-object v2, LCX0/l;->a:LCX0/l;

    .line 46
    .line 47
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->F2()LS91/k0;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iget-object v3, p1, LS91/k0;->b:Landroid/widget/ImageView;

    .line 52
    .line 53
    const/4 p1, 0x0

    .line 54
    new-array v8, p1, [LYW0/d;

    .line 55
    .line 56
    const/16 v12, 0xee

    .line 57
    .line 58
    const/4 v13, 0x0

    .line 59
    const/4 v5, 0x0

    .line 60
    const/4 v6, 0x0

    .line 61
    const/4 v7, 0x0

    .line 62
    const/4 v9, 0x0

    .line 63
    const/4 v10, 0x0

    .line 64
    const/4 v11, 0x0

    .line 65
    invoke-static/range {v2 .. v13}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->F2()LS91/k0;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iget-object p1, p1, LS91/k0;->i:Landroidx/viewpager2/widget/ViewPager2;

    .line 73
    .line 74
    new-instance v5, Lyb1/c;

    .line 75
    .line 76
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->D2()Z

    .line 77
    .line 78
    .line 79
    move-result v7

    .line 80
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->C2()Z

    .line 81
    .line 82
    .line 83
    move-result v8

    .line 84
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->E2()LMl0/a;

    .line 85
    .line 86
    .line 87
    move-result-object v10

    .line 88
    move-object v6, p0

    .line 89
    move-object v9, v1

    .line 90
    invoke-direct/range {v5 .. v10}, Lyb1/c;-><init>(Landroidx/fragment/app/Fragment;ZZLjava/lang/String;LMl0/a;)V

    .line 91
    .line 92
    .line 93
    invoke-virtual {p1, v5}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 94
    .line 95
    .line 96
    new-instance p1, Lu01/j;

    .line 97
    .line 98
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->F2()LS91/k0;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    iget-object v0, v0, LS91/k0;->e:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 103
    .line 104
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->F2()LS91/k0;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    iget-object v1, v1, LS91/k0;->i:Landroidx/viewpager2/widget/ViewPager2;

    .line 109
    .line 110
    new-instance v2, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/a;

    .line 111
    .line 112
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/a;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;)V

    .line 113
    .line 114
    .line 115
    invoke-direct {p1, v0, v1, v2}, Lu01/j;-><init>(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Landroidx/viewpager2/widget/ViewPager2;Lkotlin/jvm/functions/Function1;)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {p1}, Lu01/j;->d()V

    .line 119
    .line 120
    .line 121
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lub1/d;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lub1/d;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lub1/d;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->C2()Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    invoke-virtual {v2, v0}, Lub1/d;->a(Z)Lub1/c;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {v0, p0}, Lub1/c;->b(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;)V

    .line 61
    .line 62
    .line 63
    return-void

    .line 64
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 65
    .line 66
    new-instance v2, Ljava/lang/StringBuilder;

    .line 67
    .line 68
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 69
    .line 70
    .line 71
    const-string v3, "Cannot create dependency "

    .line 72
    .line 73
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 88
    .line 89
    .line 90
    throw v0
.end method
