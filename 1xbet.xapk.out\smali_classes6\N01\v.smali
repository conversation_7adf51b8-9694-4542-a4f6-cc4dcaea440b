.class public final synthetic LN01/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/searchfield/SearchField;

.field public final synthetic b:L<PERSON>lin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/searchfield/SearchField;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/v;->a:Lorg/xbet/uikit/components/searchfield/SearchField;

    iput-object p2, p0, LN01/v;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, LN01/v;->a:Lorg/xbet/uikit/components/searchfield/SearchField;

    iget-object v1, p0, LN01/v;->b:<PERSON><PERSON><PERSON>/jvm/functions/Function1;

    invoke-static {v0, v1}, Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;->x(Lorg/xbet/uikit/components/searchfield/SearchField;Lkotlin/jvm/functions/Function1;)V

    return-void
.end method
