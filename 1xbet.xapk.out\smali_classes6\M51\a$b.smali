.class public final LM51/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LM51/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LM51/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Lz41/a;

.field public final b:LM51/a$b;


# direct methods
.method public constructor <init>(Lz41/a;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LM51/a$b;->b:LM51/a$b;

    .line 4
    iput-object p1, p0, LM51/a$b;->a:Lz41/a;

    return-void
.end method

.method public synthetic constructor <init>(Lz41/a;LM51/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LM51/a$b;-><init>(Lz41/a;)V

    return-void
.end method


# virtual methods
.method public a()LL51/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LM51/a$b;->b()LN51/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final b()LN51/a;
    .locals 2

    .line 1
    new-instance v0, LN51/a;

    .line 2
    .line 3
    iget-object v1, p0, LM51/a$b;->a:Lz41/a;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LN51/a;-><init>(Lz41/a;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method
