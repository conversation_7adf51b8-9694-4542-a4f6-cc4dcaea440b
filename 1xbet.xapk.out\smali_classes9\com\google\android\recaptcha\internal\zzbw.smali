.class final Lcom/google/android/recaptcha/internal/zzbw;
.super Lkotlin/jvm/internal/Lambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field final synthetic zza:Lcom/google/android/recaptcha/internal/zzca;


# direct methods
.method public constructor <init>(Lcom/google/android/recaptcha/internal/zzca;)V
    .locals 0

    iput-object p1, p0, Lcom/google/android/recaptcha/internal/zzbw;->zza:Lcom/google/android/recaptcha/internal/zzca;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lcom/google/android/recaptcha/internal/zzpq;

    .line 2
    .line 3
    const-string p1, ""

    .line 4
    .line 5
    return-object p1
.end method
