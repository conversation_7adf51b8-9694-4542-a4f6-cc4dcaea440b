.class public LmY0/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmY0/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0015\u0008\u0017\u0018\u0000 42\u00020\u0001:\u0001\u0008B\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u001b\u0010\u0008\u001a\u00020\u00072\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0004\u0008\u0008\u0010\tJ-\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0017\u0010\u0016J9\u0010\u001a\u001a\u00020\u0007*\u0008\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u0010\u0014\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ1\u0010\u001d\u001a\u00020\u0007*\u0008\u0012\u0004\u0012\u00020\u001c0\u00182\u0006\u0010\u0014\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ9\u0010 \u001a\u00020\u0007*\u0008\u0012\u0004\u0012\u00020\u001f0\u00182\u0006\u0010\u0014\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008 \u0010\u001bJ9\u0010\"\u001a\u00020\u0007*\u0008\u0012\u0004\u0012\u00020!0\u00182\u0006\u0010\u0014\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\"\u0010\u001bJ\u000f\u0010#\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008#\u0010\u0003R2\u0010)\u001a\u001a\u0012\u0008\u0012\u0006\u0012\u0002\u0008\u00030\u00180$j\u000c\u0012\u0008\u0012\u0006\u0012\u0002\u0008\u00030\u0018`%8\u0000X\u0080\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0008\u0010&\u001a\u0004\u0008\'\u0010(R;\u00100\u001a\n\u0012\u0004\u0012\u00020\u0019\u0018\u00010\u00182\u000e\u0010*\u001a\n\u0012\u0004\u0012\u00020\u0019\u0018\u00010\u00188F@FX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\u0017\u0010+\u001a\u0004\u0008,\u0010-\"\u0004\u0008.\u0010/R;\u00103\u001a\n\u0012\u0004\u0012\u00020\u001c\u0018\u00010\u00182\u000e\u0010*\u001a\n\u0012\u0004\u0012\u00020\u001c\u0018\u00010\u00188F@FX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\u0015\u0010+\u001a\u0004\u00081\u0010-\"\u0004\u00082\u0010/R;\u00106\u001a\n\u0012\u0004\u0012\u00020\u001f\u0018\u00010\u00182\u000e\u0010*\u001a\n\u0012\u0004\u0012\u00020\u001f\u0018\u00010\u00188F@FX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\'\u0010+\u001a\u0004\u00084\u0010-\"\u0004\u00085\u0010/R;\u00109\u001a\n\u0012\u0004\u0012\u00020!\u0018\u00010\u00182\u000e\u0010*\u001a\n\u0012\u0004\u0012\u00020!\u0018\u00010\u00188F@FX\u0086\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00087\u0010+\u001a\u0004\u00087\u0010-\"\u0004\u00088\u0010/\u00a8\u0006:"
    }
    d2 = {
        "LmY0/c;",
        "",
        "<init>",
        "()V",
        "",
        "LwY0/a;",
        "destination",
        "",
        "a",
        "(Ljava/util/List;)V",
        "LIY0/d;",
        "measureContext",
        "Landroid/graphics/RectF;",
        "contentBounds",
        "chartBounds",
        "LwY0/c;",
        "insets",
        "i",
        "(LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V",
        "LuY0/a;",
        "context",
        "c",
        "(LuY0/a;)V",
        "b",
        "LmY0/e;",
        "LmY0/d$b$b;",
        "p",
        "(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V",
        "LmY0/d$a$b;",
        "r",
        "(LmY0/e;LIY0/d;Landroid/graphics/RectF;LwY0/c;)V",
        "LmY0/d$b$a;",
        "m",
        "LmY0/d$a$a;",
        "k",
        "n",
        "Ljava/util/ArrayList;",
        "Lkotlin/collections/ArrayList;",
        "Ljava/util/ArrayList;",
        "d",
        "()Ljava/util/ArrayList;",
        "axisCache",
        "<set-?>",
        "LRc/d;",
        "g",
        "()LmY0/e;",
        "o",
        "(LmY0/e;)V",
        "startAxis",
        "h",
        "q",
        "topAxis",
        "f",
        "l",
        "endAxis",
        "e",
        "j",
        "bottomAxis",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final f:LmY0/c$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic g:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final h:I


# instance fields
.field public final a:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "LmY0/e<",
            "*>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LRc/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LRc/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LRc/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LRc/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 8

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, LmY0/c;

    .line 4
    .line 5
    const-string v2, "startAxis"

    .line 6
    .line 7
    const-string v3, "getStartAxis()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/axis/AxisRenderer;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "topAxis"

    .line 20
    .line 21
    const-string v5, "getTopAxis()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/axis/AxisRenderer;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "endAxis"

    .line 33
    .line 34
    const-string v6, "getEndAxis()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/axis/AxisRenderer;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "bottomAxis"

    .line 46
    .line 47
    const-string v7, "getBottomAxis()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/axis/AxisRenderer;"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    const/4 v5, 0x4

    .line 57
    new-array v5, v5, [Lkotlin/reflect/m;

    .line 58
    .line 59
    aput-object v0, v5, v4

    .line 60
    .line 61
    const/4 v0, 0x1

    .line 62
    aput-object v2, v5, v0

    .line 63
    .line 64
    const/4 v0, 0x2

    .line 65
    aput-object v3, v5, v0

    .line 66
    .line 67
    const/4 v0, 0x3

    .line 68
    aput-object v1, v5, v0

    .line 69
    .line 70
    sput-object v5, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 71
    .line 72
    new-instance v0, LmY0/c$a;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    invoke-direct {v0, v1}, LmY0/c$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    sput-object v0, LmY0/c;->f:LmY0/c$a;

    .line 79
    .line 80
    const/16 v0, 0x8

    .line 81
    .line 82
    sput v0, LmY0/c;->h:I

    .line 83
    .line 84
    return-void
.end method

.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Ljava/util/ArrayList;

    .line 5
    .line 6
    const/4 v1, 0x4

    .line 7
    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 8
    .line 9
    .line 10
    iput-object v0, p0, LmY0/c;->a:Ljava/util/ArrayList;

    .line 11
    .line 12
    invoke-static {}, LzY0/a;->a()LRc/d;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iput-object v0, p0, LmY0/c;->b:LRc/d;

    .line 17
    .line 18
    invoke-static {}, LzY0/a;->a()LRc/d;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iput-object v0, p0, LmY0/c;->c:LRc/d;

    .line 23
    .line 24
    invoke-static {}, LzY0/a;->a()LRc/d;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iput-object v0, p0, LmY0/c;->d:LRc/d;

    .line 29
    .line 30
    invoke-static {}, LzY0/a;->a()LRc/d;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    iput-object v0, p0, LmY0/c;->e:LRc/d;

    .line 35
    .line 36
    return-void
.end method


# virtual methods
.method public final a(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LwY0/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, LmY0/c;->g()LmY0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    :cond_0
    invoke-virtual {p0}, LmY0/c;->h()LmY0/e;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0}, LmY0/c;->f()LmY0/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    if-eqz v0, :cond_2

    .line 24
    .line 25
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    :cond_2
    invoke-virtual {p0}, LmY0/c;->e()LmY0/e;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    if-eqz v0, :cond_3

    .line 33
    .line 34
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 35
    .line 36
    .line 37
    :cond_3
    return-void
.end method

.method public final b(LuY0/a;)V
    .locals 2
    .param p1    # LuY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/c;->a:Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, LmY0/e;

    .line 18
    .line 19
    invoke-interface {v1, p1}, LmY0/e;->a(LuY0/a;)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final c(LuY0/a;)V
    .locals 2
    .param p1    # LuY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/c;->a:Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    check-cast v1, LmY0/e;

    .line 18
    .line 19
    invoke-interface {v1, p1}, LmY0/e;->i(LuY0/a;)V

    .line 20
    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    return-void
.end method

.method public final d()Ljava/util/ArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/ArrayList<",
            "LmY0/e<",
            "*>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->a:Ljava/util/ArrayList;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()LmY0/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LmY0/e<",
            "LmY0/d$a$a;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->e:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/d;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LmY0/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final f()LmY0/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LmY0/e<",
            "LmY0/d$b$a;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->d:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/d;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LmY0/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final g()LmY0/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LmY0/e<",
            "LmY0/d$b$b;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->b:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/d;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LmY0/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final h()LmY0/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LmY0/e<",
            "LmY0/d$a$b;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->c:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/d;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LmY0/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final i(LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V
    .locals 8
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwY0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, LmY0/c;->g()LmY0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    move-object v0, p0

    .line 8
    move-object v2, p1

    .line 9
    move-object v3, p2

    .line 10
    move-object v4, p3

    .line 11
    move-object v5, p4

    .line 12
    invoke-virtual/range {v0 .. v5}, LmY0/c;->p(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V

    .line 13
    .line 14
    .line 15
    move-object v6, v4

    .line 16
    move-object v7, v5

    .line 17
    move-object v4, v2

    .line 18
    move-object v5, v3

    .line 19
    move-object v2, v0

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    move-object v2, p0

    .line 22
    move-object v4, p1

    .line 23
    move-object v5, p2

    .line 24
    move-object v6, p3

    .line 25
    move-object v7, p4

    .line 26
    :goto_0
    invoke-virtual {p0}, LmY0/c;->h()LmY0/e;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    if-eqz p1, :cond_1

    .line 31
    .line 32
    invoke-virtual {p0, p1, v4, v5, v7}, LmY0/c;->r(LmY0/e;LIY0/d;Landroid/graphics/RectF;LwY0/c;)V

    .line 33
    .line 34
    .line 35
    :cond_1
    invoke-virtual {p0}, LmY0/c;->f()LmY0/e;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    if-eqz v3, :cond_2

    .line 40
    .line 41
    invoke-virtual/range {v2 .. v7}, LmY0/c;->m(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V

    .line 42
    .line 43
    .line 44
    :cond_2
    invoke-virtual {p0}, LmY0/c;->e()LmY0/e;

    .line 45
    .line 46
    .line 47
    move-result-object v3

    .line 48
    if-eqz v3, :cond_3

    .line 49
    .line 50
    move-object v2, p0

    .line 51
    invoke-virtual/range {v2 .. v7}, LmY0/c;->k(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V

    .line 52
    .line 53
    .line 54
    :cond_3
    invoke-virtual {p0}, LmY0/c;->n()V

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method public final j(LmY0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$a$a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->e:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1, p1}, LRc/d;->a(Ljava/lang/Object;Lkotlin/reflect/m;Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final k(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$a$a;",
            ">;",
            "LIY0/d;",
            "Landroid/graphics/RectF;",
            "Landroid/graphics/RectF;",
            "LwY0/c;",
            ")V"
        }
    .end annotation

    .line 1
    iget v0, p3, Landroid/graphics/RectF;->left:F

    .line 2
    .line 3
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p5}, LwY0/c;->g()F

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-virtual {p5}, LwY0/c;->d()F

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    :goto_0
    add-float/2addr v0, v1

    .line 19
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget v1, p4, Landroid/graphics/RectF;->bottom:F

    .line 24
    .line 25
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    iget p3, p3, Landroid/graphics/RectF;->right:F

    .line 30
    .line 31
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 32
    .line 33
    .line 34
    move-result p2

    .line 35
    if-eqz p2, :cond_1

    .line 36
    .line 37
    invoke-virtual {p5}, LwY0/c;->d()F

    .line 38
    .line 39
    .line 40
    move-result p2

    .line 41
    goto :goto_1

    .line 42
    :cond_1
    invoke-virtual {p5}, LwY0/c;->g()F

    .line 43
    .line 44
    .line 45
    move-result p2

    .line 46
    :goto_1
    sub-float/2addr p3, p2

    .line 47
    invoke-static {p3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    iget p3, p4, Landroid/graphics/RectF;->bottom:F

    .line 52
    .line 53
    invoke-virtual {p5}, LwY0/c;->c()F

    .line 54
    .line 55
    .line 56
    move-result p4

    .line 57
    add-float/2addr p3, p4

    .line 58
    invoke-static {p3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 59
    .line 60
    .line 61
    move-result-object p3

    .line 62
    invoke-interface {p1, v0, v1, p2, p3}, LJY0/a;->c(Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public final l(LmY0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$b$a;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->d:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1, p1}, LRc/d;->a(Ljava/lang/Object;Lkotlin/reflect/m;Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final m(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$b$a;",
            ">;",
            "LIY0/d;",
            "Landroid/graphics/RectF;",
            "Landroid/graphics/RectF;",
            "LwY0/c;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget v0, p3, Landroid/graphics/RectF;->right:F

    .line 8
    .line 9
    invoke-virtual {p5}, LwY0/c;->d()F

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    sub-float/2addr v0, v1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    iget v0, p3, Landroid/graphics/RectF;->left:F

    .line 16
    .line 17
    :goto_0
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget v1, p4, Landroid/graphics/RectF;->top:F

    .line 22
    .line 23
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 28
    .line 29
    .line 30
    move-result p2

    .line 31
    if-eqz p2, :cond_1

    .line 32
    .line 33
    iget p2, p3, Landroid/graphics/RectF;->right:F

    .line 34
    .line 35
    goto :goto_1

    .line 36
    :cond_1
    iget p2, p3, Landroid/graphics/RectF;->left:F

    .line 37
    .line 38
    invoke-virtual {p5}, LwY0/c;->d()F

    .line 39
    .line 40
    .line 41
    move-result p3

    .line 42
    add-float/2addr p2, p3

    .line 43
    :goto_1
    invoke-static {p2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 44
    .line 45
    .line 46
    move-result-object p2

    .line 47
    iget p3, p4, Landroid/graphics/RectF;->bottom:F

    .line 48
    .line 49
    invoke-static {p3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 50
    .line 51
    .line 52
    move-result-object p3

    .line 53
    invoke-interface {p1, v0, v1, p2, p3}, LJY0/a;->c(Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final n()V
    .locals 10

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x1

    .line 3
    const/4 v2, 0x0

    .line 4
    const/4 v3, 0x3

    .line 5
    invoke-virtual {p0}, LmY0/c;->g()LmY0/e;

    .line 6
    .line 7
    .line 8
    move-result-object v4

    .line 9
    const/4 v5, 0x0

    .line 10
    if-eqz v4, :cond_3

    .line 11
    .line 12
    invoke-virtual {p0}, LmY0/c;->h()LmY0/e;

    .line 13
    .line 14
    .line 15
    move-result-object v6

    .line 16
    if-eqz v6, :cond_0

    .line 17
    .line 18
    invoke-interface {v6}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 19
    .line 20
    .line 21
    move-result-object v6

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    move-object v6, v5

    .line 24
    :goto_0
    invoke-virtual {p0}, LmY0/c;->f()LmY0/e;

    .line 25
    .line 26
    .line 27
    move-result-object v7

    .line 28
    if-eqz v7, :cond_1

    .line 29
    .line 30
    invoke-interface {v7}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 31
    .line 32
    .line 33
    move-result-object v7

    .line 34
    goto :goto_1

    .line 35
    :cond_1
    move-object v7, v5

    .line 36
    :goto_1
    invoke-virtual {p0}, LmY0/c;->e()LmY0/e;

    .line 37
    .line 38
    .line 39
    move-result-object v8

    .line 40
    if-eqz v8, :cond_2

    .line 41
    .line 42
    invoke-interface {v8}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 43
    .line 44
    .line 45
    move-result-object v8

    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v8, v5

    .line 48
    :goto_2
    new-array v9, v3, [Landroid/graphics/RectF;

    .line 49
    .line 50
    aput-object v6, v9, v2

    .line 51
    .line 52
    aput-object v7, v9, v1

    .line 53
    .line 54
    aput-object v8, v9, v0

    .line 55
    .line 56
    invoke-interface {v4, v9}, LmY0/e;->b([Landroid/graphics/RectF;)V

    .line 57
    .line 58
    .line 59
    :cond_3
    invoke-virtual {p0}, LmY0/c;->h()LmY0/e;

    .line 60
    .line 61
    .line 62
    move-result-object v4

    .line 63
    if-eqz v4, :cond_7

    .line 64
    .line 65
    invoke-virtual {p0}, LmY0/c;->g()LmY0/e;

    .line 66
    .line 67
    .line 68
    move-result-object v6

    .line 69
    if-eqz v6, :cond_4

    .line 70
    .line 71
    invoke-interface {v6}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 72
    .line 73
    .line 74
    move-result-object v6

    .line 75
    goto :goto_3

    .line 76
    :cond_4
    move-object v6, v5

    .line 77
    :goto_3
    invoke-virtual {p0}, LmY0/c;->f()LmY0/e;

    .line 78
    .line 79
    .line 80
    move-result-object v7

    .line 81
    if-eqz v7, :cond_5

    .line 82
    .line 83
    invoke-interface {v7}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 84
    .line 85
    .line 86
    move-result-object v7

    .line 87
    goto :goto_4

    .line 88
    :cond_5
    move-object v7, v5

    .line 89
    :goto_4
    invoke-virtual {p0}, LmY0/c;->e()LmY0/e;

    .line 90
    .line 91
    .line 92
    move-result-object v8

    .line 93
    if-eqz v8, :cond_6

    .line 94
    .line 95
    invoke-interface {v8}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 96
    .line 97
    .line 98
    move-result-object v8

    .line 99
    goto :goto_5

    .line 100
    :cond_6
    move-object v8, v5

    .line 101
    :goto_5
    new-array v9, v3, [Landroid/graphics/RectF;

    .line 102
    .line 103
    aput-object v6, v9, v2

    .line 104
    .line 105
    aput-object v7, v9, v1

    .line 106
    .line 107
    aput-object v8, v9, v0

    .line 108
    .line 109
    invoke-interface {v4, v9}, LmY0/e;->b([Landroid/graphics/RectF;)V

    .line 110
    .line 111
    .line 112
    :cond_7
    invoke-virtual {p0}, LmY0/c;->f()LmY0/e;

    .line 113
    .line 114
    .line 115
    move-result-object v4

    .line 116
    if-eqz v4, :cond_b

    .line 117
    .line 118
    invoke-virtual {p0}, LmY0/c;->h()LmY0/e;

    .line 119
    .line 120
    .line 121
    move-result-object v6

    .line 122
    if-eqz v6, :cond_8

    .line 123
    .line 124
    invoke-interface {v6}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 125
    .line 126
    .line 127
    move-result-object v6

    .line 128
    goto :goto_6

    .line 129
    :cond_8
    move-object v6, v5

    .line 130
    :goto_6
    invoke-virtual {p0}, LmY0/c;->g()LmY0/e;

    .line 131
    .line 132
    .line 133
    move-result-object v7

    .line 134
    if-eqz v7, :cond_9

    .line 135
    .line 136
    invoke-interface {v7}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 137
    .line 138
    .line 139
    move-result-object v7

    .line 140
    goto :goto_7

    .line 141
    :cond_9
    move-object v7, v5

    .line 142
    :goto_7
    invoke-virtual {p0}, LmY0/c;->e()LmY0/e;

    .line 143
    .line 144
    .line 145
    move-result-object v8

    .line 146
    if-eqz v8, :cond_a

    .line 147
    .line 148
    invoke-interface {v8}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 149
    .line 150
    .line 151
    move-result-object v8

    .line 152
    goto :goto_8

    .line 153
    :cond_a
    move-object v8, v5

    .line 154
    :goto_8
    new-array v9, v3, [Landroid/graphics/RectF;

    .line 155
    .line 156
    aput-object v6, v9, v2

    .line 157
    .line 158
    aput-object v7, v9, v1

    .line 159
    .line 160
    aput-object v8, v9, v0

    .line 161
    .line 162
    invoke-interface {v4, v9}, LmY0/e;->b([Landroid/graphics/RectF;)V

    .line 163
    .line 164
    .line 165
    :cond_b
    invoke-virtual {p0}, LmY0/c;->e()LmY0/e;

    .line 166
    .line 167
    .line 168
    move-result-object v4

    .line 169
    if-eqz v4, :cond_f

    .line 170
    .line 171
    invoke-virtual {p0}, LmY0/c;->h()LmY0/e;

    .line 172
    .line 173
    .line 174
    move-result-object v6

    .line 175
    if-eqz v6, :cond_c

    .line 176
    .line 177
    invoke-interface {v6}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 178
    .line 179
    .line 180
    move-result-object v6

    .line 181
    goto :goto_9

    .line 182
    :cond_c
    move-object v6, v5

    .line 183
    :goto_9
    invoke-virtual {p0}, LmY0/c;->f()LmY0/e;

    .line 184
    .line 185
    .line 186
    move-result-object v7

    .line 187
    if-eqz v7, :cond_d

    .line 188
    .line 189
    invoke-interface {v7}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 190
    .line 191
    .line 192
    move-result-object v7

    .line 193
    goto :goto_a

    .line 194
    :cond_d
    move-object v7, v5

    .line 195
    :goto_a
    invoke-virtual {p0}, LmY0/c;->g()LmY0/e;

    .line 196
    .line 197
    .line 198
    move-result-object v8

    .line 199
    if-eqz v8, :cond_e

    .line 200
    .line 201
    invoke-interface {v8}, LJY0/a;->getBounds()Landroid/graphics/RectF;

    .line 202
    .line 203
    .line 204
    move-result-object v5

    .line 205
    :cond_e
    new-array v3, v3, [Landroid/graphics/RectF;

    .line 206
    .line 207
    aput-object v6, v3, v2

    .line 208
    .line 209
    aput-object v7, v3, v1

    .line 210
    .line 211
    aput-object v5, v3, v0

    .line 212
    .line 213
    invoke-interface {v4, v3}, LmY0/e;->b([Landroid/graphics/RectF;)V

    .line 214
    .line 215
    .line 216
    :cond_f
    return-void
.end method

.method public final o(LmY0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$b$b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->b:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1, p1}, LRc/d;->a(Ljava/lang/Object;Lkotlin/reflect/m;Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final p(LmY0/e;LIY0/d;Landroid/graphics/RectF;Landroid/graphics/RectF;LwY0/c;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$b$b;",
            ">;",
            "LIY0/d;",
            "Landroid/graphics/RectF;",
            "Landroid/graphics/RectF;",
            "LwY0/c;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget v0, p3, Landroid/graphics/RectF;->left:F

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget v0, p3, Landroid/graphics/RectF;->right:F

    .line 11
    .line 12
    invoke-virtual {p5}, LwY0/c;->g()F

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    sub-float/2addr v0, v1

    .line 17
    :goto_0
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    iget v1, p4, Landroid/graphics/RectF;->top:F

    .line 22
    .line 23
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 28
    .line 29
    .line 30
    move-result p2

    .line 31
    if-eqz p2, :cond_1

    .line 32
    .line 33
    iget p2, p3, Landroid/graphics/RectF;->left:F

    .line 34
    .line 35
    invoke-virtual {p5}, LwY0/c;->g()F

    .line 36
    .line 37
    .line 38
    move-result p3

    .line 39
    add-float/2addr p2, p3

    .line 40
    goto :goto_1

    .line 41
    :cond_1
    iget p2, p3, Landroid/graphics/RectF;->right:F

    .line 42
    .line 43
    :goto_1
    invoke-static {p2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 44
    .line 45
    .line 46
    move-result-object p2

    .line 47
    iget p3, p4, Landroid/graphics/RectF;->bottom:F

    .line 48
    .line 49
    invoke-static {p3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 50
    .line 51
    .line 52
    move-result-object p3

    .line 53
    invoke-interface {p1, v0, v1, p2, p3}, LJY0/a;->c(Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public final q(LmY0/e;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$a$b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/c;->c:LRc/d;

    .line 2
    .line 3
    sget-object v1, LmY0/c;->g:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1, p1}, LRc/d;->a(Ljava/lang/Object;Lkotlin/reflect/m;Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final r(LmY0/e;LIY0/d;Landroid/graphics/RectF;LwY0/c;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LmY0/e<",
            "LmY0/d$a$b;",
            ">;",
            "LIY0/d;",
            "Landroid/graphics/RectF;",
            "LwY0/c;",
            ")V"
        }
    .end annotation

    .line 1
    iget v0, p3, Landroid/graphics/RectF;->left:F

    .line 2
    .line 3
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p4}, LwY0/c;->g()F

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    invoke-virtual {p4}, LwY0/c;->d()F

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    :goto_0
    add-float/2addr v0, v1

    .line 19
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget v1, p3, Landroid/graphics/RectF;->top:F

    .line 24
    .line 25
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    iget v2, p3, Landroid/graphics/RectF;->right:F

    .line 30
    .line 31
    invoke-interface {p2}, LIY0/d;->S()Z

    .line 32
    .line 33
    .line 34
    move-result p2

    .line 35
    if-eqz p2, :cond_1

    .line 36
    .line 37
    invoke-virtual {p4}, LwY0/c;->d()F

    .line 38
    .line 39
    .line 40
    move-result p2

    .line 41
    goto :goto_1

    .line 42
    :cond_1
    invoke-virtual {p4}, LwY0/c;->g()F

    .line 43
    .line 44
    .line 45
    move-result p2

    .line 46
    :goto_1
    sub-float/2addr v2, p2

    .line 47
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    iget p3, p3, Landroid/graphics/RectF;->top:F

    .line 52
    .line 53
    invoke-virtual {p4}, LwY0/c;->h()F

    .line 54
    .line 55
    .line 56
    move-result p4

    .line 57
    add-float/2addr p3, p4

    .line 58
    invoke-static {p3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 59
    .line 60
    .line 61
    move-result-object p3

    .line 62
    invoke-interface {p1, v0, v1, p2, p3}, LJY0/a;->c(Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 63
    .line 64
    .line 65
    return-void
.end method
