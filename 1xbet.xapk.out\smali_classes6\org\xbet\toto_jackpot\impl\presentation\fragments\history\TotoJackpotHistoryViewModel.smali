.class public final Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;,
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$b;,
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c;,
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;,
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e;,
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008!\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0018\u0000 {2\u00020\u0001:\u0008|}~\u007f\u0080\u0001\u0081\u0001Bk\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0008\u0008\u0001\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010\u001f\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u001eJ\u000f\u0010 \u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008 \u0010\u001eJ%\u0010&\u001a\u00020\u001c2\u000c\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\"0!2\u0006\u0010%\u001a\u00020$H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010(\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008(\u0010\u001eJ%\u0010)\u001a\u00020\u001c2\u000c\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\"0!2\u0006\u0010%\u001a\u00020$H\u0002\u00a2\u0006\u0004\u0008)\u0010\'J\u0017\u0010,\u001a\u00020\u001c2\u0006\u0010+\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008,\u0010-J%\u00100\u001a\u0008\u0012\u0004\u0012\u00020/0!2\u0006\u0010.\u001a\u00020\"2\u0006\u0010%\u001a\u00020$H\u0002\u00a2\u0006\u0004\u00080\u00101J\u0013\u00104\u001a\u0008\u0012\u0004\u0012\u00020302\u00a2\u0006\u0004\u00084\u00105J\u0013\u00107\u001a\u0008\u0012\u0004\u0012\u00020602\u00a2\u0006\u0004\u00087\u00105J\u0013\u00108\u001a\u0008\u0012\u0004\u0012\u00020602\u00a2\u0006\u0004\u00088\u00105J\u0013\u0010:\u001a\u0008\u0012\u0004\u0012\u00020902\u00a2\u0006\u0004\u0008:\u00105J\u0013\u0010<\u001a\u0008\u0012\u0004\u0012\u00020;02\u00a2\u0006\u0004\u0008<\u00105J\u0013\u0010>\u001a\u0008\u0012\u0004\u0012\u00020=02\u00a2\u0006\u0004\u0008>\u00105J\u0013\u0010@\u001a\u0008\u0012\u0004\u0012\u00020?02\u00a2\u0006\u0004\u0008@\u00105J\u0013\u0010B\u001a\u0008\u0012\u0004\u0012\u00020$0A\u00a2\u0006\u0004\u0008B\u0010CJ\r\u0010D\u001a\u00020\u001c\u00a2\u0006\u0004\u0008D\u0010\u001eJ\r\u0010E\u001a\u00020\u001c\u00a2\u0006\u0004\u0008E\u0010\u001eJ\r\u0010F\u001a\u00020\u001c\u00a2\u0006\u0004\u0008F\u0010\u001eJ\r\u0010G\u001a\u00020\u001c\u00a2\u0006\u0004\u0008G\u0010\u001eR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0016\u0010b\u001a\u0002068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0018\u0010f\u001a\u0004\u0018\u00010c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u001a\u0010j\u001a\u0008\u0012\u0004\u0012\u0002060g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u001a\u0010l\u001a\u0008\u0012\u0004\u0012\u0002060g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010iR\u001a\u0010n\u001a\u0008\u0012\u0004\u0012\u0002090g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010iR\u001a\u0010p\u001a\u0008\u0012\u0004\u0012\u0002030g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010iR\u001a\u0010r\u001a\u0008\u0012\u0004\u0012\u00020;0g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010iR\u001a\u0010t\u001a\u0008\u0012\u0004\u0012\u00020=0g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010iR\u001a\u0010v\u001a\u0008\u0012\u0004\u0012\u00020?0g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010iR\u001a\u0010z\u001a\u0008\u0012\u0004\u0012\u00020$0w8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010y\u00a8\u0006\u0082\u0001"
    }
    d2 = {
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwW0/a;",
        "getAvailableTotoTypesUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LwW0/e;",
        "getJackpotTypeUseCase",
        "LwW0/o;",
        "setHasTiragUseCase",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;",
        "clearOutcomesUseCase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;",
        "getJackpotHistoryScenario",
        "LyW0/a;",
        "clearJackpotHistoryUseCase",
        "LwW0/c;",
        "getCacheJackpotTiragUseCase",
        "LwX0/c;",
        "router",
        "<init>",
        "(LwW0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwW0/e;LwW0/o;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;LwX0/c;)V",
        "",
        "c4",
        "()V",
        "N3",
        "T3",
        "",
        "Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
        "items",
        "",
        "currencySymbol",
        "i4",
        "(Ljava/util/List;Ljava/lang/String;)V",
        "Q3",
        "j4",
        "LSX0/e;",
        "state",
        "g4",
        "(LSX0/e;)V",
        "historyItem",
        "LHW0/b;",
        "X3",
        "(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;Ljava/lang/String;)Ljava/util/List;",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e;",
        "a4",
        "()Lkotlinx/coroutines/flow/f0;",
        "",
        "Y3",
        "L3",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;",
        "M3",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;",
        "Z3",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c;",
        "W3",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;",
        "b4",
        "Lkotlinx/coroutines/flow/Z;",
        "P3",
        "()Lkotlinx/coroutines/flow/Z;",
        "f4",
        "e4",
        "e2",
        "d4",
        "v1",
        "LwW0/a;",
        "x1",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "y1",
        "LwW0/e;",
        "F1",
        "LwW0/o;",
        "H1",
        "LSX0/a;",
        "I1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "P1",
        "Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;",
        "S1",
        "Lorg/xbet/ui_common/utils/M;",
        "V1",
        "Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;",
        "b2",
        "LyW0/a;",
        "v2",
        "LwW0/c;",
        "x2",
        "LwX0/c;",
        "y2",
        "Z",
        "lastConnection",
        "Lkotlinx/coroutines/x0;",
        "F2",
        "Lkotlinx/coroutines/x0;",
        "jackpotHistoryJob",
        "Lkotlinx/coroutines/flow/V;",
        "H2",
        "Lkotlinx/coroutines/flow/V;",
        "loadingState",
        "I2",
        "animationState",
        "P2",
        "appBarState",
        "S2",
        "timeTiragState",
        "V2",
        "lottieViewState",
        "X2",
        "jackpotHistoryState",
        "F3",
        "tiragState",
        "Lkotlinx/coroutines/flow/U;",
        "H3",
        "Lkotlinx/coroutines/flow/U;",
        "errorState",
        "I3",
        "a",
        "d",
        "e",
        "c",
        "f",
        "b",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final I3:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:LwW0/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F2:Lkotlinx/coroutines/x0;

.field public final F3:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LyW0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwW0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:LwW0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LwW0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y2:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I3:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$b;

    return-void
.end method

.method public constructor <init>(LwW0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwW0/e;LwW0/o;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;Lorg/xbet/ui_common/utils/M;Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;LyW0/a;LwW0/c;LwX0/c;)V
    .locals 0
    .param p1    # LwW0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwW0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LwW0/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # LyW0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LwW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->v1:LwW0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->x1:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->y1:LwW0/e;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F1:LwW0/o;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H1:LSX0/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I1:Lorg/xbet/ui_common/utils/internet/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P1:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V1:Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->b2:LyW0/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->v2:LwW0/c;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->x2:LwX0/c;

    .line 27
    .line 28
    const/4 p1, 0x1

    .line 29
    iput-boolean p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->y2:Z

    .line 30
    .line 31
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 32
    .line 33
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H2:Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 40
    .line 41
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 46
    .line 47
    sget-object p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$a;

    .line 48
    .line 49
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 54
    .line 55
    sget-object p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e$a;

    .line 56
    .line 57
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 62
    .line 63
    sget-object p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$c;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$c;

    .line 64
    .line 65
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 70
    .line 71
    sget-object p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c$a;

    .line 72
    .line 73
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->X2:Lkotlinx/coroutines/flow/V;

    .line 78
    .line 79
    sget-object p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$b;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$b;

    .line 80
    .line 81
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 86
    .line 87
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H3:Lkotlinx/coroutines/flow/U;

    .line 92
    .line 93
    invoke-direct {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->c4()V

    .line 94
    .line 95
    .line 96
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V1:Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)LwW0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->y1:LwW0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->x1:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->T3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->y2:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic F3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)LwW0/o;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F1:LwW0/o;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->y2:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic I3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;LSX0/e;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->g4(LSX0/e;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic J3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/util/List;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->i4(Ljava/util/List;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic K3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/util/List;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->j4(Ljava/util/List;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final O3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lorg/xbet/ui_common/utils/M;->i(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, LSX0/e$c;->a:LSX0/e$c;

    .line 7
    .line 8
    invoke-direct {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->g4(LSX0/e;)V

    .line 9
    .line 10
    .line 11
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F1:LwW0/o;

    .line 12
    .line 13
    const/4 p1, 0x0

    .line 14
    invoke-virtual {p0, p1}, LwW0/o;->a(Z)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static final R3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/q;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/q;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final S3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final U3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->Q3()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S1:Lorg/xbet/ui_common/utils/M;

    .line 5
    .line 6
    new-instance v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/p;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/p;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final V3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H3:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    invoke-interface {p0, p2}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method

.method private final c4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I1:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$observeConnection$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$observeConnection$1;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private final g4(LSX0/e;)V
    .locals 10

    .line 1
    sget-object v0, LSX0/e$b;->a:LSX0/e$b;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H1:LSX0/a;

    .line 10
    .line 11
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 12
    .line 13
    sget v3, Lpb/k;->empty_tiraj_history:I

    .line 14
    .line 15
    const/16 v8, 0x1c

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v4, 0x0

    .line 19
    const/4 v5, 0x0

    .line 20
    const-wide/16 v6, 0x0

    .line 21
    .line 22
    invoke-static/range {v1 .. v9}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    goto :goto_0

    .line 27
    :cond_0
    sget-object v0, LSX0/e$a;->a:LSX0/e$a;

    .line 28
    .line 29
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H1:LSX0/a;

    .line 36
    .line 37
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 38
    .line 39
    sget v3, Lpb/k;->data_retrieval_error:I

    .line 40
    .line 41
    const/16 v8, 0x1c

    .line 42
    .line 43
    const/4 v9, 0x0

    .line 44
    const/4 v4, 0x0

    .line 45
    const/4 v5, 0x0

    .line 46
    const-wide/16 v6, 0x0

    .line 47
    .line 48
    invoke-static/range {v1 .. v9}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    goto :goto_0

    .line 53
    :cond_1
    sget-object v0, LSX0/e$c;->a:LSX0/e$c;

    .line 54
    .line 55
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 56
    .line 57
    .line 58
    move-result p1

    .line 59
    if-eqz p1, :cond_2

    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H1:LSX0/a;

    .line 62
    .line 63
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 64
    .line 65
    sget v2, Lpb/k;->data_retrieval_error:I

    .line 66
    .line 67
    sget v3, Lpb/k;->refresh_data:I

    .line 68
    .line 69
    new-instance v4, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/n;

    .line 70
    .line 71
    invoke-direct {v4, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/n;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V

    .line 72
    .line 73
    .line 74
    const/16 v7, 0x10

    .line 75
    .line 76
    const/4 v8, 0x0

    .line 77
    const-wide/16 v5, 0x0

    .line 78
    .line 79
    invoke-static/range {v0 .. v8}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    goto :goto_0

    .line 84
    :cond_2
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H1:LSX0/a;

    .line 85
    .line 86
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->NOTHING:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 87
    .line 88
    const/16 v7, 0x1e

    .line 89
    .line 90
    const/4 v8, 0x0

    .line 91
    const/4 v2, 0x0

    .line 92
    const/4 v3, 0x0

    .line 93
    const/4 v4, 0x0

    .line 94
    const-wide/16 v5, 0x0

    .line 95
    .line 96
    invoke-static/range {v0 .. v8}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    :goto_0
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H2:Lkotlinx/coroutines/flow/V;

    .line 101
    .line 102
    :cond_3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    move-object v2, v1

    .line 107
    check-cast v2, Ljava/lang/Boolean;

    .line 108
    .line 109
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 110
    .line 111
    .line 112
    sget-object v2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 113
    .line 114
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 115
    .line 116
    .line 117
    move-result v1

    .line 118
    if-eqz v1, :cond_3

    .line 119
    .line 120
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 121
    .line 122
    :cond_4
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    move-object v2, v0

    .line 127
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;

    .line 128
    .line 129
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$c;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$c;

    .line 130
    .line 131
    invoke-interface {v1, v0, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 132
    .line 133
    .line 134
    move-result v0

    .line 135
    if-eqz v0, :cond_4

    .line 136
    .line 137
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 138
    .line 139
    :cond_5
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    move-object v2, v1

    .line 144
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;

    .line 145
    .line 146
    new-instance v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$b;

    .line 147
    .line 148
    invoke-direct {v2, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$b;-><init>(Lorg/xbet/uikit/components/lottie/a;)V

    .line 149
    .line 150
    .line 151
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 152
    .line 153
    .line 154
    move-result v1

    .line 155
    if-eqz v1, :cond_5

    .line 156
    .line 157
    return-void
.end method

.method public static final h4(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlin/Unit;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Ljava/lang/Boolean;

    .line 9
    .line 10
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 11
    .line 12
    .line 13
    sget-object v2, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 14
    .line 15
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-eqz v1, :cond_0

    .line 20
    .line 21
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 22
    .line 23
    :cond_1
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    move-object v2, v0

    .line 28
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;

    .line 29
    .line 30
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$a;

    .line 31
    .line 32
    invoke-interface {v1, v0, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    if-eqz v0, :cond_1

    .line 37
    .line 38
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 39
    .line 40
    :cond_2
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    move-object v2, v1

    .line 45
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;

    .line 46
    .line 47
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$a;

    .line 48
    .line 49
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    move-result v1

    .line 53
    if-eqz v1, :cond_2

    .line 54
    .line 55
    invoke-direct {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->c4()V

    .line 56
    .line 57
    .line 58
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 59
    .line 60
    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->O3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->h4(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->R3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->U3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->N3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic y3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->Q3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic z3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)LwW0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->v1:LwW0/a;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final L3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final M3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final N3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/l;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/l;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, v2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final P3()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H3:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->d(Lkotlinx/coroutines/flow/U;)Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final Q3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/o;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/o;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getFromCacheJackpotHistories$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, v2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getFromCacheJackpotHistories$2;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final T3()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->y1:LwW0/e;

    .line 4
    .line 5
    invoke-virtual {v1}, LwW0/e;->a()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-nez v1, :cond_0

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    iget-object v2, v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 13
    .line 14
    :cond_1
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    move-object v4, v3

    .line 19
    check-cast v4, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;

    .line 20
    .line 21
    sget-object v4, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d$a;

    .line 22
    .line 23
    invoke-interface {v2, v3, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    if-eqz v3, :cond_1

    .line 28
    .line 29
    iget-object v2, v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F2:Lkotlinx/coroutines/x0;

    .line 30
    .line 31
    if-eqz v2, :cond_2

    .line 32
    .line 33
    invoke-interface {v2}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    const/4 v3, 0x1

    .line 38
    if-ne v2, v3, :cond_2

    .line 39
    .line 40
    return-void

    .line 41
    :cond_2
    invoke-static {v0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 42
    .line 43
    .line 44
    move-result-object v4

    .line 45
    new-instance v10, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;

    .line 46
    .line 47
    const/4 v2, 0x0

    .line 48
    invoke-direct {v10, v0, v1, v2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;ILkotlin/coroutines/e;)V

    .line 49
    .line 50
    .line 51
    new-instance v13, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/m;

    .line 52
    .line 53
    invoke-direct {v13, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/m;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V

    .line 54
    .line 55
    .line 56
    const/16 v15, 0x168

    .line 57
    .line 58
    const/16 v16, 0x0

    .line 59
    .line 60
    const-string v5, "getTotoJackpotHistory"

    .line 61
    .line 62
    const/4 v6, 0x5

    .line 63
    const-wide/16 v7, 0x5

    .line 64
    .line 65
    const/4 v9, 0x0

    .line 66
    const/4 v11, 0x0

    .line 67
    const/4 v12, 0x0

    .line 68
    const/4 v14, 0x0

    .line 69
    invoke-static/range {v4 .. v16}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->Y(Lkotlinx/coroutines/N;Ljava/lang/String;IJLjava/util/List;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F2:Lkotlinx/coroutines/x0;

    .line 74
    .line 75
    return-void
.end method

.method public final W3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->X2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final X3(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;Ljava/lang/String;)Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "LHW0/b;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, LHW0/b;

    .line 2
    .line 3
    new-instance v1, LHW0/b$a$a;

    .line 4
    .line 5
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->h()Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->e()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->g()I

    .line 14
    .line 15
    .line 16
    move-result v4

    .line 17
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->f()J

    .line 18
    .line 19
    .line 20
    move-result-wide v5

    .line 21
    invoke-direct/range {v1 .. v6}, LHW0/b$a$a;-><init>(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;Ljava/lang/String;IJ)V

    .line 22
    .line 23
    .line 24
    invoke-direct {v0, v1}, LHW0/b;-><init>(LHW0/b$a;)V

    .line 25
    .line 26
    .line 27
    new-instance v1, LHW0/b;

    .line 28
    .line 29
    new-instance v2, LHW0/b$a$b;

    .line 30
    .line 31
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->h()Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->g()I

    .line 36
    .line 37
    .line 38
    move-result v4

    .line 39
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->f()J

    .line 40
    .line 41
    .line 42
    move-result-wide v5

    .line 43
    sget-object v7, LAW0/a;->a:LAW0/a;

    .line 44
    .line 45
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->d()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v8

    .line 49
    invoke-virtual {v7, v8}, LAW0/a;->a(Ljava/lang/String;)Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v7

    .line 53
    new-instance v8, Ljava/lang/StringBuilder;

    .line 54
    .line 55
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 56
    .line 57
    .line 58
    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 59
    .line 60
    .line 61
    const-string v7, " "

    .line 62
    .line 63
    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 64
    .line 65
    .line 66
    invoke-virtual {v8, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v7

    .line 73
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->a()I

    .line 74
    .line 75
    .line 76
    move-result p2

    .line 77
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 78
    .line 79
    .line 80
    move-result-object v8

    .line 81
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->c()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object v9

    .line 85
    invoke-virtual {p1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->b()I

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object v10

    .line 93
    invoke-direct/range {v2 .. v10}, LHW0/b$a$b;-><init>(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 94
    .line 95
    .line 96
    invoke-direct {v1, v2}, LHW0/b;-><init>(LHW0/b$a;)V

    .line 97
    .line 98
    .line 99
    const/4 p1, 0x2

    .line 100
    new-array p1, p1, [LHW0/b;

    .line 101
    .line 102
    const/4 p2, 0x0

    .line 103
    aput-object v0, p1, p2

    .line 104
    .line 105
    const/4 p2, 0x1

    .line 106
    aput-object v1, p1, p2

    .line 107
    .line 108
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    return-object p1
.end method

.method public final Y3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->H2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final Z3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->V2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final a4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final b4()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final d4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;

    .line 9
    .line 10
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$b;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$b;

    .line 11
    .line 12
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    return-void
.end method

.method public final e2()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->b2:LyW0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LyW0/a;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->x2:LwX0/c;

    .line 7
    .line 8
    invoke-virtual {v0}, LwX0/c;->h()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final e4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P1:Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/domain/usecase/jackpot/a;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->x2:LwX0/c;

    .line 7
    .line 8
    new-instance v1, LzW0/b;

    .line 9
    .line 10
    invoke-direct {v1}, LzW0/b;-><init>()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1}, LwX0/c;->g(Lq4/q;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final f4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->b2:LyW0/a;

    .line 2
    .line 3
    invoke-virtual {v0}, LyW0/a;->a()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->X2:Lkotlinx/coroutines/flow/V;

    .line 7
    .line 8
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    move-object v2, v1

    .line 13
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c;

    .line 14
    .line 15
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c$a;

    .line 16
    .line 17
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    if-eqz v1, :cond_0

    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->T3()V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final i4(Ljava/util/List;Ljava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    sget-object v0, LSX0/e$b;->a:LSX0/e$b;

    .line 8
    .line 9
    invoke-direct {p0, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->g4(LSX0/e;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 13
    .line 14
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 15
    .line 16
    .line 17
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    :cond_1
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_2

    .line 26
    .line 27
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    move-object v2, v1

    .line 32
    check-cast v2, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    .line 33
    .line 34
    invoke-virtual {v2}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->h()Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    sget-object v3, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;->ACTIVE:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;

    .line 39
    .line 40
    if-eq v2, v3, :cond_1

    .line 41
    .line 42
    invoke-interface {v0, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    goto :goto_0

    .line 46
    :cond_2
    new-instance p1, Ljava/util/ArrayList;

    .line 47
    .line 48
    const/16 v1, 0xa

    .line 49
    .line 50
    invoke-static {v0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 51
    .line 52
    .line 53
    move-result v1

    .line 54
    invoke-direct {p1, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 55
    .line 56
    .line 57
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 62
    .line 63
    .line 64
    move-result v1

    .line 65
    if-eqz v1, :cond_3

    .line 66
    .line 67
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    check-cast v1, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    .line 72
    .line 73
    invoke-virtual {p0, v1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->X3(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;Ljava/lang/String;)Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    invoke-interface {p1, v1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 78
    .line 79
    .line 80
    goto :goto_1

    .line 81
    :cond_3
    invoke-static {p1}, Lkotlin/collections/w;->A(Ljava/lang/Iterable;)Ljava/util/List;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iget-object p2, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->X2:Lkotlinx/coroutines/flow/V;

    .line 86
    .line 87
    :cond_4
    invoke-interface {p2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    move-object v1, v0

    .line 92
    check-cast v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c;

    .line 93
    .line 94
    new-instance v1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c$b;

    .line 95
    .line 96
    invoke-direct {v1, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$c$b;-><init>(Ljava/util/List;)V

    .line 97
    .line 98
    .line 99
    invoke-interface {p2, v0, v1}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 100
    .line 101
    .line 102
    move-result v0

    .line 103
    if-eqz v0, :cond_4

    .line 104
    .line 105
    invoke-interface {p1}, Ljava/util/Collection;->isEmpty()Z

    .line 106
    .line 107
    .line 108
    move-result p1

    .line 109
    if-nez p1, :cond_6

    .line 110
    .line 111
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 112
    .line 113
    :cond_5
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object p2

    .line 117
    move-object v0, p2

    .line 118
    check-cast v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;

    .line 119
    .line 120
    sget-object v0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$c;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$c;

    .line 121
    .line 122
    invoke-interface {p1, p2, v0}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result p2

    .line 126
    if-eqz p2, :cond_5

    .line 127
    .line 128
    goto :goto_2

    .line 129
    :cond_6
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 130
    .line 131
    :cond_7
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    move-object p2, p1

    .line 136
    check-cast p2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;

    .line 137
    .line 138
    sget-object p2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$a;

    .line 139
    .line 140
    invoke-interface {v0, p1, p2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 141
    .line 142
    .line 143
    move-result p1

    .line 144
    if-eqz p1, :cond_7

    .line 145
    .line 146
    :goto_2
    return-void
.end method

.method public final j4(Ljava/util/List;Ljava/lang/String;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
            ">;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    :cond_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    move-object v1, v0

    .line 16
    check-cast v1, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    .line 17
    .line 18
    invoke-virtual {v1}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->h()Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    sget-object v2, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;->ACTIVE:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel$State;

    .line 23
    .line 24
    if-ne v1, v2, :cond_0

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_1
    const/4 v0, 0x0

    .line 28
    :goto_0
    check-cast v0, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    .line 29
    .line 30
    if-nez v0, :cond_3

    .line 31
    .line 32
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 33
    .line 34
    :cond_2
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    move-object v2, v1

    .line 39
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;

    .line 40
    .line 41
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$a;

    .line 42
    .line 43
    invoke-interface {p1, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v1

    .line 47
    if-eqz v1, :cond_2

    .line 48
    .line 49
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 50
    .line 51
    :cond_3
    if-eqz v0, :cond_6

    .line 52
    .line 53
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F3:Lkotlinx/coroutines/flow/V;

    .line 54
    .line 55
    :cond_4
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    move-object v2, v1

    .line 60
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;

    .line 61
    .line 62
    new-instance v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;

    .line 63
    .line 64
    invoke-direct {v2, v0, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;-><init>(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    invoke-interface {p1, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 68
    .line 69
    .line 70
    move-result v1

    .line 71
    if-eqz v1, :cond_4

    .line 72
    .line 73
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->v2:LwW0/c;

    .line 74
    .line 75
    invoke-virtual {p1}, LwW0/c;->a()LuW0/f;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    invoke-virtual {p1}, LuW0/f;->d()J

    .line 80
    .line 81
    .line 82
    move-result-wide p1

    .line 83
    const-wide/16 v0, 0x0

    .line 84
    .line 85
    cmp-long v2, p1, v0

    .line 86
    .line 87
    if-eqz v2, :cond_6

    .line 88
    .line 89
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 90
    .line 91
    :cond_5
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    move-object v2, v1

    .line 96
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e;

    .line 97
    .line 98
    new-instance v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e$b;

    .line 99
    .line 100
    invoke-direct {v2, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$e$b;-><init>(J)V

    .line 101
    .line 102
    .line 103
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 104
    .line 105
    .line 106
    move-result v1

    .line 107
    if-eqz v1, :cond_5

    .line 108
    .line 109
    :cond_6
    return-void
.end method
