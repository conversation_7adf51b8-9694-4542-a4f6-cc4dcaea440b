.class public final Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u0001B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001f\u0010\u0010\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J7\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0017\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0019JK\u0010\"\u001a\u00020\n2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u001a2\u0014\u0008\u0002\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u00120\u001d2\u0016\u0008\u0002\u0010!\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010 \u0012\u0004\u0012\u00020\u00120\u001d\u00a2\u0006\u0004\u0008\"\u0010#J\u0017\u0010%\u001a\u00020\n2\u0008\u0008\u0001\u0010$\u001a\u00020\r\u00a2\u0006\u0004\u0008%\u0010&J\u0017\u0010%\u001a\u00020\n2\u0008\u0010(\u001a\u0004\u0018\u00010\'\u00a2\u0006\u0004\u0008%\u0010)J\u0017\u0010*\u001a\u00020\n2\u0008\u0008\u0001\u0010$\u001a\u00020\r\u00a2\u0006\u0004\u0008*\u0010&J\u0017\u0010*\u001a\u00020\n2\u0008\u0010(\u001a\u0004\u0018\u00010\'\u00a2\u0006\u0004\u0008*\u0010)J\u0017\u0010+\u001a\u00020\n2\u0008\u0008\u0001\u0010$\u001a\u00020\r\u00a2\u0006\u0004\u0008+\u0010&J\u0017\u0010+\u001a\u00020\n2\u0008\u0010(\u001a\u0004\u0018\u00010\'\u00a2\u0006\u0004\u0008+\u0010)J\u000f\u0010,\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008,\u0010-R\u0014\u00101\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0014\u00104\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u00106\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00103R\u001b\u0010<\u001a\u0002078BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00088\u00109\u001a\u0004\u0008:\u0010;R\u0014\u0010=\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u00103\u00a8\u0006>"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "tag",
        "",
        "setOnlyOneTagMargins",
        "(Lorg/xbet/uikit/components/tag/Tag;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "LL11/c;",
        "pictureLink",
        "placeholderLink",
        "Lkotlin/Function1;",
        "Landroid/graphics/drawable/Drawable;",
        "onLoaded",
        "Lcom/bumptech/glide/load/engine/GlideException;",
        "onError",
        "e",
        "(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V",
        "resId",
        "setTag",
        "(I)V",
        "",
        "text",
        "(Ljava/lang/CharSequence;)V",
        "setLiveTag",
        "setLabel",
        "i",
        "()V",
        "LC31/a;",
        "a",
        "LC31/a;",
        "binding",
        "b",
        "I",
        "margin12",
        "c",
        "margin4",
        "Lorg/xbet/uikit/utils/z;",
        "d",
        "Lkotlin/j;",
        "getLoadHelper",
        "()Lorg/xbet/uikit/utils/z;",
        "loadHelper",
        "height",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/a;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/a;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_12:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->b:I

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->c:I

    .line 7
    new-instance p1, Ln31/a;

    invoke-direct {p1, p0}, Ln31/a;-><init>(Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;)V

    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->d:Lkotlin/j;

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_108:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    const/high16 p2, 0x40000000    # 2.0f

    .line 9
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->e:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Landroid/graphics/drawable/Drawable;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->g(Landroid/graphics/drawable/Drawable;)Z

    move-result p0

    return p0
.end method

.method public static synthetic b(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->h(Lcom/bumptech/glide/load/engine/GlideException;)Z

    move-result p0

    return p0
.end method

.method public static synthetic c(Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;)Lorg/xbet/uikit/utils/z;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->d(Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;)Lorg/xbet/uikit/utils/z;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;)Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    iget-object p0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 4
    .line 5
    iget-object p0, p0, LC31/a;->b:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 6
    .line 7
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/z;-><init>(Landroid/widget/ImageView;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p6, p5, 0x4

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    new-instance p3, Ln31/b;

    .line 6
    .line 7
    invoke-direct {p3}, Ln31/b;-><init>()V

    .line 8
    .line 9
    .line 10
    :cond_0
    and-int/lit8 p5, p5, 0x8

    .line 11
    .line 12
    if-eqz p5, :cond_1

    .line 13
    .line 14
    new-instance p4, Ln31/c;

    .line 15
    .line 16
    invoke-direct {p4}, Ln31/c;-><init>()V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->e(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final g(Landroid/graphics/drawable/Drawable;)Z
    .locals 0

    .line 1
    const/4 p0, 0x0

    return p0
.end method

.method private final getLoadHelper()Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/z;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final h(Lcom/bumptech/glide/load/engine/GlideException;)Z
    .locals 0

    .line 1
    const/4 p0, 0x0

    return p0
.end method

.method private final setOnlyOneTagMargins(Lorg/xbet/uikit/components/tag/Tag;)V
    .locals 5

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    iget-object v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 10
    .line 11
    invoke-virtual {v2}, LC31/a;->getRoot()Landroid/view/View;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    invoke-static {p0}, Landroidx/core/view/e0;->C(Landroid/view/View;)I

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    if-nez v3, :cond_0

    .line 24
    .line 25
    iget v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->b:I

    .line 26
    .line 27
    add-int/2addr v0, v2

    .line 28
    add-int/2addr v1, v2

    .line 29
    invoke-virtual {p1, v2, v2, v0, v1}, Landroid/view/View;->layout(IIII)V

    .line 30
    .line 31
    .line 32
    return-void

    .line 33
    :cond_0
    iget v3, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->b:I

    .line 34
    .line 35
    sub-int v4, v2, v3

    .line 36
    .line 37
    sub-int/2addr v4, v0

    .line 38
    sub-int/2addr v2, v3

    .line 39
    add-int/2addr v1, v3

    .line 40
    invoke-virtual {p1, v4, v3, v2, v1}, Landroid/view/View;->layout(IIII)V

    .line 41
    .line 42
    .line 43
    return-void
.end method


# virtual methods
.method public final e(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .param p1    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LL11/c;",
            "LL11/c;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/graphics/drawable/Drawable;",
            "Ljava/lang/Boolean;",
            ">;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lcom/bumptech/glide/load/engine/GlideException;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->getLoadHelper()Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1, p2, p3, p4}, Lorg/xbet/uikit/utils/z;->t(LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final i()V
    .locals 9

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 2
    .line 3
    iget-object v1, v1, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 4
    .line 5
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v6

    .line 9
    iget-object v1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 10
    .line 11
    iget-object v1, v1, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v7

    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 18
    .line 19
    iget-object v1, v1, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 20
    .line 21
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result v8

    .line 25
    iget-object v1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 26
    .line 27
    iget-object v1, v1, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 28
    .line 29
    iget v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->b:I

    .line 30
    .line 31
    add-int v4, v2, v7

    .line 32
    .line 33
    add-int v5, v2, v8

    .line 34
    .line 35
    move v3, v2

    .line 36
    move-object v0, p0

    .line 37
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 38
    .line 39
    .line 40
    iget-object v1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 41
    .line 42
    iget-object v1, v1, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 43
    .line 44
    iget v3, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->b:I

    .line 45
    .line 46
    add-int v2, v3, v7

    .line 47
    .line 48
    iget v4, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->c:I

    .line 49
    .line 50
    add-int/2addr v2, v4

    .line 51
    add-int/2addr v7, v3

    .line 52
    add-int/2addr v7, v4

    .line 53
    add-int v4, v7, v6

    .line 54
    .line 55
    add-int v5, v3, v8

    .line 56
    .line 57
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 58
    .line 59
    .line 60
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    move-object p1, p0

    .line 5
    sub-int/2addr p4, p2

    .line 6
    sub-int/2addr p5, p3

    .line 7
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 8
    .line 9
    iget-object p2, p2, LC31/a;->b:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 10
    .line 11
    const/4 p3, 0x0

    .line 12
    invoke-virtual {p2, p3, p3, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 13
    .line 14
    .line 15
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 16
    .line 17
    iget-object p2, p2, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 18
    .line 19
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 20
    .line 21
    .line 22
    move-result p2

    .line 23
    if-nez p2, :cond_0

    .line 24
    .line 25
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 26
    .line 27
    iget-object p2, p2, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 28
    .line 29
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    if-nez p2, :cond_0

    .line 34
    .line 35
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->i()V

    .line 36
    .line 37
    .line 38
    return-void

    .line 39
    :cond_0
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 40
    .line 41
    iget-object p2, p2, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 42
    .line 43
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 44
    .line 45
    .line 46
    move-result p2

    .line 47
    if-nez p2, :cond_1

    .line 48
    .line 49
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 50
    .line 51
    iget-object p2, p2, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 52
    .line 53
    invoke-direct {p0, p2}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->setOnlyOneTagMargins(Lorg/xbet/uikit/components/tag/Tag;)V

    .line 54
    .line 55
    .line 56
    return-void

    .line 57
    :cond_1
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 58
    .line 59
    iget-object p2, p2, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 60
    .line 61
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 62
    .line 63
    .line 64
    move-result p2

    .line 65
    if-nez p2, :cond_2

    .line 66
    .line 67
    iget-object p2, p1, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 68
    .line 69
    iget-object p2, p2, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 70
    .line 71
    invoke-direct {p0, p2}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->setOnlyOneTagMargins(Lorg/xbet/uikit/components/tag/Tag;)V

    .line 72
    .line 73
    .line 74
    :cond_2
    return-void
.end method

.method public onMeasure(II)V
    .locals 7

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->b:I

    .line 6
    .line 7
    mul-int/lit8 v1, v1, 0x2

    .line 8
    .line 9
    sub-int v1, v0, v1

    .line 10
    .line 11
    iget-object v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 12
    .line 13
    iget-object v2, v2, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 14
    .line 15
    const/high16 v3, -0x80000000

    .line 16
    .line 17
    invoke-static {v1, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result v4

    .line 21
    const/4 v5, 0x0

    .line 22
    invoke-static {v5, v5}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 23
    .line 24
    .line 25
    move-result v6

    .line 26
    invoke-virtual {v2, v4, v6}, Landroid/view/View;->measure(II)V

    .line 27
    .line 28
    .line 29
    iget-object v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 30
    .line 31
    iget-object v4, v2, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    .line 32
    .line 33
    iget-object v2, v2, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    .line 34
    .line 35
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 36
    .line 37
    .line 38
    move-result v2

    .line 39
    sub-int v2, v1, v2

    .line 40
    .line 41
    iget v6, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->c:I

    .line 42
    .line 43
    sub-int/2addr v2, v6

    .line 44
    invoke-static {v2, v5}, Ljava/lang/Math;->max(II)I

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    invoke-static {v2, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    invoke-static {v5, v5}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 53
    .line 54
    .line 55
    move-result v6

    .line 56
    invoke-virtual {v4, v2, v6}, Landroid/view/View;->measure(II)V

    .line 57
    .line 58
    .line 59
    iget-object v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 60
    .line 61
    iget-object v2, v2, LC31/a;->c:Landroid/widget/TextView;

    .line 62
    .line 63
    invoke-static {v1, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 64
    .line 65
    .line 66
    move-result v1

    .line 67
    invoke-static {p2, v5}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 68
    .line 69
    .line 70
    move-result p2

    .line 71
    invoke-virtual {v2, v1, p2}, Landroid/view/View;->measure(II)V

    .line 72
    .line 73
    .line 74
    iget-object p2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    .line 75
    .line 76
    iget-object p2, p2, LC31/a;->b:Lorg/xbet/uikit/components/views/LoadableShapeableImageView;

    .line 77
    .line 78
    const/high16 v1, 0x40000000    # 2.0f

    .line 79
    .line 80
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 81
    .line 82
    .line 83
    move-result v0

    .line 84
    iget v2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->e:I

    .line 85
    .line 86
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 87
    .line 88
    .line 89
    move-result v1

    .line 90
    invoke-virtual {p2, v0, v1}, Landroid/view/View;->measure(II)V

    .line 91
    .line 92
    .line 93
    iget p2, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->e:I

    .line 94
    .line 95
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 96
    .line 97
    .line 98
    return-void
.end method

.method public final setLabel(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->setLabel(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setLabel(Ljava/lang/CharSequence;)V
    .locals 1

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    iget-object v0, v0, LC31/a;->c:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setLiveTag(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->setLiveTag(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setLiveTag(Ljava/lang/CharSequence;)V
    .locals 2

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    iget-object v0, v0, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    iget-object v0, v0, LC31/a;->d:Lorg/xbet/uikit/components/tag/Tag;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    if-eqz p1, :cond_2

    const/16 v1, 0x8

    .line 4
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public final setTag(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->setTag(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTag(Ljava/lang/CharSequence;)V
    .locals 2

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    iget-object v0, v0, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/bannerchampionship/SportBannerChampionship;->a:LC31/a;

    iget-object v0, v0, LC31/a;->e:Lorg/xbet/uikit/components/tag/Tag;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    if-eqz p1, :cond_2

    const/16 v1, 0x8

    .line 4
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method
