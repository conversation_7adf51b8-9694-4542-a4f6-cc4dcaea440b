.class public final LN81/m;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0092\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u00b7\u0001\u0010/\u001a\u00020.2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010!\u001a\u00020 2\u0006\u0010#\u001a\u00020\"2\u0006\u0010%\u001a\u00020$2\u0006\u0010\'\u001a\u00020&2\u0006\u0010)\u001a\u00020(2\u0006\u0010+\u001a\u00020*2\u0006\u0010-\u001a\u00020,H\u0000\u00a2\u0006\u0004\u0008/\u00100\u00a8\u00061"
    }
    d2 = {
        "LN81/m;",
        "",
        "<init>",
        "()V",
        "Lc8/h;",
        "requestParamsDataSource",
        "LRf0/l;",
        "publicPreferencesWrapper",
        "LHX0/e;",
        "resourceManager",
        "Lf8/g;",
        "serviceGenerator",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "LwX0/c;",
        "aggregatorRouter",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LxX0/a;",
        "blockPaymentNavigator",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "LwX0/a;",
        "appScreensProvider",
        "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
        "tokenRefresher",
        "LJ81/a;",
        "dailyTaskLocalDataSource",
        "Lp9/g;",
        "observeLoginStateUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LWb0/a;",
        "tipsDialogFeature",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lak/a;",
        "balanceFeature",
        "LwX0/C;",
        "routerHolder",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "aggregatorScreenFactory",
        "Lorg/xbet/analytics/domain/scope/E;",
        "dailyTasksAnalytics",
        "LN81/l;",
        "a",
        "(Lc8/h;LRf0/l;LHX0/e;Lf8/g;Lcom/xbet/onexuser/domain/user/c;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwX0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lp9/g;Lp9/c;LWb0/a;Lorg/xbet/ui_common/utils/internet/a;Lak/a;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;)LN81/l;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a(Lc8/h;LRf0/l;LHX0/e;Lf8/g;Lcom/xbet/onexuser/domain/user/c;LwX0/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwX0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lp9/g;Lp9/c;LWb0/a;Lorg/xbet/ui_common/utils/internet/a;Lak/a;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;)LN81/l;
    .locals 23
    .param p1    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LRf0/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lcom/xbet/onexuser/domain/managers/TokenRefresher;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # LJ81/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lp9/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # LWb0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lorg/xplatform/aggregator/api/navigation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lorg/xbet/analytics/domain/scope/E;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, LN81/c;->a()LN81/l$a;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    move-object/from16 v19, p1

    .line 6
    .line 7
    move-object/from16 v5, p2

    .line 8
    .line 9
    move-object/from16 v6, p3

    .line 10
    .line 11
    move-object/from16 v7, p4

    .line 12
    .line 13
    move-object/from16 v8, p5

    .line 14
    .line 15
    move-object/from16 v4, p6

    .line 16
    .line 17
    move-object/from16 v9, p7

    .line 18
    .line 19
    move-object/from16 v10, p8

    .line 20
    .line 21
    move-object/from16 v11, p9

    .line 22
    .line 23
    move-object/from16 v12, p10

    .line 24
    .line 25
    move-object/from16 v13, p11

    .line 26
    .line 27
    move-object/from16 v14, p12

    .line 28
    .line 29
    move-object/from16 v15, p13

    .line 30
    .line 31
    move-object/from16 v16, p14

    .line 32
    .line 33
    move-object/from16 v17, p15

    .line 34
    .line 35
    move-object/from16 v2, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v3, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    invoke-interface/range {v1 .. v22}, LN81/l$a;->a(LWb0/a;Lak/a;LwX0/c;LRf0/l;LHX0/e;Lf8/g;Lcom/xbet/onexuser/domain/user/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwX0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lp9/g;Lp9/c;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;)LN81/l;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    return-object v0
.end method
