.class public final Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;
.super Landroidx/recyclerview/widget/RecyclerView$s;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\'\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "org/xbet/ui_common/circleindicator/CircleIndicator$f",
        "Landroidx/recyclerview/widget/RecyclerView$s;",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "recyclerView",
        "",
        "dx",
        "dy",
        "",
        "onScrolled",
        "(Landroidx/recyclerview/widget/RecyclerView;II)V",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;


# direct methods
.method public constructor <init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$s;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onScrolled(Landroidx/recyclerview/widget/RecyclerView;II)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3}, Landroidx/recyclerview/widget/RecyclerView$s;->onScrolled(Landroidx/recyclerview/widget/RecyclerView;II)V

    .line 2
    .line 3
    .line 4
    iget-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 5
    .line 6
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 7
    .line 8
    .line 9
    move-result-object p3

    .line 10
    invoke-static {p2, p3}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->d(Lorg/xbet/ui_common/circleindicator/CircleIndicator;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I

    .line 11
    .line 12
    .line 13
    move-result p2

    .line 14
    const/4 p3, -0x1

    .line 15
    if-ne p2, p3, :cond_0

    .line 16
    .line 17
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 18
    .line 19
    invoke-static {p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->e(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    goto :goto_0

    .line 24
    :cond_0
    iget-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 25
    .line 26
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-static {p2, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->d(Lorg/xbet/ui_common/circleindicator/CircleIndicator;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    :goto_0
    iget-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 35
    .line 36
    invoke-static {p2, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->a(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 37
    .line 38
    .line 39
    return-void
.end method
