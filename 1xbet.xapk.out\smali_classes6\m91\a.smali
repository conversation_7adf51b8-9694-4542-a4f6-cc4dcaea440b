.class public final synthetic Lm91/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lm91/b;


# direct methods
.method public synthetic constructor <init>(Lm91/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm91/a;->a:Lm91/b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lm91/a;->a:Lm91/b;

    invoke-static {v0}, Lm91/b;->a(Lm91/b;)Ll91/a;

    move-result-object v0

    return-object v0
.end method
