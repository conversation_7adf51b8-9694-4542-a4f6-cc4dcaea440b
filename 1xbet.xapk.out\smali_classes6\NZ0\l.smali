.class public final synthetic LNZ0/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroid/widget/HorizontalScrollView;

.field public final synthetic b:I

.field public final synthetic c:Lorg/xbet/uikit/components/chips/DsChip;


# direct methods
.method public synthetic constructor <init>(Landroid/widget/HorizontalScrollView;ILorg/xbet/uikit/components/chips/DsChip;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/l;->a:Landroid/widget/HorizontalScrollView;

    iput p2, p0, LNZ0/l;->b:I

    iput-object p3, p0, LNZ0/l;->c:Lorg/xbet/uikit/components/chips/DsChip;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    .line 1
    iget-object v0, p0, LNZ0/l;->a:Landroid/widget/HorizontalScrollView;

    iget v1, p0, LNZ0/l;->b:I

    iget-object v2, p0, LNZ0/l;->c:Lorg/xbet/uikit/components/chips/DsChip;

    invoke-static {v0, v1, v2}, Lorg/xbet/uikit/components/chips/DsChipGroup;->a(Landroid/widget/HorizontalScrollView;ILorg/xbet/uikit/components/chips/DsChip;)V

    return-void
.end method
