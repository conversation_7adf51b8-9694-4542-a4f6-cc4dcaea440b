.class public final Lm31/f;
.super Ljava/lang/Object;


# static fields
.field public static SportCollectionWithHeader:I = 0x7f1402eb

.field public static SportsCollectionSimple:I = 0x7f1402ec

.field public static SportsCollectionSimple_CircleWithLabel:I = 0x7f1402ed

.field public static SportsCollectionSimple_RectangleL:I = 0x7f1402ee

.field public static SportsCollectionSimple_RectangleM:I = 0x7f1402ef

.field public static SportsCollectionSimple_RectangleS:I = 0x7f1402f0

.field public static SportsCollectionSimple_RectangleS_WithHeader:I = 0x7f1402f1

.field public static Widget_EventCard:I = 0x7f1406cc

.field public static Widget_EventCardHeader:I = 0x7f1406eb

.field public static Widget_EventCardHeaderButton:I = 0x7f1406ec

.field public static Widget_EventCard_Basic:I = 0x7f1406cd

.field public static Widget_EventCard_BetConstructor:I = 0x7f1406ce

.field public static Widget_EventCard_Championship:I = 0x7f1406cf

.field public static Widget_EventCard_Compact:I = 0x7f1406d0

.field public static Widget_EventCard_Empty:I = 0x7f1406d1

.field public static Widget_EventCard_Info:I = 0x7f1406d2

.field public static Widget_EventCard_Info_Championship:I = 0x7f1406d3

.field public static Widget_EventCard_Info_Favorites:I = 0x7f1406d4

.field public static Widget_EventCard_Info_History:I = 0x7f1406d5

.field public static Widget_EventCard_Info_Line:I = 0x7f1406d6

.field public static Widget_EventCard_Info_Live:I = 0x7f1406d7

.field public static Widget_EventCard_Info_Live_Alt:I = 0x7f1406d8

.field public static Widget_EventCard_Info_Seeding:I = 0x7f1406d9

.field public static Widget_EventCard_Middle:I = 0x7f1406da

.field public static Widget_EventCard_Middle_Baccarat:I = 0x7f1406db

.field public static Widget_EventCard_Middle_Championship:I = 0x7f1406dc

.field public static Widget_EventCard_Middle_Cricket:I = 0x7f1406dd

.field public static Widget_EventCard_Middle_CyberPoker:I = 0x7f1406de

.field public static Widget_EventCard_Middle_Dice:I = 0x7f1406df

.field public static Widget_EventCard_Middle_Fighting:I = 0x7f1406e0

.field public static Widget_EventCard_Middle_Score:I = 0x7f1406e1

.field public static Widget_EventCard_Middle_Sette:I = 0x7f1406e2

.field public static Widget_EventCard_Middle_WinningFormula:I = 0x7f1406e3

.field public static Widget_EventCard_Promotions:I = 0x7f1406e4

.field public static Widget_EventCard_ResultsCyber:I = 0x7f1406e5

.field public static Widget_EventCard_ResultsFavorites:I = 0x7f1406e6

.field public static Widget_EventCard_ResultsHistory:I = 0x7f1406e7

.field public static Widget_EventCard_ResultsLive:I = 0x7f1406e8

.field public static Widget_EventCard_Statistics:I = 0x7f1406e9

.field public static Widget_EventCard_Synthetics:I = 0x7f1406ea

.field public static Widget_ScoreCell_SmallTeamLogo:I = 0x7f14088d

.field public static Widget_ScoreCell_TennisScore:I = 0x7f14088e

.field public static Widget_SportCell:I = 0x7f1408b8

.field public static Widget_SportCell_SportCellLeft:I = 0x7f1408b9

.field public static Widget_SportCell_SportCellLeft_ActionIcon:I = 0x7f1408ba

.field public static Widget_SportCell_SportCellLeft_BackgroundIcon:I = 0x7f1408bb

.field public static Widget_SportCell_SportCellLeft_ChampionshipIcon:I = 0x7f1408bc

.field public static Widget_SportCell_SportCellLeft_Icon:I = 0x7f1408bd

.field public static Widget_SportCell_SportCellMiddle:I = 0x7f1408be

.field public static Widget_SportCell_SportCellMiddle_Dynamic:I = 0x7f1408bf

.field public static Widget_SportCell_SportCellMiddle_Dynamic_L:I = 0x7f1408c0

.field public static Widget_SportCell_SportCellMiddle_Dynamic_M:I = 0x7f1408c1

.field public static Widget_SportCell_SportCellMiddle_Dynamic_S:I = 0x7f1408c2

.field public static Widget_SportCell_SportCellMiddle_Static:I = 0x7f1408c3

.field public static Widget_SportCell_SportCellMiddle_Static_L:I = 0x7f1408c4

.field public static Widget_SportCell_SportCellMiddle_Static_M:I = 0x7f1408c5

.field public static Widget_SportCell_SportCellMiddle_Static_S:I = 0x7f1408c6

.field public static Widget_SportCell_SportCellMiddle_Static_XL:I = 0x7f1408c7

.field public static Widget_SportCell_SportCellRight:I = 0x7f1408c8

.field public static Widget_SportCell_SportFeedsCellRight_Large:I = 0x7f1408c9

.field public static Widget_SportCell_SportFeedsCellRight_Medium:I = 0x7f1408ca

.field public static Widget_SportCell_SportFeedsCellRight_MediumClear:I = 0x7f1408cb

.field public static Widget_SportCell_SportFeedsCellRight_Small:I = 0x7f1408cc

.field public static Widget_SportCollection_Multi:I = 0x7f1408cd

.field public static Widget_SportCollection_NoSpecificScroll:I = 0x7f1408ce

.field public static Widget_SportCollection_Single:I = 0x7f1408cf

.field public static Widget_SportCouponCard:I = 0x7f1408d0

.field public static Widget_SportCouponCard_Blocked:I = 0x7f1408d1

.field public static Widget_SportCouponCard_Default:I = 0x7f1408d2

.field public static Widget_SportCouponCard_Dependent:I = 0x7f1408d3

.field public static Widget_SportCouponCard_Skeleton:I = 0x7f1408d4

.field public static Widget_SportCouponCard_Unavailable:I = 0x7f1408d5

.field public static Widget_SportVictoryIndicatorCompact_Static:I = 0x7f1408da

.field public static Widget_SportVictoryIndicatorCompact_Theme:I = 0x7f1408db

.field public static Widget_SportVictoryIndicator_Left_Static:I = 0x7f1408d6

.field public static Widget_SportVictoryIndicator_Left_Theme:I = 0x7f1408d7

.field public static Widget_SportVictoryIndicator_Right_Static:I = 0x7f1408d8

.field public static Widget_SportVictoryIndicator_Right_Theme:I = 0x7f1408d9


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
