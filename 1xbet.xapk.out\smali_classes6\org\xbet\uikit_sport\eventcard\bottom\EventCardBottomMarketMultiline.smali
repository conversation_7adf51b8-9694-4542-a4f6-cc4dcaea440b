.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/bottom/c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$a;,
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;,
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;,
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;,
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;,
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;,
        Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ac\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0014\u0008\u0007\u0018\u0000 d2\u00020\u00012\u00020\u0002:\nB}\u0080\u0001Z\u0089\u0001\u008a\u0001zB\u001d\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J/\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J-\u0010\u0017\u001a\u00020\u000f2\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J7\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u001b\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010 \u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008 \u0010!J\u001f\u0010%\u001a\u00020$2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008%\u0010&J%\u0010)\u001a\u00020\u000f2\u000c\u0010(\u001a\u0008\u0012\u0004\u0012\u00020\'0\u00122\u0006\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008)\u0010*J\u000f\u0010+\u001a\u00020$H\u0002\u00a2\u0006\u0004\u0008+\u0010,J\u000f\u0010-\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008-\u0010.J\u001f\u0010/\u001a\u00020$2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008/\u0010&J!\u00103\u001a\u0004\u0018\u00010\t2\u0006\u00101\u001a\u0002002\u0006\u00102\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u00083\u00104J\u00a1\u0001\u0010B\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\u0013052\u000c\u00107\u001a\u0008\u0012\u0004\u0012\u0002060\u00122\u000e\u00108\u001a\n\u0012\u0004\u0012\u000206\u0018\u00010\u00122\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u00102\u001a\u00020\u000b2\u0006\u00109\u001a\u00020\u000b2\u0006\u0010:\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010;\u001a\u00020\u000b2\u0006\u0010#\u001a\u00020\"2\u0006\u0010=\u001a\u00020<2\u000c\u0010?\u001a\u0008\u0012\u0004\u0012\u00020\u000f0>2\u000c\u0010@\u001a\u0008\u0012\u0004\u0012\u00020\u000f0>2\u000c\u0010A\u001a\u0008\u0012\u0004\u0012\u00020\u000f0>H\u0002\u00a2\u0006\u0004\u0008B\u0010CJS\u0010G\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\u0013052\u0006\u00102\u001a\u00020\u000b2\u0006\u0010D\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010E\u001a\u0002062\u0008\u0010F\u001a\u0004\u0018\u000106H\u0002\u00a2\u0006\u0004\u0008G\u0010HJ9\u0010L\u001a\u00020K2\u0006\u0010I\u001a\u0002002\u0008\u0010J\u001a\u0004\u0018\u0001002\u0006\u0010=\u001a\u00020<2\u0006\u0010\r\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008L\u0010MJE\u0010T\u001a\u00020<2\u0006\u0010N\u001a\u00020\u000b2\u0008\u0010O\u001a\u0004\u0018\u00010\u000b2\u0006\u0010P\u001a\u00020\u000b2\u0008\u0010Q\u001a\u0004\u0018\u00010\u000b2\u0006\u0010R\u001a\u00020\u000b2\u0008\u0010S\u001a\u0004\u0018\u00010\u000bH\u0002\u00a2\u0006\u0004\u0008T\u0010UJI\u0010Z\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\'052\u0008\u0010V\u001a\u0004\u0018\u00010K2\u000c\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010W\u001a\u00020\u000b2\u0006\u0010Y\u001a\u00020XH\u0002\u00a2\u0006\u0004\u0008Z\u0010[JI\u0010\\\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\u0013052\u0006\u00102\u001a\u00020\u000b2\u0006\u0010D\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010E\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008\\\u0010]JA\u0010_\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\u0013052\u0006\u0010\n\u001a\u00020^2\u0006\u0010E\u001a\u0002062\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008_\u0010`JA\u0010b\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\u0013052\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000b2\u0006\u0010a\u001a\u00020XH\u0002\u00a2\u0006\u0004\u0008b\u0010cJ)\u0010d\u001a\u00020\u000f*\u0008\u0012\u0004\u0012\u00020\'052\u0006\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008d\u0010\u0018J\u001b\u0010e\u001a\u00020\u000f*\u00020^2\u0006\u0010E\u001a\u000206H\u0002\u00a2\u0006\u0004\u0008e\u0010fJ\r\u0010h\u001a\u00020g\u00a2\u0006\u0004\u0008h\u0010iJ\u001f\u0010k\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\u000b2\u0006\u0010j\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008k\u0010lJ\u0015\u0010m\u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020\u001e\u00a2\u0006\u0004\u0008m\u0010nJA\u0010r\u001a\u00020\u000f2\u0018\u0010p\u001a\u0014\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000f0o2\u0018\u0010q\u001a\u0014\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000f0o\u00a2\u0006\u0004\u0008r\u0010sJ\u001b\u0010u\u001a\u00020\u000f2\u000c\u0010t\u001a\u0008\u0012\u0004\u0012\u00020\u000f0>\u00a2\u0006\u0004\u0008u\u0010vJ-\u0010x\u001a\u00020\u000f2\u001e\u0010t\u001a\u001a\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u000f0w\u00a2\u0006\u0004\u0008x\u0010yR\u0014\u0010|\u001a\u00020g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010{R\u0014\u0010\u007f\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0016\u0010\u0081\u0001\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0080\u0001\u0010~R\u0015\u0010\u0082\u0001\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010~R\u0015\u0010\u0083\u0001\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010~R\u0015\u0010\u0084\u0001\u001a\u00020\u000b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010~R\u001a\u0010\u0086\u0001\u001a\u0004\u0018\u00010\u001e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008b\u0010\u0085\u0001R\u001a\u0010\u0088\u0001\u001a\u0004\u0018\u00010$8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008G\u0010\u0087\u0001\u00a8\u0006\u008b\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;",
        "Landroid/widget/FrameLayout;",
        "Lorg/xbet/uikit_sport/eventcard/bottom/c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Landroid/view/View;",
        "view",
        "",
        "headerCount",
        "rowCount",
        "widthMeasureSpec",
        "",
        "x",
        "(Landroid/view/View;III)V",
        "",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
        "measureViews",
        "maxColumn",
        "bottomWidth",
        "w",
        "(Ljava/util/List;II)V",
        "viewIndex",
        "rowIndex",
        "viewWidth",
        "y",
        "(Landroid/view/View;IIII)V",
        "Lorg/xbet/uikit_sport/eventcard/bottom/b;",
        "bottomMarketUiModel",
        "q",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/b;)I",
        "",
        "showMoreAvailable",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;",
        "t",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/b;Z)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
        "measureRows",
        "z",
        "(Ljava/util/List;Z)V",
        "l",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;",
        "v",
        "()V",
        "m",
        "Lorg/xbet/uikit_sport/eventcard/bottom/a;",
        "newBottomMarketHeader",
        "groupIndex",
        "r",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/a;I)Landroid/view/View;",
        "",
        "LL11/a;",
        "eventCardMarkets",
        "oldEventCardMarkets",
        "rowIndexInGroup",
        "maxColumnInGroup",
        "maxRow",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
        "measureType",
        "Lkotlin/Function0;",
        "onMarketAdded",
        "onShowMoreAdded",
        "onShowMoreReplaced",
        "e",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;IIIIIIZLorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V",
        "marketIndex",
        "market",
        "oldMarket",
        "h",
        "(Ljava/util/List;IIIIILL11/a;LL11/a;)V",
        "newHeader",
        "cacheHeader",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
        "s",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/a;Lorg/xbet/uikit_sport/eventcard/bottom/a;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
        "newMaxRow",
        "oldMaxRow",
        "newMaxColumn",
        "oldMaxColumn",
        "newRowSize",
        "oldRowSize",
        "u",
        "(ILjava/lang/Integer;ILjava/lang/Integer;ILjava/lang/Integer;)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
        "header",
        "lastViewIndexInGroup",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "rowViewType",
        "d",
        "(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V",
        "f",
        "(Ljava/util/List;IIIIILL11/a;)V",
        "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
        "j",
        "(Ljava/util/List;Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;III)V",
        "viewType",
        "g",
        "(Ljava/util/List;Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V",
        "i",
        "k",
        "(Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;)V",
        "Lorg/xbet/uikit_sport/eventcard/bottom/p;",
        "getMarketGroupBindHelper",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/p;",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "setMarkets",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/b;)V",
        "Lkotlin/Function2;",
        "clickListener",
        "longClickListener",
        "setOnMarketClickListeners",
        "(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;)V",
        "listener",
        "setShowMoreButtonListener",
        "(Lkotlin/jvm/functions/Function0;)V",
        "Lkotlin/Function3;",
        "setExpandedListener",
        "(LOc/n;)V",
        "a",
        "Lorg/xbet/uikit_sport/eventcard/bottom/p;",
        "marketGroupBindHelper",
        "b",
        "I",
        "marketHeight",
        "c",
        "headerHeight",
        "topMarginHeader",
        "horizontalPadding",
        "spaceBetween",
        "Lorg/xbet/uikit_sport/eventcard/bottom/b;",
        "cacheBottomMarketUiModel",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;",
        "cacheMeasureParams",
        "MeasureType",
        "ViewType",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final i:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final j:I


# instance fields
.field public final a:Lorg/xbet/uikit_sport/eventcard/bottom/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public g:Lorg/xbet/uikit_sport/eventcard/bottom/b;

.field public h:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->i:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->j:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    new-instance p2, Lorg/xbet/uikit_sport/eventcard/bottom/p;

    invoke-direct {p2, p0}, Lorg/xbet/uikit_sport/eventcard/bottom/p;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;)V

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 5
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_48:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->b:I

    .line 6
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_18:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->c:I

    .line 7
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_2:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d:I

    .line 8
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->space_16:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->e:I

    .line 9
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->f:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->n(Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->p(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lkotlin/jvm/internal/Ref$BooleanRef;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->o(Lkotlin/jvm/internal/Ref$BooleanRef;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final n(Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 2
    .line 3
    add-int/lit8 v0, v0, 0x1

    .line 4
    .line 5
    iput v0, p0, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 6
    .line 7
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 8
    .line 9
    return-object p0
.end method

.method public static final o(Lkotlin/jvm/internal/Ref$BooleanRef;)Lkotlin/Unit;
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final p(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$IntRef;)Lkotlin/Unit;
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 3
    .line 4
    iget p0, p1, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 5
    .line 6
    sub-int/2addr p0, v0

    .line 7
    iput p0, p1, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method


# virtual methods
.method public final d(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
            ">;",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;II",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    .line 2
    .line 3
    move-object v1, p2

    .line 4
    move-object v2, p3

    .line 5
    move v3, p4

    .line 6
    move v4, p5

    .line 7
    move-object v5, p6

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final e(Ljava/util/List;Ljava/util/List;Ljava/util/List;IIIIIIZLorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;",
            "Ljava/util/List<",
            "LL11/a;",
            ">;",
            "Ljava/util/List<",
            "LL11/a;",
            ">;IIIIIIZ",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v9, p3

    .line 4
    .line 5
    move/from16 v1, p9

    .line 6
    .line 7
    move-object/from16 v10, p11

    .line 8
    .line 9
    add-int v2, p4, p8

    .line 10
    .line 11
    add-int/lit8 v3, v2, 0x1

    .line 12
    .line 13
    const/4 v4, 0x0

    .line 14
    const/4 v5, 0x1

    .line 15
    if-lt v3, v1, :cond_0

    .line 16
    .line 17
    const/4 v11, 0x1

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 v11, 0x0

    .line 20
    :goto_0
    add-int/lit8 v2, v2, 0x2

    .line 21
    .line 22
    if-lt v2, v1, :cond_1

    .line 23
    .line 24
    const/4 v12, 0x1

    .line 25
    goto :goto_1

    .line 26
    :cond_1
    const/4 v12, 0x0

    .line 27
    :goto_1
    invoke-static/range {p2 .. p2}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 28
    .line 29
    .line 30
    move-result v13

    .line 31
    invoke-interface/range {p2 .. p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 32
    .line 33
    .line 34
    move-result-object v14

    .line 35
    :goto_2
    invoke-interface {v14}, Ljava/util/Iterator;->hasNext()Z

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    if-eqz v1, :cond_9

    .line 40
    .line 41
    invoke-interface {v14}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    add-int/lit8 v15, v4, 0x1

    .line 46
    .line 47
    if-gez v4, :cond_2

    .line 48
    .line 49
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 50
    .line 51
    .line 52
    :cond_2
    move-object v7, v1

    .line 53
    check-cast v7, LL11/a;

    .line 54
    .line 55
    if-eqz v9, :cond_3

    .line 56
    .line 57
    invoke-static {v9, v4}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    check-cast v1, LL11/a;

    .line 62
    .line 63
    :goto_3
    move-object v8, v1

    .line 64
    goto :goto_4

    .line 65
    :cond_3
    const/4 v1, 0x0

    .line 66
    goto :goto_3

    .line 67
    :goto_4
    mul-int v1, p6, p7

    .line 68
    .line 69
    add-int v3, v1, v4

    .line 70
    .line 71
    if-eqz p10, :cond_5

    .line 72
    .line 73
    if-eqz v11, :cond_5

    .line 74
    .line 75
    if-ne v13, v4, :cond_5

    .line 76
    .line 77
    invoke-interface/range {p14 .. p14}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    iget-object v1, v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 81
    .line 82
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->v()Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->SHOW_MORE:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 87
    .line 88
    move-object/from16 v1, p1

    .line 89
    .line 90
    move/from16 v3, p4

    .line 91
    .line 92
    move/from16 v5, p8

    .line 93
    .line 94
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->g(Ljava/util/List;Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 95
    .line 96
    .line 97
    :cond_4
    move-object/from16 v0, p0

    .line 98
    .line 99
    goto :goto_5

    .line 100
    :cond_5
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->MEASURE_ALL:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 101
    .line 102
    if-ne v10, v0, :cond_6

    .line 103
    .line 104
    move-object/from16 v0, p0

    .line 105
    .line 106
    move-object/from16 v1, p1

    .line 107
    .line 108
    move/from16 v2, p5

    .line 109
    .line 110
    move/from16 v6, p8

    .line 111
    .line 112
    move v5, v4

    .line 113
    move/from16 v4, p4

    .line 114
    .line 115
    invoke-virtual/range {v0 .. v7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->f(Ljava/util/List;IIIIILL11/a;)V

    .line 116
    .line 117
    .line 118
    goto :goto_5

    .line 119
    :cond_6
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->MEASURE_NEW_DATA:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 120
    .line 121
    if-ne v10, v0, :cond_4

    .line 122
    .line 123
    move-object/from16 v0, p0

    .line 124
    .line 125
    move-object/from16 v1, p1

    .line 126
    .line 127
    move/from16 v5, p4

    .line 128
    .line 129
    move/from16 v2, p5

    .line 130
    .line 131
    move/from16 v6, p8

    .line 132
    .line 133
    invoke-virtual/range {v0 .. v8}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->h(Ljava/util/List;IIIIILL11/a;LL11/a;)V

    .line 134
    .line 135
    .line 136
    :goto_5
    if-eqz p10, :cond_7

    .line 137
    .line 138
    if-nez v11, :cond_8

    .line 139
    .line 140
    if-eqz v12, :cond_7

    .line 141
    .line 142
    goto :goto_6

    .line 143
    :cond_7
    move v4, v15

    .line 144
    goto :goto_7

    .line 145
    :cond_8
    :goto_6
    add-int/lit8 v1, p7, -0x1

    .line 146
    .line 147
    if-ge v13, v1, :cond_7

    .line 148
    .line 149
    invoke-interface/range {p13 .. p13}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 150
    .line 151
    .line 152
    iget-object v1, v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 153
    .line 154
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->v()Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 155
    .line 156
    .line 157
    move-result-object v2

    .line 158
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->SHOW_MORE:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 159
    .line 160
    move-object/from16 v1, p1

    .line 161
    .line 162
    move/from16 v3, p4

    .line 163
    .line 164
    move/from16 v5, p8

    .line 165
    .line 166
    move v4, v15

    .line 167
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->g(Ljava/util/List;Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 168
    .line 169
    .line 170
    :goto_7
    invoke-interface/range {p12 .. p12}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-object/from16 v0, p0

    .line 174
    .line 175
    goto/16 :goto_2

    .line 176
    .line 177
    :cond_9
    return-void
.end method

.method public final f(Ljava/util/List;IIIIILL11/a;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;IIIII",
            "LL11/a;",
            ")V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    invoke-virtual {v0, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->o(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 4
    .line 5
    .line 6
    move-result-object p3

    .line 7
    move p2, p5

    .line 8
    move p5, p4

    .line 9
    move-object p4, p7

    .line 10
    move p7, p6

    .line 11
    move p6, p2

    .line 12
    move-object p2, p1

    .line 13
    move-object p1, p0

    .line 14
    invoke-virtual/range {p1 .. p7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->j(Ljava/util/List;Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;III)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public final g(Ljava/util/List;Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;",
            "Landroid/view/View;",
            "III",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
            ")V"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;

    .line 2
    .line 3
    move-object v1, p2

    .line 4
    move v2, p3

    .line 5
    move v3, p4

    .line 6
    move v4, p5

    .line 7
    move-object v5, p6

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;-><init>(Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 9
    .line 10
    .line 11
    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final getMarketGroupBindHelper()Lorg/xbet/uikit_sport/eventcard/bottom/p;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h(Ljava/util/List;IIIIILL11/a;LL11/a;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;IIIII",
            "LL11/a;",
            "LL11/a;",
            ")V"
        }
    .end annotation

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    invoke-virtual {v1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->u(II)Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    invoke-static/range {p7 .. p8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    if-eqz v2, :cond_0

    .line 14
    .line 15
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-eqz v1, :cond_0

    .line 20
    .line 21
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-nez v1, :cond_0

    .line 26
    .line 27
    return-void

    .line 28
    :cond_0
    if-eqz v2, :cond_1

    .line 29
    .line 30
    move-object v0, p0

    .line 31
    move-object v1, p1

    .line 32
    move v5, p4

    .line 33
    move v4, p5

    .line 34
    move v6, p6

    .line 35
    move-object v3, p7

    .line 36
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->j(Ljava/util/List;Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;III)V

    .line 37
    .line 38
    .line 39
    return-void

    .line 40
    :cond_1
    move-object v0, p0

    .line 41
    move-object v1, p1

    .line 42
    move v2, p2

    .line 43
    move v3, p3

    .line 44
    move v5, p4

    .line 45
    move v4, p5

    .line 46
    move v6, p6

    .line 47
    move-object v7, p7

    .line 48
    invoke-virtual/range {v0 .. v7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->f(Ljava/util/List;IIIIILL11/a;)V

    .line 49
    .line 50
    .line 51
    return-void
.end method

.method public final i(Ljava/util/List;II)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
            ">;II)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->v()Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 4
    .line 5
    .line 6
    move-result-object v2

    .line 7
    new-instance v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;

    .line 8
    .line 9
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->SHOW_MORE:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 10
    .line 11
    const/4 v4, 0x0

    .line 12
    move v3, p2

    .line 13
    move v5, p3

    .line 14
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;-><init>(Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 15
    .line 16
    .line 17
    invoke-static {v1}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    const/4 v7, 0x1

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v5, 0x0

    .line 24
    move-object v3, p0

    .line 25
    move-object v4, p1

    .line 26
    move-object v9, v6

    .line 27
    move-object v6, p2

    .line 28
    invoke-virtual/range {v3 .. v9}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final j(Ljava/util/List;Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;III)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;",
            "Lorg/xbet/uikit/components/market/view/MarketCoefficient;",
            "LL11/a;",
            "III)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->k(Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;)V

    .line 2
    .line 3
    .line 4
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->MARKET:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 5
    .line 6
    move-object v0, p0

    .line 7
    move-object v1, p1

    .line 8
    move-object v2, p2

    .line 9
    move v3, p4

    .line 10
    move v4, p5

    .line 11
    move v5, p6

    .line 12
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->g(Ljava/util/List;Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final k(Lorg/xbet/uikit/components/market/view/MarketCoefficient;LL11/a;)V
    .locals 1

    .line 1
    invoke-virtual {p2}, LL11/a;->h()Ljava/lang/CharSequence;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;->setDescriptionMarket(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p2}, LL11/a;->a()Ljava/lang/CharSequence;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;->setCoefficientMarket(Ljava/lang/CharSequence;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p2}, LL11/a;->b()Lorg/xbet/uikit/components/market/base/CoefficientState;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;->setCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p2}, LL11/a;->a()Ljava/lang/CharSequence;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p1, v0}, Landroid/view/View;->setContentDescription(Ljava/lang/CharSequence;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p2}, LL11/a;->f()Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;->d(Z)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p2}, LL11/a;->g()Z

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;->e(Z)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p2}, LL11/a;->e()Z

    .line 44
    .line 45
    .line 46
    move-result p2

    .line 47
    invoke-virtual {p1, p2}, Lorg/xbet/uikit/components/market/view/MarketCoefficient;->c(Z)V

    .line 48
    .line 49
    .line 50
    const/4 p2, 0x0

    .line 51
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public final l()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;
    .locals 9

    .line 1
    new-instance v2, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x0

    .line 7
    const/4 v6, 0x0

    .line 8
    :goto_0
    const/4 v1, 0x3

    .line 9
    if-ge v6, v1, :cond_0

    .line 10
    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 12
    .line 13
    invoke-virtual {v1, v6}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->m(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;

    .line 14
    .line 15
    .line 16
    move-result-object v4

    .line 17
    invoke-virtual {v4, v0}, Landroid/view/View;->setVisibility(I)V

    .line 18
    .line 19
    .line 20
    new-instance v3, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;

    .line 21
    .line 22
    const/4 v7, 0x1

    .line 23
    sget-object v8, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->BLOCK:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 24
    .line 25
    const/4 v5, 0x0

    .line 26
    invoke-direct/range {v3 .. v8}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;-><init>(Landroid/view/View;IIILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 27
    .line 28
    .line 29
    invoke-interface {v2, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    add-int/lit8 v6, v6, 0x1

    .line 33
    .line 34
    goto :goto_0

    .line 35
    :cond_0
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    .line 36
    .line 37
    const/4 v4, -0x1

    .line 38
    sget-object v5, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->BLOCK:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 39
    .line 40
    const/4 v1, 0x0

    .line 41
    const/4 v3, 0x3

    .line 42
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;-><init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 43
    .line 44
    .line 45
    new-instance v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 46
    .line 47
    invoke-static {v0}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    sget-object v2, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->MEASURE_ALL:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 52
    .line 53
    const/4 v3, 0x1

    .line 54
    invoke-direct {v1, v0, v2, v3, v3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;-><init>(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)V

    .line 55
    .line 56
    .line 57
    return-object v1
.end method

.method public final m(Lorg/xbet/uikit_sport/eventcard/bottom/b;Z)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;
    .locals 28

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    new-instance v15, Ljava/util/ArrayList;

    .line 4
    .line 5
    invoke-direct {v15}, Ljava/util/ArrayList;-><init>()V

    .line 6
    .line 7
    .line 8
    new-instance v7, Lkotlin/jvm/internal/Ref$BooleanRef;

    .line 9
    .line 10
    invoke-direct {v7}, Lkotlin/jvm/internal/Ref$BooleanRef;-><init>()V

    .line 11
    .line 12
    .line 13
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->UNDEFINED:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 14
    .line 15
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->b()Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 20
    .line 21
    .line 22
    move-result-object v16

    .line 23
    const/16 v17, 0x0

    .line 24
    .line 25
    move-object v8, v1

    .line 26
    const/4 v9, 0x0

    .line 27
    const/4 v10, 0x0

    .line 28
    const/4 v11, 0x0

    .line 29
    :goto_0
    invoke-interface/range {v16 .. v16}, Ljava/util/Iterator;->hasNext()Z

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    if-eqz v1, :cond_e

    .line 34
    .line 35
    invoke-interface/range {v16 .. v16}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    add-int/lit8 v18, v9, 0x1

    .line 40
    .line 41
    if-gez v9, :cond_0

    .line 42
    .line 43
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 44
    .line 45
    .line 46
    :cond_0
    move-object/from16 v19, v1

    .line 47
    .line 48
    check-cast v19, Lorg/xbet/uikit_sport/eventcard/bottom/q;

    .line 49
    .line 50
    add-int v1, v11, v10

    .line 51
    .line 52
    add-int/lit8 v1, v1, 0x1

    .line 53
    .line 54
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->c()I

    .line 55
    .line 56
    .line 57
    move-result v2

    .line 58
    if-lt v1, v2, :cond_2

    .line 59
    .line 60
    if-eqz p2, :cond_1

    .line 61
    .line 62
    iget-boolean v1, v7, Lkotlin/jvm/internal/Ref$BooleanRef;->element:Z

    .line 63
    .line 64
    if-nez v1, :cond_1

    .line 65
    .line 66
    invoke-virtual {v0, v15, v11, v10}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->i(Ljava/util/List;II)V

    .line 67
    .line 68
    .line 69
    add-int/lit8 v11, v11, 0x1

    .line 70
    .line 71
    :cond_1
    :goto_1
    move-object v1, v15

    .line 72
    goto/16 :goto_c

    .line 73
    .line 74
    :cond_2
    iget-object v1, v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->g:Lorg/xbet/uikit_sport/eventcard/bottom/b;

    .line 75
    .line 76
    const/16 v20, 0x0

    .line 77
    .line 78
    if-eqz v1, :cond_3

    .line 79
    .line 80
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->b()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    if-eqz v1, :cond_3

    .line 85
    .line 86
    invoke-static {v1, v9}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    check-cast v1, Lorg/xbet/uikit_sport/eventcard/bottom/q;

    .line 91
    .line 92
    goto :goto_2

    .line 93
    :cond_3
    move-object/from16 v1, v20

    .line 94
    .line 95
    :goto_2
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->a()Lorg/xbet/uikit_sport/eventcard/bottom/a;

    .line 96
    .line 97
    .line 98
    move-result-object v12

    .line 99
    if-eqz v1, :cond_4

    .line 100
    .line 101
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->a()Lorg/xbet/uikit_sport/eventcard/bottom/a;

    .line 102
    .line 103
    .line 104
    move-result-object v2

    .line 105
    move-object v13, v2

    .line 106
    goto :goto_3

    .line 107
    :cond_4
    move-object/from16 v13, v20

    .line 108
    .line 109
    :goto_3
    if-eqz v1, :cond_5

    .line 110
    .line 111
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->b()Ljava/util/List;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    if-eqz v2, :cond_5

    .line 116
    .line 117
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 118
    .line 119
    .line 120
    move-result v3

    .line 121
    invoke-static {v2, v3}, Lkotlin/collections/CollectionsKt;->j0(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 122
    .line 123
    .line 124
    move-result-object v2

    .line 125
    move-object v14, v2

    .line 126
    goto :goto_4

    .line 127
    :cond_5
    move-object/from16 v14, v20

    .line 128
    .line 129
    :goto_4
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->b()Ljava/util/List;

    .line 130
    .line 131
    .line 132
    move-result-object v2

    .line 133
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 134
    .line 135
    .line 136
    move-result v3

    .line 137
    invoke-static {v2, v3}, Lkotlin/collections/CollectionsKt;->j0(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 138
    .line 139
    .line 140
    move-result-object v21

    .line 141
    move-object v2, v1

    .line 142
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->c()I

    .line 143
    .line 144
    .line 145
    move-result v1

    .line 146
    iget-object v3, v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->g:Lorg/xbet/uikit_sport/eventcard/bottom/b;

    .line 147
    .line 148
    if-eqz v3, :cond_6

    .line 149
    .line 150
    invoke-virtual {v3}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->c()I

    .line 151
    .line 152
    .line 153
    move-result v3

    .line 154
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 155
    .line 156
    .line 157
    move-result-object v3

    .line 158
    goto :goto_5

    .line 159
    :cond_6
    move-object/from16 v3, v20

    .line 160
    .line 161
    :goto_5
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 162
    .line 163
    .line 164
    move-result v4

    .line 165
    if-eqz v2, :cond_7

    .line 166
    .line 167
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 168
    .line 169
    .line 170
    move-result v2

    .line 171
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 172
    .line 173
    .line 174
    move-result-object v2

    .line 175
    goto :goto_6

    .line 176
    :cond_7
    move-object/from16 v2, v20

    .line 177
    .line 178
    :goto_6
    invoke-interface/range {v21 .. v21}, Ljava/util/List;->size()I

    .line 179
    .line 180
    .line 181
    move-result v5

    .line 182
    if-eqz v14, :cond_8

    .line 183
    .line 184
    invoke-interface {v14}, Ljava/util/List;->size()I

    .line 185
    .line 186
    .line 187
    move-result v6

    .line 188
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 189
    .line 190
    .line 191
    move-result-object v6

    .line 192
    move/from16 v27, v4

    .line 193
    .line 194
    move-object v4, v2

    .line 195
    move-object v2, v3

    .line 196
    move/from16 v3, v27

    .line 197
    .line 198
    goto :goto_7

    .line 199
    :cond_8
    move v6, v4

    .line 200
    move-object v4, v2

    .line 201
    move-object v2, v3

    .line 202
    move v3, v6

    .line 203
    move-object/from16 v6, v20

    .line 204
    .line 205
    :goto_7
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->u(ILjava/lang/Integer;ILjava/lang/Integer;ILjava/lang/Integer;)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 206
    .line 207
    .line 208
    move-result-object v1

    .line 209
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 210
    .line 211
    .line 212
    move-result v0

    .line 213
    invoke-virtual {v8}, Ljava/lang/Enum;->ordinal()I

    .line 214
    .line 215
    .line 216
    move-result v2

    .line 217
    if-le v0, v2, :cond_9

    .line 218
    .line 219
    move-object v3, v1

    .line 220
    :goto_8
    move-object/from16 v0, p0

    .line 221
    .line 222
    move v5, v10

    .line 223
    move v4, v11

    .line 224
    move-object v1, v12

    .line 225
    move-object v2, v13

    .line 226
    goto :goto_9

    .line 227
    :cond_9
    move-object v3, v8

    .line 228
    goto :goto_8

    .line 229
    :goto_9
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->s(Lorg/xbet/uikit_sport/eventcard/bottom/a;Lorg/xbet/uikit_sport/eventcard/bottom/a;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 230
    .line 231
    .line 232
    move-result-object v22

    .line 233
    move-object v11, v3

    .line 234
    add-int/lit8 v8, v5, 0x1

    .line 235
    .line 236
    new-instance v3, Ljava/util/ArrayList;

    .line 237
    .line 238
    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 239
    .line 240
    .line 241
    new-instance v0, Lkotlin/jvm/internal/Ref$IntRef;

    .line 242
    .line 243
    invoke-direct {v0}, Lkotlin/jvm/internal/Ref$IntRef;-><init>()V

    .line 244
    .line 245
    .line 246
    invoke-interface/range {v21 .. v21}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 247
    .line 248
    .line 249
    move-result-object v21

    .line 250
    const/4 v6, 0x0

    .line 251
    :goto_a
    invoke-interface/range {v21 .. v21}, Ljava/util/Iterator;->hasNext()Z

    .line 252
    .line 253
    .line 254
    move-result v1

    .line 255
    if-eqz v1, :cond_d

    .line 256
    .line 257
    invoke-interface/range {v21 .. v21}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 258
    .line 259
    .line 260
    move-result-object v1

    .line 261
    add-int/lit8 v23, v6, 0x1

    .line 262
    .line 263
    if-gez v6, :cond_a

    .line 264
    .line 265
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 266
    .line 267
    .line 268
    :cond_a
    move-object v2, v1

    .line 269
    check-cast v2, Ljava/util/List;

    .line 270
    .line 271
    if-eqz v14, :cond_b

    .line 272
    .line 273
    invoke-static {v14, v6}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 274
    .line 275
    .line 276
    move-result-object v1

    .line 277
    check-cast v1, Ljava/util/List;

    .line 278
    .line 279
    goto :goto_b

    .line 280
    :cond_b
    move-object/from16 v1, v20

    .line 281
    .line 282
    :goto_b
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 283
    .line 284
    .line 285
    move-result v5

    .line 286
    move v10, v5

    .line 287
    move v5, v9

    .line 288
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->c()I

    .line 289
    .line 290
    .line 291
    move-result v9

    .line 292
    new-instance v12, Lorg/xbet/uikit_sport/eventcard/bottom/i;

    .line 293
    .line 294
    invoke-direct {v12, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/i;-><init>(Lkotlin/jvm/internal/Ref$IntRef;)V

    .line 295
    .line 296
    .line 297
    new-instance v13, Lorg/xbet/uikit_sport/eventcard/bottom/j;

    .line 298
    .line 299
    invoke-direct {v13, v7}, Lorg/xbet/uikit_sport/eventcard/bottom/j;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;)V

    .line 300
    .line 301
    .line 302
    move-object/from16 v24, v14

    .line 303
    .line 304
    new-instance v14, Lorg/xbet/uikit_sport/eventcard/bottom/k;

    .line 305
    .line 306
    invoke-direct {v14, v7, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/k;-><init>(Lkotlin/jvm/internal/Ref$BooleanRef;Lkotlin/jvm/internal/Ref$IntRef;)V

    .line 307
    .line 308
    .line 309
    move-object/from16 v25, v3

    .line 310
    .line 311
    move-object v3, v1

    .line 312
    move-object/from16 v1, v25

    .line 313
    .line 314
    move-object/from16 v26, v15

    .line 315
    .line 316
    move-object/from16 v25, v24

    .line 317
    .line 318
    move-object v15, v0

    .line 319
    move-object/from16 v24, v7

    .line 320
    .line 321
    move v7, v10

    .line 322
    move-object/from16 v0, p0

    .line 323
    .line 324
    move/from16 v10, p2

    .line 325
    .line 326
    invoke-virtual/range {v0 .. v14}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->e(Ljava/util/List;Ljava/util/List;Ljava/util/List;IIIIIIZLorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V

    .line 327
    .line 328
    .line 329
    move-object v3, v1

    .line 330
    move v7, v4

    .line 331
    add-int/lit8 v7, v7, 0x1

    .line 332
    .line 333
    add-int v0, v7, v8

    .line 334
    .line 335
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->c()I

    .line 336
    .line 337
    .line 338
    move-result v1

    .line 339
    if-lt v0, v1, :cond_c

    .line 340
    .line 341
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 342
    .line 343
    .line 344
    move-result v4

    .line 345
    iget v0, v15, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 346
    .line 347
    add-int/lit8 v5, v0, -0x1

    .line 348
    .line 349
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->MARKET:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 350
    .line 351
    move-object/from16 v0, p0

    .line 352
    .line 353
    move-object/from16 v2, v22

    .line 354
    .line 355
    move-object/from16 v1, v26

    .line 356
    .line 357
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 358
    .line 359
    .line 360
    move v10, v8

    .line 361
    move-object v8, v11

    .line 362
    move v11, v7

    .line 363
    goto :goto_c

    .line 364
    :cond_c
    move v9, v5

    .line 365
    move v4, v7

    .line 366
    move-object v0, v15

    .line 367
    move/from16 v6, v23

    .line 368
    .line 369
    move-object/from16 v7, v24

    .line 370
    .line 371
    move-object/from16 v14, v25

    .line 372
    .line 373
    move-object/from16 v15, v26

    .line 374
    .line 375
    goto :goto_a

    .line 376
    :cond_d
    move-object/from16 v24, v7

    .line 377
    .line 378
    move-object v1, v15

    .line 379
    move-object/from16 v2, v22

    .line 380
    .line 381
    move-object v15, v0

    .line 382
    move v7, v4

    .line 383
    invoke-virtual/range {v19 .. v19}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 384
    .line 385
    .line 386
    move-result v4

    .line 387
    iget v0, v15, Lkotlin/jvm/internal/Ref$IntRef;->element:I

    .line 388
    .line 389
    add-int/lit8 v5, v0, -0x1

    .line 390
    .line 391
    sget-object v6, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->MARKET:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 392
    .line 393
    move-object/from16 v0, p0

    .line 394
    .line 395
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V

    .line 396
    .line 397
    .line 398
    move-object v15, v1

    .line 399
    move v10, v8

    .line 400
    move-object v8, v11

    .line 401
    move/from16 v9, v18

    .line 402
    .line 403
    move v11, v7

    .line 404
    move-object/from16 v7, v24

    .line 405
    .line 406
    goto/16 :goto_0

    .line 407
    .line 408
    :cond_e
    move v5, v10

    .line 409
    move v4, v11

    .line 410
    goto/16 :goto_1

    .line 411
    .line 412
    :goto_c
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 413
    .line 414
    invoke-direct {v0, v1, v8, v11, v10}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;-><init>(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)V

    .line 415
    .line 416
    .line 417
    return-object v0
.end method

.method public onMeasure(II)V
    .locals 5

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->h:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 6
    .line 7
    if-eqz v0, :cond_1

    .line 8
    .line 9
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    if-eqz v1, :cond_1

    .line 24
    .line 25
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    check-cast v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    .line 30
    .line 31
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    if-eqz v2, :cond_0

    .line 36
    .line 37
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;->d()Landroid/view/View;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    if-eqz v2, :cond_0

    .line 42
    .line 43
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 44
    .line 45
    .line 46
    move-result-object v3

    .line 47
    invoke-virtual {v3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;->b()I

    .line 48
    .line 49
    .line 50
    move-result v3

    .line 51
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 52
    .line 53
    .line 54
    move-result-object v4

    .line 55
    invoke-virtual {v4}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;->c()I

    .line 56
    .line 57
    .line 58
    move-result v4

    .line 59
    invoke-virtual {p0, v2, v3, v4, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->x(Landroid/view/View;III)V

    .line 60
    .line 61
    .line 62
    :cond_0
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d()Ljava/util/List;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c()I

    .line 67
    .line 68
    .line 69
    move-result v1

    .line 70
    invoke-virtual {p0, v2, v1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->w(Ljava/util/List;II)V

    .line 71
    .line 72
    .line 73
    goto :goto_0

    .line 74
    :cond_1
    iget p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->b:I

    .line 75
    .line 76
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->h:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 77
    .line 78
    const/4 v1, 0x0

    .line 79
    if-eqz v0, :cond_2

    .line 80
    .line 81
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d()I

    .line 82
    .line 83
    .line 84
    move-result v0

    .line 85
    goto :goto_1

    .line 86
    :cond_2
    const/4 v0, 0x0

    .line 87
    :goto_1
    mul-int p1, p1, v0

    .line 88
    .line 89
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->c:I

    .line 90
    .line 91
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d:I

    .line 92
    .line 93
    add-int/2addr v0, v2

    .line 94
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->h:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 95
    .line 96
    if-eqz v2, :cond_3

    .line 97
    .line 98
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a()I

    .line 99
    .line 100
    .line 101
    move-result v1

    .line 102
    :cond_3
    mul-int v0, v0, v1

    .line 103
    .line 104
    add-int/2addr p1, v0

    .line 105
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 106
    .line 107
    .line 108
    return-void
.end method

.method public final q(Lorg/xbet/uikit_sport/eventcard/bottom/b;)I
    .locals 4

    .line 1
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->b()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const/4 v1, 0x0

    .line 10
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    if-eqz v2, :cond_0

    .line 15
    .line 16
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    check-cast v2, Lorg/xbet/uikit_sport/eventcard/bottom/q;

    .line 21
    .line 22
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->b()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v3

    .line 26
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/eventcard/bottom/q;->c()I

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    invoke-static {v3, v2}, Lkotlin/collections/CollectionsKt;->j0(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v2

    .line 34
    invoke-interface {v2}, Ljava/util/List;->size()I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    add-int/2addr v1, v2

    .line 39
    goto :goto_0

    .line 40
    :cond_0
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->b()Ljava/util/List;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    add-int/2addr v1, p1

    .line 49
    return v1
.end method

.method public final r(Lorg/xbet/uikit_sport/eventcard/bottom/a;I)Landroid/view/View;
    .locals 3

    .line 1
    instance-of v0, p1, Lorg/xbet/uikit_sport/eventcard/bottom/a$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    move-object v0, p1

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/eventcard/bottom/a$b;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    move-object v0, v1

    .line 11
    :goto_0
    const/4 v2, 0x0

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 15
    .line 16
    invoke-virtual {v0, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->p(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    invoke-virtual {p2, v2}, Landroid/view/View;->setVisibility(I)V

    .line 21
    .line 22
    .line 23
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/a$b;

    .line 24
    .line 25
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/a$b;->b()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/a$b;->a()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p2, v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->setHeader(Ljava/lang/String;Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    return-object p2

    .line 37
    :cond_1
    instance-of v0, p1, Lorg/xbet/uikit_sport/eventcard/bottom/a$a;

    .line 38
    .line 39
    if-eqz v0, :cond_2

    .line 40
    .line 41
    move-object v0, p1

    .line 42
    check-cast v0, Lorg/xbet/uikit_sport/eventcard/bottom/a$a;

    .line 43
    .line 44
    goto :goto_1

    .line 45
    :cond_2
    move-object v0, v1

    .line 46
    :goto_1
    if-eqz v0, :cond_3

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 49
    .line 50
    invoke-virtual {v0, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->n(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-virtual {p2, v2}, Landroid/view/View;->setVisibility(I)V

    .line 55
    .line 56
    .line 57
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/a$a;

    .line 58
    .line 59
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/a$a;->b()Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/a$a;->a()Z

    .line 64
    .line 65
    .line 66
    move-result p1

    .line 67
    invoke-virtual {p2, v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->setHeader(Ljava/lang/String;Z)V

    .line 68
    .line 69
    .line 70
    return-object p2

    .line 71
    :cond_3
    return-object v1
.end method

.method public final s(Lorg/xbet/uikit_sport/eventcard/bottom/a;Lorg/xbet/uikit_sport/eventcard/bottom/a;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;
    .locals 0

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p2

    .line 5
    if-eqz p2, :cond_1

    .line 6
    .line 7
    sget-object p2, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->UNDEFINED:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 8
    .line 9
    if-eq p3, p2, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 p2, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    invoke-virtual {p0, p1, p5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->r(Lorg/xbet/uikit_sport/eventcard/bottom/a;I)Landroid/view/View;

    .line 15
    .line 16
    .line 17
    move-result-object p2

    .line 18
    :goto_1
    instance-of p1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/a$a;

    .line 19
    .line 20
    new-instance p3, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 21
    .line 22
    invoke-direct {p3, p2, p4, p5, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;-><init>(Landroid/view/View;IIZ)V

    .line 23
    .line 24
    .line 25
    return-object p3
.end method

.method public final setExpandedListener(LOc/n;)V
    .locals 1
    .param p1    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Landroid/view/View;",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->z(LOc/n;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setMarkets(Lorg/xbet/uikit_sport/eventcard/bottom/b;)V
    .locals 3
    .param p1    # Lorg/xbet/uikit_sport/eventcard/bottom/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->q(Lorg/xbet/uikit_sport/eventcard/bottom/b;)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->c()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-le v0, v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->d()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    const/4 v0, 0x1

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    const/4 v0, 0x0

    .line 20
    :goto_0
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->t(Lorg/xbet/uikit_sport/eventcard/bottom/b;Z)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b()Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v2

    .line 28
    invoke-virtual {p0, v2, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->z(Ljava/util/List;Z)V

    .line 29
    .line 30
    .line 31
    iput-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->h:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 32
    .line 33
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->g:Lorg/xbet/uikit_sport/eventcard/bottom/b;

    .line 34
    .line 35
    invoke-virtual {v1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->UNDEFINED:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 40
    .line 41
    if-eq p1, v0, :cond_1

    .line 42
    .line 43
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 44
    .line 45
    .line 46
    :cond_1
    return-void
.end method

.method public final setOnMarketClickListeners(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ljava/lang/Integer;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->A(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setShowMoreButtonListener(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->B(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final t(Lorg/xbet/uikit_sport/eventcard/bottom/b;Z)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;
    .locals 1

    .line 1
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->b()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/b;->a()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->l()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    return-object p1

    .line 22
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->v()V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->m(Lorg/xbet/uikit_sport/eventcard/bottom/b;Z)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    return-object p1
.end method

.method public final u(ILjava/lang/Integer;ILjava/lang/Integer;ILjava/lang/Integer;)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;
    .locals 0

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    goto :goto_0

    .line 4
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    .line 5
    .line 6
    .line 7
    move-result p2

    .line 8
    if-ne p1, p2, :cond_4

    .line 9
    .line 10
    if-nez p4, :cond_1

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_1
    invoke-virtual {p4}, Ljava/lang/Integer;->intValue()I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    if-ne p3, p1, :cond_4

    .line 18
    .line 19
    if-nez p6, :cond_2

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_2
    invoke-virtual {p6}, Ljava/lang/Integer;->intValue()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    if-eq p5, p1, :cond_3

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_3
    sget-object p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->MEASURE_NEW_DATA:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 30
    .line 31
    return-object p1

    .line 32
    :cond_4
    :goto_0
    sget-object p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;->MEASURE_ALL:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 33
    .line 34
    return-object p1
.end method

.method public final v()V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    :goto_0
    const/4 v1, 0x3

    .line 3
    if-ge v0, v1, :cond_1

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 6
    .line 7
    invoke-virtual {v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->q(I)Lorg/xbet/uikit/components/market/view/MarketBlocked;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    const/16 v2, 0x8

    .line 14
    .line 15
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    :cond_0
    add-int/lit8 v0, v0, 0x1

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_1
    return-void
.end method

.method public final w(Ljava/util/List;II)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;II)V"
        }
    .end annotation

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->e:I

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->f:I

    .line 4
    .line 5
    add-int/lit8 v2, p2, -0x1

    .line 6
    .line 7
    mul-int v1, v1, v2

    .line 8
    .line 9
    add-int/2addr v0, v1

    .line 10
    sub-int/2addr p3, v0

    .line 11
    div-int/2addr p3, p2

    .line 12
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;

    .line 27
    .line 28
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->e()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    sget-object v2, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->SHOW_MORE:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 33
    .line 34
    if-ne v1, v2, :cond_0

    .line 35
    .line 36
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d()I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    sub-int v1, p2, v1

    .line 41
    .line 42
    mul-int v1, v1, p3

    .line 43
    .line 44
    move v6, v1

    .line 45
    goto :goto_1

    .line 46
    :cond_0
    move v6, p3

    .line 47
    :goto_1
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->c()Landroid/view/View;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->d()I

    .line 52
    .line 53
    .line 54
    move-result v4

    .line 55
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->b()I

    .line 56
    .line 57
    .line 58
    move-result v5

    .line 59
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;->a()I

    .line 60
    .line 61
    .line 62
    move-result v7

    .line 63
    move-object v2, p0

    .line 64
    invoke-virtual/range {v2 .. v7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->y(Landroid/view/View;IIII)V

    .line 65
    .line 66
    .line 67
    goto :goto_0

    .line 68
    :cond_1
    return-void
.end method

.method public final x(Landroid/view/View;III)V
    .locals 3

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    check-cast v0, Landroid/widget/FrameLayout$LayoutParams;

    .line 8
    .line 9
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->c:I

    .line 10
    .line 11
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d:I

    .line 12
    .line 13
    add-int/2addr v1, v2

    .line 14
    mul-int v1, v1, p2

    .line 15
    .line 16
    iget p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->b:I

    .line 17
    .line 18
    mul-int p3, p3, p2

    .line 19
    .line 20
    add-int/2addr v1, p3

    .line 21
    iput v1, v0, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 22
    .line 23
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 24
    .line 25
    .line 26
    iget p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->c:I

    .line 27
    .line 28
    const/high16 p3, 0x40000000    # 2.0f

    .line 29
    .line 30
    invoke-static {p2, p3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result p2

    .line 34
    invoke-virtual {p1, p4, p2}, Landroid/view/View;->measure(II)V

    .line 35
    .line 36
    .line 37
    return-void

    .line 38
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    .line 39
    .line 40
    const-string p2, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 41
    .line 42
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 43
    .line 44
    .line 45
    throw p1
.end method

.method public final y(Landroid/view/View;IIII)V
    .locals 3

    .line 1
    :try_start_0
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    check-cast v0, Landroid/widget/FrameLayout$LayoutParams;

    .line 8
    .line 9
    const/4 v1, 0x3

    .line 10
    iput v1, v0, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 11
    .line 12
    mul-int v1, p2, p4

    .line 13
    .line 14
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->f:I

    .line 15
    .line 16
    add-int/lit8 p2, p2, 0x1

    .line 17
    .line 18
    mul-int v2, v2, p2

    .line 19
    .line 20
    add-int/2addr v1, v2

    .line 21
    iput v1, v0, Landroid/widget/FrameLayout$LayoutParams;->leftMargin:I

    .line 22
    .line 23
    iget p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->b:I

    .line 24
    .line 25
    mul-int p3, p3, p2

    .line 26
    .line 27
    iget p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->c:I

    .line 28
    .line 29
    mul-int p2, p2, p5

    .line 30
    .line 31
    add-int/2addr p3, p2

    .line 32
    iget p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->d:I

    .line 33
    .line 34
    add-int/lit8 p5, p5, -0x1

    .line 35
    .line 36
    mul-int p2, p2, p5

    .line 37
    .line 38
    add-int/2addr p3, p2

    .line 39
    iput p3, v0, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 40
    .line 41
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 42
    .line 43
    .line 44
    const/high16 p2, 0x40000000    # 2.0f

    .line 45
    .line 46
    invoke-static {p4, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 47
    .line 48
    .line 49
    move-result p3

    .line 50
    iget p4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->b:I

    .line 51
    .line 52
    invoke-static {p4, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 53
    .line 54
    .line 55
    move-result p2

    .line 56
    invoke-virtual {p1, p3, p2}, Landroid/view/View;->measure(II)V

    .line 57
    .line 58
    .line 59
    return-void

    .line 60
    :catch_0
    move-exception p1

    .line 61
    goto :goto_0

    .line 62
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    .line 63
    .line 64
    const-string p2, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 65
    .line 66
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 67
    .line 68
    .line 69
    throw p1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 70
    :goto_0
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 71
    .line 72
    .line 73
    return-void
.end method

.method public final z(Ljava/util/List;Z)V
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    sub-int/2addr v0, v1

    .line 7
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 8
    .line 9
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->t()Ljava/util/Collection;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    invoke-interface {v2}, Ljava/util/Collection;->size()I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 18
    .line 19
    invoke-virtual {v3}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->y()Ljava/util/Map;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    invoke-interface {v3}, Ljava/util/Map;->size()I

    .line 24
    .line 25
    .line 26
    move-result v3

    .line 27
    invoke-static {v2, v3}, Ljava/lang/Math;->max(II)I

    .line 28
    .line 29
    .line 30
    move-result v2

    .line 31
    const/16 v3, 0x8

    .line 32
    .line 33
    const/4 v4, 0x0

    .line 34
    if-ltz v2, :cond_13

    .line 35
    .line 36
    const/4 v5, 0x0

    .line 37
    :goto_0
    iget-object v6, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 38
    .line 39
    invoke-virtual {v6, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->s(I)Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object v6

    .line 43
    invoke-static {p1, v5}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v7

    .line 47
    check-cast v7, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    .line 48
    .line 49
    if-eqz v7, :cond_0

    .line 50
    .line 51
    invoke-virtual {v7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b()I

    .line 52
    .line 53
    .line 54
    move-result v8

    .line 55
    goto :goto_1

    .line 56
    :cond_0
    const/4 v8, -0x1

    .line 57
    :goto_1
    invoke-static {p1, v5}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object v9

    .line 61
    check-cast v9, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    .line 62
    .line 63
    const/4 v10, 0x0

    .line 64
    if-eqz v9, :cond_1

    .line 65
    .line 66
    invoke-virtual {v9}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 67
    .line 68
    .line 69
    move-result-object v9

    .line 70
    goto :goto_2

    .line 71
    :cond_1
    move-object v9, v10

    .line 72
    :goto_2
    sget-object v11, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->BLOCK:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 73
    .line 74
    if-eq v9, v11, :cond_2

    .line 75
    .line 76
    sget-object v11, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;->SHOW_MORE:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 77
    .line 78
    if-eq v9, v11, :cond_2

    .line 79
    .line 80
    const/4 v9, 0x1

    .line 81
    goto :goto_3

    .line 82
    :cond_2
    const/4 v9, 0x0

    .line 83
    :goto_3
    if-gt v5, v0, :cond_3

    .line 84
    .line 85
    if-eqz v9, :cond_3

    .line 86
    .line 87
    const/4 v9, 0x1

    .line 88
    goto :goto_4

    .line 89
    :cond_3
    const/4 v9, 0x0

    .line 90
    :goto_4
    if-eqz v7, :cond_4

    .line 91
    .line 92
    invoke-virtual {v7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 93
    .line 94
    .line 95
    move-result-object v7

    .line 96
    if-eqz v7, :cond_4

    .line 97
    .line 98
    invoke-virtual {v7}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;->a()Z

    .line 99
    .line 100
    .line 101
    move-result v7

    .line 102
    invoke-static {v7}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 103
    .line 104
    .line 105
    move-result-object v10

    .line 106
    :cond_4
    sget-object v7, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 107
    .line 108
    invoke-static {v10, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 109
    .line 110
    .line 111
    move-result v7

    .line 112
    if-eqz v7, :cond_7

    .line 113
    .line 114
    iget-object v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 115
    .line 116
    invoke-virtual {v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->r(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 117
    .line 118
    .line 119
    move-result-object v7

    .line 120
    if-eqz v7, :cond_6

    .line 121
    .line 122
    if-eqz v9, :cond_5

    .line 123
    .line 124
    const/4 v10, 0x0

    .line 125
    goto :goto_5

    .line 126
    :cond_5
    const/16 v10, 0x8

    .line 127
    .line 128
    :goto_5
    invoke-virtual {v7, v10}, Landroid/view/View;->setVisibility(I)V

    .line 129
    .line 130
    .line 131
    :cond_6
    iget-object v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 132
    .line 133
    invoke-virtual {v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->x(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 134
    .line 135
    .line 136
    move-result-object v7

    .line 137
    if-eqz v7, :cond_e

    .line 138
    .line 139
    invoke-virtual {v7, v3}, Landroid/view/View;->setVisibility(I)V

    .line 140
    .line 141
    .line 142
    goto :goto_9

    .line 143
    :cond_7
    sget-object v7, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 144
    .line 145
    invoke-static {v10, v7}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 146
    .line 147
    .line 148
    move-result v7

    .line 149
    if-eqz v7, :cond_a

    .line 150
    .line 151
    iget-object v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 152
    .line 153
    invoke-virtual {v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->r(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 154
    .line 155
    .line 156
    move-result-object v7

    .line 157
    if-eqz v7, :cond_8

    .line 158
    .line 159
    invoke-virtual {v7, v3}, Landroid/view/View;->setVisibility(I)V

    .line 160
    .line 161
    .line 162
    :cond_8
    iget-object v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 163
    .line 164
    invoke-virtual {v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->x(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 165
    .line 166
    .line 167
    move-result-object v7

    .line 168
    if-eqz v7, :cond_e

    .line 169
    .line 170
    if-eqz v9, :cond_9

    .line 171
    .line 172
    const/4 v10, 0x0

    .line 173
    goto :goto_6

    .line 174
    :cond_9
    const/16 v10, 0x8

    .line 175
    .line 176
    :goto_6
    invoke-virtual {v7, v10}, Landroid/view/View;->setVisibility(I)V

    .line 177
    .line 178
    .line 179
    goto :goto_9

    .line 180
    :cond_a
    iget-object v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 181
    .line 182
    invoke-virtual {v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->x(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;

    .line 183
    .line 184
    .line 185
    move-result-object v7

    .line 186
    if-eqz v7, :cond_c

    .line 187
    .line 188
    if-eqz v9, :cond_b

    .line 189
    .line 190
    const/4 v10, 0x0

    .line 191
    goto :goto_7

    .line 192
    :cond_b
    const/16 v10, 0x8

    .line 193
    .line 194
    :goto_7
    invoke-virtual {v7, v10}, Landroid/view/View;->setVisibility(I)V

    .line 195
    .line 196
    .line 197
    :cond_c
    iget-object v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 198
    .line 199
    invoke-virtual {v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->r(I)Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    .line 200
    .line 201
    .line 202
    move-result-object v7

    .line 203
    if-eqz v7, :cond_e

    .line 204
    .line 205
    if-eqz v9, :cond_d

    .line 206
    .line 207
    const/4 v10, 0x0

    .line 208
    goto :goto_8

    .line 209
    :cond_d
    const/16 v10, 0x8

    .line 210
    .line 211
    :goto_8
    invoke-virtual {v7, v10}, Landroid/view/View;->setVisibility(I)V

    .line 212
    .line 213
    .line 214
    :cond_e
    :goto_9
    if-eqz v6, :cond_12

    .line 215
    .line 216
    invoke-interface {v6}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 217
    .line 218
    .line 219
    move-result-object v6

    .line 220
    const/4 v7, 0x0

    .line 221
    :goto_a
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    .line 222
    .line 223
    .line 224
    move-result v10

    .line 225
    if-eqz v10, :cond_12

    .line 226
    .line 227
    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 228
    .line 229
    .line 230
    move-result-object v10

    .line 231
    add-int/lit8 v11, v7, 0x1

    .line 232
    .line 233
    if-gez v7, :cond_f

    .line 234
    .line 235
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 236
    .line 237
    .line 238
    :cond_f
    check-cast v10, Lorg/xbet/uikit/components/market/view/MarketCoefficient;

    .line 239
    .line 240
    if-eqz v9, :cond_10

    .line 241
    .line 242
    if-gt v7, v8, :cond_10

    .line 243
    .line 244
    const/4 v7, 0x1

    .line 245
    goto :goto_b

    .line 246
    :cond_10
    const/4 v7, 0x0

    .line 247
    :goto_b
    if-eqz v7, :cond_11

    .line 248
    .line 249
    const/4 v7, 0x0

    .line 250
    goto :goto_c

    .line 251
    :cond_11
    const/16 v7, 0x8

    .line 252
    .line 253
    :goto_c
    invoke-virtual {v10, v7}, Landroid/view/View;->setVisibility(I)V

    .line 254
    .line 255
    .line 256
    move v7, v11

    .line 257
    goto :goto_a

    .line 258
    :cond_12
    if-eq v5, v2, :cond_13

    .line 259
    .line 260
    add-int/lit8 v5, v5, 0x1

    .line 261
    .line 262
    goto/16 :goto_0

    .line 263
    .line 264
    :cond_13
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    .line 265
    .line 266
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->v()Lorg/xbet/uikit/components/market/view/MarketShowMoreButton;

    .line 267
    .line 268
    .line 269
    move-result-object p1

    .line 270
    if-eqz p2, :cond_14

    .line 271
    .line 272
    const/4 v3, 0x0

    .line 273
    :cond_14
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 274
    .line 275
    .line 276
    return-void
.end method
