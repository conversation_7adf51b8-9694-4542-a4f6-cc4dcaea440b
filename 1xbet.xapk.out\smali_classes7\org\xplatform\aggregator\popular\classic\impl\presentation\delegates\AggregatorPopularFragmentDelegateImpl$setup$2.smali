.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.delegates.AggregatorPopularFragmentDelegateImpl$setup$2"
    f = "AggregatorPopularFragmentDelegateImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lq81/c;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lq81/c;",
        "action",
        "",
        "<anonymous>",
        "(Lq81/c;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $aggregatorPopularViewModel:LAb1/a;

.field final synthetic $fragment:Landroidx/fragment/app/Fragment;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;LAb1/a;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;",
            "Landroidx/fragment/app/Fragment;",
            "LAb1/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$aggregatorPopularViewModel:LAb1/a;

    .line 6
    .line 7
    const/4 p1, 0x2

    .line 8
    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$aggregatorPopularViewModel:LAb1/a;

    invoke-direct {v0, v1, v2, v3, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;LAb1/a;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lq81/c;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->invoke(Lq81/c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lq81/c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lq81/c;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_5

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lq81/c;

    .line 14
    .line 15
    instance-of v0, p1, Lq81/c$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 22
    .line 23
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    instance-of v0, p1, Lq81/c$b;

    .line 28
    .line 29
    if-eqz v0, :cond_1

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 32
    .line 33
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 34
    .line 35
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$aggregatorPopularViewModel:LAb1/a;

    .line 36
    .line 37
    check-cast p1, Lq81/c$b;

    .line 38
    .line 39
    invoke-virtual {p1}, Lq81/c$b;->a()Lorg/xplatform/aggregator/api/model/Game;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-static {v0, v1, v2, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->f(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 44
    .line 45
    .line 46
    goto :goto_0

    .line 47
    :cond_1
    instance-of v0, p1, Lq81/c$c;

    .line 48
    .line 49
    if-eqz v0, :cond_2

    .line 50
    .line 51
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 52
    .line 53
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 54
    .line 55
    check-cast p1, Lq81/c$c;

    .line 56
    .line 57
    invoke-virtual {p1}, Lq81/c$c;->a()Lorg/xplatform/aggregator/api/model/Game;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$aggregatorPopularViewModel:LAb1/a;

    .line 62
    .line 63
    invoke-static {v0, v1, p1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V

    .line 64
    .line 65
    .line 66
    goto :goto_0

    .line 67
    :cond_2
    instance-of v0, p1, Lq81/c$d;

    .line 68
    .line 69
    if-eqz v0, :cond_3

    .line 70
    .line 71
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 72
    .line 73
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 74
    .line 75
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;)V

    .line 76
    .line 77
    .line 78
    goto :goto_0

    .line 79
    :cond_3
    instance-of v0, p1, Lq81/c$e;

    .line 80
    .line 81
    if-eqz v0, :cond_4

    .line 82
    .line 83
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 84
    .line 85
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$2;->$fragment:Landroidx/fragment/app/Fragment;

    .line 86
    .line 87
    check-cast p1, Lq81/c$e;

    .line 88
    .line 89
    invoke-virtual {p1}, Lq81/c$e;->a()Lorg/xplatform/aggregator/api/model/Game;

    .line 90
    .line 91
    .line 92
    move-result-object p1

    .line 93
    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 94
    .line 95
    .line 96
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 97
    .line 98
    return-object p1

    .line 99
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 100
    .line 101
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 102
    .line 103
    .line 104
    throw p1

    .line 105
    :cond_5
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 106
    .line 107
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 108
    .line 109
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 110
    .line 111
    .line 112
    throw p1
.end method
