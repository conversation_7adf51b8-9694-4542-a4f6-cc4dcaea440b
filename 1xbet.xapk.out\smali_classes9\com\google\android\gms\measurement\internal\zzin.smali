.class final Lcom/google/android/gms/measurement/internal/zzin;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lcom/google/android/gms/measurement/internal/zzke;

.field public final synthetic b:Lcom/google/android/gms/measurement/internal/zzio;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/measurement/internal/zzio;Lcom/google/android/gms/measurement/internal/zzke;)V
    .locals 0

    iput-object p2, p0, Lcom/google/android/gms/measurement/internal/zzin;->a:Lcom/google/android/gms/measurement/internal/zzke;

    iput-object p1, p0, Lcom/google/android/gms/measurement/internal/zzin;->b:Lcom/google/android/gms/measurement/internal/zzio;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/google/android/gms/measurement/internal/zzin;->b:Lcom/google/android/gms/measurement/internal/zzio;

    .line 2
    .line 3
    iget-object v1, p0, Lcom/google/android/gms/measurement/internal/zzin;->a:Lcom/google/android/gms/measurement/internal/zzke;

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/google/android/gms/measurement/internal/zzio;->h(Lcom/google/android/gms/measurement/internal/zzio;Lcom/google/android/gms/measurement/internal/zzke;)V

    .line 6
    .line 7
    .line 8
    iget-object v1, v1, Lcom/google/android/gms/measurement/internal/zzke;->g:Lcom/google/android/gms/internal/measurement/zzdh;

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Lcom/google/android/gms/measurement/internal/zzio;->m(Lcom/google/android/gms/internal/measurement/zzdh;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method
