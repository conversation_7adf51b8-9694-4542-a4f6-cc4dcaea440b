.class public final Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LU31/a;
.implements LU31/j;
.implements LU31/h;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00a2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0010\u0007\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0007\u0018\u0000 \u008a\u00012\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u0004:\u0001MB\'\u0008\u0007\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\n\u0008\u0002\u0010\u0008\u001a\u0004\u0018\u00010\u0007\u0012\u0008\u0008\u0002\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001f\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u001a\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0011\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0016\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u001f\u0010 \u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\tH\u0014\u00a2\u0006\u0004\u0008 \u0010\u0015J7\u0010\'\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020!2\u0006\u0010#\u001a\u00020\t2\u0006\u0010$\u001a\u00020\t2\u0006\u0010%\u001a\u00020\t2\u0006\u0010&\u001a\u00020\tH\u0014\u00a2\u0006\u0004\u0008\'\u0010(J\u0017\u0010+\u001a\u00020\u000f2\u0006\u0010*\u001a\u00020)H\u0014\u00a2\u0006\u0004\u0008+\u0010,J\u0019\u0010/\u001a\u00020\u000f2\u0008\u0010.\u001a\u0004\u0018\u00010-H\u0016\u00a2\u0006\u0004\u0008/\u00100J\u0019\u00102\u001a\u00020\u000f2\u0008\u00101\u001a\u0004\u0018\u00010-H\u0016\u00a2\u0006\u0004\u00082\u00100J\u0019\u00104\u001a\u00020\u000f2\u0008\u00103\u001a\u0004\u0018\u00010-H\u0016\u00a2\u0006\u0004\u00084\u00100J\u0019\u00106\u001a\u00020\u000f2\u0008\u0008\u0001\u00105\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u00086\u0010\u001cJ\u0019\u00108\u001a\u00020\u000f2\u0008\u00107\u001a\u0004\u0018\u00010-H\u0016\u00a2\u0006\u0004\u00088\u00100J\u001b\u0010:\u001a\u00020\u000f2\n\u0008\u0001\u00109\u001a\u0004\u0018\u00010\tH\u0016\u00a2\u0006\u0004\u0008:\u0010;J\u0019\u0010=\u001a\u00020\u000f2\u0008\u0010<\u001a\u0004\u0018\u00010-H\u0016\u00a2\u0006\u0004\u0008=\u00100J!\u0010?\u001a\u00020\u000f2\u0008\u0010>\u001a\u0004\u0018\u00010-2\u0006\u0010\u000e\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008?\u0010@J\u0019\u0010B\u001a\u00020\u000f2\u0008\u0010A\u001a\u0004\u0018\u00010-H\u0016\u00a2\u0006\u0004\u0008B\u00100J\u0017\u0010E\u001a\u00020\u000f2\u0006\u0010D\u001a\u00020CH\u0016\u00a2\u0006\u0004\u0008E\u0010FJ%\u0010J\u001a\u00020\u000f2\u0014\u0010I\u001a\u0010\u0012\u0004\u0012\u00020H\u0012\u0004\u0012\u00020\u000f\u0018\u00010GH\u0016\u00a2\u0006\u0004\u0008J\u0010KJ%\u0010L\u001a\u00020\u000f2\u0014\u0010I\u001a\u0010\u0012\u0004\u0012\u00020H\u0012\u0004\u0012\u00020\u000f\u0018\u00010GH\u0016\u00a2\u0006\u0004\u0008L\u0010KJ\u000f\u0010M\u001a\u00020\u000fH\u0016\u00a2\u0006\u0004\u0008M\u0010\u0019J\u0017\u0010/\u001a\u00020\u000f2\u0008\u0008\u0001\u0010.\u001a\u00020\t\u00a2\u0006\u0004\u0008/\u0010\u001cJ\u0015\u00106\u001a\u00020\u000f2\u0006\u0010N\u001a\u00020\u001d\u00a2\u0006\u0004\u00086\u0010OJ\u0017\u00108\u001a\u00020\u000f2\u0008\u0008\u0001\u00107\u001a\u00020\t\u00a2\u0006\u0004\u00088\u0010\u001cJ!\u0010Q\u001a\u00020\u000f2\u0008\u0010.\u001a\u0004\u0018\u00010-2\u0008\u0010P\u001a\u0004\u0018\u00010-\u00a2\u0006\u0004\u0008Q\u0010RR\u001b\u0010W\u001a\u00020S8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008M\u0010T\u001a\u0004\u0008U\u0010VR\u0014\u0010[\u001a\u00020X8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u001b\u0010_\u001a\u00020X8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\\\u0010T\u001a\u0004\u0008]\u0010^R\u001b\u0010b\u001a\u00020X8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008`\u0010T\u001a\u0004\u0008a\u0010^R\u001b\u0010g\u001a\u00020c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008d\u0010T\u001a\u0004\u0008e\u0010fR\u001b\u0010l\u001a\u00020h8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008i\u0010T\u001a\u0004\u0008j\u0010kR\u0014\u0010p\u001a\u00020m8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008n\u0010oR\u0014\u0010s\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010t\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010rR\u0014\u0010u\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010rR\u0014\u0010v\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010rR\u0014\u0010w\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010rR\u0014\u0010y\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010rR\u0014\u0010{\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010rR\u0014\u0010\u007f\u001a\u00020|8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0016\u0010\u0081\u0001\u001a\u00020|8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0080\u0001\u0010~R\u0018\u0010\u0085\u0001\u001a\u00030\u0082\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010\u0087\u0001\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0086\u0001\u0010rR\u0016\u0010\u0089\u0001\u001a\u00020\t8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0088\u0001\u0010r\u00a8\u0006\u008b\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;",
        "Landroid/widget/FrameLayout;",
        "LU31/a;",
        "LU31/j;",
        "LU31/h;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "",
        "setMarketCoefficientState",
        "(Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "k",
        "(II)V",
        "j",
        "()I",
        "i",
        "()V",
        "parentWidth",
        "l",
        "(I)V",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "onMeasure",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "",
        "title",
        "setTitle",
        "(Ljava/lang/CharSequence;)V",
        "subtitle",
        "setSubTitle",
        "tag",
        "setTagText",
        "tagColorAttr",
        "setTagColor",
        "error",
        "setError",
        "styleResId",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "description",
        "setMarketDescription",
        "coef",
        "setMarketCoefficient",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "bonusTitle",
        "setCouponBonusTitle",
        "LX31/c;",
        "couponCardUiModel",
        "setModel",
        "(LX31/c;)V",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "a",
        "tagColor",
        "(Landroid/content/res/ColorStateList;)V",
        "coefficient",
        "setMarket",
        "(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V",
        "Lorg/xbet/uikit/utils/e;",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "LW31/h;",
        "b",
        "LW31/h;",
        "titleDelegate",
        "c",
        "getSubTitleDelegate",
        "()LW31/h;",
        "subTitleDelegate",
        "d",
        "getErrorDelegate",
        "errorDelegate",
        "LW31/f;",
        "e",
        "getTagDelegate",
        "()LW31/f;",
        "tagDelegate",
        "LW31/e;",
        "f",
        "getShimmerDelegate",
        "()LW31/e;",
        "shimmerDelegate",
        "LW31/i;",
        "g",
        "LW31/i;",
        "buttonsDelegate",
        "h",
        "I",
        "verticalPadding",
        "marketVerticalMargin",
        "marketHorizontalMargin",
        "buttonHorizontalMargin",
        "topElementsMargin",
        "m",
        "subtitleGoneMargin",
        "n",
        "shimmerHeight",
        "",
        "o",
        "F",
        "shrinkCoefTextSize",
        "p",
        "usualCoefTextSize",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "q",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "marketView",
        "r",
        "iconSize",
        "s",
        "horizontalPadding",
        "t",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final t:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final u:I


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LW31/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LW31/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:F

.field public final p:F

.field public final q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:I

.field public final s:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->t:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->u:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 9
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct/range {p0 .. p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    new-instance v0, LV31/o;

    invoke-direct {v0, p0}, LV31/o;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->a:Lkotlin/j;

    .line 7
    new-instance v0, LW31/h;

    .line 8
    sget v4, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    const/4 v5, 0x4

    const/4 v6, 0x0

    const/4 v2, 0x3

    const/4 v3, 0x0

    move-object v1, p0

    .line 9
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    .line 10
    new-instance v0, LV31/p;

    invoke-direct {v0, p0}, LV31/p;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->c:Lkotlin/j;

    .line 11
    new-instance v0, LV31/q;

    invoke-direct {v0, p0}, LV31/q;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->d:Lkotlin/j;

    .line 12
    new-instance v0, LV31/r;

    invoke-direct {v0, p0}, LV31/r;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->e:Lkotlin/j;

    .line 13
    new-instance v0, LV31/s;

    invoke-direct {v0, p0}, LV31/s;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->f:Lkotlin/j;

    .line 14
    new-instance v0, LW31/i;

    .line 15
    sget v2, LlZ0/h;->ic_glyph_move_vertical_large:I

    .line 16
    sget v3, LlZ0/h;->ic_glyph_cancel_small:I

    .line 17
    sget v4, LlZ0/g;->size_40:I

    move v5, v4

    .line 18
    invoke-direct/range {v0 .. v5}, LW31/i;-><init>(Landroid/view/ViewGroup;IIII)V

    .line 19
    invoke-virtual {v0}, LW31/c;->e()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    invoke-virtual {v0}, LW31/c;->c()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 21
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_12:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->h:I

    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_8:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->i:I

    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_8:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->j:I

    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_8:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->k:I

    .line 26
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_4:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 27
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_10:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->m:I

    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_100:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->n:I

    .line 29
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->text_12:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v7

    iput v7, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->o:F

    .line 30
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->text_14:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v8

    iput v8, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->p:F

    .line 31
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 32
    sget-object v2, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketBlockedIconPosition(Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;)V

    .line 33
    sget v2, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionTextStyle(I)V

    .line 34
    sget v2, LlZ0/n;->TextStyle_Text_Bold_TextPrimary:I

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientTextStyle(I)V

    const v2, 0x7fffffff

    .line 35
    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionMaxLines(I)V

    .line 36
    invoke-virtual {v0, v8}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMaxTextSize(F)V

    .line 37
    invoke-virtual {v0, v7}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMinTextSize(F)V

    .line 38
    sget v2, LlZ0/d;->uikitBackground:I

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setBackgroundTintAttr(I)V

    .line 39
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 40
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_40:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 41
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 42
    sget-object v0, Lm31/g;->SportCouponCardView:[I

    const/4 v2, 0x0

    .line 43
    invoke-virtual {p1, p2, v0, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v0

    .line 44
    sget v3, Lm31/g;->SportCouponCardView_showSkeleton:I

    invoke-virtual {v0, v3, v2}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 45
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->a()V

    .line 46
    :cond_0
    sget v3, Lm31/g;->SportCouponCardView_title:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v3

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    goto :goto_0

    :cond_1
    move-object v3, v4

    :goto_0
    const-string v5, ""

    if-nez v3, :cond_2

    move-object v3, v5

    :cond_2
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTitle(Ljava/lang/CharSequence;)V

    .line 47
    sget v3, Lm31/g;->SportCouponCardView_subtitle:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v3

    if-eqz v3, :cond_3

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    goto :goto_1

    :cond_3
    move-object v3, v4

    :goto_1
    if-nez v3, :cond_4

    move-object v3, v5

    :cond_4
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 48
    sget v3, Lm31/g;->SportCouponCardView_tag:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v3

    if-eqz v3, :cond_5

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    goto :goto_2

    :cond_5
    move-object v3, v4

    :goto_2
    if-nez v3, :cond_6

    move-object v3, v5

    :cond_6
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTagText(Ljava/lang/CharSequence;)V

    .line 49
    sget v3, Lm31/g;->SportCouponCardView_tagColor:I

    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->c(Landroid/content/res/TypedArray;Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    move-result-object v3

    if-eqz v3, :cond_7

    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTagColor(Landroid/content/res/ColorStateList;)V

    .line 50
    :cond_7
    sget v3, Lm31/g;->SportCouponCardView_error:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v3

    if-eqz v3, :cond_8

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    goto :goto_3

    :cond_8
    move-object v3, v4

    :goto_3
    if-nez v3, :cond_9

    move-object v3, v5

    :cond_9
    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setError(Ljava/lang/CharSequence;)V

    .line 51
    sget v3, Lm31/g;->SportCouponCardView_couponMarketStyle:I

    invoke-virtual {v0, v3, v2}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setMarketStyle(Ljava/lang/Integer;)V

    .line 52
    sget v2, Lm31/g;->SportCouponCardView_marketTitle:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v2

    if-eqz v2, :cond_a

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    goto :goto_4

    :cond_a
    move-object v2, v4

    :goto_4
    if-nez v2, :cond_b

    move-object v2, v5

    .line 53
    :cond_b
    sget v3, Lm31/g;->SportCouponCardView_marketCoefficient:I

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    .line 54
    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v1

    if-eqz v1, :cond_c

    .line 55
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    :cond_c
    if-nez v4, :cond_d

    goto :goto_5

    :cond_d
    move-object v5, v4

    .line 56
    :goto_5
    invoke-virtual {p0, v2, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 57
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 58
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCouponCardStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->m(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->h(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->n(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->o(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/f;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getErrorDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getShimmerDelegate()LW31/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->f:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSubTitleDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTagDelegate()LW31/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final h(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Warning:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const v2, 0x7fffffff

    .line 8
    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    move-object v1, p0

    .line 12
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method private final i()V
    .locals 4

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->k:I

    .line 4
    .line 5
    mul-int/lit8 v1, v1, 0x2

    .line 6
    .line 7
    add-int/2addr v0, v1

    .line 8
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v1}, LW31/h;->e()Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-nez v1, :cond_0

    .line 17
    .line 18
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {v1}, LW31/h;->c()I

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 27
    .line 28
    add-int/2addr v1, v2

    .line 29
    goto :goto_0

    .line 30
    :cond_0
    const/4 v1, 0x0

    .line 31
    :goto_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->h:I

    .line 32
    .line 33
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    .line 34
    .line 35
    invoke-virtual {v3}, LW31/h;->c()I

    .line 36
    .line 37
    .line 38
    move-result v3

    .line 39
    add-int/2addr v2, v3

    .line 40
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 41
    .line 42
    add-int/2addr v2, v3

    .line 43
    add-int/2addr v2, v1

    .line 44
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 49
    .line 50
    invoke-virtual {v1, v3, v0, v2}, LW31/f;->b(III)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method private final j()I
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    .line 2
    .line 3
    invoke-virtual {v0}, LW31/h;->c()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    const/4 v2, 0x0

    .line 20
    if-nez v1, :cond_0

    .line 21
    .line 22
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 35
    .line 36
    add-int/2addr v1, v3

    .line 37
    goto :goto_0

    .line 38
    :cond_0
    const/4 v1, 0x0

    .line 39
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getErrorDelegate()LW31/h;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    invoke-virtual {v3}, LW31/h;->e()Z

    .line 44
    .line 45
    .line 46
    move-result v3

    .line 47
    if-nez v3, :cond_1

    .line 48
    .line 49
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getErrorDelegate()LW31/h;

    .line 50
    .line 51
    .line 52
    move-result-object v3

    .line 53
    invoke-virtual {v3}, LW31/h;->c()I

    .line 54
    .line 55
    .line 56
    move-result v3

    .line 57
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 58
    .line 59
    add-int/2addr v3, v4

    .line 60
    goto :goto_1

    .line 61
    :cond_1
    const/4 v3, 0x0

    .line 62
    :goto_1
    if-gtz v1, :cond_3

    .line 63
    .line 64
    if-lez v3, :cond_2

    .line 65
    .line 66
    goto :goto_2

    .line 67
    :cond_2
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->m:I

    .line 68
    .line 69
    :cond_3
    :goto_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 70
    .line 71
    .line 72
    move-result-object v4

    .line 73
    invoke-virtual {v4}, LW31/h;->e()Z

    .line 74
    .line 75
    .line 76
    move-result v4

    .line 77
    if-nez v4, :cond_4

    .line 78
    .line 79
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    invoke-virtual {v2}, LW31/h;->c()I

    .line 84
    .line 85
    .line 86
    move-result v2

    .line 87
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 88
    .line 89
    add-int/2addr v2, v4

    .line 90
    :cond_4
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 91
    .line 92
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 93
    .line 94
    .line 95
    move-result v4

    .line 96
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->i:I

    .line 97
    .line 98
    mul-int/lit8 v5, v5, 0x2

    .line 99
    .line 100
    add-int/2addr v4, v5

    .line 101
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getShimmerDelegate()LW31/e;

    .line 102
    .line 103
    .line 104
    move-result-object v5

    .line 105
    invoke-virtual {v5}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 106
    .line 107
    .line 108
    move-result-object v5

    .line 109
    invoke-virtual {v5}, Landroid/view/View;->getVisibility()I

    .line 110
    .line 111
    .line 112
    move-result v5

    .line 113
    if-nez v5, :cond_5

    .line 114
    .line 115
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->n:I

    .line 116
    .line 117
    goto :goto_3

    .line 118
    :cond_5
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->h:I

    .line 119
    .line 120
    add-int/2addr v5, v0

    .line 121
    add-int/2addr v5, v2

    .line 122
    add-int/2addr v5, v1

    .line 123
    add-int/2addr v5, v3

    .line 124
    add-int v0, v5, v4

    .line 125
    .line 126
    :goto_3
    const/high16 v1, 0x40000000    # 2.0f

    .line 127
    .line 128
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 129
    .line 130
    .line 131
    move-result v0

    .line 132
    return v0
.end method

.method public static final m(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/e;
    .locals 2

    .line 1
    new-instance v0, LW31/e;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->n:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/e;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final n(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    move-object v1, p0

    .line 10
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static final o(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;)LW31/f;
    .locals 2

    .line 1
    new-instance v0, LW31/f;

    .line 2
    .line 3
    sget v1, LlZ0/n;->Widget_Tag_RectangularS_Red:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/f;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method private final setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/e;->d()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final k(II)V
    .locals 2

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 6
    .line 7
    invoke-virtual {v0}, LW31/c;->e()Landroid/widget/ImageView;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_0

    .line 16
    .line 17
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->j:I

    .line 21
    .line 22
    :goto_0
    sub-int/2addr p1, v0

    .line 23
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->j:I

    .line 24
    .line 25
    sub-int/2addr p1, v0

    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 27
    .line 28
    const/high16 v1, 0x40000000    # 2.0f

    .line 29
    .line 30
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final l(I)V
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 2
    .line 3
    sub-int/2addr p1, v0

    .line 4
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 5
    .line 6
    sub-int/2addr p1, v0

    .line 7
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->k:I

    .line 8
    .line 9
    sub-int/2addr p1, v0

    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    .line 11
    .line 12
    invoke-virtual {v0, p1}, LW31/h;->f(I)V

    .line 13
    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {v0, p1}, LW31/h;->f(I)V

    .line 20
    .line 21
    .line 22
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getErrorDelegate()LW31/h;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {v0, p1}, LW31/h;->f(I)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->h:I

    .line 2
    .line 3
    int-to-float v0, v0

    .line 4
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    .line 5
    .line 6
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 7
    .line 8
    int-to-float v2, v2

    .line 9
    invoke-virtual {v1, p1, v2, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    .line 13
    .line 14
    invoke-virtual {v1}, LW31/h;->c()I

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 19
    .line 20
    add-int/2addr v1, v2

    .line 21
    int-to-float v1, v1

    .line 22
    add-float/2addr v0, v1

    .line 23
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 28
    .line 29
    int-to-float v2, v2

    .line 30
    invoke-virtual {v1, p1, v2, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 31
    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-virtual {v1}, LW31/h;->c()I

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    const/4 v2, 0x0

    .line 42
    if-lez v1, :cond_0

    .line 43
    .line 44
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 45
    .line 46
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v3}, LW31/h;->c()I

    .line 51
    .line 52
    .line 53
    move-result v3

    .line 54
    add-int/2addr v1, v3

    .line 55
    goto :goto_0

    .line 56
    :cond_0
    const/4 v1, 0x0

    .line 57
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    invoke-virtual {v3}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 62
    .line 63
    .line 64
    move-result-object v3

    .line 65
    invoke-virtual {v3}, Landroid/view/View;->getVisibility()I

    .line 66
    .line 67
    .line 68
    move-result v3

    .line 69
    if-nez v3, :cond_1

    .line 70
    .line 71
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l:I

    .line 72
    .line 73
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    invoke-virtual {v3}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 78
    .line 79
    .line 80
    move-result-object v3

    .line 81
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    add-int/2addr v2, v3

    .line 86
    :cond_1
    add-int/2addr v1, v2

    .line 87
    int-to-float v1, v1

    .line 88
    add-float/2addr v0, v1

    .line 89
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getErrorDelegate()LW31/h;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 94
    .line 95
    int-to-float v2, v2

    .line 96
    invoke-virtual {v1, p1, v2, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 97
    .line 98
    .line 99
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    if-nez p1, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getShimmerDelegate()LW31/e;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, LW31/e;->b()V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 24
    .line 25
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 26
    .line 27
    div-int/lit8 p2, p2, 0x2

    .line 28
    .line 29
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 30
    .line 31
    .line 32
    move-result p3

    .line 33
    iget p4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->i:I

    .line 34
    .line 35
    sub-int/2addr p3, p4

    .line 36
    iget-object p4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 37
    .line 38
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 39
    .line 40
    .line 41
    move-result p4

    .line 42
    div-int/lit8 p4, p4, 0x2

    .line 43
    .line 44
    sub-int/2addr p3, p4

    .line 45
    invoke-virtual {p1, p2, p3}, LW31/i;->l(II)V

    .line 46
    .line 47
    .line 48
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->i()V

    .line 49
    .line 50
    .line 51
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 52
    .line 53
    invoke-virtual {p1}, LW31/c;->e()Landroid/widget/ImageView;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 58
    .line 59
    .line 60
    move-result p1

    .line 61
    if-nez p1, :cond_1

    .line 62
    .line 63
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 64
    .line 65
    goto :goto_0

    .line 66
    :cond_1
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->j:I

    .line 67
    .line 68
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 69
    .line 70
    .line 71
    move-result p2

    .line 72
    iget-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 73
    .line 74
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 75
    .line 76
    .line 77
    move-result p3

    .line 78
    sub-int/2addr p2, p3

    .line 79
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->i:I

    .line 80
    .line 81
    sub-int v3, p2, p3

    .line 82
    .line 83
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 84
    .line 85
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->j:I

    .line 86
    .line 87
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 88
    .line 89
    .line 90
    move-result p2

    .line 91
    sub-int v4, p2, p1

    .line 92
    .line 93
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 94
    .line 95
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 96
    .line 97
    .line 98
    move-result p1

    .line 99
    add-int v5, v3, p1

    .line 100
    .line 101
    move-object v0, p0

    .line 102
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 103
    .line 104
    .line 105
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getShimmerDelegate()LW31/e;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getShimmerDelegate()LW31/e;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-virtual {p2, v0}, LW31/e;->c(I)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->l(I)V

    .line 28
    .line 29
    .line 30
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->s:I

    .line 35
    .line 36
    sub-int/2addr v0, v2

    .line 37
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->k:I

    .line 38
    .line 39
    mul-int/lit8 v2, v2, 0x2

    .line 40
    .line 41
    sub-int/2addr v0, v2

    .line 42
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->r:I

    .line 43
    .line 44
    sub-int/2addr v0, v2

    .line 45
    invoke-virtual {v1, v0}, LW31/f;->c(I)V

    .line 46
    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 49
    .line 50
    invoke-virtual {v0}, LW31/c;->g()V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->k(II)V

    .line 54
    .line 55
    .line 56
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->j()I

    .line 57
    .line 58
    .line 59
    move-result p2

    .line 60
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 61
    .line 62
    .line 63
    return-void
.end method

.method public setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->h(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setCouponBonusTitle(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTitle(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final setError(I)V
    .locals 1

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->g(I)V

    return-void
.end method

.method public setError(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 17
    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 20
    .line 21
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 7
    .line 8
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setMarketStyle(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->q:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketStyle(Ljava/lang/Integer;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public setModel(LX31/c;)V
    .locals 2
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LX31/c;->m()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTagText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, LX31/c;->l()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTagColor(I)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1}, LX31/c;->o()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setTitle(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1}, LX31/c;->k()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/c;->c()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setError(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1}, LX31/c;->f()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p1}, LX31/c;->e()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p1}, LX31/c;->h()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setMarketStyle(Ljava/lang/Integer;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p1}, LX31/c;->b()Lorg/xbet/uikit/components/market/base/CoefficientState;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->g:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->j(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setSubTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getSubTitleDelegate()LW31/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public setTagColor(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->d(I)V

    return-void
.end method

.method public final setTagColor(Landroid/content/res/ColorStateList;)V
    .locals 1
    .param p1    # Landroid/content/res/ColorStateList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->e(Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public setTagText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->getTagDelegate()LW31/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, LW31/f;->g(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    invoke-virtual {v0, p1}, LW31/h;->g(I)V

    return-void
.end method

.method public setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBgV2;->b:LW31/h;

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method
