.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Ld41/b;",
        "clickListener",
        "LA4/c;",
        "",
        "Le41/a;",
        "g",
        "(Ld41/b;)LA4/c;",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;->l(Ld41/b;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/L;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/L;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;->j(Ld41/b;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic d(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;->k(Ld41/b;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic e(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;->m(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Ld41/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt;->i(Ld41/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final g(Ld41/b;)LA4/c;
    .locals 4
    .param p0    # Ld41/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ld41/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "Le41/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lc41/m;

    .line 2
    .line 3
    invoke-direct {v0}, Lc41/m;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lc41/n;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lc41/n;-><init>(Ld41/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt$sportFeedsCellSportMediumViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt$sportFeedsCellSportMediumViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt$sportFeedsCellSportMediumViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportMediumViewHolderKt$sportFeedsCellSportMediumViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/L;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC31/L;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/L;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Ld41/b;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LC31/L;

    .line 6
    .line 7
    iget-object v0, v0, LC31/L;->e:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMedium;

    .line 8
    .line 9
    new-instance v1, Lc41/o;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, Lc41/o;-><init>(Ld41/b;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, LC31/L;

    .line 22
    .line 23
    iget-object v0, v0, LC31/L;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 24
    .line 25
    new-instance v1, Lc41/p;

    .line 26
    .line 27
    invoke-direct {v1, p0, p1}, Lc41/p;-><init>(Ld41/b;LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionClickListener(Landroid/view/View$OnClickListener;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, LC31/L;

    .line 38
    .line 39
    iget-object v0, v0, LC31/L;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 40
    .line 41
    new-instance v1, Lc41/q;

    .line 42
    .line 43
    invoke-direct {v1, p0, p1}, Lc41/q;-><init>(Ld41/b;LB4/a;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckBoxClickListener(Landroid/view/View$OnClickListener;)V

    .line 47
    .line 48
    .line 49
    new-instance p0, Lc41/r;

    .line 50
    .line 51
    invoke-direct {p0, p1}, Lc41/r;-><init>(LB4/a;)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 55
    .line 56
    .line 57
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 58
    .line 59
    return-object p0
.end method

.method public static final j(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lg41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/b;->b(Lg41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final k(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lg41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/b;->a(Lg41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final l(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lg41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/b;->c(Lg41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final m(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LC31/L;

    .line 6
    .line 7
    iget-object p1, p1, LC31/L;->e:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMedium;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lg41/a;

    .line 14
    .line 15
    invoke-virtual {v0}, Lg41/a;->g()Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportMedium;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LC31/L;

    .line 27
    .line 28
    iget-object p1, p1, LC31/L;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lg41/a;

    .line 35
    .line 36
    invoke-virtual {v0}, Lg41/a;->i()I

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIcon(I)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    check-cast p1, LC31/L;

    .line 48
    .line 49
    iget-object p1, p1, LC31/L;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 50
    .line 51
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    check-cast v0, Lg41/a;

    .line 56
    .line 57
    invoke-virtual {v0}, Lg41/a;->j()Ljava/lang/Integer;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, LC31/L;

    .line 69
    .line 70
    iget-object p1, p1, LC31/L;->c:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 71
    .line 72
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    check-cast v0, Lg41/a;

    .line 77
    .line 78
    invoke-virtual {v0}, Lg41/a;->l()Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleText(Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    check-cast p1, LC31/L;

    .line 90
    .line 91
    iget-object p1, p1, LC31/L;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 92
    .line 93
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    check-cast v0, Lg41/a;

    .line 98
    .line 99
    invoke-virtual {v0}, Lg41/a;->f()Z

    .line 100
    .line 101
    .line 102
    move-result v0

    .line 103
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckboxChecked(Z)V

    .line 104
    .line 105
    .line 106
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    check-cast p1, LC31/L;

    .line 111
    .line 112
    iget-object p1, p1, LC31/L;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 113
    .line 114
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    check-cast v0, Lg41/a;

    .line 119
    .line 120
    invoke-virtual {v0}, Lg41/a;->h()I

    .line 121
    .line 122
    .line 123
    move-result v0

    .line 124
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 125
    .line 126
    .line 127
    move-result-object v0

    .line 128
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 129
    .line 130
    .line 131
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    check-cast p1, LC31/L;

    .line 136
    .line 137
    iget-object p1, p1, LC31/L;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 138
    .line 139
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 140
    .line 141
    .line 142
    move-result-object v0

    .line 143
    check-cast v0, Lg41/a;

    .line 144
    .line 145
    invoke-virtual {v0}, Lg41/a;->d()Z

    .line 146
    .line 147
    .line 148
    move-result v0

    .line 149
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionExpanded(Z)V

    .line 150
    .line 151
    .line 152
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    check-cast p1, LC31/L;

    .line 157
    .line 158
    iget-object p1, p1, LC31/L;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 159
    .line 160
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    check-cast v0, Lg41/a;

    .line 165
    .line 166
    invoke-virtual {v0}, Lg41/a;->e()Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 167
    .line 168
    .line 169
    move-result-object v0

    .line 170
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setBadge(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    .line 171
    .line 172
    .line 173
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 174
    .line 175
    .line 176
    move-result-object p0

    .line 177
    check-cast p0, LC31/L;

    .line 178
    .line 179
    iget-object p0, p0, LC31/L;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 180
    .line 181
    const/4 p1, 0x0

    .line 182
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setTagVisible(Z)V

    .line 183
    .line 184
    .line 185
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 186
    .line 187
    return-object p0
.end method
