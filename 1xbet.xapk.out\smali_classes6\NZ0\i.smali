.class public final synthetic LNZ0/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/uikit/components/chips/DsChip;

    invoke-static {p1}, Lorg/xbet/uikit/components/chips/DsChipGroup;->b(Lorg/xbet/uikit/components/chips/DsChip;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Bo<PERSON>;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
