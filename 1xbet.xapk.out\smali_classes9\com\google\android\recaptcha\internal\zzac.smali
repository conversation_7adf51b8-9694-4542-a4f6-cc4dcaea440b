.class public final Lcom/google/android/recaptcha/internal/zzac;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field protected final zza:Lcom/google/android/recaptcha/internal/zzfc;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/google/android/recaptcha/internal/zzfc;

    invoke-direct {v0}, Lcom/google/android/recaptcha/internal/zzfc;-><init>()V

    iput-object v0, p0, Lcom/google/android/recaptcha/internal/zzac;->zza:Lcom/google/android/recaptcha/internal/zzfc;

    return-void
.end method
