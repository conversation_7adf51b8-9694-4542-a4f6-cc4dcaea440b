.class public final Lorg/xbet/ui_common/moxy/activities/k;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\u0008\u0001\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0013\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xbet/ui_common/moxy/activities/k;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/onexlocalization/b;",
        "getLanguageStreamUseCase",
        "<init>",
        "(Lorg/xbet/onexlocalization/b;)V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "p3",
        "()Lkotlinx/coroutines/flow/e;",
        "v1",
        "Lorg/xbet/onexlocalization/b;",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final v1:Lorg/xbet/onexlocalization/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/onexlocalization/b;)V
    .locals 0
    .param p1    # Lorg/xbet/onexlocalization/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/ui_common/moxy/activities/k;->v1:Lorg/xbet/onexlocalization/b;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final p3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/moxy/activities/k;->v1:Lorg/xbet/onexlocalization/b;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/onexlocalization/b;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
