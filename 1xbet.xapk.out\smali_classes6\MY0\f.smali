.class public final synthetic LMY0/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;

.field public final synthetic b:LIY0/c;

.field public final synthetic c:Ljava/lang/Object;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function0;LIY0/c;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LMY0/f;->a:Lkotlin/jvm/functions/Function0;

    iput-object p2, p0, LMY0/f;->b:LIY0/c;

    iput-object p3, p0, LMY0/f;->c:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LMY0/f;->a:L<PERSON><PERSON>/jvm/functions/Function0;

    iget-object v1, p0, LMY0/f;->b:LIY0/c;

    iget-object v2, p0, LMY0/f;->c:Ljava/lang/Object;

    invoke-static {v0, v1, v2}, LMY0/g;->a(Lkotlin/jvm/functions/Function0;LIY0/c;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
