.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u000e\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u000e\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\r2\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0015\u0010\u0017\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u0013\u001a\u00020\r2\u0008\u0008\u0001\u0010\u0019\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0013\u0010\u0010J\u0015\u0010\u0013\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u0013\u0010\u001cJ\u001f\u0010\u0013\u001a\u00020\r2\u0006\u0010\u001b\u001a\u00020\u001a2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u001eR\u0014\u0010\"\u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010!\u00a8\u0006#"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "text",
        "",
        "setTeamName",
        "(Ljava/lang/CharSequence;)V",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "setTeamLogo",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "visible",
        "setTeamLogoVisibility",
        "(Z)V",
        "resId",
        "",
        "url",
        "(Ljava/lang/String;)V",
        "placeholder",
        "(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V",
        "LC31/B;",
        "a",
        "LC31/B;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/B;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/B;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/B;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final setTeamLogo(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->setTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    if-eqz p1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 1
    :goto_0
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->setTeamLogoVisibility(Z)V

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    iget-object v0, v0, LC31/B;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    iget-object v1, v0, LC31/B;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    iget-object v1, v0, LC31/B;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTeamLogoVisibility(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    .line 2
    .line 3
    iget-object v0, v0, LC31/B;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 v1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    .line 15
    .line 16
    iget-object v0, v0, LC31/B;->c:Landroid/widget/TextView;

    .line 17
    .line 18
    if-eqz p1, :cond_1

    .line 19
    .line 20
    const p1, 0x800003

    .line 21
    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_1
    const/4 p1, 0x1

    .line 25
    :goto_1
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setGravity(I)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final setTeamName(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->setTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTeamName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSingleTeam;->a:LC31/B;

    iget-object v0, v0, LC31/B;->c:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
