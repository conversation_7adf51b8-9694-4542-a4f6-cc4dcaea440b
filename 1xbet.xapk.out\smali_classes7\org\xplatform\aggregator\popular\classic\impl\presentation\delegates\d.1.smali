.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/Throwable;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/d;->a:Ljava/lang/Throwable;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/d;->a:Ljava/lang/Throwable;

    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, <PERSON><PERSON><PERSON>/lang/String;

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularViewModelDelegateImpl;->n(Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
