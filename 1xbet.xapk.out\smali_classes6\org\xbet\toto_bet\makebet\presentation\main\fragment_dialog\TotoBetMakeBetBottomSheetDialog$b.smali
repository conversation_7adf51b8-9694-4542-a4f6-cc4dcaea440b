.class public final Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog$b;
.super Landroidx/viewpager2/widget/ViewPager2$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->Z2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "org/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog$b",
        "Landroidx/viewpager2/widget/ViewPager2$i;",
        "",
        "position",
        "",
        "onPageSelected",
        "(I)V",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic b:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog$b;->b:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/viewpager2/widget/ViewPager2$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onPageSelected(I)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog$b;->b:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->T2()LOU0/w;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, LOU0/w;->f:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog$b;->b:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    const/4 v3, 0x1

    .line 13
    invoke-static {v0, v2, v2, v3, v2}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentSelectedListener$default(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setSelectedPosition(I)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/h;->j(Landroid/view/View;)V

    .line 20
    .line 21
    .line 22
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->O2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;)Lkotlin/jvm/functions/Function1;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-static {v0, v2, p1, v3, v2}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentSelectedListener$default(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    return-void
.end method
