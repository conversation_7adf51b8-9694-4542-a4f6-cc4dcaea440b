.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u000c\u0008\u0086\u0008\u0018\u00002\u00020\u0001B%\u0012\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0010\u0010\u000c\u001a\u00020\u000bH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u000eH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001a\u0010\u0014\u001a\u00020\u00132\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0014\u0010\u0015R\u001d\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0017\u001a\u0004\u0008\u0016\u0010\u0018R\u0017\u0010\u0006\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u0019\u0010\u001bR\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001c\u0010\u001e\u00a8\u0006\u001f"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;",
        "",
        "Lkb1/A;",
        "content",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "tournamentKind",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "userActionButton",
        "<init>",
        "(Ljava/util/List;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/util/List;",
        "()Ljava/util/List;",
        "b",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "c",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkb1/A;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lkb1/A;",
            ">;",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a:Ljava/util/List;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lkb1/A;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a:Ljava/util/List;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a:Ljava/util/List;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object p1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a:Ljava/util/List;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a:Ljava/util/List;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Content(content="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", tournamentKind="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", userActionButton="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
