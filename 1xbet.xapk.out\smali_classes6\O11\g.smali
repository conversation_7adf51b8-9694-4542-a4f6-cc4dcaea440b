.class public final synthetic LO11/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/lifecycle/w;

    check-cast p2, Landroidx/lifecycle/v;

    invoke-static {p1, p2}, LO11/j;->e(Landroidx/lifecycle/w;Landroidx/lifecycle/v;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
