.class public final Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType$a;",
        "",
        "<init>",
        "()V",
        "",
        "style",
        "Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
        "a",
        "(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const v1, -0x1d49e90a

    .line 6
    .line 7
    .line 8
    if-eq v0, v1, :cond_4

    .line 9
    .line 10
    const v1, -0x105a8138

    .line 11
    .line 12
    .line 13
    if-eq v0, v1, :cond_2

    .line 14
    .line 15
    const v1, 0x3ddf71c

    .line 16
    .line 17
    .line 18
    if-eq v0, v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const-string v0, "CardL"

    .line 22
    .line 23
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    if-nez p1, :cond_1

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_1
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->CARDS_L:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 31
    .line 32
    return-object p1

    .line 33
    :cond_2
    const-string v0, "TransparentVertical"

    .line 34
    .line 35
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    if-nez p1, :cond_3

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_3
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->TRANSPARENT_VERTICAL:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 43
    .line 44
    return-object p1

    .line 45
    :cond_4
    const-string v0, "TransparentHorizontal"

    .line 46
    .line 47
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    if-nez p1, :cond_5

    .line 52
    .line 53
    :goto_0
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->CARDS_S:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 54
    .line 55
    return-object p1

    .line 56
    :cond_5
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;->TRANSPARENT_HORIZONTAL:Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 57
    .line 58
    return-object p1
.end method
