.class public final LmW0/b;
.super Ljava/lang/Object;


# static fields
.field public static fragment_dialog_make_bet:I = 0x7f0d038e

.field public static fragment_simple_bet_toto_jackpot:I = 0x7f0d0488

.field public static fragment_toto_jackpot:I = 0x7f0d04dc

.field public static fragment_toto_jackpot_history:I = 0x7f0d04dd

.field public static item_toto_jackpot_champ_game:I = 0x7f0d070c

.field public static item_toto_jackpot_divider:I = 0x7f0d070d

.field public static item_toto_jackpot_header:I = 0x7f0d070e

.field public static item_toto_jackpot_history:I = 0x7f0d070f

.field public static item_toto_jackpot_history_header:I = 0x7f0d0710

.field public static jackpot_tirag_header_history_layout:I = 0x7f0d0734

.field public static jackpot_tirag_header_layout:I = 0x7f0d0735

.field public static preset_balance_jackpot:I = 0x7f0d0858

.field public static simple_bet_dialog:I = 0x7f0d08f8

.field public static toto_jackpot_prediction_layout:I = 0x7f0d0a41


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
