.class public final Ln41/c;
.super Landroidx/recyclerview/widget/s;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ln41/c$a;,
        Ln41/c$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/s<",
        "Ln41/m;",
        "Ln41/c$b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010!\n\u0002\u0010\u0000\n\u0002\u0008\u0011\u0008\u0001\u0018\u0000 &2\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0002\'(B5\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u001c\u0008\u0002\u0010\u000b\u001a\u0016\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n\u0018\u00010\u0008\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001f\u0010\u0011\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001f\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J-\u0010\u001a\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\t2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u0017H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR6\u0010\u000b\u001a\u0016\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n\u0018\u00010\u00088\u0000@\u0000X\u0080\u000e\u00a2\u0006\u0012\n\u0004\u0008 \u0010!\u001a\u0004\u0008\"\u0010#\"\u0004\u0008$\u0010%\u00a8\u0006)"
    }
    d2 = {
        "Ln41/c;",
        "Landroidx/recyclerview/widget/s;",
        "Ln41/m;",
        "Ln41/c$b;",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "bannerType",
        "LL11/c;",
        "placeholder",
        "Lkotlin/Function2;",
        "",
        "",
        "clickListener",
        "<init>",
        "(Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;Lkotlin/jvm/functions/Function2;)V",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "y",
        "(Landroid/view/ViewGroup;I)Ln41/c$b;",
        "holder",
        "position",
        "u",
        "(Ln41/c$b;I)V",
        "",
        "",
        "payloads",
        "v",
        "(Ln41/c$b;ILjava/util/List;)V",
        "f",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "g",
        "LL11/c;",
        "h",
        "Lkotlin/jvm/functions/Function2;",
        "getClickListener$uikit_web_games_release",
        "()Lkotlin/jvm/functions/Function2;",
        "z",
        "(Lkotlin/jvm/functions/Function2;)V",
        "i",
        "b",
        "a",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final i:Ln41/c$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final j:I


# instance fields
.field public final f:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:LL11/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public h:Lkotlin/jvm/functions/Function2;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ln41/m;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Ln41/c$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ln41/c$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Ln41/c;->i:Ln41/c$a;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, Ln41/c;->j:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;Lkotlin/jvm/functions/Function2;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
            "LL11/c;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ln41/m;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 2
    sget-object v0, Ln41/c;->i:Ln41/c$a;

    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/s;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 3
    iput-object p1, p0, Ln41/c;->f:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 4
    iput-object p2, p0, Ln41/c;->g:LL11/c;

    .line 5
    iput-object p3, p0, Ln41/c;->h:Lkotlin/jvm/functions/Function2;

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;Lkotlin/jvm/functions/Function2;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_0

    const/4 p3, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2, p3}, Ln41/c;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;Lkotlin/jvm/functions/Function2;)V

    return-void
.end method

.method public static synthetic s(Ljava/lang/Object;)Ljava/util/Set;
    .locals 0

    .line 1
    invoke-static {p0}, Ln41/c;->x(Ljava/lang/Object;)Ljava/util/Set;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t(Ln41/c;Ln41/m;ILandroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ln41/c;->w(Ln41/c;Ln41/m;ILandroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final w(Ln41/c;Ln41/m;ILandroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Ln41/c;->h:Lkotlin/jvm/functions/Function2;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-static {p2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-interface {p0, p1, p2}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 13
    .line 14
    return-object p0
.end method

.method private static final x(Ljava/lang/Object;)Ljava/util/Set;
    .locals 0

    .line 1
    check-cast p0, Ljava/util/Set;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public bridge synthetic onBindViewHolder(Landroidx/recyclerview/widget/RecyclerView$D;I)V
    .locals 0

    .line 1
    check-cast p1, Ln41/c$b;

    invoke-virtual {p0, p1, p2}, Ln41/c;->u(Ln41/c$b;I)V

    return-void
.end method

.method public bridge synthetic onBindViewHolder(Landroidx/recyclerview/widget/RecyclerView$D;ILjava/util/List;)V
    .locals 0

    .line 2
    check-cast p1, Ln41/c$b;

    invoke-virtual {p0, p1, p2, p3}, Ln41/c;->v(Ln41/c$b;ILjava/util/List;)V

    return-void
.end method

.method public bridge synthetic onCreateViewHolder(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Ln41/c;->y(Landroid/view/ViewGroup;I)Ln41/c$b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public u(Ln41/c$b;I)V
    .locals 3
    .param p1    # Ln41/c$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p2}, Landroidx/recyclerview/widget/s;->o(I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Ln41/m;

    .line 6
    .line 7
    invoke-virtual {p1}, Ln41/c$b;->e()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1, v0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setModel(Ln41/m;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p1}, Ln41/c$b;->e()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    sget-object v1, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_1500:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 19
    .line 20
    new-instance v2, Ln41/a;

    .line 21
    .line 22
    invoke-direct {v2, p0, v0, p2}, Ln41/a;-><init>(Ln41/c;Ln41/m;I)V

    .line 23
    .line 24
    .line 25
    invoke-static {p1, v1, v2}, LN11/f;->m(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public v(Ln41/c$b;ILjava/util/List;)V
    .locals 3
    .param p1    # Ln41/c$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln41/c$b;",
            "I",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p3}, Ljava/util/Collection;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_4

    .line 6
    .line 7
    invoke-static {p3}, Lkotlin/collections/CollectionsKt;->h0(Ljava/lang/Iterable;)Lkotlin/sequences/Sequence;

    .line 8
    .line 9
    .line 10
    move-result-object p2

    .line 11
    new-instance p3, Ln41/b;

    .line 12
    .line 13
    invoke-direct {p3}, Ln41/b;-><init>()V

    .line 14
    .line 15
    .line 16
    invoke-static {p2, p3}, Lkotlin/sequences/SequencesKt___SequencesKt;->b0(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    invoke-static {p2}, Lkotlin/sequences/s;->p(Lkotlin/sequences/Sequence;)Lkotlin/sequences/Sequence;

    .line 21
    .line 22
    .line 23
    move-result-object p2

    .line 24
    invoke-interface {p2}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 25
    .line 26
    .line 27
    move-result-object p2

    .line 28
    :cond_0
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 29
    .line 30
    .line 31
    move-result p3

    .line 32
    if-eqz p3, :cond_3

    .line 33
    .line 34
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object p3

    .line 38
    instance-of v0, p3, Ln41/p;

    .line 39
    .line 40
    if-eqz v0, :cond_1

    .line 41
    .line 42
    invoke-virtual {p1}, Ln41/c$b;->e()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    check-cast p3, Ln41/p;

    .line 47
    .line 48
    invoke-virtual {p3}, Ln41/p;->f()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p3

    .line 52
    invoke-virtual {v0, p3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setLabel(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    goto :goto_0

    .line 56
    :cond_1
    instance-of v0, p3, Ln41/q;

    .line 57
    .line 58
    if-eqz v0, :cond_2

    .line 59
    .line 60
    invoke-virtual {p1}, Ln41/c$b;->e()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    check-cast p3, Ln41/q;

    .line 65
    .line 66
    invoke-virtual {p3}, Ln41/q;->c()LL11/c;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    invoke-virtual {p3}, Ln41/q;->b()Lkotlin/jvm/functions/Function2;

    .line 71
    .line 72
    .line 73
    move-result-object v2

    .line 74
    invoke-virtual {p3}, Ln41/q;->a()Lkotlin/jvm/functions/Function1;

    .line 75
    .line 76
    .line 77
    move-result-object p3

    .line 78
    invoke-virtual {v0, v1, v2, p3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setPicture(LL11/c;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V

    .line 79
    .line 80
    .line 81
    goto :goto_0

    .line 82
    :cond_2
    instance-of v0, p3, Ln41/o;

    .line 83
    .line 84
    if-eqz v0, :cond_0

    .line 85
    .line 86
    invoke-virtual {p1}, Ln41/c$b;->e()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    check-cast p3, Ln41/o;

    .line 91
    .line 92
    invoke-virtual {p3}, Ln41/o;->f()Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;

    .line 93
    .line 94
    .line 95
    move-result-object p3

    .line 96
    invoke-virtual {v0, p3}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->setType(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItemType;)V

    .line 97
    .line 98
    .line 99
    goto :goto_0

    .line 100
    :cond_3
    return-void

    .line 101
    :cond_4
    invoke-super {p0, p1, p2, p3}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->onBindViewHolder(Landroidx/recyclerview/widget/RecyclerView$D;ILjava/util/List;)V

    .line 102
    .line 103
    .line 104
    return-void
.end method

.method public y(Landroid/view/ViewGroup;I)Ln41/c$b;
    .locals 8
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance p2, Ln41/c$b;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    .line 4
    .line 5
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v4, p0, Ln41/c;->f:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 10
    .line 11
    iget-object v5, p0, Ln41/c;->g:LL11/c;

    .line 12
    .line 13
    const/4 v6, 0x6

    .line 14
    const/4 v7, 0x0

    .line 15
    const/4 v2, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    invoke-direct/range {v0 .. v7}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;LL11/c;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 18
    .line 19
    .line 20
    invoke-direct {p2, v0}, Ln41/c$b;-><init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public final z(Lkotlin/jvm/functions/Function2;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Ln41/m;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Ln41/c;->h:Lkotlin/jvm/functions/Function2;

    .line 2
    .line 3
    return-void
.end method
