.class public final synthetic Lm11/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lm11/H$p;


# direct methods
.method public synthetic constructor <init>(ZLm11/H$p;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lm11/o;->a:Z

    iput-object p2, p0, Lm11/o;->b:Lm11/H$p;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-boolean v0, p0, Lm11/o;->a:Z

    iget-object v1, p0, Lm11/o;->b:Lm11/H$p;

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, <PERSON>java/lang/<PERSON>olean;->booleanValue()Z

    move-result p1

    invoke-static {v0, v1, p1}, Lm11/G;->k(ZLm11/H$p;Z)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
