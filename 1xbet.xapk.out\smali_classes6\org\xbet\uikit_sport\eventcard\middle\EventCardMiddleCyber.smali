.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010\r\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 <2\u00020\u00012\u00020\u0002:\u00013B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ7\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u001a\u001a\u00020\r2\u0008\u0010\u0019\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0017\u0010\u001c\u001a\u00020\r2\u0008\u0010\u0019\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u0017\u0010\u001f\u001a\u00020\r2\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010\u001f\u001a\u00020\r2\u0008\u0008\u0001\u0010!\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001f\u0010\"J\u0015\u0010\u001f\u001a\u00020\r2\u0006\u0010$\u001a\u00020#\u00a2\u0006\u0004\u0008\u001f\u0010%J\u001f\u0010\u001f\u001a\u00020\r2\u0006\u0010$\u001a\u00020#2\u0008\u0010&\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008\u001f\u0010\'J\u0017\u0010(\u001a\u00020\r2\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008(\u0010 J\u0017\u0010(\u001a\u00020\r2\u0008\u0008\u0001\u0010!\u001a\u00020\u0007\u00a2\u0006\u0004\u0008(\u0010\"J\u0015\u0010(\u001a\u00020\r2\u0006\u0010$\u001a\u00020#\u00a2\u0006\u0004\u0008(\u0010%J\u001f\u0010(\u001a\u00020\r2\u0006\u0010$\u001a\u00020#2\u0008\u0010&\u001a\u0004\u0018\u00010\u001d\u00a2\u0006\u0004\u0008(\u0010\'J\u0015\u0010+\u001a\u00020\r2\u0006\u0010*\u001a\u00020)\u00a2\u0006\u0004\u0008+\u0010,J\u0015\u0010.\u001a\u00020\r2\u0006\u0010-\u001a\u00020\u0018\u00a2\u0006\u0004\u0008.\u0010\u001bJ\u001d\u00101\u001a\u00020\r2\u0006\u0010/\u001a\u00020\u00072\u0006\u00100\u001a\u00020\u0007\u00a2\u0006\u0004\u00081\u0010\u000fJ\u001d\u00102\u001a\u00020\r2\u0006\u0010/\u001a\u00020\u00072\u0006\u00100\u001a\u00020\u0007\u00a2\u0006\u0004\u00082\u0010\u000fR\u0014\u00105\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u00107\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00104R\u0014\u0010;\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:\u00a8\u0006="
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;",
        "Landroid/widget/FrameLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "",
        "text",
        "setFirstTeamName",
        "(Ljava/lang/CharSequence;)V",
        "setSecondTeamName",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "setFirstTeamLogo",
        "(Landroid/graphics/drawable/Drawable;)V",
        "resId",
        "(I)V",
        "",
        "url",
        "(Ljava/lang/String;)V",
        "placeholder",
        "(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V",
        "setSecondTeamLogo",
        "Lorg/xbet/uikit_sport/score/a;",
        "scoreModel",
        "setScoreModel",
        "(Lorg/xbet/uikit_sport/score/a;)V",
        "score",
        "setScore",
        "totalCount",
        "winCount",
        "setFirstVictoryIndicator",
        "setSecondVictoryIndicator",
        "a",
        "I",
        "space4",
        "b",
        "space8",
        "LC31/v;",
        "c",
        "LC31/v;",
        "binding",
        "d",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final d:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final e:I


# instance fields
.field public final a:I

.field public final b:I

.field public final c:LC31/v;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->d:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->e:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_4:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->a:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_8:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->b:I

    .line 8
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/v;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/v;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    const/4 p1, 0x0

    .line 9
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutDirection(I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public onLayout(ZIIII)V
    .locals 2

    .line 1
    sub-int/2addr p4, p2

    .line 2
    sub-int/2addr p5, p3

    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 4
    .line 5
    iget-object p1, p1, LC31/v;->f:Lorg/xbet/uikit_sport/score/SportScore;

    .line 6
    .line 7
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 12
    .line 13
    iget-object p2, p2, LC31/v;->f:Lorg/xbet/uikit_sport/score/SportScore;

    .line 14
    .line 15
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    sub-int/2addr p4, p1

    .line 20
    div-int/lit8 p4, p4, 0x2

    .line 21
    .line 22
    sub-int p3, p5, p2

    .line 23
    .line 24
    div-int/lit8 p3, p3, 0x2

    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 27
    .line 28
    iget-object v0, v0, LC31/v;->f:Lorg/xbet/uikit_sport/score/SportScore;

    .line 29
    .line 30
    add-int/2addr p1, p4

    .line 31
    add-int/2addr p2, p3

    .line 32
    invoke-virtual {v0, p4, p3, p1, p2}, Landroid/view/View;->layout(IIII)V

    .line 33
    .line 34
    .line 35
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 36
    .line 37
    iget-object p2, p2, LC31/v;->e:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 38
    .line 39
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 40
    .line 41
    .line 42
    move-result p2

    .line 43
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 44
    .line 45
    iget-object p3, p3, LC31/v;->e:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 46
    .line 47
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 48
    .line 49
    .line 50
    move-result p3

    .line 51
    sub-int/2addr p4, p2

    .line 52
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->a:I

    .line 53
    .line 54
    sub-int/2addr p4, v0

    .line 55
    sub-int v0, p5, p3

    .line 56
    .line 57
    div-int/lit8 v0, v0, 0x2

    .line 58
    .line 59
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 60
    .line 61
    iget-object v1, v1, LC31/v;->e:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 62
    .line 63
    add-int/2addr p2, p4

    .line 64
    add-int/2addr p3, v0

    .line 65
    invoke-virtual {v1, p4, v0, p2, p3}, Landroid/view/View;->layout(IIII)V

    .line 66
    .line 67
    .line 68
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 69
    .line 70
    iget-object p2, p2, LC31/v;->j:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 71
    .line 72
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 73
    .line 74
    .line 75
    move-result p2

    .line 76
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 77
    .line 78
    iget-object p3, p3, LC31/v;->j:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 79
    .line 80
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 81
    .line 82
    .line 83
    move-result p3

    .line 84
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->a:I

    .line 85
    .line 86
    add-int/2addr p1, v0

    .line 87
    sub-int v0, p5, p3

    .line 88
    .line 89
    div-int/lit8 v0, v0, 0x2

    .line 90
    .line 91
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 92
    .line 93
    iget-object v1, v1, LC31/v;->j:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 94
    .line 95
    add-int/2addr p2, p1

    .line 96
    add-int/2addr p3, v0

    .line 97
    invoke-virtual {v1, p1, v0, p2, p3}, Landroid/view/View;->layout(IIII)V

    .line 98
    .line 99
    .line 100
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 101
    .line 102
    iget-object p1, p1, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 103
    .line 104
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 105
    .line 106
    .line 107
    move-result p1

    .line 108
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 109
    .line 110
    iget-object p3, p3, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 111
    .line 112
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 113
    .line 114
    .line 115
    move-result p3

    .line 116
    sub-int/2addr p4, p1

    .line 117
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->b:I

    .line 118
    .line 119
    sub-int/2addr p4, v0

    .line 120
    sub-int v0, p5, p3

    .line 121
    .line 122
    div-int/lit8 v0, v0, 0x2

    .line 123
    .line 124
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 125
    .line 126
    iget-object v1, v1, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 127
    .line 128
    add-int/2addr p1, p4

    .line 129
    add-int/2addr p3, v0

    .line 130
    invoke-virtual {v1, p4, v0, p1, p3}, Landroid/view/View;->layout(IIII)V

    .line 131
    .line 132
    .line 133
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 134
    .line 135
    iget-object p1, p1, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 136
    .line 137
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 138
    .line 139
    .line 140
    move-result p1

    .line 141
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 142
    .line 143
    iget-object p3, p3, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 144
    .line 145
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 146
    .line 147
    .line 148
    move-result p3

    .line 149
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->b:I

    .line 150
    .line 151
    add-int/2addr p2, v0

    .line 152
    sub-int v0, p5, p3

    .line 153
    .line 154
    div-int/lit8 v0, v0, 0x2

    .line 155
    .line 156
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 157
    .line 158
    iget-object v1, v1, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 159
    .line 160
    add-int/2addr p1, p2

    .line 161
    add-int/2addr p3, v0

    .line 162
    invoke-virtual {v1, p2, v0, p1, p3}, Landroid/view/View;->layout(IIII)V

    .line 163
    .line 164
    .line 165
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 166
    .line 167
    iget-object p2, p2, LC31/v;->d:Landroid/widget/TextView;

    .line 168
    .line 169
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 170
    .line 171
    .line 172
    move-result p2

    .line 173
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 174
    .line 175
    iget-object p3, p3, LC31/v;->d:Landroid/widget/TextView;

    .line 176
    .line 177
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 178
    .line 179
    .line 180
    move-result p3

    .line 181
    sub-int/2addr p4, p2

    .line 182
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->b:I

    .line 183
    .line 184
    sub-int/2addr p4, v0

    .line 185
    sub-int v0, p5, p3

    .line 186
    .line 187
    div-int/lit8 v0, v0, 0x2

    .line 188
    .line 189
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 190
    .line 191
    iget-object v1, v1, LC31/v;->d:Landroid/widget/TextView;

    .line 192
    .line 193
    add-int/2addr p2, p4

    .line 194
    add-int/2addr p3, v0

    .line 195
    invoke-virtual {v1, p4, v0, p2, p3}, Landroid/view/View;->layout(IIII)V

    .line 196
    .line 197
    .line 198
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 199
    .line 200
    iget-object p2, p2, LC31/v;->i:Landroid/widget/TextView;

    .line 201
    .line 202
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 203
    .line 204
    .line 205
    move-result p2

    .line 206
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 207
    .line 208
    iget-object p3, p3, LC31/v;->i:Landroid/widget/TextView;

    .line 209
    .line 210
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 211
    .line 212
    .line 213
    move-result p3

    .line 214
    iget p4, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->b:I

    .line 215
    .line 216
    add-int/2addr p1, p4

    .line 217
    sub-int/2addr p5, p3

    .line 218
    div-int/lit8 p5, p5, 0x2

    .line 219
    .line 220
    iget-object p4, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 221
    .line 222
    iget-object p4, p4, LC31/v;->i:Landroid/widget/TextView;

    .line 223
    .line 224
    add-int/2addr p2, p1

    .line 225
    add-int/2addr p3, p5

    .line 226
    invoke-virtual {p4, p1, p5, p2, p3}, Landroid/view/View;->layout(IIII)V

    .line 227
    .line 228
    .line 229
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    iget p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->b:I

    .line 5
    .line 6
    mul-int/lit8 p1, p1, 0x6

    .line 7
    .line 8
    iget p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->a:I

    .line 9
    .line 10
    mul-int/lit8 p2, p2, 0x2

    .line 11
    .line 12
    add-int/2addr p1, p2

    .line 13
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 14
    .line 15
    iget-object p2, p2, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 18
    .line 19
    .line 20
    move-result p2

    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 22
    .line 23
    iget-object v0, v0, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 24
    .line 25
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    add-int/2addr p2, v0

    .line 30
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 31
    .line 32
    iget-object v0, v0, LC31/v;->e:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 33
    .line 34
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 39
    .line 40
    iget-object v1, v1, LC31/v;->j:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 41
    .line 42
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    add-int/2addr v0, v1

    .line 47
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    sub-int/2addr v1, p2

    .line 52
    sub-int/2addr v1, v0

    .line 53
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 54
    .line 55
    iget-object p2, p2, LC31/v;->f:Lorg/xbet/uikit_sport/score/SportScore;

    .line 56
    .line 57
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredWidth()I

    .line 58
    .line 59
    .line 60
    move-result p2

    .line 61
    sub-int/2addr v1, p2

    .line 62
    sub-int/2addr v1, p1

    .line 63
    div-int/lit8 v1, v1, 0x2

    .line 64
    .line 65
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 66
    .line 67
    iget-object p1, p1, LC31/v;->d:Landroid/widget/TextView;

    .line 68
    .line 69
    const/high16 p2, 0x40000000    # 2.0f

    .line 70
    .line 71
    invoke-static {v1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 72
    .line 73
    .line 74
    move-result v0

    .line 75
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 76
    .line 77
    .line 78
    move-result v2

    .line 79
    invoke-static {v2, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 80
    .line 81
    .line 82
    move-result v2

    .line 83
    invoke-virtual {p1, v0, v2}, Landroid/view/View;->measure(II)V

    .line 84
    .line 85
    .line 86
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 87
    .line 88
    iget-object p1, p1, LC31/v;->i:Landroid/widget/TextView;

    .line 89
    .line 90
    invoke-static {v1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 91
    .line 92
    .line 93
    move-result v0

    .line 94
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 95
    .line 96
    .line 97
    move-result v1

    .line 98
    invoke-static {v1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 99
    .line 100
    .line 101
    move-result p2

    .line 102
    invoke-virtual {p1, v0, p2}, Landroid/view/View;->measure(II)V

    .line 103
    .line 104
    .line 105
    return-void
.end method

.method public final setFirstTeamLogo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->setFirstTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setFirstTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v0, v0, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 2
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v0, v0, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setFirstTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v1, v0, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setFirstTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v1, v0, LC31/v;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setFirstTeamName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 2
    .line 3
    iget-object v0, v0, LC31/v;->d:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setFirstVictoryIndicator(II)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 2
    .line 3
    iget-object v0, v0, LC31/v;->e:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-lez p1, :cond_0

    .line 7
    .line 8
    const/4 v2, 0x1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v2, 0x0

    .line 11
    :goto_0
    if-eqz v2, :cond_1

    .line 12
    .line 13
    goto :goto_1

    .line 14
    :cond_1
    const/16 v1, 0x8

    .line 15
    .line 16
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final setScore(Ljava/lang/CharSequence;)V
    .locals 1
    .param p1    # Ljava/lang/CharSequence;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 2
    .line 3
    iget-object v0, v0, LC31/v;->f:Lorg/xbet/uikit_sport/score/SportScore;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setScoreModel(Lorg/xbet/uikit_sport/score/a;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_sport/score/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 2
    .line 3
    iget-object v0, v0, LC31/v;->f:Lorg/xbet/uikit_sport/score/SportScore;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/score/SportScore;->setScore(Lorg/xbet/uikit_sport/score/a;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setSecondTeamLogo(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->setSecondTeamLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setSecondTeamLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v0, v0, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 2
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v0, v0, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setSecondTeamLogo(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v1, v0, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xe

    const/4 v7, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setSecondTeamLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 6
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    iget-object v1, v0, LC31/v;->h:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setSecondTeamName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 2
    .line 3
    iget-object v0, v0, LC31/v;->i:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setSecondVictoryIndicator(II)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleCyber;->c:LC31/v;

    .line 2
    .line 3
    iget-object v0, v0, LC31/v;->j:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-lez p1, :cond_0

    .line 7
    .line 8
    const/4 v2, 0x1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v2, 0x0

    .line 11
    :goto_0
    if-eqz v2, :cond_1

    .line 12
    .line 13
    goto :goto_1

    .line 14
    :cond_1
    const/16 v1, 0x8

    .line 15
    .line 16
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 20
    .line 21
    .line 22
    return-void
.end method
