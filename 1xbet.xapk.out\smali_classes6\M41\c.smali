.class public final LM41/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LH41/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u0018\u00002\u00020\u0001B\u0011\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J,\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\u0007\u001a\u00020\u00062\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u0008H\u0096\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "LM41/c;",
        "LH41/c;",
        "LL41/a;",
        "verificationRepository",
        "<init>",
        "(LL41/a;)V",
        "Lorg/xbet/verification/core/api/domain/models/VerificationType;",
        "verificationType",
        "",
        "",
        "params",
        "",
        "a",
        "(Lorg/xbet/verification/core/api/domain/models/VerificationType;Ljava/util/Map;)V",
        "LL41/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LL41/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LL41/a;)V
    .locals 0
    .param p1    # LL41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LM41/c;->a:LL41/a;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/verification/core/api/domain/models/VerificationType;Ljava/util/Map;)V
    .locals 1
    .param p1    # Lorg/xbet/verification/core/api/domain/models/VerificationType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/Map;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/verification/core/api/domain/models/VerificationType;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, LM41/c;->a:LL41/a;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, LL41/a;->b(Lorg/xbet/verification/core/api/domain/models/VerificationType;Ljava/util/Map;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
