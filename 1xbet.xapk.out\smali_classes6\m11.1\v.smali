.class public final synthetic Lm11/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:Lm11/H$j;

.field public final synthetic c:I

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;Lm11/H$j;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm11/v;->a:Landroidx/compose/ui/l;

    iput-object p2, p0, Lm11/v;->b:Lm11/H$j;

    iput p3, p0, Lm11/v;->c:I

    iput p4, p0, Lm11/v;->d:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lm11/v;->a:Landroidx/compose/ui/l;

    iget-object v1, p0, Lm11/v;->b:Lm11/H$j;

    iget v2, p0, Lm11/v;->c:I

    iget v3, p0, Lm11/v;->d:I

    move-object v4, p1

    check-cast v4, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    invoke-static/range {v0 .. v5}, Lm11/G;->j(Landroidx/compose/ui/l;Lm11/H$j;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
