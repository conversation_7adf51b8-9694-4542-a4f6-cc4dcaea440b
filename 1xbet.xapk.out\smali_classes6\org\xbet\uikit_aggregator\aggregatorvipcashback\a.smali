.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u001a)\u0010\n\u001a\u00020\t*\u00020\u00002\u0006\u0010\u0005\u001a\u00020\u00042\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006H\u0000\u00a2\u0006\u0004\u0008\n\u0010\u000b\u001a!\u0010\r\u001a\u00020\u000c*\u00020\u00002\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006H\u0000\u00a2\u0006\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lg31/h;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;",
        "c",
        "(Lg31/h;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;",
        "",
        "isVertical",
        "",
        "Lg31/b;",
        "levels",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;",
        "b",
        "(Lg31/h;ZLjava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;",
        "a",
        "(Lg31/h;Ljava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;",
        "uikit_aggregator_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lg31/h;Ljava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;
    .locals 9
    .param p0    # Lg31/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg31/h;",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;)",
            "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lg31/h;->a()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    const-string v0, ""

    .line 8
    .line 9
    :cond_0
    move-object v2, v0

    .line 10
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    const/4 v1, 0x0

    .line 19
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v4

    .line 23
    if-eqz v4, :cond_2

    .line 24
    .line 25
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v4

    .line 29
    check-cast v4, Lg31/b;

    .line 30
    .line 31
    invoke-virtual {v4}, Lg31/b;->c()I

    .line 32
    .line 33
    .line 34
    move-result v4

    .line 35
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 36
    .line 37
    .line 38
    move-result-object v5

    .line 39
    invoke-virtual {v5}, Lg31/b;->c()I

    .line 40
    .line 41
    .line 42
    move-result v5

    .line 43
    if-ne v4, v5, :cond_1

    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    add-int/lit8 v1, v1, 0x1

    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_2
    const/4 v1, -0x1

    .line 50
    :goto_1
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 59
    .line 60
    .line 61
    move-result v4

    .line 62
    add-int/lit8 v4, v4, -0x1

    .line 63
    .line 64
    const/4 v5, 0x0

    .line 65
    if-ge v1, v4, :cond_3

    .line 66
    .line 67
    if-ltz v1, :cond_3

    .line 68
    .line 69
    goto :goto_2

    .line 70
    :cond_3
    move-object v0, v5

    .line 71
    :goto_2
    if-eqz v0, :cond_4

    .line 72
    .line 73
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    add-int/lit8 v0, v0, 0x1

    .line 78
    .line 79
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    move-object v5, p1

    .line 84
    check-cast v5, Lg31/b;

    .line 85
    .line 86
    :cond_4
    move-object v4, v5

    .line 87
    invoke-virtual {p0}, Lg31/h;->c()J

    .line 88
    .line 89
    .line 90
    move-result-wide v5

    .line 91
    invoke-virtual {p0}, Lg31/h;->d()J

    .line 92
    .line 93
    .line 94
    move-result-wide v7

    .line 95
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;

    .line 96
    .line 97
    invoke-direct/range {v1 .. v8}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$a;-><init>(Ljava/lang/String;Lg31/b;Lg31/b;JJ)V

    .line 98
    .line 99
    .line 100
    return-object v1
.end method

.method public static final b(Lg31/h;ZLjava/util/List;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;
    .locals 11
    .param p0    # Lg31/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lg31/h;",
            "Z",
            "Ljava/util/List<",
            "Lg31/b;",
            ">;)",
            "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p2}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-interface {p2}, Ljava/util/Collection;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :cond_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-eqz v2, :cond_2

    .line 27
    .line 28
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v2

    .line 32
    check-cast v2, Lg31/b;

    .line 33
    .line 34
    invoke-virtual {v2}, Lg31/b;->c()I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    invoke-virtual {v3}, Lg31/b;->c()I

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-ne v2, v3, :cond_1

    .line 47
    .line 48
    goto :goto_1

    .line 49
    :cond_2
    :goto_0
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 54
    .line 55
    .line 56
    :goto_1
    invoke-interface {v0, p2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 60
    .line 61
    .line 62
    move-result-object p2

    .line 63
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    const/4 v1, 0x0

    .line 68
    const/4 v2, 0x0

    .line 69
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 70
    .line 71
    .line 72
    move-result v3

    .line 73
    if-eqz v3, :cond_4

    .line 74
    .line 75
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v3

    .line 79
    check-cast v3, Lg31/b;

    .line 80
    .line 81
    invoke-virtual {v3}, Lg31/b;->c()I

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 86
    .line 87
    .line 88
    move-result-object v4

    .line 89
    invoke-virtual {v4}, Lg31/b;->c()I

    .line 90
    .line 91
    .line 92
    move-result v4

    .line 93
    if-ne v3, v4, :cond_3

    .line 94
    .line 95
    goto :goto_3

    .line 96
    :cond_3
    add-int/lit8 v2, v2, 0x1

    .line 97
    .line 98
    goto :goto_2

    .line 99
    :cond_4
    const/4 v2, -0x1

    .line 100
    :goto_3
    new-instance v4, Ljava/util/ArrayList;

    .line 101
    .line 102
    const/16 v0, 0xa

    .line 103
    .line 104
    invoke-static {p2, v0}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 105
    .line 106
    .line 107
    move-result v0

    .line 108
    invoke-direct {v4, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 109
    .line 110
    .line 111
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 112
    .line 113
    .line 114
    move-result-object p2

    .line 115
    const/4 v0, 0x0

    .line 116
    :goto_4
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 117
    .line 118
    .line 119
    move-result v3

    .line 120
    if-eqz v3, :cond_7

    .line 121
    .line 122
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v3

    .line 126
    add-int/lit8 v5, v0, 0x1

    .line 127
    .line 128
    if-gez v0, :cond_5

    .line 129
    .line 130
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 131
    .line 132
    .line 133
    :cond_5
    check-cast v3, Lg31/b;

    .line 134
    .line 135
    new-instance v6, Lh31/b;

    .line 136
    .line 137
    if-le v0, v2, :cond_6

    .line 138
    .line 139
    const/4 v0, 0x1

    .line 140
    goto :goto_5

    .line 141
    :cond_6
    const/4 v0, 0x0

    .line 142
    :goto_5
    invoke-direct {v6, v3, v0}, Lh31/b;-><init>(Lg31/b;Z)V

    .line 143
    .line 144
    .line 145
    invoke-interface {v4, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 146
    .line 147
    .line 148
    move v0, v5

    .line 149
    goto :goto_4

    .line 150
    :cond_7
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 151
    .line 152
    .line 153
    move-result-object v5

    .line 154
    invoke-virtual {p0}, Lg31/h;->c()J

    .line 155
    .line 156
    .line 157
    move-result-wide v6

    .line 158
    invoke-virtual {p0}, Lg31/h;->d()J

    .line 159
    .line 160
    .line 161
    move-result-wide v8

    .line 162
    new-instance v3, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;

    .line 163
    .line 164
    move v10, p1

    .line 165
    invoke-direct/range {v3 .. v10}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;-><init>(Ljava/util/List;Lg31/b;JJZ)V

    .line 166
    .line 167
    .line 168
    return-object v3
.end method

.method public static final c(Lg31/h;)Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;
    .locals 9
    .param p0    # Lg31/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;

    .line 2
    .line 3
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lg31/b;->e()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {p0}, Lg31/h;->a()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    if-nez v2, :cond_0

    .line 16
    .line 17
    const-string v2, ""

    .line 18
    .line 19
    :cond_0
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    invoke-virtual {v3}, Lg31/b;->a()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-virtual {p0}, Lg31/h;->b()Lg31/b;

    .line 28
    .line 29
    .line 30
    move-result-object v4

    .line 31
    invoke-virtual {v4}, Lg31/b;->d()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    invoke-virtual {p0}, Lg31/h;->c()J

    .line 36
    .line 37
    .line 38
    move-result-wide v5

    .line 39
    invoke-virtual {p0}, Lg31/h;->d()J

    .line 40
    .line 41
    .line 42
    move-result-wide v7

    .line 43
    invoke-direct/range {v0 .. v8}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$c;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;JJ)V

    .line 44
    .line 45
    .line 46
    return-object v0
.end method
