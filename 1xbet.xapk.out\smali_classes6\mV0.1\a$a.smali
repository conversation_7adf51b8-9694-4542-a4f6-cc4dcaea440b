.class public final LmV0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LmV0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LmV0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LmV0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LmV0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;ILf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)LmV0/c;
    .locals 23

    .line 1
    invoke-static/range {p1 .. p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static/range {p2 .. p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    invoke-static/range {p12 .. p12}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    invoke-static/range {p13 .. p13}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    invoke-static/range {p14 .. p14}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    invoke-static/range {p15 .. p15}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    invoke-static/range {p16 .. p16}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    invoke-static/range {p17 .. p17}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    invoke-static/range {p18 .. p18}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    invoke-static/range {p19 .. p19}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    invoke-static/range {p20 .. p20}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    new-instance v1, LmV0/a$b;

    .line 66
    .line 67
    invoke-static/range {p5 .. p5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 68
    .line 69
    .line 70
    move-result-object v6

    .line 71
    const/16 v22, 0x0

    .line 72
    .line 73
    move-object/from16 v2, p1

    .line 74
    .line 75
    move-object/from16 v3, p2

    .line 76
    .line 77
    move-object/from16 v4, p3

    .line 78
    .line 79
    move-object/from16 v5, p4

    .line 80
    .line 81
    move-object/from16 v7, p6

    .line 82
    .line 83
    move-object/from16 v8, p7

    .line 84
    .line 85
    move-object/from16 v9, p8

    .line 86
    .line 87
    move-object/from16 v10, p9

    .line 88
    .line 89
    move-object/from16 v11, p10

    .line 90
    .line 91
    move-object/from16 v12, p11

    .line 92
    .line 93
    move-object/from16 v13, p12

    .line 94
    .line 95
    move-object/from16 v14, p13

    .line 96
    .line 97
    move-object/from16 v15, p14

    .line 98
    .line 99
    move-object/from16 v16, p15

    .line 100
    .line 101
    move-object/from16 v17, p16

    .line 102
    .line 103
    move-object/from16 v18, p17

    .line 104
    .line 105
    move-object/from16 v19, p18

    .line 106
    .line 107
    move-object/from16 v20, p19

    .line 108
    .line 109
    move-object/from16 v21, p20

    .line 110
    .line 111
    invoke-direct/range {v1 .. v22}, LmV0/a$b;-><init>(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;LmV0/b;)V

    .line 112
    .line 113
    .line 114
    return-object v1
.end method
