.class public final synthetic LNZ0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/chips/Chip;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/chips/Chip;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/b;->a:Lorg/xbet/uikit/components/chips/Chip;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LNZ0/b;->a:Lorg/xbet/uikit/components/chips/Chip;

    invoke-static {v0}, Lorg/xbet/uikit/components/chips/Chip;->a(Lorg/xbet/uikit/components/chips/Chip;)Z

    move-result v0

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method
