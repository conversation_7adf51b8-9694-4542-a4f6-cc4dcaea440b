.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.delegates.AggregatorPopularFragmentDelegateImpl$setup$3"
    f = "AggregatorPopularFragmentDelegateImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lb81/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lb81/a;",
        "singleEventState",
        "",
        "<anonymous>",
        "(Lb81/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $fragment:Landroidx/fragment/app/Fragment;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;",
            "Landroidx/fragment/app/Fragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->$fragment:Landroidx/fragment/app/Fragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->$fragment:Landroidx/fragment/app/Fragment;

    invoke-direct {v0, v1, v2, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(Lb81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lb81/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Lb81/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->invoke(Lb81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 13

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lb81/a;

    .line 14
    .line 15
    instance-of v0, p1, Lb81/a$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;

    .line 20
    .line 21
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;)LzX0/k;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    new-instance v2, Ly01/g;

    .line 26
    .line 27
    sget-object v3, Ly01/i$c;->a:Ly01/i$c;

    .line 28
    .line 29
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->$fragment:Landroidx/fragment/app/Fragment;

    .line 30
    .line 31
    check-cast p1, Lb81/a$a;

    .line 32
    .line 33
    invoke-virtual {p1}, Lb81/a$a;->a()I

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    invoke-virtual {v0, p1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    const/16 v9, 0x3c

    .line 42
    .line 43
    const/4 v10, 0x0

    .line 44
    const/4 v5, 0x0

    .line 45
    const/4 v6, 0x0

    .line 46
    const/4 v7, 0x0

    .line 47
    const/4 v8, 0x0

    .line 48
    invoke-direct/range {v2 .. v10}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 49
    .line 50
    .line 51
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/AggregatorPopularFragmentDelegateImpl$setup$3;->$fragment:Landroidx/fragment/app/Fragment;

    .line 52
    .line 53
    const/16 v11, 0x1fc

    .line 54
    .line 55
    const/4 v12, 0x0

    .line 56
    const/4 v4, 0x0

    .line 57
    const/4 v6, 0x0

    .line 58
    const/4 v7, 0x0

    .line 59
    const/4 v9, 0x0

    .line 60
    invoke-static/range {v1 .. v12}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 61
    .line 62
    .line 63
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 64
    .line 65
    return-object p1

    .line 66
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 67
    .line 68
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 69
    .line 70
    .line 71
    throw p1

    .line 72
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 73
    .line 74
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 75
    .line 76
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    throw p1
.end method
