.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LZ31/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u00085\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u0010H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\u0014H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u0019\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ;\u0010#\u001a\u00020\r*\u00020\u00102\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u001d2\u0006\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u001dH\u0002\u00a2\u0006\u0004\u0008#\u0010$R\u0016\u0010&\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010%R\u0016\u0010\'\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008#\u0010%R\u0016\u0010)\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008(\u0010%R\u0016\u0010+\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010%R\u0016\u0010-\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008,\u0010%R\u0016\u0010/\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010%R\u0016\u00101\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00080\u0010%R\u0016\u00103\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00082\u0010%R\u0016\u00105\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u0010%R\u0016\u00107\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00086\u0010%R\u0016\u00109\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00088\u0010%R\u0016\u0010;\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008:\u0010%R\u0014\u0010>\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010@\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010=R\u0014\u0010C\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010E\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010BR\u0014\u0010G\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010BR\u0016\u0010J\u001a\u00020\u001b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0016\u0010L\u001a\u00020\u001b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008K\u0010IR\u0016\u0010N\u001a\u00020\u001b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008M\u0010IR\u0016\u0010P\u001a\u00020\u001b8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008O\u0010IR\u0016\u0010R\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Q\u0010BR\u0016\u0010T\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008S\u0010B\u00a8\u0006U"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;",
        "Landroid/widget/FrameLayout;",
        "LZ31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "totalWidth",
        "a",
        "(I)V",
        "",
        "text",
        "",
        "posX",
        "posY",
        "Landroid/text/TextPaint;",
        "textPaint",
        "textWidth",
        "b",
        "(Landroid/graphics/Canvas;Ljava/lang/String;FFLandroid/text/TextPaint;F)V",
        "F",
        "firstMainScoreTextLeftBound",
        "secondMainScoreTextLeftBound",
        "c",
        "firstMainScoreTextBottomBound",
        "d",
        "secondMainScoreTextBottomBound",
        "e",
        "firstOversScoreTextLeftBound",
        "f",
        "secondOversScoreTextLeftBound",
        "g",
        "firstOversScoreTextBottomBound",
        "h",
        "secondOversScoreTextBottomBound",
        "i",
        "firstTeamMainScoreWidth",
        "j",
        "secondTeamMainScoreWidth",
        "k",
        "firstTeamOversScoreWidth",
        "l",
        "secondTeamOversScoreWidth",
        "m",
        "Landroid/text/TextPaint;",
        "teamMainScoreTextPaint",
        "n",
        "teamOversScoreTextPaint",
        "o",
        "I",
        "scoreItemHeight",
        "p",
        "mainAndOversBetweenMargin",
        "q",
        "totalHeight",
        "r",
        "Ljava/lang/String;",
        "firstTeamMainScore",
        "s",
        "firstTeamOversScore",
        "t",
        "secondTeamMainScore",
        "u",
        "secondTeamOversScore",
        "v",
        "firstTeamScoreColor",
        "w",
        "secondTeamScoreColor",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:F

.field public b:F

.field public c:F

.field public d:F

.field public e:F

.field public f:F

.field public g:F

.field public h:F

.field public i:F

.field public j:F

.field public k:F

.field public l:F

.field public final m:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:I

.field public final p:I

.field public final q:I

.field public r:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public s:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public t:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public u:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public v:I

.field public w:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2}, Landroid/text/TextPaint;-><init>()V

    const/4 p3, 0x1

    .line 6
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 7
    sget-object v0, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 8
    sget v1, LlZ0/n;->TextStyle_Caption_Bold_L_TextPrimary:I

    invoke-static {p2, p1, v1}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 9
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 10
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2}, Landroid/text/TextPaint;-><init>()V

    .line 11
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 12
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 13
    sget p3, LlZ0/n;->TextStyle_Caption_Bold_M_TextPrimary:I

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 14
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_32:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->o:I

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_2:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->p:I

    mul-int/lit8 p1, p1, 0x2

    .line 17
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->q:I

    .line 18
    const-string p1, ""

    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->r:Ljava/lang/String;

    .line 19
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->s:Ljava/lang/String;

    .line 20
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->t:Ljava/lang/String;

    .line 21
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->u:Ljava/lang/String;

    .line 22
    sget-object p1, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->DEFAULT:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->getColorResAttr()I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->v:I

    .line 23
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->getColorResAttr()I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->w:I

    const/4 p1, 0x0

    .line 24
    invoke-virtual {p0, p1}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final a(I)V
    .locals 4

    .line 1
    int-to-float p1, p1

    .line 2
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->k:F

    .line 3
    .line 4
    sub-float v0, p1, v0

    .line 5
    .line 6
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->e:F

    .line 7
    .line 8
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->p:I

    .line 9
    .line 10
    int-to-float v1, v1

    .line 11
    sub-float/2addr v0, v1

    .line 12
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->i:F

    .line 13
    .line 14
    sub-float/2addr v0, v1

    .line 15
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->a:F

    .line 16
    .line 17
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->o:I

    .line 18
    .line 19
    const/4 v1, 0x2

    .line 20
    div-int/2addr v0, v1

    .line 21
    int-to-float v0, v0

    .line 22
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 23
    .line 24
    invoke-virtual {v2}, Landroid/graphics/Paint;->getTextSize()F

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    int-to-float v3, v1

    .line 29
    div-float/2addr v2, v3

    .line 30
    add-float/2addr v0, v2

    .line 31
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->c:F

    .line 32
    .line 33
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->o:I

    .line 34
    .line 35
    div-int/2addr v0, v1

    .line 36
    int-to-float v0, v0

    .line 37
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 38
    .line 39
    invoke-virtual {v1}, Landroid/graphics/Paint;->getTextSize()F

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    div-float/2addr v1, v3

    .line 44
    add-float/2addr v0, v1

    .line 45
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->g:F

    .line 46
    .line 47
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->l:F

    .line 48
    .line 49
    sub-float/2addr p1, v0

    .line 50
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->f:F

    .line 51
    .line 52
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->p:I

    .line 53
    .line 54
    int-to-float v0, v0

    .line 55
    sub-float/2addr p1, v0

    .line 56
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->j:F

    .line 57
    .line 58
    sub-float/2addr p1, v0

    .line 59
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->b:F

    .line 60
    .line 61
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->o:I

    .line 62
    .line 63
    div-int/lit8 v0, p1, 0x2

    .line 64
    .line 65
    add-int/2addr p1, v0

    .line 66
    int-to-float p1, p1

    .line 67
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 68
    .line 69
    invoke-virtual {v0}, Landroid/graphics/Paint;->getTextSize()F

    .line 70
    .line 71
    .line 72
    move-result v0

    .line 73
    div-float/2addr v0, v3

    .line 74
    add-float/2addr p1, v0

    .line 75
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->d:F

    .line 76
    .line 77
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->o:I

    .line 78
    .line 79
    div-int/lit8 v0, p1, 0x2

    .line 80
    .line 81
    add-int/2addr p1, v0

    .line 82
    int-to-float p1, p1

    .line 83
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 84
    .line 85
    invoke-virtual {v0}, Landroid/graphics/Paint;->getTextSize()F

    .line 86
    .line 87
    .line 88
    move-result v0

    .line 89
    div-float/2addr v0, v3

    .line 90
    add-float/2addr p1, v0

    .line 91
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->h:F

    .line 92
    .line 93
    return-void
.end method

.method public final b(Landroid/graphics/Canvas;Ljava/lang/String;FFLandroid/text/TextPaint;F)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    int-to-float v0, v0

    .line 13
    sub-float/2addr v0, p6

    .line 14
    sub-float p3, v0, p3

    .line 15
    .line 16
    :cond_0
    invoke-virtual {p1, p2, p3, p4, p5}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 7
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->v:I

    .line 4
    .line 5
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 6
    .line 7
    .line 8
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 9
    .line 10
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->v:I

    .line 11
    .line 12
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 13
    .line 14
    .line 15
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->r:Ljava/lang/String;

    .line 16
    .line 17
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->a:F

    .line 18
    .line 19
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->c:F

    .line 20
    .line 21
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 22
    .line 23
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->i:F

    .line 24
    .line 25
    move-object v0, p0

    .line 26
    move-object v1, p1

    .line 27
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->b(Landroid/graphics/Canvas;Ljava/lang/String;FFLandroid/text/TextPaint;F)V

    .line 28
    .line 29
    .line 30
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->s:Ljava/lang/String;

    .line 31
    .line 32
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->e:F

    .line 33
    .line 34
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->g:F

    .line 35
    .line 36
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 37
    .line 38
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->k:F

    .line 39
    .line 40
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->b(Landroid/graphics/Canvas;Ljava/lang/String;FFLandroid/text/TextPaint;F)V

    .line 41
    .line 42
    .line 43
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 44
    .line 45
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->w:I

    .line 46
    .line 47
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 48
    .line 49
    .line 50
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 51
    .line 52
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->w:I

    .line 53
    .line 54
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 55
    .line 56
    .line 57
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->t:Ljava/lang/String;

    .line 58
    .line 59
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->b:F

    .line 60
    .line 61
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->d:F

    .line 62
    .line 63
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 64
    .line 65
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->j:F

    .line 66
    .line 67
    move-object v1, p1

    .line 68
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->b(Landroid/graphics/Canvas;Ljava/lang/String;FFLandroid/text/TextPaint;F)V

    .line 69
    .line 70
    .line 71
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->u:Ljava/lang/String;

    .line 72
    .line 73
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->f:F

    .line 74
    .line 75
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->h:F

    .line 76
    .line 77
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 78
    .line 79
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->l:F

    .line 80
    .line 81
    invoke-virtual/range {v0 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->b(Landroid/graphics/Canvas;Ljava/lang/String;FFLandroid/text/TextPaint;F)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 2
    .line 3
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->r:Ljava/lang/String;

    .line 4
    .line 5
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->i:F

    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 12
    .line 13
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->s:Ljava/lang/String;

    .line 14
    .line 15
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->k:F

    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->m:Landroid/text/TextPaint;

    .line 22
    .line 23
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->t:Ljava/lang/String;

    .line 24
    .line 25
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->j:F

    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->n:Landroid/text/TextPaint;

    .line 32
    .line 33
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->u:Ljava/lang/String;

    .line 34
    .line 35
    invoke-virtual {p1, p2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->l:F

    .line 40
    .line 41
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->i:F

    .line 42
    .line 43
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->k:F

    .line 44
    .line 45
    add-float/2addr p2, v0

    .line 46
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->p:I

    .line 47
    .line 48
    int-to-float v1, v0

    .line 49
    add-float/2addr p2, v1

    .line 50
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->j:F

    .line 51
    .line 52
    add-float/2addr v1, p1

    .line 53
    int-to-float p1, v0

    .line 54
    add-float/2addr v1, p1

    .line 55
    invoke-static {p2, v1}, Ljava/lang/Math;->max(FF)F

    .line 56
    .line 57
    .line 58
    move-result p1

    .line 59
    float-to-int p1, p1

    .line 60
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->a(I)V

    .line 61
    .line 62
    .line 63
    const/high16 p2, 0x40000000    # 2.0f

    .line 64
    .line 65
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 66
    .line 67
    .line 68
    move-result p1

    .line 69
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->q:I

    .line 70
    .line 71
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 72
    .line 73
    .line 74
    move-result p2

    .line 75
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 76
    .line 77
    .line 78
    return-void
.end method

.method public setScoreUiModel(LX31/a;)V
    .locals 4
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    check-cast p1, LX31/a$a;

    .line 7
    .line 8
    goto :goto_0

    .line 9
    :cond_0
    move-object p1, v1

    .line 10
    :goto_0
    if-nez p1, :cond_1

    .line 11
    .line 12
    return-void

    .line 13
    :cond_1
    invoke-virtual {p1}, LX31/a$a;->a()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->r:Ljava/lang/String;

    .line 18
    .line 19
    invoke-virtual {p1}, LX31/a$a;->b()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->s:Ljava/lang/String;

    .line 24
    .line 25
    invoke-virtual {p1}, LX31/a$a;->d()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->t:Ljava/lang/String;

    .line 30
    .line 31
    invoke-virtual {p1}, LX31/a$a;->e()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->u:Ljava/lang/String;

    .line 36
    .line 37
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-virtual {p1}, LX31/a$a;->c()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->getColorResAttr()I

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    const/4 v3, 0x2

    .line 50
    invoke-static {v0, v2, v1, v3, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 51
    .line 52
    .line 53
    move-result v0

    .line 54
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->v:I

    .line 55
    .line 56
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-virtual {p1}, LX31/a$a;->f()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;->getColorResAttr()I

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    invoke-static {v0, p1, v1, v3, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 69
    .line 70
    .line 71
    move-result p1

    .line 72
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/CricketScoreView;->w:I

    .line 73
    .line 74
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 75
    .line 76
    .line 77
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 78
    .line 79
    .line 80
    return-void
.end method
