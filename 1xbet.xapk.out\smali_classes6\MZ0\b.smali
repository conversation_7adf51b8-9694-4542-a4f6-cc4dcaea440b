.class public final synthetic LMZ0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lc01/m;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/cells/right/CellRightListCheckBox;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/cells/right/CellRightListCheckBox;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LMZ0/b;->a:Lorg/xbet/uikit/components/cells/right/CellRightListCheckBox;

    return-void
.end method


# virtual methods
.method public final a(Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;Z)V
    .locals 1

    .line 1
    iget-object v0, p0, LMZ0/b;->a:Lorg/xbet/uikit/components/cells/right/CellRightListCheckBox;

    invoke-static {v0, p1, p2}, Lorg/xbet/uikit/components/cells/right/CellRightListCheckBox;->c(Lorg/xbet/uikit/components/cells/right/CellRightListCheckBox;Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;Z)V

    return-void
.end method
