.class public final Lo31/d$b;
.super Landroidx/recyclerview/widget/RecyclerView$D;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lo31/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0006\u0010\u0007\u001a\u0004\u0008\u0006\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lo31/d$b;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;",
        "bcItem",
        "<init>",
        "(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;)V",
        "e",
        "Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;",
        "()Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final e:Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1}, Landroidx/recyclerview/widget/RecyclerView$D;-><init>(Landroid/view/View;)V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lo31/d$b;->e:Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final e()Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lo31/d$b;->e:Lorg/xbet/uikit_sport/championshipcardcollection/items/ChampionshipCardCollectionItem;

    .line 2
    .line 3
    return-object v0
.end method
