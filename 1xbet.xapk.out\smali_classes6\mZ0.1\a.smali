.class public final synthetic LmZ0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Landroid/content/Context;

.field public final synthetic b:Lorg/xbet/uikit/components/accordion/Accordion;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;Lorg/xbet/uikit/components/accordion/Accordion;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LmZ0/a;->a:Landroid/content/Context;

    iput-object p2, p0, LmZ0/a;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LmZ0/a;->a:Landroid/content/Context;

    iget-object v1, p0, LmZ0/a;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    check-cast p1, Landroid/content/res/TypedArray;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/accordion/Accordion;->a(Landroid/content/Context;Lorg/xbet/uikit/components/accordion/Accordion;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
