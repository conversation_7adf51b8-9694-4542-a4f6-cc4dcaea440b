.class public final Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements LE31/j;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\u001d\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J!\u0010\u000e\u001a\u00020\r2\u0008\u0010\n\u001a\u0004\u0018\u00010\t2\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ!\u0010\u0010\u001a\u00020\r2\u0008\u0010\n\u001a\u0004\u0018\u00010\t2\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u000b\u00a2\u0006\u0004\u0008\u0010\u0010\u000fJ\u0015\u0010\u0013\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0015\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\u0004\u0008\u0015\u0010\u0014R\u0014\u0010\u0019\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010\u0018\u00a8\u0006\u001a"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "LE31/j;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "text",
        "Lorg/xbet/uikit/core/eventcard/top/TeamNumber;",
        "teamNumber",
        "",
        "setStartTag",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V",
        "setEndTag",
        "Landroid/view/View$OnClickListener;",
        "click",
        "setStartTagOnClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "setEndTagOnClickListener",
        "LC31/b;",
        "a",
        "LC31/b;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/b;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/b;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->a:LC31/b;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic setEndTag$default(Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    sget-object p2, Lorg/xbet/uikit/core/eventcard/top/TeamNumber;->None:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    .line 6
    .line 7
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->setEndTag(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static synthetic setStartTag$default(Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    sget-object p2, Lorg/xbet/uikit/core/eventcard/top/TeamNumber;->None:Lorg/xbet/uikit/core/eventcard/top/TeamNumber;

    .line 6
    .line 7
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->setStartTag(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final setEndTag(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/top/TeamNumber;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->a:LC31/b;

    .line 2
    .line 3
    iget-object v0, v0, LC31/b;->b:Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;->setText(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setEndTagOnClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->a:LC31/b;

    .line 2
    .line 3
    iget-object v0, v0, LC31/b;->c:Landroid/widget/FrameLayout;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setStartTag(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/top/TeamNumber;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->a:LC31/b;

    .line 2
    .line 3
    iget-object v0, v0, LC31/b;->f:Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeaderTag;->setText(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/top/TeamNumber;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setStartTagOnClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1
    .param p1    # Landroid/view/View$OnClickListener;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/top/BetConstructorHeader;->a:LC31/b;

    .line 2
    .line 3
    iget-object v0, v0, LC31/b;->d:Landroid/widget/FrameLayout;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
