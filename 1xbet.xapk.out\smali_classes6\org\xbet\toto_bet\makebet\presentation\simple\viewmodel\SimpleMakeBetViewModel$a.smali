.class public interface abstract Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$c;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$h;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001:\u0008\u0002\u0003\u0004\u0005\u0006\u0007\u0008\t\u0082\u0001\u0008\n\u000b\u000c\r\u000e\u000f\u0010\u0011\u00a8\u0006\u0012"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
        "",
        "a",
        "h",
        "f",
        "e",
        "g",
        "d",
        "c",
        "b",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$c;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$h;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
