.class public final LO31/n;
.super Landroidx/recyclerview/widget/s;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LO31/n$a;,
        LO31/n$b;,
        LO31/n$c;,
        LO31/n$d;,
        LO31/n$e;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/s<",
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u000e\n\u0002\u0008\u0010\u0008\u0001\u0018\u0000 $2\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0004%&\'(B\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0017\u0010\u0008\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u00032\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000c\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001f\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0014\u001a\u00020\u00102\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0014\u0010\u0015R@\u0010 \u001a \u0012\u0004\u0012\u00020\u0017\u0012\u0006\u0012\u0004\u0018\u00010\u0018\u0012\u0006\u0012\u0004\u0018\u00010\u0019\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u00168\u0000@\u0000X\u0080\u000e\u00a2\u0006\u0012\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001d\"\u0004\u0008\u001e\u0010\u001fR\u0016\u0010#\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008!\u0010\"\u00a8\u0006)"
    }
    d2 = {
        "LO31/n;",
        "Landroidx/recyclerview/widget/s;",
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        "Landroidx/recyclerview/widget/RecyclerView$D;",
        "<init>",
        "()V",
        "",
        "position",
        "getItemViewType",
        "(I)I",
        "Landroid/view/ViewGroup;",
        "parent",
        "viewType",
        "onCreateViewHolder",
        "(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;",
        "holder",
        "",
        "onBindViewHolder",
        "(Landroidx/recyclerview/widget/RecyclerView$D;I)V",
        "style",
        "z",
        "(I)V",
        "Lkotlin/Function3;",
        "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
        "",
        "",
        "f",
        "LOc/n;",
        "getClickListener$uikit_sport_release",
        "()LOc/n;",
        "y",
        "(LOc/n;)V",
        "clickListener",
        "g",
        "I",
        "defStyleAttr",
        "h",
        "d",
        "a",
        "b",
        "c",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:LO31/n$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final i:I


# instance fields
.field public f:LOc/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/n<",
            "-",
            "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public g:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, LO31/n$c;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, LO31/n$c;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, LO31/n;->h:LO31/n$c;

    .line 8
    .line 9
    const/16 v0, 0x8

    .line 10
    .line 11
    sput v0, LO31/n;->i:I

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, LO31/n;->h:LO31/n$c;

    .line 2
    .line 3
    invoke-direct {p0, v0}, Landroidx/recyclerview/widget/s;-><init>(Landroidx/recyclerview/widget/i$f;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public static synthetic s(LO31/n;Lorg/xbet/uikit_sport/sport_collection/b;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LO31/n;->v(LO31/n;Lorg/xbet/uikit_sport/sport_collection/b;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t(LO31/n;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LO31/n;->w(LO31/n;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u(LO31/n;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LO31/n;->x(LO31/n;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final v(LO31/n;Lorg/xbet/uikit_sport/sport_collection/b;Landroid/view/View;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object p0, p0, LO31/n;->f:LOc/n;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    sget-object p2, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->SPORT:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 6
    .line 7
    check-cast p1, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 8
    .line 9
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_collection/b$c;->b()LP31/j;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, LP31/j;->b()J

    .line 14
    .line 15
    .line 16
    move-result-wide v0

    .line 17
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_collection/b$c;->b()LP31/j;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-virtual {p1}, LP31/j;->c()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-interface {p0, p2, v0, p1}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 33
    .line 34
    return-object p0
.end method

.method public static final w(LO31/n;Landroid/view/View;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, LO31/n;->f:LOc/n;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->ALL:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    invoke-interface {p0, p1, v0, v0}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final x(LO31/n;Landroid/view/View;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, LO31/n;->f:LOc/n;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->FILTER:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    invoke-interface {p0, p1, v0, v0}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    :cond_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public getItemViewType(I)I
    .locals 1

    .line 1
    invoke-virtual {p0, p1}, Landroidx/recyclerview/widget/s;->o(I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lorg/xbet/uikit_sport/sport_collection/b;

    .line 6
    .line 7
    instance-of v0, p1, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->SPORT:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 12
    .line 13
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    return p1

    .line 18
    :cond_0
    instance-of v0, p1, Lorg/xbet/uikit_sport/sport_collection/b$a;

    .line 19
    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->ALL:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 23
    .line 24
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    return p1

    .line 29
    :cond_1
    instance-of p1, p1, Lorg/xbet/uikit_sport/sport_collection/b$b;

    .line 30
    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->FILTER:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 34
    .line 35
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 36
    .line 37
    .line 38
    move-result p1

    .line 39
    return p1

    .line 40
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 41
    .line 42
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 43
    .line 44
    .line 45
    throw p1
.end method

.method public onBindViewHolder(Landroidx/recyclerview/widget/RecyclerView$D;I)V
    .locals 4
    .param p1    # Landroidx/recyclerview/widget/RecyclerView$D;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p2}, Landroidx/recyclerview/widget/s;->o(I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/b;

    .line 6
    .line 7
    instance-of v0, p2, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 8
    .line 9
    const/4 v1, 0x1

    .line 10
    const/4 v2, 0x0

    .line 11
    if-eqz v0, :cond_2

    .line 12
    .line 13
    instance-of v0, p1, LO31/n$d;

    .line 14
    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    move-object v0, p1

    .line 18
    check-cast v0, LO31/n$d;

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    move-object v0, v2

    .line 22
    :goto_0
    if-eqz v0, :cond_1

    .line 23
    .line 24
    move-object v3, p2

    .line 25
    check-cast v3, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 26
    .line 27
    invoke-virtual {v3}, Lorg/xbet/uikit_sport/sport_collection/b$c;->b()LP31/j;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    invoke-virtual {v0, v3}, LO31/n$d;->d(LP31/j;)V

    .line 32
    .line 33
    .line 34
    :cond_1
    iget-object p1, p1, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 35
    .line 36
    new-instance v0, LO31/k;

    .line 37
    .line 38
    invoke-direct {v0, p0, p2}, LO31/k;-><init>(LO31/n;Lorg/xbet/uikit_sport/sport_collection/b;)V

    .line 39
    .line 40
    .line 41
    invoke-static {p1, v2, v0, v1, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 42
    .line 43
    .line 44
    return-void

    .line 45
    :cond_2
    instance-of v0, p2, Lorg/xbet/uikit_sport/sport_collection/b$a;

    .line 46
    .line 47
    if-eqz v0, :cond_5

    .line 48
    .line 49
    instance-of v0, p1, LO31/n$a;

    .line 50
    .line 51
    if-eqz v0, :cond_3

    .line 52
    .line 53
    move-object v0, p1

    .line 54
    check-cast v0, LO31/n$a;

    .line 55
    .line 56
    goto :goto_1

    .line 57
    :cond_3
    move-object v0, v2

    .line 58
    :goto_1
    if-eqz v0, :cond_4

    .line 59
    .line 60
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/b$a;

    .line 61
    .line 62
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/sport_collection/b$a;->b()LP31/a;

    .line 63
    .line 64
    .line 65
    move-result-object p2

    .line 66
    invoke-virtual {v0, p2}, LO31/n$a;->d(LP31/a;)V

    .line 67
    .line 68
    .line 69
    :cond_4
    iget-object p1, p1, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 70
    .line 71
    new-instance p2, LO31/l;

    .line 72
    .line 73
    invoke-direct {p2, p0}, LO31/l;-><init>(LO31/n;)V

    .line 74
    .line 75
    .line 76
    invoke-static {p1, v2, p2, v1, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 77
    .line 78
    .line 79
    return-void

    .line 80
    :cond_5
    instance-of v0, p2, Lorg/xbet/uikit_sport/sport_collection/b$b;

    .line 81
    .line 82
    if-eqz v0, :cond_8

    .line 83
    .line 84
    instance-of v0, p1, LO31/n$b;

    .line 85
    .line 86
    if-eqz v0, :cond_6

    .line 87
    .line 88
    move-object v0, p1

    .line 89
    check-cast v0, LO31/n$b;

    .line 90
    .line 91
    goto :goto_2

    .line 92
    :cond_6
    move-object v0, v2

    .line 93
    :goto_2
    if-eqz v0, :cond_7

    .line 94
    .line 95
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/b$b;

    .line 96
    .line 97
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/sport_collection/b$b;->b()LP31/b;

    .line 98
    .line 99
    .line 100
    move-result-object p2

    .line 101
    invoke-virtual {v0, p2}, LO31/n$b;->d(LP31/b;)V

    .line 102
    .line 103
    .line 104
    :cond_7
    iget-object p1, p1, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 105
    .line 106
    new-instance p2, LO31/m;

    .line 107
    .line 108
    invoke-direct {p2, p0}, LO31/m;-><init>(LO31/n;)V

    .line 109
    .line 110
    .line 111
    invoke-static {p1, v2, p2, v1, v2}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 112
    .line 113
    .line 114
    return-void

    .line 115
    :cond_8
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 116
    .line 117
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 118
    .line 119
    .line 120
    throw p1
.end method

.method public onCreateViewHolder(Landroid/view/ViewGroup;I)Landroidx/recyclerview/widget/RecyclerView$D;
    .locals 7
    .param p1    # Landroid/view/ViewGroup;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 10
    .line 11
    sget-object v0, LO31/n$e;->a:[I

    .line 12
    .line 13
    invoke-virtual {p2}, Ljava/lang/Enum;->ordinal()I

    .line 14
    .line 15
    .line 16
    move-result p2

    .line 17
    aget p2, v0, p2

    .line 18
    .line 19
    const/4 v0, 0x1

    .line 20
    if-eq p2, v0, :cond_2

    .line 21
    .line 22
    const/4 v0, 0x2

    .line 23
    if-eq p2, v0, :cond_1

    .line 24
    .line 25
    const/4 v0, 0x3

    .line 26
    if-ne p2, v0, :cond_0

    .line 27
    .line 28
    new-instance v1, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;

    .line 29
    .line 30
    new-instance v2, Landroid/view/ContextThemeWrapper;

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iget p2, p0, LO31/n;->g:I

    .line 37
    .line 38
    invoke-direct {v2, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 39
    .line 40
    .line 41
    const/4 v5, 0x6

    .line 42
    const/4 v6, 0x0

    .line 43
    const/4 v3, 0x0

    .line 44
    const/4 v4, 0x0

    .line 45
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 46
    .line 47
    .line 48
    new-instance p1, LO31/n$b;

    .line 49
    .line 50
    invoke-direct {p1, v1}, LO31/n$b;-><init>(Landroid/view/View;)V

    .line 51
    .line 52
    .line 53
    return-object p1

    .line 54
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 55
    .line 56
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 57
    .line 58
    .line 59
    throw p1

    .line 60
    :cond_1
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;

    .line 61
    .line 62
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 63
    .line 64
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    iget p2, p0, LO31/n;->g:I

    .line 69
    .line 70
    invoke-direct {v1, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 71
    .line 72
    .line 73
    const/4 v4, 0x6

    .line 74
    const/4 v5, 0x0

    .line 75
    const/4 v2, 0x0

    .line 76
    const/4 v3, 0x0

    .line 77
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 78
    .line 79
    .line 80
    new-instance p1, LO31/n$a;

    .line 81
    .line 82
    invoke-direct {p1, v0}, LO31/n$a;-><init>(Landroid/view/View;)V

    .line 83
    .line 84
    .line 85
    return-object p1

    .line 86
    :cond_2
    new-instance v1, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;

    .line 87
    .line 88
    new-instance v2, Landroid/view/ContextThemeWrapper;

    .line 89
    .line 90
    invoke-virtual {p1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    iget p2, p0, LO31/n;->g:I

    .line 95
    .line 96
    invoke-direct {v2, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 97
    .line 98
    .line 99
    const/4 v5, 0x6

    .line 100
    const/4 v6, 0x0

    .line 101
    const/4 v3, 0x0

    .line 102
    const/4 v4, 0x0

    .line 103
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/sport_collection/views/items/SportsCollectionSimpleItem;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 104
    .line 105
    .line 106
    new-instance p1, LO31/n$d;

    .line 107
    .line 108
    invoke-direct {p1, v1}, LO31/n$d;-><init>(Landroid/view/View;)V

    .line 109
    .line 110
    .line 111
    return-object p1
.end method

.method public final y(LOc/n;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LO31/n;->f:LOc/n;

    .line 2
    .line 3
    return-void
.end method

.method public final z(I)V
    .locals 0

    .line 1
    iput p1, p0, LO31/n;->g:I

    .line 2
    .line 3
    return-void
.end method
