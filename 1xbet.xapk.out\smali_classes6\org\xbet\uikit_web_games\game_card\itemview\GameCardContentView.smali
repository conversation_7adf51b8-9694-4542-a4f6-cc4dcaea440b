.class public final Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00bc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0014\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0014\n\u0002\u0010\u0007\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 \u00a6\u00012\u00020\u0001:\u0001CB\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\nJ\u000f\u0010\u000c\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\nJ\u000f\u0010\r\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\r\u0010\nJ\u000f\u0010\u000e\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\nJ\u000f\u0010\u000f\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\nJ\u000f\u0010\u0010\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\nJ\u000f\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\nJ\u000f\u0010\u0012\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\nJ\u000f\u0010\u0013\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\nJ\u000f\u0010\u0014\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\nJ\u000f\u0010\u0015\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\nJ\u000f\u0010\u0016\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\nJ\u000f\u0010\u0017\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\nJ\u000f\u0010\u0018\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\nJ\u000f\u0010\u0019\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\nJ\u000f\u0010\u001a\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\nJ\u000f\u0010\u001b\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\nJ\u000f\u0010\u001c\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\nJ\u0017\u0010\u001f\u001a\u00020\u00082\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u001f\u0010$\u001a\u00020\u00082\u0006\u0010\"\u001a\u00020!2\u0008\u0010#\u001a\u0004\u0018\u00010!\u00a2\u0006\u0004\u0008$\u0010%J!\u0010*\u001a\u00020\u00082\u0008\u0010\'\u001a\u0004\u0018\u00010&2\u0008\u0008\u0002\u0010)\u001a\u00020(\u00a2\u0006\u0004\u0008*\u0010+J\u001d\u0010.\u001a\u00020\u00082\u0006\u0010,\u001a\u00020&2\u0006\u0010-\u001a\u00020&\u00a2\u0006\u0004\u0008.\u0010/J\u0015\u00102\u001a\u00020\u00082\u0006\u00101\u001a\u000200\u00a2\u0006\u0004\u00082\u00103J\u0015\u00106\u001a\u00020\u00082\u0006\u00105\u001a\u000204\u00a2\u0006\u0004\u00086\u00107J\u0015\u00109\u001a\u00020\u00082\u0006\u00108\u001a\u000204\u00a2\u0006\u0004\u00089\u00107J\u001f\u0010<\u001a\u00020\u00082\u0006\u0010:\u001a\u00020\u001d2\u0006\u0010;\u001a\u00020\u001dH\u0014\u00a2\u0006\u0004\u0008<\u0010=J7\u0010A\u001a\u00020\u00082\u0006\u0010>\u001a\u0002042\u0006\u0010?\u001a\u00020\u001d2\u0006\u0010\u0010\u001a\u00020\u001d2\u0006\u0010\r\u001a\u00020\u001d2\u0006\u0010@\u001a\u00020\u001dH\u0014\u00a2\u0006\u0004\u0008A\u0010BR\u0016\u0010E\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0016\u0010F\u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008@\u0010DR\u0014\u0010H\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010DR\u0014\u0010L\u001a\u00020I8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010N\u001a\u00020I8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010KR\u0014\u0010P\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010DR\u0014\u0010R\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010DR\u0014\u0010S\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010DR\u0014\u0010U\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010DR\u0014\u0010V\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010DR\u0014\u0010W\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010DR\u0014\u0010X\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010DR\u0014\u0010Y\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010DR\u0014\u0010Z\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010DR\u0014\u0010[\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010DR\u0014\u0010\\\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010DR\u0014\u0010]\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010DR\u0014\u0010^\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010DR\u0014\u0010_\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010DR\u0014\u0010`\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010DR\u0014\u0010a\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010DR\u0014\u0010b\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010DR\u0014\u0010e\u001a\u00020c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010dR\u001b\u0010j\u001a\u00020f8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010g\u001a\u0004\u0008h\u0010iR\u0016\u0010l\u001a\u0002048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010kR\u0018\u0010n\u001a\u0004\u0018\u0001008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010mR\u0016\u0010o\u001a\u0002048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010kR\u0016\u0010s\u001a\u0004\u0018\u00010p8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010v\u001a\u00020t8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010uR\u0014\u0010z\u001a\u00020w8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010yR\u0014\u0010{\u001a\u00020w8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010yR\u0014\u0010~\u001a\u00020|8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010}R\u0015\u0010\u0080\u0001\u001a\u00020w8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u007f\u0010yR\u0018\u0010\u0084\u0001\u001a\u00030\u0081\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0082\u0001\u0010\u0083\u0001R\u0017\u0010\u0087\u0001\u001a\u00030\u0085\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008D\u0010\u0086\u0001R\u0018\u0010\u008b\u0001\u001a\u00030\u0088\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0018\u0010\u008f\u0001\u001a\u00030\u008c\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008d\u0001\u0010\u008e\u0001R\u0018\u0010\u0091\u0001\u001a\u00030\u0088\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0001\u0010\u008a\u0001R\u0018\u0010\u0095\u0001\u001a\u00030\u0092\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0094\u0001R8\u0010\u009d\u0001\u001a\u0011\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\u0008\u0018\u00010\u0096\u00018\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0018\n\u0006\u0008\u0097\u0001\u0010\u0098\u0001\u001a\u0006\u0008\u0099\u0001\u0010\u009a\u0001\"\u0006\u0008\u009b\u0001\u0010\u009c\u0001R2\u0010\u00a5\u0001\u001a\u000b\u0012\u0004\u0012\u00020\u0008\u0018\u00010\u009e\u00018\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0018\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001\u001a\u0006\u0008\u00a1\u0001\u0010\u00a2\u0001\"\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001\u00a8\u0006\u00a7\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "E",
        "()V",
        "n",
        "p",
        "r",
        "o",
        "q",
        "t",
        "s",
        "m",
        "w",
        "y",
        "v",
        "x",
        "A",
        "z",
        "u",
        "j",
        "k",
        "C",
        "",
        "iconCenterX",
        "setupRightSideTouchDelegate",
        "(I)V",
        "LL11/c;",
        "image",
        "placeHolder",
        "setBannerImage",
        "(LL11/c;LL11/c;)V",
        "",
        "text",
        "Ll41/b;",
        "style",
        "setTag",
        "(Ljava/lang/String;Ll41/b;)V",
        "textLabel",
        "coefficientLabel",
        "setWinning",
        "(Ljava/lang/String;Ljava/lang/String;)V",
        "Ll41/a;",
        "type",
        "setActionIcon",
        "(Ll41/a;)V",
        "",
        "checked",
        "setChecked",
        "(Z)V",
        "enable",
        "h",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "changed",
        "l",
        "b",
        "onLayout",
        "(ZIIII)V",
        "a",
        "I",
        "topHeight",
        "totalWidth",
        "c",
        "bottomHeight",
        "",
        "d",
        "F",
        "radius16dp",
        "e",
        "radius12dp",
        "f",
        "offset1dp",
        "g",
        "actionTouchWidth",
        "size8dp",
        "i",
        "size4dp",
        "size6dp",
        "size12dp",
        "size40dp",
        "size20dp",
        "size44dp",
        "colorBackground",
        "technicalWorkColorBackground",
        "overlayBackground",
        "actionColor",
        "labelTextColor",
        "textLabelSize",
        "coefficientLabelSize",
        "coefficientTextColor",
        "Lm41/a;",
        "Lm41/a;",
        "textBuilder",
        "Lorg/xbet/uikit/utils/z;",
        "Lkotlin/j;",
        "getLoadHelper",
        "()Lorg/xbet/uikit/utils/z;",
        "loadHelper",
        "Z",
        "isActionStyle",
        "Ll41/a;",
        "currentActionIconType",
        "isChecked",
        "Landroid/graphics/drawable/Drawable;",
        "B",
        "Landroid/graphics/drawable/Drawable;",
        "backgroundContent16",
        "Lcom/google/android/material/shape/ShapeAppearanceModel;",
        "Lcom/google/android/material/shape/ShapeAppearanceModel;",
        "imageShapeModel",
        "Lcom/google/android/material/imageview/ShapeableImageView;",
        "D",
        "Lcom/google/android/material/imageview/ShapeableImageView;",
        "bannerImageView",
        "overlayView",
        "Landroid/widget/ImageView;",
        "Landroid/widget/ImageView;",
        "technicalWorksIconView",
        "G",
        "technicalWorksBackground",
        "Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;",
        "H",
        "Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;",
        "checkBox",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "tagView",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "J",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "winningView",
        "Landroid/view/View;",
        "K",
        "Landroid/view/View;",
        "winningBackgroundView",
        "L",
        "titleView",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "M",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "actionIconView",
        "Lkotlin/Function1;",
        "N",
        "Lkotlin/jvm/functions/Function1;",
        "getOnFavoriteChanged",
        "()Lkotlin/jvm/functions/Function1;",
        "setOnFavoriteChanged",
        "(Lkotlin/jvm/functions/Function1;)V",
        "onFavoriteChanged",
        "Lkotlin/Function0;",
        "O",
        "Lkotlin/jvm/functions/Function0;",
        "getOnMenuClick",
        "()Lkotlin/jvm/functions/Function0;",
        "setOnMenuClick",
        "(Lkotlin/jvm/functions/Function0;)V",
        "onMenuClick",
        "P",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final P:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final Q:I


# instance fields
.field public A:Z

.field public final B:Landroid/graphics/drawable/Drawable;

.field public final C:Lcom/google/android/material/shape/ShapeAppearanceModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D:Lcom/google/android/material/imageview/ShapeableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final E:Lcom/google/android/material/imageview/ShapeableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final G:Lcom/google/android/material/imageview/ShapeableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I:Lorg/xbet/uikit/components/tag/Tag;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K:Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M:Landroidx/appcompat/widget/AppCompatImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public N:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public O:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public a:I

.field public b:I

.field public final c:I

.field public final d:F

.field public final e:F

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:I

.field public final s:I

.field public final t:I

.field public final u:I

.field public final v:I

.field public final w:Lm41/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public y:Z

.field public z:Ll41/a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->P:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->Q:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 23
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move-object/from16 v0, p0

    move-object/from16 v2, p1

    .line 3
    invoke-direct/range {p0 .. p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->size_52:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->radius_16:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->d:F

    .line 6
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->radius_12:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v8

    iput v8, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->e:F

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_1:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->f:I

    .line 8
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_36:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->g:I

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_8:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 10
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_4:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->i:I

    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_6:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->j:I

    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_12:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->k:I

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_40:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->l:I

    .line 14
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_20:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->m:I

    .line 15
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_44:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->n:I

    .line 16
    sget v3, LlZ0/d;->uikitSecondary20:I

    const/4 v9, 0x0

    const/4 v10, 0x2

    invoke-static {v2, v3, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->o:I

    .line 17
    sget v4, LlZ0/d;->uikitBackgroundLight60:I

    invoke-static {v2, v4, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->p:I

    .line 18
    sget v5, LlZ0/d;->uikitStaticBlack60:I

    invoke-static {v2, v5, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v5

    iput v5, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->q:I

    .line 19
    sget v6, LlZ0/d;->uikitPrimary:I

    invoke-static {v2, v6, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v11

    iput v11, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->r:I

    .line 20
    sget v6, LlZ0/d;->uikitTextPrimary:I

    invoke-static {v2, v6, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v6

    iput v6, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->s:I

    .line 21
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v6

    sget v12, LlZ0/g;->text_10:I

    invoke-virtual {v6, v12}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v6

    iput v6, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->t:I

    .line 22
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v12

    sget v13, LlZ0/g;->text_14:I

    invoke-virtual {v12, v13}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v12

    iput v12, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->u:I

    .line 23
    sget v13, LlZ0/d;->uikitStaticGamesCoefficient:I

    invoke-static {v2, v13, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v13

    iput v13, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->v:I

    .line 24
    new-instance v14, Lm41/a;

    invoke-direct {v14, v2, v12, v6, v13}, Lm41/a;-><init>(Landroid/content/Context;III)V

    iput-object v14, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->w:Lm41/a;

    .line 25
    new-instance v6, Lorg/xbet/uikit_web_games/game_card/itemview/c;

    invoke-direct {v6, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/c;-><init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V

    invoke-static {v6}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v6

    iput-object v6, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->x:Lkotlin/j;

    .line 26
    sget v6, LlZ0/h;->rounded_background_16_content:I

    .line 27
    invoke-static {v2, v6}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v12

    iput-object v12, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->B:Landroid/graphics/drawable/Drawable;

    .line 28
    invoke-static {}, Lcom/google/android/material/shape/ShapeAppearanceModel;->builder()Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object v6

    const/4 v13, 0x0

    .line 29
    invoke-virtual {v6, v13, v1}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->setTopLeftCorner(IF)Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object v6

    .line 30
    invoke-virtual {v6, v13, v1}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->setTopRightCorner(IF)Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object v1

    .line 31
    invoke-virtual {v1, v13, v8}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->setBottomLeftCorner(IF)Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object v1

    .line 32
    invoke-virtual {v1, v13, v8}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->setBottomRightCorner(IF)Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;

    move-result-object v1

    .line 33
    invoke-virtual {v1}, Lcom/google/android/material/shape/ShapeAppearanceModel$Builder;->build()Lcom/google/android/material/shape/ShapeAppearanceModel;

    move-result-object v1

    iput-object v1, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->C:Lcom/google/android/material/shape/ShapeAppearanceModel;

    .line 34
    new-instance v14, Lcom/google/android/material/imageview/ShapeableImageView;

    invoke-direct {v14, v2}, Lcom/google/android/material/imageview/ShapeableImageView;-><init>(Landroid/content/Context;)V

    .line 35
    const-string v6, "DSGameCardBanner"

    invoke-virtual {v14, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 36
    invoke-virtual {v14, v1}, Lcom/google/android/material/imageview/ShapeableImageView;->setShapeAppearanceModel(Lcom/google/android/material/shape/ShapeAppearanceModel;)V

    .line 37
    sget-object v6, Landroid/widget/ImageView$ScaleType;->CENTER_CROP:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v14, v6}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 38
    invoke-virtual {v14, v3}, Landroid/view/View;->setBackgroundColor(I)V

    .line 39
    iput-object v14, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->D:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 40
    new-instance v15, Lcom/google/android/material/imageview/ShapeableImageView;

    invoke-direct {v15, v2}, Lcom/google/android/material/imageview/ShapeableImageView;-><init>(Landroid/content/Context;)V

    .line 41
    const-string v3, "DSGameCardOverlayView"

    invoke-virtual {v15, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 42
    invoke-virtual {v15, v1}, Lcom/google/android/material/imageview/ShapeableImageView;->setShapeAppearanceModel(Lcom/google/android/material/shape/ShapeAppearanceModel;)V

    const/16 v3, 0x8

    .line 43
    invoke-virtual {v15, v3}, Landroid/view/View;->setVisibility(I)V

    .line 44
    invoke-virtual {v15, v5}, Landroid/view/View;->setBackgroundColor(I)V

    .line 45
    iput-object v15, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->E:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 46
    new-instance v5, Landroid/widget/ImageView;

    invoke-direct {v5, v2}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 47
    const-string v6, "TechnicalWorksIcon"

    invoke-virtual {v5, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 48
    sget v6, LlZ0/h;->ic_glyph_technical_works:I

    invoke-virtual {v5, v6}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 49
    invoke-virtual {v5, v3}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 50
    iput-object v5, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->F:Landroid/widget/ImageView;

    .line 51
    new-instance v6, Lcom/google/android/material/imageview/ShapeableImageView;

    invoke-direct {v6, v2}, Lcom/google/android/material/imageview/ShapeableImageView;-><init>(Landroid/content/Context;)V

    const/16 v16, 0x0

    .line 52
    const-string v7, "TechnicalWorksBackground"

    invoke-virtual {v6, v7}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 53
    invoke-virtual {v6, v1}, Lcom/google/android/material/imageview/ShapeableImageView;->setShapeAppearanceModel(Lcom/google/android/material/shape/ShapeAppearanceModel;)V

    .line 54
    invoke-virtual {v6, v4}, Landroid/view/View;->setBackgroundColor(I)V

    .line 55
    invoke-virtual {v6, v3}, Landroid/view/View;->setVisibility(I)V

    .line 56
    iput-object v6, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->G:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 57
    sget-object v1, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->g:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox$a;

    sget-object v4, Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;->OVERLAY:Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;

    invoke-virtual {v1, v2, v4}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox$a;->a(Landroid/content/Context;Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;)Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    move-result-object v7

    .line 58
    const-string v1, "DSGameCardCheckBox"

    invoke-virtual {v7, v1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 59
    invoke-virtual {v7, v13}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->setClickable(Z)V

    const/4 v1, 0x1

    .line 60
    invoke-virtual {v7, v1}, Landroid/view/View;->setFocusable(Z)V

    .line 61
    invoke-virtual {v7, v3}, Landroid/view/View;->setVisibility(I)V

    .line 62
    new-instance v4, Lorg/xbet/uikit_web_games/game_card/itemview/d;

    invoke-direct {v4, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/d;-><init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V

    invoke-virtual {v7, v4}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 63
    iput-object v7, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->H:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    const/4 v4, 0x1

    .line 64
    new-instance v1, Lorg/xbet/uikit/components/tag/Tag;

    move-object/from16 v17, v5

    const/4 v5, 0x6

    move-object/from16 v18, v6

    const/4 v6, 0x0

    const/16 v19, 0x8

    const/4 v3, 0x0

    const/16 v20, 0x1

    const/4 v4, 0x0

    move-object/from16 v21, v17

    move-object/from16 v22, v18

    const/4 v9, 0x1

    const/16 v13, 0x8

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/tag/Tag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 65
    const-string v3, "DSGameCardTag"

    invoke-virtual {v1, v3}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 66
    sget v3, LlZ0/n;->Widget_Tag_Rounded:I

    invoke-virtual {v1, v3}, Lorg/xbet/uikit/components/tag/Tag;->setStyle(I)V

    .line 67
    invoke-virtual {v1, v9}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 68
    sget-object v3, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v1, v3}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/16 v4, 0x11

    .line 69
    invoke-virtual {v1, v4}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v4, 0x3

    .line 70
    invoke-virtual {v1, v4}, Landroid/view/View;->setLayoutDirection(I)V

    .line 71
    invoke-virtual {v1, v13}, Landroid/view/View;->setVisibility(I)V

    .line 72
    iput-object v1, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 73
    new-instance v5, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v5, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 74
    const-string v6, "DSGameCardWinning"

    invoke-virtual {v5, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 75
    sget v6, LlZ0/n;->TextStyle_Caption_Medium_M:I

    invoke-static {v5, v6}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 76
    sget v6, LlZ0/d;->uikitStaticWhite:I

    const/4 v9, 0x0

    invoke-static {v2, v6, v9, v10, v9}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v6

    invoke-virtual {v5, v6}, Landroid/widget/TextView;->setTextColor(I)V

    const/4 v6, 0x0

    .line 77
    invoke-virtual {v5, v6}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    const/4 v6, 0x5

    .line 78
    invoke-virtual {v5, v6}, Landroid/view/View;->setTextDirection(I)V

    .line 79
    invoke-virtual {v5, v3}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 80
    invoke-virtual {v5, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 81
    invoke-virtual {v5, v13}, Landroid/view/View;->setVisibility(I)V

    .line 82
    iput-object v5, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 83
    new-instance v9, Landroid/view/View;

    invoke-direct {v9, v2}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    const/16 v17, 0x3

    .line 84
    const-string v4, "DSGameCardWinningBackground"

    invoke-virtual {v9, v4}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 85
    invoke-virtual {v9, v13}, Landroid/view/View;->setVisibility(I)V

    .line 86
    new-instance v4, Landroid/graphics/drawable/GradientDrawable;

    .line 87
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v18

    invoke-virtual/range {v18 .. v18}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object v18

    const/16 v19, 0x5

    invoke-virtual/range {v18 .. v18}, Landroid/content/res/Configuration;->getLayoutDirection()I

    move-result v6

    const/4 v10, 0x1

    const/16 v18, 0x2

    if-ne v6, v10, :cond_0

    .line 88
    sget-object v6, Landroid/graphics/drawable/GradientDrawable$Orientation;->RIGHT_LEFT:Landroid/graphics/drawable/GradientDrawable$Orientation;

    :goto_0
    const/16 v20, 0x1

    goto :goto_1

    .line 89
    :cond_0
    sget-object v6, Landroid/graphics/drawable/GradientDrawable$Orientation;->LEFT_RIGHT:Landroid/graphics/drawable/GradientDrawable$Orientation;

    goto :goto_0

    :goto_1
    const/high16 v10, -0x34000000    # -3.3554432E7f

    const/4 v13, 0x0

    .line 90
    filled-new-array {v10, v13}, [I

    move-result-object v10

    .line 91
    invoke-direct {v4, v6, v10}, Landroid/graphics/drawable/GradientDrawable;-><init>(Landroid/graphics/drawable/GradientDrawable$Orientation;[I)V

    const/16 v6, 0x8

    .line 92
    new-array v10, v6, [F

    aput v16, v10, v13

    aput v16, v10, v20

    aput v16, v10, v18

    aput v16, v10, v17

    const/4 v6, 0x4

    aput v8, v10, v6

    aput v8, v10, v19

    const/4 v6, 0x6

    aput v8, v10, v6

    const/4 v6, 0x7

    aput v8, v10, v6

    .line 93
    invoke-virtual {v4, v10}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadii([F)V

    .line 94
    invoke-virtual {v9, v4}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 95
    iput-object v9, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->K:Landroid/view/View;

    .line 96
    new-instance v4, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v4, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 97
    const-string v6, "DSGameCardTitle"

    invoke-virtual {v4, v6}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 98
    sget v6, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {v4, v6}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 99
    invoke-virtual {v4, v3}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v3, 0x2

    .line 100
    invoke-virtual {v4, v3}, Landroid/widget/TextView;->setMaxLines(I)V

    const v3, 0x800003

    .line 101
    invoke-virtual {v4, v3}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v3, 0x5

    .line 102
    invoke-virtual {v4, v3}, Landroid/view/View;->setTextDirection(I)V

    .line 103
    iput-object v4, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 104
    new-instance v3, Landroidx/appcompat/widget/AppCompatImageView;

    invoke-direct {v3, v2}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 105
    const-string v2, "DSGameCardActionIcon"

    invoke-virtual {v3, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 106
    sget-object v2, Landroid/widget/ImageView$ScaleType;->CENTER_INSIDE:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v3, v2}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    const/16 v13, 0x8

    .line 107
    invoke-virtual {v3, v13}, Landroid/view/View;->setVisibility(I)V

    const/4 v10, 0x1

    .line 108
    invoke-virtual {v3, v10}, Landroid/view/View;->setClickable(Z)V

    .line 109
    invoke-virtual {v3, v10}, Landroid/view/View;->setFocusable(Z)V

    .line 110
    invoke-static {v11}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v2

    invoke-virtual {v3, v2}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 111
    iput-object v3, v0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    const/4 v13, 0x0

    .line 112
    invoke-virtual {v0, v13}, Landroid/view/View;->setWillNotDraw(Z)V

    .line 113
    invoke-virtual {v0, v12}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 114
    invoke-virtual {v0, v14}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    move-object/from16 v2, v22

    .line 115
    invoke-virtual {v0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    move-object/from16 v2, v21

    .line 116
    invoke-virtual {v0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 117
    invoke-virtual {v0, v9}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 118
    invoke-virtual {v0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 119
    invoke-virtual {v0, v15}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 120
    invoke-virtual {v0, v7}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 121
    invoke-virtual {v0, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 122
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 123
    invoke-virtual {v0, v4}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static final B(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->z:Ll41/a;

    .line 2
    .line 3
    instance-of v0, p1, Ll41/a$c;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    iget-object p0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->O:Lkotlin/jvm/functions/Function0;

    .line 8
    .line 9
    if-eqz p0, :cond_2

    .line 10
    .line 11
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    instance-of v0, p1, Ll41/a$b;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    check-cast p1, Ll41/a$b;

    .line 20
    .line 21
    invoke-virtual {p1}, Ll41/a$b;->a()Z

    .line 22
    .line 23
    .line 24
    move-result p1

    .line 25
    xor-int/lit8 p1, p1, 0x1

    .line 26
    .line 27
    new-instance v0, Ll41/a$b;

    .line 28
    .line 29
    invoke-direct {v0, p1}, Ll41/a$b;-><init>(Z)V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setActionIcon(Ll41/a;)V

    .line 33
    .line 34
    .line 35
    iget-object p0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->N:Lkotlin/jvm/functions/Function1;

    .line 36
    .line 37
    if-eqz p0, :cond_2

    .line 38
    .line 39
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    return-void

    .line 47
    :cond_1
    instance-of p0, p1, Ll41/a$a;

    .line 48
    .line 49
    if-eqz p0, :cond_2

    .line 50
    .line 51
    check-cast p1, Ll41/a$a;

    .line 52
    .line 53
    invoke-virtual {p1}, Ll41/a$a;->b()Lkotlin/jvm/functions/Function0;

    .line 54
    .line 55
    .line 56
    move-result-object p0

    .line 57
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    :cond_2
    return-void
.end method

.method public static final D(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;I)V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    instance-of v1, v0, Landroid/view/View;

    .line 8
    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    check-cast v0, Landroid/view/View;

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    :goto_0
    if-nez v0, :cond_1

    .line 16
    .line 17
    return-void

    .line 18
    :cond_1
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 19
    .line 20
    sub-int/2addr v1, p1

    .line 21
    invoke-virtual {v0}, Landroid/view/View;->getLayoutDirection()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    if-nez v2, :cond_2

    .line 26
    .line 27
    new-instance v2, Landroid/graphics/Rect;

    .line 28
    .line 29
    sub-int/2addr p1, v1

    .line 30
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 31
    .line 32
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 33
    .line 34
    iget v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 35
    .line 36
    add-int/2addr v4, v1

    .line 37
    invoke-direct {v2, p1, v1, v3, v4}, Landroid/graphics/Rect;-><init>(IIII)V

    .line 38
    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_2
    new-instance v2, Landroid/graphics/Rect;

    .line 42
    .line 43
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 44
    .line 45
    iget v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 46
    .line 47
    sub-int/2addr p1, v1

    .line 48
    sub-int/2addr v4, p1

    .line 49
    iget p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 50
    .line 51
    add-int/2addr p1, v3

    .line 52
    const/4 v1, 0x0

    .line 53
    invoke-direct {v2, v1, v3, v4, p1}, Landroid/graphics/Rect;-><init>(IIII)V

    .line 54
    .line 55
    .line 56
    :goto_1
    new-instance p1, Landroid/view/TouchDelegate;

    .line 57
    .line 58
    iget-object p0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 59
    .line 60
    invoke-direct {p1, v2, p0}, Landroid/view/TouchDelegate;-><init>(Landroid/graphics/Rect;Landroid/view/View;)V

    .line 61
    .line 62
    .line 63
    invoke-virtual {v0, p1}, Landroid/view/View;->setTouchDelegate(Landroid/view/TouchDelegate;)V

    .line 64
    .line 65
    .line 66
    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;I)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->D(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;I)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)Lorg/xbet/uikit/utils/z;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->l(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)Lorg/xbet/uikit/utils/z;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(ZLorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->i(ZLorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic d(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->B(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->g(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic f(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setupFullCardTouchDelegate$lambda$18(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V

    return-void
.end method

.method public static final g(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->E()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final getLoadHelper()Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->x:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/z;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final i(ZLorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V
    .locals 0

    .line 1
    if-eqz p0, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->E()V

    .line 4
    .line 5
    .line 6
    :cond_0
    return-void
.end method

.method public static final l(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)Lorg/xbet/uikit/utils/z;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    iget-object p0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->D:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 4
    .line 5
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/z;-><init>(Landroid/widget/ImageView;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public static synthetic setTag$default(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Ljava/lang/String;Ll41/b;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    sget-object p2, Ll41/b$a;->b:Ll41/b$a;

    .line 6
    .line 7
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setTag(Ljava/lang/String;Ll41/b;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method private static final setupFullCardTouchDelegate$lambda$18(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    instance-of v1, v0, Landroid/view/View;

    .line 8
    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    check-cast v0, Landroid/view/View;

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    :goto_0
    if-nez v0, :cond_1

    .line 16
    .line 17
    return-void

    .line 18
    :cond_1
    new-instance v1, Landroid/view/TouchDelegate;

    .line 19
    .line 20
    new-instance v2, Landroid/graphics/Rect;

    .line 21
    .line 22
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 25
    .line 26
    .line 27
    move-result v4

    .line 28
    const/4 v5, 0x0

    .line 29
    invoke-direct {v2, v5, v5, v3, v4}, Landroid/graphics/Rect;-><init>(IIII)V

    .line 30
    .line 31
    .line 32
    iget-object p0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 33
    .line 34
    invoke-direct {v1, v2, p0}, Landroid/view/TouchDelegate;-><init>(Landroid/graphics/Rect;Landroid/view/View;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {v0, v1}, Landroid/view/View;->setTouchDelegate(Landroid/view/TouchDelegate;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method private final setupRightSideTouchDelegate(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/uikit_web_games/game_card/itemview/e;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/e;-><init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final A()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 10
    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->K:Landroid/view/View;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    sub-int/2addr v0, v1

    .line 18
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->K:Landroid/view/View;

    .line 19
    .line 20
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 21
    .line 22
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 23
    .line 24
    const/4 v4, 0x0

    .line 25
    invoke-virtual {v1, v4, v0, v2, v3}, Landroid/view/View;->layout(IIII)V

    .line 26
    .line 27
    .line 28
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->i:I

    .line 29
    .line 30
    add-int v5, v0, v1

    .line 31
    .line 32
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    iget v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 39
    .line 40
    add-int v6, v4, v0

    .line 41
    .line 42
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    .line 44
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    add-int v7, v5, v0

    .line 49
    .line 50
    iget-object v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 51
    .line 52
    move-object v2, p0

    .line 53
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 54
    .line 55
    .line 56
    :cond_0
    return-void
.end method

.method public final C()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/uikit_web_games/game_card/itemview/b;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xbet/uikit_web_games/game_card/itemview/b;-><init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, v1}, Landroid/view/View;->post(Ljava/lang/Runnable;)Z

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final E()V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->A:Z

    .line 2
    .line 3
    xor-int/lit8 v0, v0, 0x1

    .line 4
    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setChecked(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final getOnFavoriteChanged()Lkotlin/jvm/functions/Function1;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->N:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getOnMenuClick()Lkotlin/jvm/functions/Function0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->O:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h(Z)V
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit_web_games/game_card/itemview/f;

    .line 2
    .line 3
    invoke-direct {v0, p1, p0}, Lorg/xbet/uikit_web_games/game_card/itemview/f;-><init>(ZLorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final j()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget-object v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 14
    .line 15
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 16
    .line 17
    .line 18
    move-result v2

    .line 19
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->k:I

    .line 20
    .line 21
    add-int/2addr v3, v1

    .line 22
    add-int/2addr v3, v2

    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    sub-int/2addr v2, v3

    .line 28
    div-int/lit8 v6, v2, 0x2

    .line 29
    .line 30
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 31
    .line 32
    sub-int/2addr v2, v0

    .line 33
    div-int/lit8 v5, v2, 0x2

    .line 34
    .line 35
    iget-object v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 36
    .line 37
    add-int v7, v5, v0

    .line 38
    .line 39
    add-int v8, v6, v1

    .line 40
    .line 41
    move-object v3, p0

    .line 42
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->C()V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final k()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 14
    .line 15
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 16
    .line 17
    sub-int/2addr v2, v3

    .line 18
    sub-int v5, v2, v0

    .line 19
    .line 20
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 21
    .line 22
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 23
    .line 24
    sub-int/2addr v3, v1

    .line 25
    div-int/lit8 v3, v3, 0x2

    .line 26
    .line 27
    add-int v6, v2, v3

    .line 28
    .line 29
    iget-object v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 30
    .line 31
    add-int v7, v5, v0

    .line 32
    .line 33
    add-int v8, v6, v1

    .line 34
    .line 35
    move-object v3, p0

    .line 36
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 37
    .line 38
    .line 39
    div-int/lit8 v0, v0, 0x2

    .line 40
    .line 41
    add-int/2addr v5, v0

    .line 42
    invoke-direct {p0, v5}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setupRightSideTouchDelegate(I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final m()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget-boolean v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->y:Z

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    :goto_0
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->m:I

    .line 15
    .line 16
    goto :goto_1

    .line 17
    :cond_1
    const/4 v0, 0x0

    .line 18
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 19
    .line 20
    const/high16 v2, 0x40000000    # 2.0f

    .line 21
    .line 22
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 23
    .line 24
    .line 25
    move-result v3

    .line 26
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    invoke-virtual {v1, v3, v0}, Landroid/view/View;->measure(II)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final n()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->D:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 4
    .line 5
    const/high16 v2, 0x40000000    # 2.0f

    .line 6
    .line 7
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 12
    .line 13
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final o()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->H:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->l:I

    .line 4
    .line 5
    const/high16 v2, 0x40000000    # 2.0f

    .line 6
    .line 7
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->l:I

    .line 12
    .line 13
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->D:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    iget p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 4
    .line 5
    iget p3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 6
    .line 7
    const/4 p4, 0x0

    .line 8
    invoke-virtual {p1, p4, p4, p2, p3}, Landroid/view/View;->layout(IIII)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->y()V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->w()V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->v()V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->x()V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->A()V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->z()V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->u()V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public onMeasure(II)V
    .locals 0

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    iput p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 6
    .line 7
    int-to-float p1, p1

    .line 8
    const p2, 0x3f2aaaab

    .line 9
    .line 10
    .line 11
    mul-float p1, p1, p2

    .line 12
    .line 13
    float-to-int p1, p1

    .line 14
    iput p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 15
    .line 16
    iget p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 17
    .line 18
    add-int/2addr p1, p2

    .line 19
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->n()V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->r()V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->p()V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->o()V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->q()V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->t()V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->m()V

    .line 38
    .line 39
    .line 40
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->s()V

    .line 41
    .line 42
    .line 43
    iget p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 44
    .line 45
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 46
    .line 47
    .line 48
    return-void
.end method

.method public final p()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->E:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 4
    .line 5
    const/high16 v2, 0x40000000    # 2.0f

    .line 6
    .line 7
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 12
    .line 13
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final q()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->H:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-nez v0, :cond_0

    .line 9
    .line 10
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->l:I

    .line 11
    .line 12
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 13
    .line 14
    add-int/2addr v0, v2

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 v0, 0x0

    .line 17
    :goto_0
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 18
    .line 19
    sub-int/2addr v2, v0

    .line 20
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 21
    .line 22
    const/high16 v3, -0x80000000

    .line 23
    .line 24
    invoke-static {v2, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public final r()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->G:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 4
    .line 5
    const/high16 v2, 0x40000000    # 2.0f

    .line 6
    .line 7
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 12
    .line 13
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result v3

    .line 17
    invoke-virtual {v0, v1, v3}, Landroid/view/View;->measure(II)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->F:Landroid/widget/ImageView;

    .line 21
    .line 22
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->n:I

    .line 23
    .line 24
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->n:I

    .line 29
    .line 30
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result v2

    .line 34
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final s()V
    .locals 4

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->y:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 6
    .line 7
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 8
    .line 9
    mul-int/lit8 v1, v1, 0x2

    .line 10
    .line 11
    sub-int/2addr v0, v1

    .line 12
    goto :goto_1

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-nez v0, :cond_1

    .line 20
    .line 21
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->g:I

    .line 22
    .line 23
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 24
    .line 25
    add-int/2addr v0, v1

    .line 26
    goto :goto_0

    .line 27
    :cond_1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 28
    .line 29
    :goto_0
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 30
    .line 31
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 32
    .line 33
    sub-int/2addr v1, v2

    .line 34
    sub-int v0, v1, v0

    .line 35
    .line 36
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 37
    .line 38
    const/high16 v2, -0x80000000

    .line 39
    .line 40
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 41
    .line 42
    .line 43
    move-result v0

    .line 44
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 45
    .line 46
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 47
    .line 48
    .line 49
    move-result v2

    .line 50
    invoke-virtual {v1, v0, v2}, Landroid/view/View;->measure(II)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public final setActionIcon(Ll41/a;)V
    .locals 2
    .param p1    # Ll41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->z:Ll41/a;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 7
    .line 8
    .line 9
    instance-of v0, p1, Ll41/a$d;

    .line 10
    .line 11
    const/4 v1, 0x0

    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 15
    .line 16
    const/16 v0, 0x8

    .line 17
    .line 18
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 22
    .line 23
    invoke-virtual {p1, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 24
    .line 25
    .line 26
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 27
    .line 28
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    instance-of v0, p1, Landroid/view/View;

    .line 33
    .line 34
    if-eqz v0, :cond_0

    .line 35
    .line 36
    check-cast p1, Landroid/view/View;

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    move-object p1, v1

    .line 40
    :goto_0
    if-eqz p1, :cond_6

    .line 41
    .line 42
    invoke-virtual {p1, v1}, Landroid/view/View;->setTouchDelegate(Landroid/view/TouchDelegate;)V

    .line 43
    .line 44
    .line 45
    goto :goto_2

    .line 46
    :cond_1
    instance-of v0, p1, Ll41/a$c;

    .line 47
    .line 48
    if-eqz v0, :cond_2

    .line 49
    .line 50
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 51
    .line 52
    sget v0, LlZ0/h;->ic_glyph_more_vertically:I

    .line 53
    .line 54
    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 55
    .line 56
    .line 57
    goto :goto_2

    .line 58
    :cond_2
    instance-of v0, p1, Ll41/a$b;

    .line 59
    .line 60
    if-eqz v0, :cond_4

    .line 61
    .line 62
    check-cast p1, Ll41/a$b;

    .line 63
    .line 64
    invoke-virtual {p1}, Ll41/a$b;->a()Z

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-eqz p1, :cond_3

    .line 69
    .line 70
    sget p1, LlZ0/h;->ic_glyph_favourite_active:I

    .line 71
    .line 72
    goto :goto_1

    .line 73
    :cond_3
    sget p1, LlZ0/h;->ic_glyph_favourite_inactive:I

    .line 74
    .line 75
    :goto_1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 76
    .line 77
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 78
    .line 79
    .line 80
    goto :goto_2

    .line 81
    :cond_4
    instance-of v0, p1, Ll41/a$a;

    .line 82
    .line 83
    if-eqz v0, :cond_7

    .line 84
    .line 85
    check-cast p1, Ll41/a$a;

    .line 86
    .line 87
    invoke-virtual {p1}, Ll41/a$a;->a()I

    .line 88
    .line 89
    .line 90
    move-result v0

    .line 91
    if-eqz v0, :cond_5

    .line 92
    .line 93
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 94
    .line 95
    invoke-virtual {p1}, Ll41/a$a;->a()I

    .line 96
    .line 97
    .line 98
    move-result p1

    .line 99
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 100
    .line 101
    .line 102
    goto :goto_2

    .line 103
    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 104
    .line 105
    invoke-virtual {p1, v1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 106
    .line 107
    .line 108
    :cond_6
    :goto_2
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 109
    .line 110
    new-instance v0, Lorg/xbet/uikit_web_games/game_card/itemview/a;

    .line 111
    .line 112
    invoke-direct {v0, p0}, Lorg/xbet/uikit_web_games/game_card/itemview/a;-><init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V

    .line 113
    .line 114
    .line 115
    invoke-virtual {p1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 116
    .line 117
    .line 118
    return-void

    .line 119
    :cond_7
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 120
    .line 121
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 122
    .line 123
    .line 124
    throw p1
.end method

.method public final setBannerImage(LL11/c;LL11/c;)V
    .locals 7
    .param p1    # LL11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->getLoadHelper()Lorg/xbet/uikit/utils/z;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-nez p2, :cond_0

    .line 6
    .line 7
    sget p2, LlZ0/h;->banner_item_placeholder:I

    .line 8
    .line 9
    invoke-static {p2}, LL11/c$c;->d(I)I

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    invoke-static {p2}, LL11/c$c;->c(I)LL11/c$c;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    :cond_0
    move-object v2, p2

    .line 18
    const/16 v5, 0xc

    .line 19
    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    move-object v1, p1

    .line 24
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/utils/z;->y(Lorg/xbet/uikit/utils/z;LL11/c;LL11/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final setChecked(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->A:Z

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->H:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->setChecked(Z)V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->E:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 9
    .line 10
    iget-boolean v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->A:Z

    .line 11
    .line 12
    if-eqz v0, :cond_0

    .line 13
    .line 14
    const/16 v0, 0x8

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 v0, 0x0

    .line 18
    :goto_0
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public final setOnFavoriteChanged(Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->N:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-void
.end method

.method public final setOnMenuClick(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->O:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method

.method public final setTag(Ljava/lang/String;Ll41/b;)V
    .locals 1
    .param p2    # Ll41/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    if-eqz p1, :cond_1

    .line 2
    .line 3
    invoke-static {p1}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 11
    .line 12
    invoke-virtual {p2}, Ll41/b;->a()I

    .line 13
    .line 14
    .line 15
    move-result p2

    .line 16
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/components/tag/Tag;->setStyle(I)V

    .line 17
    .line 18
    .line 19
    iget-object p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 20
    .line 21
    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 22
    .line 23
    .line 24
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 25
    .line 26
    const/4 p2, 0x0

    .line 27
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 28
    .line 29
    .line 30
    goto :goto_1

    .line 31
    :cond_1
    :goto_0
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 32
    .line 33
    const/16 p2, 0x8

    .line 34
    .line 35
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 36
    .line 37
    .line 38
    :goto_1
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 39
    .line 40
    invoke-virtual {p1}, Landroid/view/View;->requestLayout()V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public final setWinning(Ljava/lang/String;Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p1}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {p2}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 14
    .line 15
    const-string p2, ""

    .line 16
    .line 17
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 21
    .line 22
    const/16 p2, 0x8

    .line 23
    .line 24
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->K:Landroid/view/View;

    .line 28
    .line 29
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 30
    .line 31
    .line 32
    return-void

    .line 33
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 34
    .line 35
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->w:Lm41/a;

    .line 36
    .line 37
    invoke-virtual {v1, p1, p2}, Lm41/a;->c(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/CharSequence;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 42
    .line 43
    .line 44
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 45
    .line 46
    const/4 p2, 0x0

    .line 47
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 48
    .line 49
    .line 50
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->K:Landroid/view/View;

    .line 51
    .line 52
    invoke-virtual {p1, p2}, Landroid/view/View;->setVisibility(I)V

    .line 53
    .line 54
    .line 55
    return-void
.end method

.method public final t()V
    .locals 5

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 2
    .line 3
    mul-int/lit8 v0, v0, 0x2

    .line 4
    .line 5
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 6
    .line 7
    sub-int/2addr v1, v0

    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    const/4 v2, 0x0

    .line 17
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 22
    .line 23
    .line 24
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->K:Landroid/view/View;

    .line 25
    .line 26
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 27
    .line 28
    const/high16 v2, 0x40000000    # 2.0f

    .line 29
    .line 30
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    iget-object v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->J:Landroidx/appcompat/widget/AppCompatTextView;

    .line 35
    .line 36
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 37
    .line 38
    .line 39
    move-result v3

    .line 40
    iget v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->i:I

    .line 41
    .line 42
    add-int/2addr v3, v4

    .line 43
    iget v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->j:I

    .line 44
    .line 45
    add-int/2addr v3, v4

    .line 46
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 47
    .line 48
    .line 49
    move-result v2

    .line 50
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public final u()V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->y:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->j()V

    .line 6
    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-nez v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->k()V

    .line 18
    .line 19
    .line 20
    :cond_1
    return-void
.end method

.method public final v()V
    .locals 7

    .line 1
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 2
    .line 3
    iget v6, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->l:I

    .line 4
    .line 5
    sub-int v3, v0, v6

    .line 6
    .line 7
    add-int v5, v3, v6

    .line 8
    .line 9
    iget-object v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->H:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 10
    .line 11
    const/4 v4, 0x0

    .line 12
    move-object v1, p0

    .line 13
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final w()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->E:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    invoke-virtual {v0, v3, v3, v1, v2}, Landroid/view/View;->layout(IIII)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final x()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->f:I

    .line 10
    .line 11
    neg-int v3, v0

    .line 12
    neg-int v4, v0

    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 14
    .line 15
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    add-int/2addr v0, v3

    .line 20
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->f:I

    .line 21
    .line 22
    mul-int/lit8 v1, v1, 0x2

    .line 23
    .line 24
    add-int v5, v0, v1

    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 27
    .line 28
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    add-int v6, v4, v0

    .line 33
    .line 34
    iget-object v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->I:Lorg/xbet/uikit/components/tag/Tag;

    .line 35
    .line 36
    move-object v1, p0

    .line 37
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 38
    .line 39
    .line 40
    :cond_0
    return-void
.end method

.method public final y()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->G:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 4
    .line 5
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    invoke-virtual {v0, v3, v3, v1, v2}, Landroid/view/View;->layout(IIII)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->F:Landroid/widget/ImageView;

    .line 12
    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->F:Landroid/widget/ImageView;

    .line 18
    .line 19
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 24
    .line 25
    sub-int/2addr v2, v0

    .line 26
    div-int/lit8 v2, v2, 0x2

    .line 27
    .line 28
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a:I

    .line 29
    .line 30
    sub-int/2addr v3, v1

    .line 31
    div-int/lit8 v3, v3, 0x2

    .line 32
    .line 33
    iget-object v4, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->F:Landroid/widget/ImageView;

    .line 34
    .line 35
    add-int/2addr v0, v2

    .line 36
    add-int/2addr v1, v3

    .line 37
    invoke-virtual {v4, v2, v3, v0, v1}, Landroid/view/View;->layout(IIII)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public final z()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 8
    .line 9
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget-boolean v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->y:Z

    .line 14
    .line 15
    if-eqz v2, :cond_0

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->M:Landroidx/appcompat/widget/AppCompatImageView;

    .line 18
    .line 19
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->k:I

    .line 24
    .line 25
    add-int v4, v2, v3

    .line 26
    .line 27
    add-int/2addr v4, v1

    .line 28
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 29
    .line 30
    .line 31
    move-result v5

    .line 32
    sub-int/2addr v5, v4

    .line 33
    div-int/lit8 v5, v5, 0x2

    .line 34
    .line 35
    add-int/2addr v5, v2

    .line 36
    add-int/2addr v5, v3

    .line 37
    iget v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->b:I

    .line 38
    .line 39
    sub-int/2addr v2, v0

    .line 40
    div-int/lit8 v2, v2, 0x2

    .line 41
    .line 42
    iget-object v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    .line 44
    add-int/2addr v0, v2

    .line 45
    add-int/2addr v1, v5

    .line 46
    invoke-virtual {v3, v2, v5, v0, v1}, Landroid/view/View;->layout(IIII)V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    iget v3, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c:I

    .line 55
    .line 56
    sub-int/2addr v2, v3

    .line 57
    sub-int/2addr v3, v1

    .line 58
    div-int/lit8 v3, v3, 0x2

    .line 59
    .line 60
    add-int v7, v2, v3

    .line 61
    .line 62
    add-int v9, v7, v1

    .line 63
    .line 64
    iget v6, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->h:I

    .line 65
    .line 66
    add-int v8, v6, v0

    .line 67
    .line 68
    iget-object v5, p0, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->L:Landroidx/appcompat/widget/AppCompatTextView;

    .line 69
    .line 70
    move-object v4, p0

    .line 71
    invoke-static/range {v4 .. v9}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 72
    .line 73
    .line 74
    return-void
.end method
