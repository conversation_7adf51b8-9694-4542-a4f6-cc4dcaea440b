.class public final Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXb1/d;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXb1/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LS8/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "LXb1/d;",
            ">;",
            "LBc/a<",
            "LXb1/a;",
            ">;",
            "LBc/a<",
            "LS8/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->e:LBc/a;

    .line 13
    .line 14
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lc8/h;",
            ">;",
            "LBc/a<",
            "LXb1/d;",
            ">;",
            "LBc/a<",
            "LXb1/a;",
            ">;",
            "LBc/a<",
            "LS8/a;",
            ">;)",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static c(Lm8/a;Lc8/h;LXb1/d;LXb1/a;LS8/a;)Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;
    .locals 6

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;-><init>(Lm8/a;Lc8/h;LXb1/d;LXb1/a;LS8/a;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lm8/a;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lc8/h;

    .line 16
    .line 17
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->c:LBc/a;

    .line 18
    .line 19
    invoke-interface {v2}, LBc/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, LXb1/d;

    .line 24
    .line 25
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->d:LBc/a;

    .line 26
    .line 27
    invoke-interface {v3}, LBc/a;->get()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    check-cast v3, LXb1/a;

    .line 32
    .line 33
    iget-object v4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->e:LBc/a;

    .line 34
    .line 35
    invoke-interface {v4}, LBc/a;->get()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    check-cast v4, LS8/a;

    .line 40
    .line 41
    invoke-static {v0, v1, v2, v3, v4}, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->c(Lm8/a;Lc8/h;LXb1/d;LXb1/a;LS8/a;)Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/a;->b()Lorg/xplatform/aggregator/popular/dashboard/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
