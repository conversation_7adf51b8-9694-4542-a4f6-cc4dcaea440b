.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u000f\u0008\u0082\u0008\u0018\u00002\u00020\u0001B7\u0012\u0008\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u000eH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0010\u0010\u0011\u001a\u00020\u0007H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u001a\u0010\u0015\u001a\u00020\u00142\u0008\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R\u0019\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0018\u001a\u0004\u0008\u0017\u0010\u0019R\u001d\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001dR\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008\u001e\u0010\u0012R\u0017\u0010\t\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001f\u001a\u0004\u0008\u001a\u0010\u0012R\u0017\u0010\u000b\u001a\u00020\n8\u0006\u00a2\u0006\u000c\n\u0004\u0008 \u0010!\u001a\u0004\u0008 \u0010\"\u00a8\u0006#"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
        "",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
        "header",
        "",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
        "measureViews",
        "",
        "maxColumn",
        "marketsSizeInGroup",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "rowViewType",
        "<init>",
        "(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
        "b",
        "Ljava/util/List;",
        "d",
        "()Ljava/util/List;",
        "c",
        "I",
        "e",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

.field public final b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:I

.field public final d:I

.field public final e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;Ljava/util/List;IILorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;)V
    .locals 0
    .param p2    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;II",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b:Ljava/util/List;

    .line 7
    .line 8
    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c:I

    .line 9
    .line 10
    iput p4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d:I

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final a()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final c()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final d()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$e;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b:Ljava/util/List;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b:Ljava/util/List;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c:I

    iget v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c:I

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d:I

    iget v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d:I

    if-eq v1, v3, :cond_5

    return v2

    :cond_5
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    iget-object p1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    if-eq v1, p1, :cond_6

    return v2

    :cond_6
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;->hashCode()I

    move-result v0

    :goto_0
    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b:Ljava/util/List;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 7
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$b;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->b:Ljava/util/List;

    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->c:I

    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->d:I

    iget-object v4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;->e:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$ViewType;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "MeasureMarketsGroup(header="

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", measureViews="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", maxColumn="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", marketsSizeInGroup="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", rowViewType="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
