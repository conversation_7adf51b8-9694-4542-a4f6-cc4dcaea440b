.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info_alt_design.TournamentMainInfoAltDesignFragment$onObserveData$1"
    f = "TournamentMainInfoAltDesignFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkb1/F<",
        "+",
        "Lkb1/n;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u00032\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkb1/F;",
        "Lkb1/n;",
        "state",
        "",
        "<anonymous>",
        "(Lkb1/F;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkb1/F;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->invoke(Lkb1/F;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkb1/F;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkb1/F<",
            "Lkb1/n;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lkb1/F;

    .line 14
    .line 15
    instance-of v0, p1, Lkb1/F$f;

    .line 16
    .line 17
    const/4 v1, 0x0

    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 21
    .line 22
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->L2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Z)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 26
    .line 27
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;)Leb1/e;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    check-cast p1, Lkb1/F$f;

    .line 32
    .line 33
    invoke-virtual {p1}, Lkb1/F$f;->a()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    check-cast p1, Lkb1/n;

    .line 38
    .line 39
    invoke-virtual {p1}, Lkb1/n;->b()Ljava/util/List;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 44
    .line 45
    .line 46
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->G2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;)LS91/Y;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iget-object p1, p1, LS91/Y;->e:Landroidx/recyclerview/widget/RecyclerView;

    .line 53
    .line 54
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 55
    .line 56
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    sget v1, Lpb/f;->space_32:I

    .line 61
    .line 62
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 63
    .line 64
    .line 65
    move-result v0

    .line 66
    invoke-virtual {p1}, Landroid/view/View;->getPaddingLeft()I

    .line 67
    .line 68
    .line 69
    move-result v1

    .line 70
    invoke-virtual {p1}, Landroid/view/View;->getPaddingTop()I

    .line 71
    .line 72
    .line 73
    move-result v2

    .line 74
    invoke-virtual {p1}, Landroid/view/View;->getPaddingRight()I

    .line 75
    .line 76
    .line 77
    move-result v3

    .line 78
    invoke-virtual {p1, v1, v2, v3, v0}, Landroid/view/View;->setPadding(IIII)V

    .line 79
    .line 80
    .line 81
    goto :goto_1

    .line 82
    :cond_0
    instance-of v0, p1, Lkb1/F$d;

    .line 83
    .line 84
    if-eqz v0, :cond_2

    .line 85
    .line 86
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 87
    .line 88
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->L2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Z)V

    .line 89
    .line 90
    .line 91
    check-cast p1, Lkb1/F$d;

    .line 92
    .line 93
    invoke-virtual {p1}, Lkb1/F$d;->a()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    check-cast v0, Lkb1/n;

    .line 98
    .line 99
    invoke-virtual {v0}, Lkb1/n;->b()Ljava/util/List;

    .line 100
    .line 101
    .line 102
    move-result-object v0

    .line 103
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    .line 104
    .line 105
    .line 106
    move-result v0

    .line 107
    if-eqz v0, :cond_1

    .line 108
    .line 109
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 110
    .line 111
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->H2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->K2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 120
    .line 121
    .line 122
    goto :goto_0

    .line 123
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 124
    .line 125
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;)Leb1/e;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-virtual {p1}, Lkb1/F$d;->a()Ljava/lang/Object;

    .line 130
    .line 131
    .line 132
    move-result-object v1

    .line 133
    check-cast v1, Lkb1/n;

    .line 134
    .line 135
    invoke-virtual {v1}, Lkb1/n;->b()Ljava/util/List;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    invoke-virtual {v0, v1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 140
    .line 141
    .line 142
    :goto_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 143
    .line 144
    invoke-virtual {p1}, Lkb1/F$d;->a()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    check-cast p1, Lkb1/n;

    .line 149
    .line 150
    invoke-virtual {p1}, Lkb1/n;->a()Z

    .line 151
    .line 152
    .line 153
    move-result p1

    .line 154
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Z)V

    .line 155
    .line 156
    .line 157
    goto :goto_1

    .line 158
    :cond_2
    instance-of v0, p1, Lkb1/w;

    .line 159
    .line 160
    if-eqz v0, :cond_3

    .line 161
    .line 162
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;

    .line 163
    .line 164
    check-cast p1, Lkb1/w;

    .line 165
    .line 166
    invoke-virtual {p1}, Lkb1/w;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;->K2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentMainInfoAltDesignFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 171
    .line 172
    .line 173
    :cond_3
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 174
    .line 175
    return-object p1

    .line 176
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 177
    .line 178
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 179
    .line 180
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 181
    .line 182
    .line 183
    throw p1
.end method
