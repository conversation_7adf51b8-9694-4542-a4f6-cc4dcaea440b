.class final Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.simple.viewmodel.SimpleMakeBetDsViewModel$onMakeBet$2"
    f = "SimpleMakeBetDsViewModel.kt"
    l = {
        0x8a,
        0x8f,
        0x94
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->i4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field D$0:D

.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;

    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 32

    .line 1
    move-object/from16 v3, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v6

    .line 7
    iget v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 8
    .line 9
    const/4 v7, 0x3

    .line 10
    const/4 v1, 0x2

    .line 11
    const/4 v2, 0x1

    .line 12
    if-eqz v0, :cond_3

    .line 13
    .line 14
    if-eq v0, v2, :cond_2

    .line 15
    .line 16
    if-eq v0, v1, :cond_1

    .line 17
    .line 18
    if-ne v0, v7, :cond_0

    .line 19
    .line 20
    iget-wide v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->D$0:D

    .line 21
    .line 22
    iget-object v2, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->L$0:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 25
    .line 26
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    move-wide v9, v0

    .line 30
    move-object/from16 v0, p1

    .line 31
    .line 32
    goto/16 :goto_3

    .line 33
    .line 34
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 35
    .line 36
    const-string v1, "call to \'resume\' before \'invoke\' with coroutine"

    .line 37
    .line 38
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw v0

    .line 42
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    move-object/from16 v0, p1

    .line 46
    .line 47
    goto :goto_1

    .line 48
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    move-object/from16 v0, p1

    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->w3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/internet/a;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput v2, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 68
    .line 69
    invoke-static {v0, v3}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    if-ne v0, v6, :cond_4

    .line 74
    .line 75
    goto :goto_2

    .line 76
    :cond_4
    :goto_0
    check-cast v0, Ljava/lang/Boolean;

    .line 77
    .line 78
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 79
    .line 80
    .line 81
    move-result v0

    .line 82
    if-nez v0, :cond_5

    .line 83
    .line 84
    iget-object v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 85
    .line 86
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->U3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V

    .line 87
    .line 88
    .line 89
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object v0

    .line 92
    :cond_5
    iget-object v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 93
    .line 94
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->r3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    sget-object v2, LZU0/a$a;->a:LZU0/a$a;

    .line 99
    .line 100
    invoke-virtual {v0, v2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 101
    .line 102
    .line 103
    iget-object v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 104
    .line 105
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->C3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lek/d;

    .line 106
    .line 107
    .line 108
    move-result-object v0

    .line 109
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->TEMPORARY:Lorg/xbet/balance/model/BalanceScreenType;

    .line 110
    .line 111
    iput v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 112
    .line 113
    move-object v1, v2

    .line 114
    const/4 v2, 0x0

    .line 115
    const/4 v4, 0x2

    .line 116
    const/4 v5, 0x0

    .line 117
    invoke-static/range {v0 .. v5}, Lek/d$a;->a(Lek/d;Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    if-ne v0, v6, :cond_6

    .line 122
    .line 123
    goto :goto_2

    .line 124
    :cond_6
    :goto_1
    iget-object v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 125
    .line 126
    move-object v8, v0

    .line 127
    check-cast v8, Lorg/xbet/balance/model/BalanceModel;

    .line 128
    .line 129
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->s3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lfk/b;

    .line 130
    .line 131
    .line 132
    move-result-object v0

    .line 133
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->TEMPORARY:Lorg/xbet/balance/model/BalanceScreenType;

    .line 134
    .line 135
    invoke-interface {v0, v1, v8}, Lfk/b;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 136
    .line 137
    .line 138
    iget-object v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 139
    .line 140
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->u3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 141
    .line 142
    .line 143
    move-result-object v0

    .line 144
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object v0

    .line 148
    check-cast v0, LYU0/a;

    .line 149
    .line 150
    invoke-virtual {v0}, LYU0/a;->d()Ljava/math/BigDecimal;

    .line 151
    .line 152
    .line 153
    move-result-object v0

    .line 154
    invoke-virtual {v0}, Ljava/math/BigDecimal;->doubleValue()D

    .line 155
    .line 156
    .line 157
    move-result-wide v1

    .line 158
    iget-object v0, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 159
    .line 160
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->G3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/n;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    invoke-virtual {v8}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 165
    .line 166
    .line 167
    move-result-wide v4

    .line 168
    iput-object v8, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->L$0:Ljava/lang/Object;

    .line 169
    .line 170
    iput-wide v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->D$0:D

    .line 171
    .line 172
    iput v7, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 173
    .line 174
    move-wide/from16 v30, v4

    .line 175
    .line 176
    move-object v5, v3

    .line 177
    move-wide/from16 v3, v30

    .line 178
    .line 179
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/toto_bet/makebet/domain/usecase/n;->a(DJLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 180
    .line 181
    .line 182
    move-result-object v0

    .line 183
    move-object v3, v5

    .line 184
    if-ne v0, v6, :cond_7

    .line 185
    .line 186
    :goto_2
    return-object v6

    .line 187
    :cond_7
    move-wide v9, v1

    .line 188
    move-object v2, v8

    .line 189
    :goto_3
    check-cast v0, LUU0/a;

    .line 190
    .line 191
    iget-object v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 192
    .line 193
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->D3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 194
    .line 195
    .line 196
    move-result-object v1

    .line 197
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/domain/usecase/e;->a()LZV0/g;

    .line 198
    .line 199
    .line 200
    move-result-object v1

    .line 201
    iget-object v4, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 202
    .line 203
    invoke-static {v4}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->J3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)LHX0/e;

    .line 204
    .line 205
    .line 206
    move-result-object v11

    .line 207
    invoke-static {v9, v10}, LHc/a;->c(D)Ljava/lang/Double;

    .line 208
    .line 209
    .line 210
    move-result-object v4

    .line 211
    invoke-virtual {v4}, Ljava/lang/Number;->doubleValue()D

    .line 212
    .line 213
    .line 214
    invoke-virtual {v1}, LZV0/g;->f()Lorg/xbet/toto_bet/domain/TotoBetType;

    .line 215
    .line 216
    .line 217
    move-result-object v5

    .line 218
    sget-object v6, Lorg/xbet/toto_bet/domain/TotoBetType;->TOTO_1XTOTO:Lorg/xbet/toto_bet/domain/TotoBetType;

    .line 219
    .line 220
    if-eq v5, v6, :cond_8

    .line 221
    .line 222
    goto :goto_4

    .line 223
    :cond_8
    const/4 v4, 0x0

    .line 224
    :goto_4
    if-eqz v4, :cond_9

    .line 225
    .line 226
    invoke-virtual {v4}, Ljava/lang/Double;->doubleValue()D

    .line 227
    .line 228
    .line 229
    move-result-wide v4

    .line 230
    :goto_5
    move-wide v13, v4

    .line 231
    goto :goto_6

    .line 232
    :cond_9
    const-wide/16 v4, 0x0

    .line 233
    .line 234
    goto :goto_5

    .line 235
    :goto_6
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 236
    .line 237
    .line 238
    move-result-object v15

    .line 239
    invoke-virtual {v0}, LUU0/a;->c()Ljava/lang/String;

    .line 240
    .line 241
    .line 242
    move-result-object v16

    .line 243
    const-string v12, ""

    .line 244
    .line 245
    invoke-static/range {v11 .. v16}, Lio/a;->c(LHX0/e;Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 246
    .line 247
    .line 248
    move-result-object v5

    .line 249
    iget-object v4, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 250
    .line 251
    invoke-static {v4}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->r3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 252
    .line 253
    .line 254
    move-result-object v13

    .line 255
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 256
    .line 257
    .line 258
    move-result-wide v6

    .line 259
    invoke-virtual {v0}, LUU0/a;->c()Ljava/lang/String;

    .line 260
    .line 261
    .line 262
    move-result-object v8

    .line 263
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 264
    .line 265
    .line 266
    move-result-object v12

    .line 267
    invoke-virtual {v1}, LZV0/g;->d()Ljava/lang/String;

    .line 268
    .line 269
    .line 270
    move-result-object v11

    .line 271
    new-instance v4, LZU0/a$e;

    .line 272
    .line 273
    invoke-direct/range {v4 .. v12}, LZU0/a$e;-><init>(Ljava/lang/String;JLjava/lang/String;DLjava/lang/String;Ljava/lang/String;)V

    .line 274
    .line 275
    .line 276
    invoke-virtual {v13, v4}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 277
    .line 278
    .line 279
    iget-object v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 280
    .line 281
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->P3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V

    .line 282
    .line 283
    .line 284
    iget-object v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 285
    .line 286
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->N3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lfk/w;

    .line 287
    .line 288
    .line 289
    move-result-object v1

    .line 290
    invoke-virtual {v2}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 291
    .line 292
    .line 293
    move-result-wide v4

    .line 294
    invoke-virtual {v0}, LUU0/a;->a()D

    .line 295
    .line 296
    .line 297
    move-result-wide v6

    .line 298
    invoke-interface {v1, v4, v5, v6, v7}, Lfk/w;->a(JD)V

    .line 299
    .line 300
    .line 301
    iget-object v1, v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 302
    .line 303
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->O3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lek/f;

    .line 304
    .line 305
    .line 306
    move-result-object v1

    .line 307
    sget-object v4, Lorg/xbet/balance/model/BalanceScreenType;->MAKE_BET:Lorg/xbet/balance/model/BalanceScreenType;

    .line 308
    .line 309
    invoke-virtual {v0}, LUU0/a;->a()D

    .line 310
    .line 311
    .line 312
    move-result-wide v14

    .line 313
    const/16 v28, 0x1ffd

    .line 314
    .line 315
    const/16 v29, 0x0

    .line 316
    .line 317
    const-wide/16 v12, 0x0

    .line 318
    .line 319
    const/16 v16, 0x0

    .line 320
    .line 321
    const/16 v17, 0x0

    .line 322
    .line 323
    const-wide/16 v18, 0x0

    .line 324
    .line 325
    const/16 v20, 0x0

    .line 326
    .line 327
    const/16 v21, 0x0

    .line 328
    .line 329
    const/16 v22, 0x0

    .line 330
    .line 331
    const/16 v23, 0x0

    .line 332
    .line 333
    const/16 v24, 0x0

    .line 334
    .line 335
    const/16 v25, 0x0

    .line 336
    .line 337
    const/16 v26, 0x0

    .line 338
    .line 339
    const/16 v27, 0x0

    .line 340
    .line 341
    move-object v11, v2

    .line 342
    invoke-static/range {v11 .. v29}, Lorg/xbet/balance/model/BalanceModel;->copy$default(Lorg/xbet/balance/model/BalanceModel;JDZZJLjava/lang/String;Ljava/lang/String;ILcom/xbet/onexcore/data/configs/TypeAccount;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;ILjava/lang/Object;)Lorg/xbet/balance/model/BalanceModel;

    .line 343
    .line 344
    .line 345
    move-result-object v0

    .line 346
    invoke-interface {v1, v4, v0}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 347
    .line 348
    .line 349
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 350
    .line 351
    return-object v0
.end method
