.class public final synthetic LN01/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;

.field public final synthetic b:Z

.field public final synthetic c:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;ZLkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/o;->a:Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;

    iput-boolean p2, p0, LN01/o;->b:Z

    iput-object p3, p0, LN01/o;->c:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(L<PERSON><PERSON>/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LN01/o;->a:Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;

    iget-boolean v1, p0, LN01/o;->b:Z

    iget-object v2, p0, LN01/o;->c:Lkotlin/jvm/functions/Function0;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;->v(Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;ZLkotlin/jvm/functions/Function0;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
