.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u000c\u0008\u0082\u0008\u0018\u00002\u00020\u0001B-\u0012\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0010\u0010\r\u001a\u00020\u000cH\u00d6\u0001\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0010\u0010\u000f\u001a\u00020\u0007H\u00d6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u001a\u0010\u0013\u001a\u00020\u00122\u0008\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u001d\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00030\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0015\u0010\u0016\u001a\u0004\u0008\u0017\u0010\u0018R\u0017\u0010\u0006\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u0017\u0010\u0008\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001c\u001a\u0004\u0008\u001d\u0010\u0010R\u0017\u0010\t\u001a\u00020\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001d\u0010\u001c\u001a\u0004\u0008\u0015\u0010\u0010\u00a8\u0006\u001e"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;",
        "",
        "",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
        "measureRows",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
        "measureType",
        "",
        "rowCount",
        "headerCount",
        "<init>",
        "(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "hashCode",
        "()I",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Ljava/util/List;",
        "b",
        "()Ljava/util/List;",
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
        "c",
        "()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
        "I",
        "d",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:I

.field public final d:I


# direct methods
.method public constructor <init>(Ljava/util/List;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;II)V
    .locals 0
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
            ">;",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;",
            "II)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a:Ljava/util/List;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 7
    .line 8
    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c:I

    .line 9
    .line 10
    iput p4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d:I

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final b()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a:Ljava/util/List;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a:Ljava/util/List;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    if-eq v1, v3, :cond_3

    return v2

    :cond_3
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c:I

    iget v3, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c:I

    if-eq v1, v3, :cond_4

    return v2

    :cond_4
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d:I

    iget p1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d:I

    if-eq v1, p1, :cond_5

    return v2

    :cond_5
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a:Ljava/util/List;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c:I

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d:I

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 6
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->a:Ljava/util/List;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$MeasureType;

    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->c:I

    iget v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketMultiline$d;->d:I

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "MeasureParams(measureRows="

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", measureType="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", rowCount="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ", headerCount="

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
