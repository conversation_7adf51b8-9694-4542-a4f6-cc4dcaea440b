<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <org.xbet.uikit.components.toolbar.base.DSNavigationBarBasic
        android:id="@+id/navigationBarAggregator"
        style="@style/Widgets.BasicNavigationBar.Title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:showShadow="false"
        app:title="@string/providers" />

    <FrameLayout
        android:id="@+id/authButtonsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/uikitBackgroundContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigationBarAggregator">

        <org.xbet.uikit.components.authorizationbuttons.AuthorizationButtons
            android:id="@+id/authButtonsView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_12"
            android:layout_marginVertical="@dimen/space_8"
            android:visibility="gone"
            app:dsAuthorizationButtonLabel="@string/button_login"
            app:dsRegistrationButtonLabel="@string/registration" />
    </FrameLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/authButtonsLayout">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/static_transparent"
            app:elevation="@dimen/elevation_0">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsingToolBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:layout_scrollFlags="scroll|exitUntilCollapsed|snap">

                <org.xbet.uikit.components.accountselection.AccountSelection
                    android:id="@+id/accountSelection"
                    style="@style/Widget.AccountSelection.Primary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top"
                    android:layout_marginBottom="@dimen/size_8"
                    android:elevation="@dimen/elevation_2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/navigationBarAggregator"
                    app:topUpButtonText="@string/top_up" />

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:orientation="vertical"
            android:overScrollMode="never"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvProviders"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingBottom="@dimen/space_88"
                android:clipToPadding="false"
                android:overScrollMode="never"
                android:scrollbarStyle="outsideOverlay"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="8"
                tools:listitem="@layout/category_with_providers_header_item" />

        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <org.xbet.uikit.components.lottie_empty.DsLottieEmptyContainer
        android:id="@+id/lottieEmptyView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/navigationBarAggregator"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible" />

    <View
        android:id="@+id/closeKeyboardArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:elevation="@dimen/elevation_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigationBarAggregator" />

</androidx.constraintlayout.widget.ConstraintLayout>