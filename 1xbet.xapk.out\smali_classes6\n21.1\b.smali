.class public interface abstract Ln21/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008f\u0018\u00002\u00020\u0001R\u0014\u0010\u0005\u001a\u00020\u00028&X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0006"
    }
    d2 = {
        "Ln21/b;",
        "",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "a",
        "()Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "styleType",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a()Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method
