.class public final Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000h\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\t\n\u0002\u0008\u001b\u0008\u0000\u0018\u0000 B2\u00020\u0001:\u00012BI\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J4\u0010\u001c\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u001a0\u00192\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0014H\u0086\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ[\u0010$\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a2\u000c\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001a2\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001a2\u000c\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001a2\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001a2\u000c\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001aH\u0002\u00a2\u0006\u0004\u0008$\u0010%J;\u0010&\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u001a0\u0019*\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001b0\u001a0\u00192\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0018\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J;\u0010,\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001e0\u001a0\u00192\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010)\u001a\u00020(2\u0006\u0010*\u001a\u00020(2\u0006\u0010+\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008,\u0010-J&\u0010.\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u001a2\u0006\u0010*\u001a\u00020(2\u0006\u0010+\u001a\u00020\u0016H\u0082@\u00a2\u0006\u0004\u0008.\u0010/J+\u00100\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001e0\u001a0\u00192\u0006\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u00080\u00101R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010A\u00a8\u0006C"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
        "",
        "Lv81/r;",
        "getPopularGamesUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "Lv81/m;",
        "getFavoriteGamesFlowUseCase",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lf81/b;",
        "getFavoriteGamesFlowWithoutCatchingUseCase",
        "Lv81/f;",
        "clearFavoritesCacheUseCase",
        "Lv81/j;",
        "getCategoriesUseCase",
        "<init>",
        "(Lv81/r;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;Lv81/m;Lcom/xbet/onexuser/domain/user/c;Lf81/b;Lv81/f;Lv81/j;)V",
        "",
        "isVirtual",
        "",
        "limitLoadGames",
        "initRequest",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "LLb1/a;",
        "o",
        "(ZIZ)Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "slotsExclusive",
        "aggregatorLive",
        "slotsPopular",
        "aggregatorPopular",
        "favorites",
        "k",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;",
        "p",
        "(Lkotlinx/coroutines/flow/e;ZZ)Lkotlinx/coroutines/flow/e;",
        "",
        "partitionId",
        "categoryId",
        "limit",
        "m",
        "(ZJJI)Lkotlinx/coroutines/flow/e;",
        "n",
        "(JILkotlin/coroutines/e;)Ljava/lang/Object;",
        "l",
        "(ZZ)Lkotlinx/coroutines/flow/e;",
        "a",
        "Lv81/r;",
        "b",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "c",
        "Li8/j;",
        "d",
        "Lv81/m;",
        "e",
        "Lcom/xbet/onexuser/domain/user/c;",
        "f",
        "Lf81/b;",
        "g",
        "Lv81/f;",
        "h",
        "Lv81/j;",
        "i",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final i:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lv81/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lv81/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lf81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lv81/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lv81/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->i:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$a;

    return-void
.end method

.method public constructor <init>(Lv81/r;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;Lv81/m;Lcom/xbet/onexuser/domain/user/c;Lf81/b;Lv81/f;Lv81/j;)V
    .locals 0
    .param p1    # Lv81/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lv81/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lf81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lv81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lv81/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->a:Lv81/r;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->c:Li8/j;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->d:Lv81/m;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->e:Lcom/xbet/onexuser/domain/user/c;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->f:Lf81/b;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->g:Lv81/f;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->h:Lv81/j;

    .line 19
    .line 20
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->k(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lv81/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->g:Lv81/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ZZ)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->l(ZZ)Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lv81/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->h:Lv81/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lv81/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->d:Lv81/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lf81/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->f:Lf81/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lorg/xbet/remoteconfig/domain/usecases/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Li8/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->c:Li8/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lcom/xbet/onexuser/domain/user/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->e:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;JILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->n(JILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final k(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;)",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 6
    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 8
    .line 9
    .line 10
    move-result-wide v3

    .line 11
    sget-object v6, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->EXCLUSIVE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 12
    .line 13
    new-instance v2, LLb1/a;

    .line 14
    .line 15
    const-string v7, ""

    .line 16
    .line 17
    const/4 v9, 0x0

    .line 18
    move-object/from16 v5, p1

    .line 19
    .line 20
    move-object/from16 v8, p5

    .line 21
    .line 22
    invoke-direct/range {v2 .. v9}, LLb1/a;-><init>(JLjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/lang/String;Ljava/util/List;Z)V

    .line 23
    .line 24
    .line 25
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    sget-object v2, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 29
    .line 30
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 31
    .line 32
    .line 33
    move-result-wide v11

    .line 34
    sget-object v14, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 35
    .line 36
    new-instance v10, LLb1/a;

    .line 37
    .line 38
    const-string v15, ""

    .line 39
    .line 40
    const/16 v17, 0x0

    .line 41
    .line 42
    move-object/from16 v13, p2

    .line 43
    .line 44
    move-object/from16 v16, p5

    .line 45
    .line 46
    invoke-direct/range {v10 .. v17}, LLb1/a;-><init>(JLjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/lang/String;Ljava/util/List;Z)V

    .line 47
    .line 48
    .line 49
    invoke-interface {v0, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 53
    .line 54
    .line 55
    move-result-wide v11

    .line 56
    sget-object v14, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->SLOTS_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 57
    .line 58
    new-instance v10, LLb1/a;

    .line 59
    .line 60
    const-string v15, ""

    .line 61
    .line 62
    move-object/from16 v13, p3

    .line 63
    .line 64
    invoke-direct/range {v10 .. v17}, LLb1/a;-><init>(JLjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/lang/String;Ljava/util/List;Z)V

    .line 65
    .line 66
    .line 67
    invoke-interface {v0, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 68
    .line 69
    .line 70
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 71
    .line 72
    .line 73
    move-result-wide v11

    .line 74
    sget-object v14, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->LIVE_AGGREGATOR_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 75
    .line 76
    new-instance v10, LLb1/a;

    .line 77
    .line 78
    const-string v15, ""

    .line 79
    .line 80
    move-object/from16 v13, p4

    .line 81
    .line 82
    invoke-direct/range {v10 .. v17}, LLb1/a;-><init>(JLjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/lang/String;Ljava/util/List;Z)V

    .line 83
    .line 84
    .line 85
    invoke-interface {v0, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 86
    .line 87
    .line 88
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 89
    .line 90
    .line 91
    move-result-object v0

    .line 92
    new-instance v1, Ljava/util/ArrayList;

    .line 93
    .line 94
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 95
    .line 96
    .line 97
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 102
    .line 103
    .line 104
    move-result v2

    .line 105
    if-eqz v2, :cond_1

    .line 106
    .line 107
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    move-object v3, v2

    .line 112
    check-cast v3, LLb1/a;

    .line 113
    .line 114
    invoke-virtual {v3}, LLb1/a;->b()Ljava/util/List;

    .line 115
    .line 116
    .line 117
    move-result-object v3

    .line 118
    invoke-interface {v3}, Ljava/util/Collection;->isEmpty()Z

    .line 119
    .line 120
    .line 121
    move-result v3

    .line 122
    if-nez v3, :cond_0

    .line 123
    .line 124
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 125
    .line 126
    .line 127
    goto :goto_0

    .line 128
    :cond_1
    return-object v1
.end method

.method public final l(ZZ)Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->e:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/user/c;->d()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, v2, p0, p2, p1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ZZ)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    return-object p1
.end method

.method public final m(ZJJI)Lkotlinx/coroutines/flow/e;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZJJI)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->a:Lv81/r;

    .line 2
    .line 3
    const-wide/16 v1, 0x0

    .line 4
    .line 5
    cmp-long v3, p4, v1

    .line 6
    .line 7
    if-eqz v3, :cond_0

    .line 8
    .line 9
    invoke-static {p4, p5}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object p4

    .line 13
    invoke-static {p4}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object p4

    .line 17
    :goto_0
    move-object v3, p4

    .line 18
    goto :goto_1

    .line 19
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 20
    .line 21
    .line 22
    move-result-object p4

    .line 23
    goto :goto_0

    .line 24
    :goto_1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v4

    .line 28
    iget-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 29
    .line 30
    invoke-interface {p4}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 31
    .line 32
    .line 33
    move-result-object p4

    .line 34
    invoke-virtual {p4}, Lek0/o;->o()Lek0/a;

    .line 35
    .line 36
    .line 37
    move-result-object p4

    .line 38
    invoke-virtual {p4}, Lek0/a;->c()Z

    .line 39
    .line 40
    .line 41
    move-result v6

    .line 42
    iget-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->c:Li8/j;

    .line 43
    .line 44
    invoke-interface {p4}, Li8/j;->invoke()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v5

    .line 48
    const/4 v8, 0x0

    .line 49
    move-wide v1, p2

    .line 50
    move v7, p6

    .line 51
    invoke-interface/range {v0 .. v8}, Lv81/r;->a(JLjava/util/List;Ljava/util/List;Ljava/lang/String;ZIZ)Lkotlinx/coroutines/flow/e;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    const/4 p3, 0x0

    .line 56
    const/4 p4, 0x2

    .line 57
    const/4 p5, 0x0

    .line 58
    invoke-static {p2, p1, p3, p4, p5}, Lcom/xbet/onexcore/utils/flows/ScreenRetryStrategiesExtentionsKt;->g(Lkotlinx/coroutines/flow/e;ZZILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    new-instance p2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getGames$1;

    .line 63
    .line 64
    invoke-direct {p2, p5}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getGames$1;-><init>(Lkotlin/coroutines/e;)V

    .line 65
    .line 66
    .line 67
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    return-object p1
.end method

.method public final n(JILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 25
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p4

    .line 4
    .line 5
    instance-of v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v0

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;

    .line 25
    .line 26
    invoke-direct {v2, v1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v0, v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x1

    .line 38
    if-eqz v4, :cond_2

    .line 39
    .line 40
    if-ne v4, v5, :cond_1

    .line 41
    .line 42
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :catchall_0
    move-exception v0

    .line 47
    goto :goto_2

    .line 48
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw v0

    .line 56
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 60
    .line 61
    iget-object v6, v1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->a:Lv81/r;

    .line 62
    .line 63
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 64
    .line 65
    .line 66
    move-result-object v9

    .line 67
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v10

    .line 71
    iget-object v0, v1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 72
    .line 73
    invoke-interface {v0}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    invoke-virtual {v0}, Lek0/o;->o()Lek0/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {v0}, Lek0/a;->c()Z

    .line 82
    .line 83
    .line 84
    move-result v12

    .line 85
    iget-object v0, v1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->c:Li8/j;

    .line 86
    .line 87
    invoke-interface {v0}, Li8/j;->invoke()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v11

    .line 91
    const/4 v14, 0x0

    .line 92
    move-wide/from16 v7, p1

    .line 93
    .line 94
    move/from16 v13, p3

    .line 95
    .line 96
    invoke-interface/range {v6 .. v14}, Lv81/r;->a(JLjava/util/List;Ljava/util/List;Ljava/lang/String;ZIZ)Lkotlinx/coroutines/flow/e;

    .line 97
    .line 98
    .line 99
    move-result-object v15

    .line 100
    const-string v16, "GetPopularGamesCategoriesScenario.getVirtualGames"

    .line 101
    .line 102
    const/16 v23, 0x38

    .line 103
    .line 104
    const/16 v24, 0x0

    .line 105
    .line 106
    const/16 v17, 0x3

    .line 107
    .line 108
    const-wide/16 v18, 0x3

    .line 109
    .line 110
    const/16 v20, 0x0

    .line 111
    .line 112
    const/16 v21, 0x0

    .line 113
    .line 114
    const/16 v22, 0x0

    .line 115
    .line 116
    invoke-static/range {v15 .. v24}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->e(Lkotlinx/coroutines/flow/e;Ljava/lang/String;IJLjava/util/List;Ljava/util/List;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    new-instance v4, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$2$1;

    .line 121
    .line 122
    const/4 v6, 0x0

    .line 123
    invoke-direct {v4, v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$2$1;-><init>(Lkotlin/coroutines/e;)V

    .line 124
    .line 125
    .line 126
    invoke-static {v0, v4}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    iput v5, v2, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getVirtualGames$1;->label:I

    .line 131
    .line 132
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->N(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    if-ne v0, v3, :cond_3

    .line 137
    .line 138
    return-object v3

    .line 139
    :cond_3
    :goto_1
    check-cast v0, Ljava/util/List;

    .line 140
    .line 141
    if-nez v0, :cond_4

    .line 142
    .line 143
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 144
    .line 145
    .line 146
    move-result-object v0

    .line 147
    :cond_4
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 151
    goto :goto_3

    .line 152
    :goto_2
    instance-of v2, v0, Ljava/net/SocketTimeoutException;

    .line 153
    .line 154
    if-nez v2, :cond_6

    .line 155
    .line 156
    instance-of v2, v0, Ljava/net/ConnectException;

    .line 157
    .line 158
    if-nez v2, :cond_6

    .line 159
    .line 160
    instance-of v2, v0, Ljava/net/UnknownHostException;

    .line 161
    .line 162
    if-nez v2, :cond_6

    .line 163
    .line 164
    instance-of v2, v0, Ljava/util/concurrent/CancellationException;

    .line 165
    .line 166
    if-nez v2, :cond_6

    .line 167
    .line 168
    instance-of v2, v0, Lcom/xbet/onexuser/domain/exceptions/NotValidRefreshTokenException;

    .line 169
    .line 170
    if-nez v2, :cond_6

    .line 171
    .line 172
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 173
    .line 174
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    move-result-object v0

    .line 178
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 179
    .line 180
    .line 181
    move-result-object v0

    .line 182
    :goto_3
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 183
    .line 184
    .line 185
    move-result-object v2

    .line 186
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 187
    .line 188
    .line 189
    move-result v3

    .line 190
    if-eqz v3, :cond_5

    .line 191
    .line 192
    move-object v0, v2

    .line 193
    :cond_5
    return-object v0

    .line 194
    :cond_6
    throw v0
.end method

.method public final o(ZIZ)Lkotlinx/coroutines/flow/e;
    .locals 15
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZIZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    move/from16 v1, p3

    .line 2
    .line 3
    const/4 v7, 0x0

    .line 4
    if-eqz p1, :cond_0

    .line 5
    .line 6
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;

    .line 7
    .line 8
    move/from16 v6, p2

    .line 9
    .line 10
    invoke-direct {v0, p0, v6, v7}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ILkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->V(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v2, 0x1

    .line 18
    invoke-virtual {p0, v0, v2, v1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->p(Lkotlinx/coroutines/flow/e;ZZ)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    return-object v0

    .line 23
    :cond_0
    move/from16 v6, p2

    .line 24
    .line 25
    sget-object v8, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 26
    .line 27
    invoke-virtual {v8}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 28
    .line 29
    .line 30
    move-result-wide v2

    .line 31
    const-wide/16 v4, 0x59

    .line 32
    .line 33
    move-object v0, p0

    .line 34
    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->m(ZJJI)Lkotlinx/coroutines/flow/e;

    .line 35
    .line 36
    .line 37
    move-result-object v9

    .line 38
    sget-object v10, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 39
    .line 40
    invoke-virtual {v10}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 41
    .line 42
    .line 43
    move-result-wide v2

    .line 44
    const-wide/16 v4, 0x4b

    .line 45
    .line 46
    move/from16 v1, p3

    .line 47
    .line 48
    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->m(ZJJI)Lkotlinx/coroutines/flow/e;

    .line 49
    .line 50
    .line 51
    move-result-object v11

    .line 52
    invoke-virtual {v8}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 53
    .line 54
    .line 55
    move-result-wide v2

    .line 56
    const-wide/16 v4, 0x11

    .line 57
    .line 58
    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->m(ZJJI)Lkotlinx/coroutines/flow/e;

    .line 59
    .line 60
    .line 61
    move-result-object v8

    .line 62
    invoke-virtual {v10}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 63
    .line 64
    .line 65
    move-result-wide v2

    .line 66
    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->m(ZJJI)Lkotlinx/coroutines/flow/e;

    .line 67
    .line 68
    .line 69
    move-result-object v12

    .line 70
    const/4 v2, 0x0

    .line 71
    invoke-virtual {p0, v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->l(ZZ)Lkotlinx/coroutines/flow/e;

    .line 72
    .line 73
    .line 74
    move-result-object v13

    .line 75
    new-instance v14, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$2;

    .line 76
    .line 77
    invoke-direct {v14, p0, v7}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;Lkotlin/coroutines/e;)V

    .line 78
    .line 79
    .line 80
    move-object v10, v11

    .line 81
    move-object v11, v8

    .line 82
    invoke-static/range {v9 .. v14}, Lkotlinx/coroutines/flow/g;->r(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/q;)Lkotlinx/coroutines/flow/e;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    return-object v1
.end method

.method public final p(Lkotlinx/coroutines/flow/e;ZZ)Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;>;ZZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$$inlined$flatMapLatest$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1, p0, p3, p2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ZZ)V

    .line 5
    .line 6
    .line 7
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method
