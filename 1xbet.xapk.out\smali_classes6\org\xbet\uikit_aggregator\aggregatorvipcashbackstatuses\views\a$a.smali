.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static a(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static b(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static c(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static d(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static e(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static f(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static g(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static h(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method

.method public static i(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V
    .locals 0
    .param p0    # Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    return-void
.end method
