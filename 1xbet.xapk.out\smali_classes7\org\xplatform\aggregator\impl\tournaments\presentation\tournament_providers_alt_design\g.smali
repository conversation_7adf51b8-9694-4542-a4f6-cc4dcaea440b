.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel;

.field public final synthetic b:J

.field public final synthetic c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

.field public final synthetic d:Ljava/lang/String;

.field public final synthetic e:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->b:J

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->d:Ljava/lang/String;

    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->e:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel;

    iget-wide v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->b:J

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->c:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->d:Ljava/lang/String;

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/g;->e:Ljava/lang/String;

    invoke-static/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$onParticipateClick$2;->a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
