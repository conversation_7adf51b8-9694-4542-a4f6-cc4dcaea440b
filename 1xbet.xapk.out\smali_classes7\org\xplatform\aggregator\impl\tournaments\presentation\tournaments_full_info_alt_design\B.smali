.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/B;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/fragment/app/J;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerFixedStyleFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerFixedStyleFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/B;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerFixedStyleFragment;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/B;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerFixedStyleFragment;

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerFixedStyleFragment;->F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoContainerFixedStyleFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method
