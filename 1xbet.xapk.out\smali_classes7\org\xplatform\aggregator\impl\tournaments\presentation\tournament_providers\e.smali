.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
