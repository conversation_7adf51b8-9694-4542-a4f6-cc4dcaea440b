.class public final LNZ0/n;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "",
        "Lorg/xbet/uikit/components/chips/DsChipStyle;",
        "a",
        "(I)Lorg/xbet/uikit/components/chips/DsChipStyle;",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(I)Lorg/xbet/uikit/components/chips/DsChipStyle;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    packed-switch p0, :pswitch_data_0

    .line 2
    .line 3
    .line 4
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->PRIMARY:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 5
    .line 6
    return-object p0

    .line 7
    :pswitch_0
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->OVERLAY_VALUE:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 8
    .line 9
    return-object p0

    .line 10
    :pswitch_1
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->OVERLAY:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 11
    .line 12
    return-object p0

    .line 13
    :pswitch_2
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->TERTIARY_VALUE:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 14
    .line 15
    return-object p0

    .line 16
    :pswitch_3
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->TERTIARY:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 17
    .line 18
    return-object p0

    .line 19
    :pswitch_4
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->SECONDARY_VALUE:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 20
    .line 21
    return-object p0

    .line 22
    :pswitch_5
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->SECONDARY:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 23
    .line 24
    return-object p0

    .line 25
    :pswitch_6
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->PRIMARY_VALUE:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 26
    .line 27
    return-object p0

    .line 28
    :pswitch_7
    sget-object p0, Lorg/xbet/uikit/components/chips/DsChipStyle;->PRIMARY:Lorg/xbet/uikit/components/chips/DsChipStyle;

    .line 29
    .line 30
    return-object p0

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
