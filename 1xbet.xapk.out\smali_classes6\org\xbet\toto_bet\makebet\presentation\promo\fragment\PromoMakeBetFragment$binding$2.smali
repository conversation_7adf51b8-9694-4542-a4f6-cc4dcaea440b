.class final synthetic Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;
.super Lkotlin/jvm/internal/FunctionReferenceImpl;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1001
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/FunctionReferenceImpl;",
        "Lkotlin/jvm/functions/Function1<",
        "Landroid/view/View;",
        "LOU0/f;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;

    invoke-direct {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;-><init>()V

    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;

    return-void
.end method

.method public constructor <init>()V
    .locals 6

    const-string v4, "bind(Landroid/view/View;)Lorg/xbet/toto_bet/impl/databinding/FragmentPromoBetTotoBetBinding;"

    const/4 v5, 0x0

    const/4 v1, 0x1

    const-class v2, LOU0/f;

    const-string v3, "bind"

    move-object v0, p0

    invoke-direct/range {v0 .. v5}, Lkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method public final invoke(Landroid/view/View;)LOU0/f;
    .locals 0

    .line 1
    invoke-static {p1}, LOU0/f;->a(Landroid/view/View;)LOU0/f;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Landroid/view/View;

    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$binding$2;->invoke(Landroid/view/View;)LOU0/f;

    move-result-object p1

    return-object p1
.end method
