.class public final synthetic LO11/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LO11/m;


# direct methods
.method public synthetic constructor <init>(LO11/m;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO11/l;->a:LO11/m;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LO11/l;->a:LO11/m;

    invoke-static {v0}, LO11/m;->c(LO11/m;)V

    return-void
.end method
