.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0003\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u00084\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 \u0099\u00012\u00020\u0001:\u0002\u009a\u0001B\u00c9\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u00a2\u0006\u0004\u00082\u00103J\u000f\u00105\u001a\u000204H\u0002\u00a2\u0006\u0004\u00085\u00106J\u0017\u00109\u001a\u0002042\u0006\u00108\u001a\u000207H\u0002\u00a2\u0006\u0004\u00089\u0010:J\u000f\u0010;\u001a\u000204H\u0002\u00a2\u0006\u0004\u0008;\u00106J\u0017\u0010>\u001a\u0002042\u0006\u0010=\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008>\u0010?J\u000f\u0010@\u001a\u000204H\u0002\u00a2\u0006\u0004\u0008@\u00106J\u000f\u0010A\u001a\u000204H\u0002\u00a2\u0006\u0004\u0008A\u00106J\u0010\u0010B\u001a\u000204H\u0082@\u00a2\u0006\u0004\u0008B\u0010CJ\u0017\u0010E\u001a\u0002042\u0006\u0010=\u001a\u00020DH\u0002\u00a2\u0006\u0004\u0008E\u0010FJ\u0013\u0010I\u001a\u0008\u0012\u0004\u0012\u00020H0G\u00a2\u0006\u0004\u0008I\u0010JJ\u0013\u0010L\u001a\u0008\u0012\u0004\u0012\u00020K0G\u00a2\u0006\u0004\u0008L\u0010JJ\u0013\u0010N\u001a\u0008\u0012\u0004\u0012\u00020M0G\u00a2\u0006\u0004\u0008N\u0010JJ\u0013\u0010Q\u001a\u0008\u0012\u0004\u0012\u00020P0O\u00a2\u0006\u0004\u0008Q\u0010RJ\r\u0010S\u001a\u000204\u00a2\u0006\u0004\u0008S\u00106J\u0015\u0010V\u001a\u0002042\u0006\u0010U\u001a\u00020T\u00a2\u0006\u0004\u0008V\u0010WJ\r\u0010X\u001a\u000204\u00a2\u0006\u0004\u0008X\u00106R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010\\R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010lR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010nR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010pR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010tR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010vR\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010~R\u0015\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u007f\u0010\u0080\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0001\u0010\u0082\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0083\u0001\u0010\u0084\u0001R\u0016\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0085\u0001\u0010\u0086\u0001R\u0016\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R\u001e\u0010\u008c\u0001\u001a\t\u0012\u0004\u0012\u00020K0\u0089\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008a\u0001\u0010\u008b\u0001R\u001f\u0010\u0091\u0001\u001a\n\u0012\u0005\u0012\u00030\u008e\u00010\u008d\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u001f\u0010\u0094\u0001\u001a\n\u0012\u0005\u0012\u00030\u0092\u00010\u008d\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0093\u0001\u0010\u0090\u0001R\u001e\u0010\u0096\u0001\u001a\t\u0012\u0004\u0012\u00020P0\u008d\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0095\u0001\u0010\u0090\u0001R \u0010\u0098\u0001\u001a\u000b\u0012\u0006\u0012\u0004\u0018\u00010M0\u008d\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0097\u0001\u0010\u0090\u0001\u00a8\u0006\u009b\u0001"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwX0/c;",
        "router",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LHX0/e;",
        "resourceManager",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "profileInteractor",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/a;",
        "getMaxBetSumUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/c;",
        "getMinBetSumScenario",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/p;",
        "refreshBalanceLimitsUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/n;",
        "makeBetWithSumScenario",
        "Lm8/a;",
        "dispatchers",
        "Lll/a;",
        "betHistoryFeature",
        "Lfk/w;",
        "updateMoneyUseCase",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lfk/r;",
        "hasUserMultipleBalancesUseCase",
        "Lfk/b;",
        "addScreenBalanceUseCase",
        "Lzg/a;",
        "betAnalytics",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "getSelectedTotoUseCase",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lfk/o;",
        "getScreenBalanceStreamUseCase",
        "Lorg/xbet/toto_bet/core/domain/usecase/a;",
        "getTaxStatusStreamUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;",
        "getTaxStatusScenario",
        "Lorg/xbet/toto_bet/core/domain/usecase/c;",
        "getTotoDsEnabledUseCase",
        "<init>",
        "(LwX0/c;LxX0/a;LHX0/e;Lek/d;Lfk/l;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/toto_bet/makebet/domain/usecase/a;Lorg/xbet/toto_bet/makebet/domain/usecase/c;Lorg/xbet/toto_bet/makebet/domain/usecase/p;Lorg/xbet/toto_bet/makebet/domain/usecase/n;Lm8/a;Lll/a;Lfk/w;Lek/f;Lfk/r;Lfk/b;Lzg/a;Lgk/b;Lorg/xbet/toto_bet/makebet/domain/usecase/e;Lorg/xbet/ui_common/utils/internet/a;Lfk/o;Lorg/xbet/toto_bet/core/domain/usecase/a;Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;Lorg/xbet/toto_bet/core/domain/usecase/c;)V",
        "",
        "e4",
        "()V",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "m4",
        "(Lorg/xbet/balance/model/BalanceModel;)V",
        "d4",
        "",
        "throwable",
        "c4",
        "(Ljava/lang/Throwable;)V",
        "n4",
        "g4",
        "Z3",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lcom/xbet/onexcore/data/model/ServerException;",
        "b4",
        "(Lcom/xbet/onexcore/data/model/ServerException;)V",
        "Lkotlinx/coroutines/flow/e;",
        "LaV0/b;",
        "Y3",
        "()Lkotlinx/coroutines/flow/e;",
        "LZU0/a;",
        "W3",
        "LIU0/a;",
        "a4",
        "Lkotlinx/coroutines/flow/f0;",
        "LaV0/a;",
        "X3",
        "()Lkotlinx/coroutines/flow/f0;",
        "i4",
        "",
        "sum",
        "V3",
        "(Ljava/lang/String;)V",
        "k4",
        "v1",
        "LwX0/c;",
        "x1",
        "LxX0/a;",
        "y1",
        "LHX0/e;",
        "F1",
        "Lek/d;",
        "H1",
        "Lfk/l;",
        "I1",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "P1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/a;",
        "S1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/c;",
        "V1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/p;",
        "b2",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/n;",
        "v2",
        "Lm8/a;",
        "x2",
        "Lll/a;",
        "y2",
        "Lfk/w;",
        "F2",
        "Lek/f;",
        "H2",
        "Lfk/r;",
        "I2",
        "Lfk/b;",
        "P2",
        "Lzg/a;",
        "S2",
        "Lgk/b;",
        "V2",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "X2",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "F3",
        "Lfk/o;",
        "H3",
        "Lorg/xbet/toto_bet/core/domain/usecase/a;",
        "I3",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;",
        "S3",
        "Lorg/xbet/toto_bet/core/domain/usecase/c;",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "H4",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "actionStreamState",
        "Lkotlinx/coroutines/flow/V;",
        "",
        "X4",
        "Lkotlinx/coroutines/flow/V;",
        "currentBalanceIdStateStream",
        "LYU0/a;",
        "v5",
        "betInputState",
        "w5",
        "balanceStateStream",
        "x5",
        "taxStatusStreamState",
        "y5",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final y5:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final z5:I


# instance fields
.field public final F1:Lek/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lfk/o;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lfk/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Lorg/xbet/toto_bet/core/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "LZU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lfk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lzg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lorg/xbet/toto_bet/core/domain/usecase/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/toto_bet/makebet/domain/usecase/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lorg/xbet/toto_bet/makebet/domain/usecase/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LYU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LaV0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lll/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LIU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lfk/w;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y5:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->z5:I

    return-void
.end method

.method public constructor <init>(LwX0/c;LxX0/a;LHX0/e;Lek/d;Lfk/l;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/toto_bet/makebet/domain/usecase/a;Lorg/xbet/toto_bet/makebet/domain/usecase/c;Lorg/xbet/toto_bet/makebet/domain/usecase/p;Lorg/xbet/toto_bet/makebet/domain/usecase/n;Lm8/a;Lll/a;Lfk/w;Lek/f;Lfk/r;Lfk/b;Lzg/a;Lgk/b;Lorg/xbet/toto_bet/makebet/domain/usecase/e;Lorg/xbet/ui_common/utils/internet/a;Lfk/o;Lorg/xbet/toto_bet/core/domain/usecase/a;Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;Lorg/xbet/toto_bet/core/domain/usecase/c;)V
    .locals 2
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/toto_bet/makebet/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/toto_bet/makebet/domain/usecase/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/toto_bet/makebet/domain/usecase/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xbet/toto_bet/makebet/domain/usecase/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lll/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lfk/w;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lfk/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lfk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lzg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lorg/xbet/toto_bet/makebet/domain/usecase/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lfk/o;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lorg/xbet/toto_bet/core/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/toto_bet/core/domain/usecase/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v1:LwX0/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x1:LxX0/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y1:LHX0/e;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->F1:Lek/d;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H1:Lfk/l;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I1:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->V1:Lorg/xbet/toto_bet/makebet/domain/usecase/p;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->b2:Lorg/xbet/toto_bet/makebet/domain/usecase/n;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v2:Lm8/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x2:Lll/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y2:Lfk/w;

    .line 29
    .line 30
    move-object/from16 p2, p14

    .line 31
    .line 32
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->F2:Lek/f;

    .line 33
    .line 34
    move-object/from16 p2, p15

    .line 35
    .line 36
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H2:Lfk/r;

    .line 37
    .line 38
    move-object/from16 p2, p16

    .line 39
    .line 40
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I2:Lfk/b;

    .line 41
    .line 42
    move-object/from16 p2, p17

    .line 43
    .line 44
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->P2:Lzg/a;

    .line 45
    .line 46
    move-object/from16 p2, p18

    .line 47
    .line 48
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->S2:Lgk/b;

    .line 49
    .line 50
    move-object/from16 p2, p19

    .line 51
    .line 52
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->V2:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 53
    .line 54
    move-object/from16 p2, p20

    .line 55
    .line 56
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->X2:Lorg/xbet/ui_common/utils/internet/a;

    .line 57
    .line 58
    move-object/from16 p2, p21

    .line 59
    .line 60
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->F3:Lfk/o;

    .line 61
    .line 62
    move-object/from16 p2, p22

    .line 63
    .line 64
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H3:Lorg/xbet/toto_bet/core/domain/usecase/a;

    .line 65
    .line 66
    move-object/from16 p2, p23

    .line 67
    .line 68
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I3:Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;

    .line 69
    .line 70
    move-object/from16 p2, p24

    .line 71
    .line 72
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->S3:Lorg/xbet/toto_bet/core/domain/usecase/c;

    .line 73
    .line 74
    new-instance p2, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 75
    .line 76
    const/4 p3, 0x3

    .line 77
    const/4 p4, 0x0

    .line 78
    const/4 p5, 0x0

    .line 79
    invoke-direct {p2, p4, p5, p3, p5}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 80
    .line 81
    .line 82
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 83
    .line 84
    const-wide/16 p2, 0x0

    .line 85
    .line 86
    invoke-static {p2, p3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 87
    .line 88
    .line 89
    move-result-object p2

    .line 90
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 91
    .line 92
    .line 93
    move-result-object p2

    .line 94
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 95
    .line 96
    new-instance p6, LYU0/a;

    .line 97
    .line 98
    new-instance p2, Ljava/math/BigDecimal;

    .line 99
    .line 100
    const-wide/high16 v0, -0x4010000000000000L    # -1.0

    .line 101
    .line 102
    invoke-static {v0, v1}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object p3

    .line 106
    invoke-direct {p2, p3}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 107
    .line 108
    .line 109
    invoke-virtual {p8}, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->a()Ljava/math/BigDecimal;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    invoke-virtual {p7}, Lorg/xbet/toto_bet/makebet/domain/usecase/a;->a()Ljava/math/BigDecimal;

    .line 114
    .line 115
    .line 116
    move-result-object p3

    .line 117
    const/4 p7, 0x1

    .line 118
    const-string v0, ""

    .line 119
    .line 120
    move-object p8, p1

    .line 121
    move-object p7, p2

    .line 122
    move-object p9, p3

    .line 123
    move-object p11, v0

    .line 124
    const/4 p10, 0x1

    .line 125
    invoke-direct/range {p6 .. p11}, LYU0/a;-><init>(Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;ZLjava/lang/String;)V

    .line 126
    .line 127
    .line 128
    invoke-static {p6}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 129
    .line 130
    .line 131
    move-result-object p1

    .line 132
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 133
    .line 134
    new-instance p1, LaV0/a;

    .line 135
    .line 136
    new-instance p2, LoZ0/e;

    .line 137
    .line 138
    const-string p3, ""

    .line 139
    .line 140
    invoke-direct {p2, p3, p3}, LoZ0/e;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 141
    .line 142
    .line 143
    const/4 p3, 0x1

    .line 144
    invoke-direct {p1, p2, p4, p3}, LaV0/a;-><init>(LoZ0/e;ZZ)V

    .line 145
    .line 146
    .line 147
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->w5:Lkotlinx/coroutines/flow/V;

    .line 152
    .line 153
    invoke-static {p5}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 158
    .line 159
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->e4()V

    .line 160
    .line 161
    .line 162
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lek/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->F1:Lek/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->V2:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/core/domain/usecase/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->S3:Lorg/xbet/toto_bet/core/domain/usecase/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lfk/r;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H2:Lfk/r;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/n;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->b2:Lorg/xbet/toto_bet/makebet/domain/usecase/n;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I1:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/p;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->V1:Lorg/xbet/toto_bet/makebet/domain/usecase/p;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v1:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->Z3(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lfk/w;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y2:Lfk/w;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->F2:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->d4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic Q3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->f4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->g4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic S3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->h4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic T3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lorg/xbet/balance/model/BalanceModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->m4(Lorg/xbet/balance/model/BalanceModel;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic U3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->n4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final c4(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 6
    .line 7
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->b4(Lcom/xbet/onexcore/data/model/ServerException;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 12
    .line 13
    if-nez v0, :cond_3

    .line 14
    .line 15
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 21
    .line 22
    new-instance v1, LZU0/a$d;

    .line 23
    .line 24
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-nez p1, :cond_2

    .line 29
    .line 30
    const-string p1, ""

    .line 31
    .line 32
    :cond_2
    invoke-direct {v1, p1}, LZU0/a$d;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    return-void

    .line 39
    :cond_3
    :goto_0
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->n4()V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public static final synthetic f4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic h4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final j4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->c4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final l4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->c4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final n4()V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    new-instance v1, LZU0/a$c;

    .line 4
    .line 5
    new-instance v2, Ly01/g;

    .line 6
    .line 7
    sget-object v3, Ly01/i$a;->a:Ly01/i$a;

    .line 8
    .line 9
    iget-object v4, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y1:LHX0/e;

    .line 10
    .line 11
    sget v5, Lpb/k;->no_connection_title_with_hyphen:I

    .line 12
    .line 13
    const/4 v6, 0x0

    .line 14
    new-array v7, v6, [Ljava/lang/Object;

    .line 15
    .line 16
    invoke-interface {v4, v5, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v4

    .line 20
    iget-object v5, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y1:LHX0/e;

    .line 21
    .line 22
    sget v7, Lpb/k;->no_connection_description:I

    .line 23
    .line 24
    new-array v6, v6, [Ljava/lang/Object;

    .line 25
    .line 26
    invoke-interface {v5, v7, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v5

    .line 30
    const/16 v9, 0x38

    .line 31
    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v6, 0x0

    .line 34
    const/4 v7, 0x0

    .line 35
    const/4 v8, 0x0

    .line 36
    invoke-direct/range {v2 .. v10}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 37
    .line 38
    .line 39
    invoke-direct {v1, v2}, LZU0/a$c;-><init>(Ly01/g;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->j4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->l4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic r3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lfk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I2:Lfk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->w5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)LxX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x1:LxX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/internet/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->X2:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->S2:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lfk/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H1:Lfk/l;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final V3(Ljava/lang/String;)V
    .locals 10
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, LYU0/a;

    .line 9
    .line 10
    invoke-static {p1}, Lkotlin/text/u;->t(Ljava/lang/String;)Ljava/math/BigDecimal;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    if-nez v3, :cond_1

    .line 15
    .line 16
    new-instance v3, Ljava/math/BigDecimal;

    .line 17
    .line 18
    const-wide/high16 v4, -0x4010000000000000L    # -1.0

    .line 19
    .line 20
    invoke-static {v4, v5}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v4

    .line 24
    invoke-direct {v3, v4}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    :cond_1
    const/16 v8, 0x1e

    .line 28
    .line 29
    const/4 v9, 0x0

    .line 30
    const/4 v4, 0x0

    .line 31
    const/4 v5, 0x0

    .line 32
    const/4 v6, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    invoke-static/range {v2 .. v9}, LYU0/a;->b(LYU0/a;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;ZLjava/lang/String;ILjava/lang/Object;)LYU0/a;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 39
    .line 40
    .line 41
    move-result v1

    .line 42
    if-eqz v1, :cond_0

    .line 43
    .line 44
    return-void
.end method

.method public final W3()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LZU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    const-wide/16 v1, 0x64

    .line 4
    .line 5
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->z(Lkotlinx/coroutines/flow/e;J)Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final X3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "LaV0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->w5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final Y3()Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LaV0/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getBetInputStateStream$$inlined$map$1;

    .line 8
    .line 9
    invoke-direct {v1, v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getBetInputStateStream$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V

    .line 10
    .line 11
    .line 12
    return-object v1
.end method

.method public final Z3(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object v0, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v0, Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :catchall_0
    move-exception p1

    .line 47
    goto :goto_2

    .line 48
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw p1

    .line 56
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    :try_start_1
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 60
    .line 61
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I3:Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;

    .line 62
    .line 63
    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->L$0:Ljava/lang/Object;

    .line 64
    .line 65
    iput v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$getTaxStatus$1;->label:I

    .line 66
    .line 67
    invoke-virtual {v2, v0}, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    if-ne v0, v1, :cond_3

    .line 72
    .line 73
    return-object v1

    .line 74
    :cond_3
    move-object v4, v0

    .line 75
    move-object v0, p1

    .line 76
    move-object p1, v4

    .line 77
    :goto_1
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 78
    .line 79
    .line 80
    goto :goto_3

    .line 81
    :goto_2
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 82
    .line 83
    .line 84
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 85
    .line 86
    return-object p1
.end method

.method public final a4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LIU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->K(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public final b4(Lcom/xbet/onexcore/data/model/ServerException;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorSource()Lcom/xbet/onexcore/data/errors/ServerError;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/xbet/onexcore/data/errors/ServerError;->getTitle()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v1

    .line 14
    :goto_0
    if-nez v0, :cond_1

    .line 15
    .line 16
    const-string v0, ""

    .line 17
    .line 18
    :cond_1
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorSource()Lcom/xbet/onexcore/data/errors/ServerError;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    if-eqz p1, :cond_2

    .line 23
    .line 24
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/errors/ServerError;->getErrorCode()Lcom/xbet/onexcore/data/errors/ErrorsCode;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-eqz p1, :cond_2

    .line 29
    .line 30
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/errors/ErrorsCode;->getErrorCode()I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    :cond_2
    if-nez v1, :cond_3

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_3
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    const/16 v1, 0x67

    .line 46
    .line 47
    if-ne p1, v1, :cond_4

    .line 48
    .line 49
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 50
    .line 51
    new-instance v1, LZU0/a$b;

    .line 52
    .line 53
    invoke-direct {v1, v0}, LZU0/a$b;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {p1, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    return-void

    .line 60
    :cond_4
    :goto_1
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H4:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 61
    .line 62
    new-instance v1, LZU0/a$d;

    .line 63
    .line 64
    invoke-direct {v1, v0}, LZU0/a$d;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {p1, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    return-void
.end method

.method public final d4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x2:Lll/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lll/a;->r()Lnl/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Lnl/e;->invoke()Lml/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lml/a;->a()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-lez v1, :cond_0

    .line 20
    .line 21
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->P2:Lzg/a;

    .line 22
    .line 23
    invoke-virtual {v0}, Lml/a;->a()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {v1, v0}, Lzg/a;->k(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    :cond_0
    return-void
.end method

.method public final e4()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->F3:Lfk/o;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->TEMPORARY:Lorg/xbet/balance/model/BalanceScreenType;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lfk/o;->a(Lorg/xbet/balance/model/BalanceScreenType;)Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$1;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    invoke-direct {v1, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->X2:Lorg/xbet/ui_common/utils/internet/a;

    .line 20
    .line 21
    invoke-interface {v1}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;

    .line 26
    .line 27
    invoke-direct {v3, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    invoke-static {v0, v1, v3}, Lkotlinx/coroutines/flow/g;->W(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    sget-object v2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$3;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$3;

    .line 39
    .line 40
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method public final g4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->H3:Lorg/xbet/toto_bet/core/domain/usecase/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/core/domain/usecase/a;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeTaxStatus$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeTaxStatus$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v2:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    sget-object v2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeTaxStatus$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeTaxStatus$2;

    .line 32
    .line 33
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final i4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/b;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->v2:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onMakeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final k4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/a;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/a;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onPaymentClicked$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$onPaymentClicked$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final m4(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->w5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, LaV0/a;

    .line 9
    .line 10
    new-instance v3, LoZ0/e;

    .line 11
    .line 12
    sget-object v4, Ll8/j;->a:Ll8/j;

    .line 13
    .line 14
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getMoney()D

    .line 15
    .line 16
    .line 17
    move-result-wide v5

    .line 18
    const/4 v8, 0x2

    .line 19
    const/4 v9, 0x0

    .line 20
    const/4 v7, 0x0

    .line 21
    invoke-static/range {v4 .. v9}, Ll8/j;->g(Ll8/j;DLcom/xbet/onexcore/utils/ValueType;ILjava/lang/Object;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    invoke-direct {v3, v4, v5}, LoZ0/e;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    const/4 v6, 0x2

    .line 33
    const/4 v4, 0x0

    .line 34
    const/4 v5, 0x0

    .line 35
    invoke-static/range {v2 .. v7}, LaV0/a;->b(LaV0/a;LoZ0/e;ZZILjava/lang/Object;)LaV0/a;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    if-eqz v1, :cond_0

    .line 44
    .line 45
    return-void
.end method
