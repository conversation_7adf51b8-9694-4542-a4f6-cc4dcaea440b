.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info_alt_design.TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2"
    f = "TournamentsFullInfoAltDesignSharedViewModel.kt"
    l = {
        0x32a
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->I4(Ljava/lang/String;JLjava/util/List;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $conditionId:J

.field final synthetic $expanded:Z

.field final synthetic $items:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkb1/d;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic $screenName:Ljava/lang/String;

.field J$0:J

.field J$1:J

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field L$6:Ljava/lang/Object;

.field L$7:Ljava/lang/Object;

.field Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;


# direct methods
.method public constructor <init>(Ljava/util/List;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;ZJLjava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lkb1/d;",
            ">;",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;",
            "ZJ",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$expanded:Z

    iput-wide p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$conditionId:J

    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$screenName:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    iget-boolean v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$expanded:Z

    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$conditionId:J

    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$screenName:Ljava/lang/String;

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;-><init>(Ljava/util/List;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;ZJLjava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 24

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    iget-wide v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->J$1:J

    .line 15
    .line 16
    iget-boolean v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->Z$0:Z

    .line 17
    .line 18
    iget-wide v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->J$0:J

    .line 19
    .line 20
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$7:Ljava/lang/Object;

    .line 21
    .line 22
    check-cast v8, Ljava/util/Collection;

    .line 23
    .line 24
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$6:Ljava/lang/Object;

    .line 25
    .line 26
    check-cast v9, Lkotlinx/coroutines/flow/V;

    .line 27
    .line 28
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$5:Ljava/lang/Object;

    .line 29
    .line 30
    check-cast v10, Lkb1/i;

    .line 31
    .line 32
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$4:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v11, Lpb1/b;

    .line 35
    .line 36
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$3:Ljava/lang/Object;

    .line 37
    .line 38
    check-cast v12, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 39
    .line 40
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$2:Ljava/lang/Object;

    .line 41
    .line 42
    check-cast v13, Ljava/util/Iterator;

    .line 43
    .line 44
    iget-object v14, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$1:Ljava/lang/Object;

    .line 45
    .line 46
    check-cast v14, Ljava/util/Collection;

    .line 47
    .line 48
    iget-object v15, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    check-cast v15, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 51
    .line 52
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    move-wide/from16 v16, v6

    .line 56
    .line 57
    move-object/from16 v18, v8

    .line 58
    .line 59
    move-object/from16 v21, v14

    .line 60
    .line 61
    move-object/from16 v22, v15

    .line 62
    .line 63
    move-wide v6, v4

    .line 64
    move-object/from16 v4, p1

    .line 65
    .line 66
    move-object/from16 v19, v9

    .line 67
    .line 68
    move-object/from16 v20, v13

    .line 69
    .line 70
    move-object v5, v11

    .line 71
    goto/16 :goto_1

    .line 72
    .line 73
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 74
    .line 75
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 76
    .line 77
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 78
    .line 79
    .line 80
    throw v1

    .line 81
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 82
    .line 83
    .line 84
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    .line 85
    .line 86
    invoke-interface {v2}, Ljava/util/Collection;->isEmpty()Z

    .line 87
    .line 88
    .line 89
    move-result v2

    .line 90
    if-nez v2, :cond_6

    .line 91
    .line 92
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 93
    .line 94
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/analytics/domain/scope/g;

    .line 95
    .line 96
    .line 97
    move-result-object v4

    .line 98
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 99
    .line 100
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->b4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)J

    .line 101
    .line 102
    .line 103
    move-result-wide v5

    .line 104
    iget-boolean v7, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$expanded:Z

    .line 105
    .line 106
    iget-wide v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$conditionId:J

    .line 107
    .line 108
    invoke-virtual/range {v4 .. v9}, Lorg/xbet/analytics/domain/scope/g;->n(JZJ)V

    .line 109
    .line 110
    .line 111
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 112
    .line 113
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LnR/d;

    .line 114
    .line 115
    .line 116
    move-result-object v4

    .line 117
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$screenName:Ljava/lang/String;

    .line 118
    .line 119
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 120
    .line 121
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->b4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)J

    .line 122
    .line 123
    .line 124
    move-result-wide v6

    .line 125
    iget-wide v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$conditionId:J

    .line 126
    .line 127
    iget-boolean v10, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$expanded:Z

    .line 128
    .line 129
    invoke-interface/range {v4 .. v10}, LnR/d;->d(Ljava/lang/String;JJZ)V

    .line 130
    .line 131
    .line 132
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 133
    .line 134
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->F3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lkotlinx/coroutines/flow/V;

    .line 135
    .line 136
    .line 137
    move-result-object v2

    .line 138
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$items:Ljava/util/List;

    .line 139
    .line 140
    iget-wide v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$conditionId:J

    .line 141
    .line 142
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 143
    .line 144
    iget-boolean v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->$expanded:Z

    .line 145
    .line 146
    new-instance v9, Ljava/util/ArrayList;

    .line 147
    .line 148
    const/16 v10, 0xa

    .line 149
    .line 150
    invoke-static {v4, v10}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 151
    .line 152
    .line 153
    move-result v10

    .line 154
    invoke-direct {v9, v10}, Ljava/util/ArrayList;-><init>(I)V

    .line 155
    .line 156
    .line 157
    invoke-interface {v4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 158
    .line 159
    .line 160
    move-result-object v4

    .line 161
    move-object v12, v9

    .line 162
    move-object v9, v2

    .line 163
    move v2, v8

    .line 164
    move-object v8, v12

    .line 165
    move-object v13, v4

    .line 166
    move-object v12, v7

    .line 167
    move-wide v6, v5

    .line 168
    :goto_0
    invoke-interface {v13}, Ljava/util/Iterator;->hasNext()Z

    .line 169
    .line 170
    .line 171
    move-result v4

    .line 172
    if-eqz v4, :cond_5

    .line 173
    .line 174
    invoke-interface {v13}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    move-result-object v4

    .line 178
    check-cast v4, Lkb1/d;

    .line 179
    .line 180
    instance-of v5, v4, Lkb1/i;

    .line 181
    .line 182
    if-eqz v5, :cond_4

    .line 183
    .line 184
    move-object v10, v4

    .line 185
    check-cast v10, Lkb1/i;

    .line 186
    .line 187
    invoke-virtual {v10}, Lkb1/i;->b()Lpb1/c;

    .line 188
    .line 189
    .line 190
    move-result-object v5

    .line 191
    instance-of v5, v5, Lpb1/b;

    .line 192
    .line 193
    if-eqz v5, :cond_4

    .line 194
    .line 195
    invoke-virtual {v10}, Lkb1/i;->b()Lpb1/c;

    .line 196
    .line 197
    .line 198
    move-result-object v5

    .line 199
    check-cast v5, Lpb1/b;

    .line 200
    .line 201
    invoke-virtual {v5}, Lpb1/b;->f()J

    .line 202
    .line 203
    .line 204
    move-result-wide v14

    .line 205
    cmp-long v5, v14, v6

    .line 206
    .line 207
    if-nez v5, :cond_4

    .line 208
    .line 209
    invoke-virtual {v10}, Lkb1/i;->b()Lpb1/c;

    .line 210
    .line 211
    .line 212
    move-result-object v4

    .line 213
    check-cast v4, Lpb1/b;

    .line 214
    .line 215
    invoke-static {v12, v2, v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->h4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;ZLpb1/b;)V

    .line 216
    .line 217
    .line 218
    if-nez v2, :cond_3

    .line 219
    .line 220
    invoke-virtual {v10}, Lkb1/i;->b()Lpb1/c;

    .line 221
    .line 222
    .line 223
    move-result-object v4

    .line 224
    move-object v11, v4

    .line 225
    check-cast v11, Lpb1/b;

    .line 226
    .line 227
    invoke-static {v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->P3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lw81/e;

    .line 228
    .line 229
    .line 230
    move-result-object v4

    .line 231
    invoke-static {v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->b4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)J

    .line 232
    .line 233
    .line 234
    move-result-wide v14

    .line 235
    invoke-static {v6, v7}, LHc/a;->f(J)Ljava/lang/Long;

    .line 236
    .line 237
    .line 238
    move-result-object v5

    .line 239
    iput-object v12, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$0:Ljava/lang/Object;

    .line 240
    .line 241
    iput-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$1:Ljava/lang/Object;

    .line 242
    .line 243
    iput-object v13, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$2:Ljava/lang/Object;

    .line 244
    .line 245
    iput-object v12, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$3:Ljava/lang/Object;

    .line 246
    .line 247
    iput-object v11, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$4:Ljava/lang/Object;

    .line 248
    .line 249
    iput-object v10, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$5:Ljava/lang/Object;

    .line 250
    .line 251
    iput-object v9, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$6:Ljava/lang/Object;

    .line 252
    .line 253
    iput-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->L$7:Ljava/lang/Object;

    .line 254
    .line 255
    iput-wide v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->J$0:J

    .line 256
    .line 257
    iput-boolean v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->Z$0:Z

    .line 258
    .line 259
    move-wide/from16 v16, v6

    .line 260
    .line 261
    const-wide/16 v6, 0x0

    .line 262
    .line 263
    iput-wide v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->J$1:J

    .line 264
    .line 265
    iput v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onConditionClick$2;->label:I

    .line 266
    .line 267
    invoke-interface {v4, v14, v15, v5, v0}, Lw81/e;->a(JLjava/lang/Long;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 268
    .line 269
    .line 270
    move-result-object v4

    .line 271
    if-ne v4, v1, :cond_2

    .line 272
    .line 273
    return-object v1

    .line 274
    :cond_2
    move-object/from16 v18, v8

    .line 275
    .line 276
    move-object/from16 v21, v18

    .line 277
    .line 278
    move-object/from16 v22, v12

    .line 279
    .line 280
    move-object/from16 v19, v9

    .line 281
    .line 282
    move-object v5, v11

    .line 283
    move-object/from16 v20, v13

    .line 284
    .line 285
    :goto_1
    check-cast v4, Ljava/util/List;

    .line 286
    .line 287
    invoke-static {v12, v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->L3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/util/List;)Ljava/lang/String;

    .line 288
    .line 289
    .line 290
    move-result-object v4

    .line 291
    const/16 v14, 0x77

    .line 292
    .line 293
    const/4 v15, 0x0

    .line 294
    const/4 v8, 0x0

    .line 295
    const/4 v9, 0x0

    .line 296
    const/4 v11, 0x0

    .line 297
    const/4 v12, 0x0

    .line 298
    const/4 v13, 0x0

    .line 299
    move-object/from16 v23, v10

    .line 300
    .line 301
    move-object v10, v4

    .line 302
    move-object/from16 v4, v23

    .line 303
    .line 304
    invoke-static/range {v5 .. v15}, Lpb1/b;->b(Lpb1/b;JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Lpb1/a;ILjava/lang/Object;)Lpb1/b;

    .line 305
    .line 306
    .line 307
    move-result-object v5

    .line 308
    invoke-virtual {v4, v5}, Lkb1/i;->a(Lpb1/c;)Lkb1/i;

    .line 309
    .line 310
    .line 311
    move-result-object v4

    .line 312
    move-object/from16 v8, v18

    .line 313
    .line 314
    move-object/from16 v9, v19

    .line 315
    .line 316
    move-object/from16 v13, v20

    .line 317
    .line 318
    move-object/from16 v12, v22

    .line 319
    .line 320
    goto :goto_2

    .line 321
    :cond_3
    move-wide/from16 v16, v6

    .line 322
    .line 323
    move-object/from16 v21, v8

    .line 324
    .line 325
    move-object v4, v10

    .line 326
    goto :goto_3

    .line 327
    :cond_4
    move-wide/from16 v16, v6

    .line 328
    .line 329
    move-object/from16 v21, v8

    .line 330
    .line 331
    :goto_2
    move-wide/from16 v6, v16

    .line 332
    .line 333
    :goto_3
    invoke-interface {v8, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 334
    .line 335
    .line 336
    move-object/from16 v8, v21

    .line 337
    .line 338
    goto/16 :goto_0

    .line 339
    .line 340
    :cond_5
    check-cast v8, Ljava/util/List;

    .line 341
    .line 342
    new-instance v1, Lkb1/c$a;

    .line 343
    .line 344
    invoke-direct {v1, v8}, Lkb1/c$a;-><init>(Ljava/util/List;)V

    .line 345
    .line 346
    .line 347
    invoke-interface {v9, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 348
    .line 349
    .line 350
    :cond_6
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 351
    .line 352
    return-object v1
.end method
