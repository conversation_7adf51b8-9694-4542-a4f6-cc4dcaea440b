.class final Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.domain.usecases.GetPopularGamesCategoriesScenario$invoke$1"
    f = "GetPopularGamesCategoriesScenario.kt"
    l = {
        0x2e,
        0x35,
        0x2e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->o(ZIZ)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "LLb1/a;",
        ">;>;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0004\u001a\u00020\u0003*\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0004\u0010\u0005"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/f;",
        "",
        "LLb1/a;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $limitLoadGames:I

.field I$0:I

.field J$0:J

.field private synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    iput p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->$limitLoadGames:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->$limitLoadGames:I

    invoke-direct {v0, v1, v2, p2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ILkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;>;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x3

    .line 10
    const/4 v4, 0x2

    .line 11
    const/4 v5, 0x1

    .line 12
    if-eqz v2, :cond_3

    .line 13
    .line 14
    if-eq v2, v5, :cond_2

    .line 15
    .line 16
    if-eq v2, v4, :cond_1

    .line 17
    .line 18
    if-ne v2, v3, :cond_0

    .line 19
    .line 20
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    goto/16 :goto_7

    .line 24
    .line 25
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 28
    .line 29
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    throw v1

    .line 33
    :cond_1
    iget-wide v5, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->J$0:J

    .line 34
    .line 35
    iget v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->I$0:I

    .line 36
    .line 37
    iget-object v7, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$5:Ljava/lang/Object;

    .line 38
    .line 39
    check-cast v7, Lkotlinx/coroutines/flow/f;

    .line 40
    .line 41
    iget-object v8, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$4:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast v8, Ljava/lang/String;

    .line 44
    .line 45
    iget-object v9, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$3:Ljava/lang/Object;

    .line 46
    .line 47
    check-cast v9, Ljava/util/Collection;

    .line 48
    .line 49
    iget-object v10, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$2:Ljava/lang/Object;

    .line 50
    .line 51
    check-cast v10, Ljava/util/Iterator;

    .line 52
    .line 53
    iget-object v11, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 54
    .line 55
    check-cast v11, Ljava/util/Collection;

    .line 56
    .line 57
    iget-object v12, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v12, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 60
    .line 61
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    move-object v14, v7

    .line 65
    move-object v15, v9

    .line 66
    move-object/from16 v16, v11

    .line 67
    .line 68
    move-object v11, v8

    .line 69
    move-wide v7, v5

    .line 70
    move-object/from16 v5, p1

    .line 71
    .line 72
    :goto_0
    move-object/from16 v17, v10

    .line 73
    .line 74
    move-object/from16 v18, v12

    .line 75
    .line 76
    goto/16 :goto_4

    .line 77
    .line 78
    :cond_2
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 79
    .line 80
    check-cast v2, Lkotlinx/coroutines/flow/f;

    .line 81
    .line 82
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 83
    .line 84
    .line 85
    move-object/from16 v5, p1

    .line 86
    .line 87
    goto :goto_1

    .line 88
    :cond_3
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 89
    .line 90
    .line 91
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 92
    .line 93
    check-cast v2, Lkotlinx/coroutines/flow/f;

    .line 94
    .line 95
    iget-object v6, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 96
    .line 97
    invoke-static {v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->d(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lv81/j;

    .line 98
    .line 99
    .line 100
    move-result-object v6

    .line 101
    iput-object v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 102
    .line 103
    iput v5, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->label:I

    .line 104
    .line 105
    invoke-interface {v6, v0}, Lv81/j;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object v5

    .line 109
    if-ne v5, v1, :cond_4

    .line 110
    .line 111
    goto/16 :goto_6

    .line 112
    .line 113
    :cond_4
    :goto_1
    iget-object v6, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 114
    .line 115
    iget v7, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->$limitLoadGames:I

    .line 116
    .line 117
    check-cast v5, Lg81/c;

    .line 118
    .line 119
    invoke-virtual {v5}, Lg81/c;->c()Ljava/util/List;

    .line 120
    .line 121
    .line 122
    move-result-object v5

    .line 123
    new-instance v8, Ljava/util/ArrayList;

    .line 124
    .line 125
    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    .line 126
    .line 127
    .line 128
    invoke-interface {v5}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 129
    .line 130
    .line 131
    move-result-object v5

    .line 132
    :cond_5
    :goto_2
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 133
    .line 134
    .line 135
    move-result v9

    .line 136
    if-eqz v9, :cond_6

    .line 137
    .line 138
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 139
    .line 140
    .line 141
    move-result-object v9

    .line 142
    move-object v10, v9

    .line 143
    check-cast v10, Lg81/b;

    .line 144
    .line 145
    invoke-virtual {v10}, Lg81/b;->l()J

    .line 146
    .line 147
    .line 148
    move-result-wide v10

    .line 149
    const-wide/16 v12, 0x3

    .line 150
    .line 151
    cmp-long v14, v10, v12

    .line 152
    .line 153
    if-eqz v14, :cond_5

    .line 154
    .line 155
    invoke-interface {v8, v9}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 156
    .line 157
    .line 158
    goto :goto_2

    .line 159
    :cond_6
    new-instance v5, Ljava/util/ArrayList;

    .line 160
    .line 161
    const/16 v9, 0xa

    .line 162
    .line 163
    invoke-static {v8, v9}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 164
    .line 165
    .line 166
    move-result v9

    .line 167
    invoke-direct {v5, v9}, Ljava/util/ArrayList;-><init>(I)V

    .line 168
    .line 169
    .line 170
    invoke-interface {v8}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 171
    .line 172
    .line 173
    move-result-object v8

    .line 174
    move v9, v7

    .line 175
    move-object v7, v2

    .line 176
    move v2, v9

    .line 177
    move-object v9, v5

    .line 178
    move-object v12, v6

    .line 179
    move-object v10, v8

    .line 180
    :goto_3
    invoke-interface {v10}, Ljava/util/Iterator;->hasNext()Z

    .line 181
    .line 182
    .line 183
    move-result v5

    .line 184
    if-eqz v5, :cond_8

    .line 185
    .line 186
    invoke-interface {v10}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 187
    .line 188
    .line 189
    move-result-object v5

    .line 190
    check-cast v5, Lg81/b;

    .line 191
    .line 192
    invoke-virtual {v5}, Lg81/b;->g()J

    .line 193
    .line 194
    .line 195
    move-result-wide v13

    .line 196
    invoke-virtual {v5}, Lg81/b;->n()Ljava/lang/String;

    .line 197
    .line 198
    .line 199
    move-result-object v8

    .line 200
    invoke-virtual {v5}, Lg81/b;->g()J

    .line 201
    .line 202
    .line 203
    move-result-wide v5

    .line 204
    iput-object v12, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 205
    .line 206
    iput-object v9, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 207
    .line 208
    iput-object v10, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$2:Ljava/lang/Object;

    .line 209
    .line 210
    iput-object v9, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$3:Ljava/lang/Object;

    .line 211
    .line 212
    iput-object v8, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$4:Ljava/lang/Object;

    .line 213
    .line 214
    iput-object v7, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$5:Ljava/lang/Object;

    .line 215
    .line 216
    iput v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->I$0:I

    .line 217
    .line 218
    iput-wide v13, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->J$0:J

    .line 219
    .line 220
    iput v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->label:I

    .line 221
    .line 222
    invoke-static {v12, v5, v6, v2, v0}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->j(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;JILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v5

    .line 226
    if-ne v5, v1, :cond_7

    .line 227
    .line 228
    goto :goto_6

    .line 229
    :cond_7
    move-object v11, v8

    .line 230
    move-object v15, v9

    .line 231
    move-object/from16 v16, v15

    .line 232
    .line 233
    move-wide/from16 v19, v13

    .line 234
    .line 235
    move-object v14, v7

    .line 236
    move-wide/from16 v7, v19

    .line 237
    .line 238
    goto/16 :goto_0

    .line 239
    .line 240
    :goto_4
    move-object v9, v5

    .line 241
    check-cast v9, Ljava/util/List;

    .line 242
    .line 243
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 244
    .line 245
    .line 246
    move-result-object v12

    .line 247
    sget-object v10, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 248
    .line 249
    new-instance v6, LLb1/a;

    .line 250
    .line 251
    const/4 v13, 0x0

    .line 252
    invoke-direct/range {v6 .. v13}, LLb1/a;-><init>(JLjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/lang/String;Ljava/util/List;Z)V

    .line 253
    .line 254
    .line 255
    invoke-interface {v15, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 256
    .line 257
    .line 258
    move-object v7, v14

    .line 259
    move-object/from16 v9, v16

    .line 260
    .line 261
    move-object/from16 v10, v17

    .line 262
    .line 263
    move-object/from16 v12, v18

    .line 264
    .line 265
    goto :goto_3

    .line 266
    :cond_8
    check-cast v9, Ljava/util/List;

    .line 267
    .line 268
    new-instance v2, Ljava/util/ArrayList;

    .line 269
    .line 270
    invoke-direct {v2}, Ljava/util/ArrayList;-><init>()V

    .line 271
    .line 272
    .line 273
    invoke-interface {v9}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 274
    .line 275
    .line 276
    move-result-object v4

    .line 277
    :cond_9
    :goto_5
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    .line 278
    .line 279
    .line 280
    move-result v5

    .line 281
    if-eqz v5, :cond_a

    .line 282
    .line 283
    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 284
    .line 285
    .line 286
    move-result-object v5

    .line 287
    move-object v6, v5

    .line 288
    check-cast v6, LLb1/a;

    .line 289
    .line 290
    invoke-virtual {v6}, LLb1/a;->b()Ljava/util/List;

    .line 291
    .line 292
    .line 293
    move-result-object v6

    .line 294
    invoke-interface {v6}, Ljava/util/Collection;->isEmpty()Z

    .line 295
    .line 296
    .line 297
    move-result v6

    .line 298
    if-nez v6, :cond_9

    .line 299
    .line 300
    invoke-interface {v2, v5}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 301
    .line 302
    .line 303
    goto :goto_5

    .line 304
    :cond_a
    const/4 v4, 0x0

    .line 305
    iput-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$0:Ljava/lang/Object;

    .line 306
    .line 307
    iput-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$1:Ljava/lang/Object;

    .line 308
    .line 309
    iput-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$2:Ljava/lang/Object;

    .line 310
    .line 311
    iput-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$3:Ljava/lang/Object;

    .line 312
    .line 313
    iput-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$4:Ljava/lang/Object;

    .line 314
    .line 315
    iput-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->L$5:Ljava/lang/Object;

    .line 316
    .line 317
    iput v3, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$invoke$1;->label:I

    .line 318
    .line 319
    invoke-interface {v7, v2, v0}, Lkotlinx/coroutines/flow/f;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 320
    .line 321
    .line 322
    move-result-object v2

    .line 323
    if-ne v2, v1, :cond_b

    .line 324
    .line 325
    :goto_6
    return-object v1

    .line 326
    :cond_b
    :goto_7
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 327
    .line 328
    return-object v1
.end method
