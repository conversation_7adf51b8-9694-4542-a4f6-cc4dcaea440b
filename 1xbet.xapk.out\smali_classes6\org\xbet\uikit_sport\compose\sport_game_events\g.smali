.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function1;

.field public final synthetic b:Ls31/a$b;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function1;Ls31/a$b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/g;->a:Lkotlin/jvm/functions/Function1;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/g;->b:Ls31/a$b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/g;->a:Lkotlin/jvm/functions/Function1;

    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/g;->b:Ls31/a$b;

    invoke-static {v0, v1}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->e(Lkotlin/jvm/functions/Function1;Ls31/a$b;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
