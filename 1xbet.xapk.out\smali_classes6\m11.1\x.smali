.class public final synthetic Lm11/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lm11/H$b;


# direct methods
.method public synthetic constructor <init>(ZLm11/H$b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lm11/x;->a:Z

    iput-object p2, p0, Lm11/x;->b:Lm11/H$b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-boolean v0, p0, Lm11/x;->a:Z

    iget-object v1, p0, Lm11/x;->b:Lm11/H$b;

    invoke-static {v0, v1}, Lm11/G;->x(ZLm11/H$b;)L<PERSON><PERSON>/Unit;

    move-result-object v0

    return-object v0
.end method
