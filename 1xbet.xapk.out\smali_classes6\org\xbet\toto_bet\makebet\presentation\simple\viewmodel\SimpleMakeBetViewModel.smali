.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$b;,
        Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ea\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0003\n\u0002\u0008\u0007\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u00085\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0008\u0001\u0018\u0000 \u0094\u00012\u00020\u0001:\u0006\u0095\u0001\u0096\u0001\u0097\u0001B\u00b9\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u00a2\u0006\u0004\u0008.\u0010/J\u000f\u00101\u001a\u000200H\u0002\u00a2\u0006\u0004\u00081\u00102J\u000f\u00103\u001a\u000200H\u0002\u00a2\u0006\u0004\u00083\u00102J\u0017\u00106\u001a\u0002002\u0006\u00105\u001a\u000204H\u0002\u00a2\u0006\u0004\u00086\u00107J\u000f\u00108\u001a\u000200H\u0002\u00a2\u0006\u0004\u00088\u00102J\u000f\u00109\u001a\u000200H\u0002\u00a2\u0006\u0004\u00089\u00102J\u0010\u0010:\u001a\u000200H\u0082@\u00a2\u0006\u0004\u0008:\u0010;J\u0017\u0010?\u001a\u00020>2\u0006\u0010=\u001a\u00020<H\u0002\u00a2\u0006\u0004\u0008?\u0010@J\u0017\u0010C\u001a\u00020>2\u0006\u0010B\u001a\u00020AH\u0002\u00a2\u0006\u0004\u0008C\u0010DJ\u0017\u0010F\u001a\u00020>2\u0006\u0010E\u001a\u00020AH\u0002\u00a2\u0006\u0004\u0008F\u0010DJ\u0017\u0010H\u001a\u0002002\u0006\u00105\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008H\u0010IJ\u0013\u0010L\u001a\u0008\u0012\u0004\u0012\u00020K0J\u00a2\u0006\u0004\u0008L\u0010MJ\u0013\u0010O\u001a\u0008\u0012\u0004\u0012\u00020N0J\u00a2\u0006\u0004\u0008O\u0010MJ\u0013\u0010Q\u001a\u0008\u0012\u0004\u0012\u00020P0J\u00a2\u0006\u0004\u0008Q\u0010MJ\u0013\u0010S\u001a\u0008\u0012\u0004\u0012\u00020R0J\u00a2\u0006\u0004\u0008S\u0010MJ\u0015\u0010V\u001a\u0002002\u0006\u0010U\u001a\u00020T\u00a2\u0006\u0004\u0008V\u0010WJ\r\u0010X\u001a\u000200\u00a2\u0006\u0004\u0008X\u00102J\r\u0010Y\u001a\u000200\u00a2\u0006\u0004\u0008Y\u00102J\u0015\u0010Z\u001a\u0002002\u0006\u0010=\u001a\u00020A\u00a2\u0006\u0004\u0008Z\u0010[J\r\u0010\\\u001a\u000200\u00a2\u0006\u0004\u0008\\\u00102J\r\u0010]\u001a\u000200\u00a2\u0006\u0004\u0008]\u00102R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010kR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010mR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008n\u0010oR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008p\u0010qR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008r\u0010sR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008t\u0010uR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008v\u0010wR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010yR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010{R\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008|\u0010}R\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008~\u0010\u007fR\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0080\u0001\u0010\u0081\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0082\u0001\u0010\u0083\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0001\u0010\u0085\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0086\u0001\u0010\u0087\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0088\u0001\u0010\u0089\u0001R\u001e\u0010\u008d\u0001\u001a\t\u0012\u0004\u0012\u00020N0\u008a\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008b\u0001\u0010\u008c\u0001R\u001e\u0010\u008f\u0001\u001a\t\u0012\u0004\u0012\u00020K0\u008a\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008e\u0001\u0010\u008c\u0001R\u001e\u0010\u0091\u0001\u001a\t\u0012\u0004\u0012\u00020R0\u008a\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0090\u0001\u0010\u008c\u0001R \u0010\u0093\u0001\u001a\u000b\u0012\u0006\u0012\u0004\u0018\u00010P0\u008a\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0092\u0001\u0010\u008c\u0001\u00a8\u0006\u0098\u0001"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwX0/c;",
        "router",
        "LxX0/a;",
        "blockPaymentNavigator",
        "LHX0/e;",
        "resourceManager",
        "Lek/d;",
        "getScreenBalanceByTypeScenario",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "profileInteractor",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/a;",
        "getMaxBetSumUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/c;",
        "getMinBetSumScenario",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/p;",
        "refreshBalanceLimitsUseCase",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lfk/r;",
        "hasUserMultipleBalancesUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/n;",
        "makeBetWithSumScenario",
        "Lm8/a;",
        "dispatchers",
        "Lorg/xbet/toto_bet/core/domain/usecase/a;",
        "getTaxStatusStreamUseCase",
        "Lorg/xbet/toto_bet/core/domain/usecase/c;",
        "getTotoDsEnabledUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;",
        "getTaxStatusScenario",
        "Lfk/w;",
        "updateMoneyUseCase",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lll/a;",
        "betHistoryFeature",
        "Lfk/b;",
        "addScreenBalanceUseCase",
        "Lzg/a;",
        "betAnalytics",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "getSelectedTotoUseCase",
        "<init>",
        "(LwX0/c;LxX0/a;LHX0/e;Lek/d;Lfk/l;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/toto_bet/makebet/domain/usecase/a;Lorg/xbet/toto_bet/makebet/domain/usecase/c;Lorg/xbet/toto_bet/makebet/domain/usecase/p;Lgk/b;Lfk/r;Lorg/xbet/toto_bet/makebet/domain/usecase/n;Lm8/a;Lorg/xbet/toto_bet/core/domain/usecase/a;Lorg/xbet/toto_bet/core/domain/usecase/c;Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;Lfk/w;Lek/f;Lll/a;Lfk/b;Lzg/a;Lorg/xbet/toto_bet/makebet/domain/usecase/e;)V",
        "",
        "X3",
        "()V",
        "m4",
        "",
        "throwable",
        "w4",
        "(Ljava/lang/Throwable;)V",
        "k4",
        "o4",
        "h4",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "",
        "sum",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;",
        "Y3",
        "(D)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;",
        "",
        "formattedMaxSum",
        "c4",
        "(Ljava/lang/String;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;",
        "formattedMinSum",
        "e4",
        "Lcom/xbet/onexcore/data/model/ServerException;",
        "j4",
        "(Lcom/xbet/onexcore/data/model/ServerException;)V",
        "Lkotlinx/coroutines/flow/e;",
        "LaV0/c;",
        "g4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
        "a4",
        "LIU0/a;",
        "i4",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$b;",
        "b4",
        "Lorg/xbet/balance/model/BalanceModel;",
        "simpleBalance",
        "x4",
        "(Lorg/xbet/balance/model/BalanceModel;)V",
        "v4",
        "q4",
        "n4",
        "(Ljava/lang/String;)V",
        "s4",
        "u4",
        "v1",
        "LwX0/c;",
        "x1",
        "LxX0/a;",
        "y1",
        "LHX0/e;",
        "F1",
        "Lek/d;",
        "H1",
        "Lfk/l;",
        "I1",
        "Lcom/xbet/onexuser/domain/profile/ProfileInteractor;",
        "P1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/a;",
        "S1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/c;",
        "V1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/p;",
        "b2",
        "Lgk/b;",
        "v2",
        "Lfk/r;",
        "x2",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/n;",
        "y2",
        "Lm8/a;",
        "F2",
        "Lorg/xbet/toto_bet/core/domain/usecase/a;",
        "H2",
        "Lorg/xbet/toto_bet/core/domain/usecase/c;",
        "I2",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;",
        "P2",
        "Lfk/w;",
        "S2",
        "Lek/f;",
        "V2",
        "Lll/a;",
        "X2",
        "Lfk/b;",
        "F3",
        "Lzg/a;",
        "H3",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "Lkotlinx/coroutines/flow/V;",
        "I3",
        "Lkotlinx/coroutines/flow/V;",
        "actionStreamState",
        "S3",
        "simpleMakeBetUiStateModelOld",
        "H4",
        "balanceStreamState",
        "X4",
        "taxStatusStreamState",
        "v5",
        "c",
        "a",
        "b",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final v5:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final w5:I


# instance fields
.field public final F1:Lek/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:Lorg/xbet/toto_bet/core/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lzg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lorg/xbet/toto_bet/core/domain/usecase/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lfk/w;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LaV0/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lorg/xbet/toto_bet/makebet/domain/usecase/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lll/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lfk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LIU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lfk/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LxX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/toto_bet/makebet/domain/usecase/n;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$c;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$c;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->v5:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$c;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->w5:I

    return-void
.end method

.method public constructor <init>(LwX0/c;LxX0/a;LHX0/e;Lek/d;Lfk/l;Lcom/xbet/onexuser/domain/profile/ProfileInteractor;Lorg/xbet/toto_bet/makebet/domain/usecase/a;Lorg/xbet/toto_bet/makebet/domain/usecase/c;Lorg/xbet/toto_bet/makebet/domain/usecase/p;Lgk/b;Lfk/r;Lorg/xbet/toto_bet/makebet/domain/usecase/n;Lm8/a;Lorg/xbet/toto_bet/core/domain/usecase/a;Lorg/xbet/toto_bet/core/domain/usecase/c;Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;Lfk/w;Lek/f;Lll/a;Lfk/b;Lzg/a;Lorg/xbet/toto_bet/makebet/domain/usecase/e;)V
    .locals 1
    .param p1    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LxX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lek/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/toto_bet/makebet/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lorg/xbet/toto_bet/makebet/domain/usecase/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lorg/xbet/toto_bet/makebet/domain/usecase/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lfk/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/toto_bet/makebet/domain/usecase/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # Lorg/xbet/toto_bet/core/domain/usecase/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/toto_bet/core/domain/usecase/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lfk/w;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # Lll/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lfk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lzg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lorg/xbet/toto_bet/makebet/domain/usecase/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->v1:LwX0/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->x1:LxX0/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->F1:Lek/d;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H1:Lfk/l;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I1:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->V1:Lorg/xbet/toto_bet/makebet/domain/usecase/p;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->b2:Lgk/b;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->v2:Lfk/r;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->x2:Lorg/xbet/toto_bet/makebet/domain/usecase/n;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y2:Lm8/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->F2:Lorg/xbet/toto_bet/core/domain/usecase/a;

    .line 31
    .line 32
    move-object/from16 p2, p15

    .line 33
    .line 34
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H2:Lorg/xbet/toto_bet/core/domain/usecase/c;

    .line 35
    .line 36
    move-object/from16 p2, p16

    .line 37
    .line 38
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I2:Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;

    .line 39
    .line 40
    move-object/from16 p2, p17

    .line 41
    .line 42
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->P2:Lfk/w;

    .line 43
    .line 44
    move-object/from16 p2, p18

    .line 45
    .line 46
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S2:Lek/f;

    .line 47
    .line 48
    move-object/from16 p2, p19

    .line 49
    .line 50
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->V2:Lll/a;

    .line 51
    .line 52
    move-object/from16 p2, p20

    .line 53
    .line 54
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X2:Lfk/b;

    .line 55
    .line 56
    move-object/from16 p2, p21

    .line 57
    .line 58
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->F3:Lzg/a;

    .line 59
    .line 60
    move-object/from16 p2, p22

    .line 61
    .line 62
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H3:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 63
    .line 64
    sget-object p2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;

    .line 65
    .line 66
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 67
    .line 68
    .line 69
    move-result-object p2

    .line 70
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 71
    .line 72
    new-instance p2, LaV0/c;

    .line 73
    .line 74
    const-string p3, ""

    .line 75
    .line 76
    const/4 p4, 0x0

    .line 77
    const/4 p5, 0x1

    .line 78
    const-string p6, "0.0"

    .line 79
    .line 80
    invoke-direct {p2, p5, p6, p3, p4}, LaV0/c;-><init>(ZLjava/lang/String;Ljava/lang/String;Z)V

    .line 81
    .line 82
    .line 83
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 84
    .line 85
    .line 86
    move-result-object p2

    .line 87
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 88
    .line 89
    sget-object p2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$b$b;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$b$b;

    .line 90
    .line 91
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 92
    .line 93
    .line 94
    move-result-object p2

    .line 95
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 96
    .line 97
    const/4 p2, 0x0

    .line 98
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 99
    .line 100
    .line 101
    move-result-object p3

    .line 102
    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 103
    .line 104
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 105
    .line 106
    .line 107
    move-result-object p4

    .line 108
    new-instance p5, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/f;

    .line 109
    .line 110
    invoke-direct {p5, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/f;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V

    .line 111
    .line 112
    .line 113
    invoke-interface {p13}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 114
    .line 115
    .line 116
    move-result-object p7

    .line 117
    new-instance p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$2;

    .line 118
    .line 119
    invoke-direct {p1, p0, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 120
    .line 121
    .line 122
    const/16 p2, 0xa

    .line 123
    .line 124
    const/4 p3, 0x0

    .line 125
    const/4 p6, 0x0

    .line 126
    const/4 v0, 0x0

    .line 127
    move-object p9, p1

    .line 128
    move-object p11, p3

    .line 129
    move-object p8, v0

    .line 130
    const/16 p10, 0xa

    .line 131
    .line 132
    invoke-static/range {p4 .. p11}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 133
    .line 134
    .line 135
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lfk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X2:Lfk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic C3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)LxX0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->x1:LxX0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic D3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->b2:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic E3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lfk/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H1:Lfk/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic F3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lek/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->F1:Lek/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H3:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lorg/xbet/toto_bet/core/domain/usecase/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H2:Lorg/xbet/toto_bet/core/domain/usecase/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lfk/r;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->v2:Lfk/r;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic J3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/n;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->x2:Lorg/xbet/toto_bet/makebet/domain/usecase/n;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lcom/xbet/onexuser/domain/profile/ProfileInteractor;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I1:Lcom/xbet/onexuser/domain/profile/ProfileInteractor;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic L3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/p;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->V1:Lorg/xbet/toto_bet/makebet/domain/usecase/p;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)LwX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->v1:LwX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->h4(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lfk/w;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->P2:Lfk/w;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic S3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S2:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic T3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->k4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic U3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->m4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic V3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->o4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic W3(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->p4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final Z3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 11

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    sget v0, Lpb/k;->min_max_bet_input:I

    .line 4
    .line 5
    const/4 v1, 0x2

    .line 6
    new-array v1, v1, [Ljava/lang/Object;

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    aput-object p1, v1, v2

    .line 10
    .line 11
    const/4 p1, 0x1

    .line 12
    aput-object p2, v1, p1

    .line 13
    .line 14
    invoke-interface {p0, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    sget v8, Lpb/c;->textColorSecondary:I

    .line 19
    .line 20
    const/16 v9, 0x1e

    .line 21
    .line 22
    const/4 v10, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    const/4 v6, 0x0

    .line 26
    const/4 v7, 0x0

    .line 27
    move-object v2, p3

    .line 28
    invoke-static/range {v2 .. v10}, LvX0/g;->b(LvX0/f;Ljava/lang/String;FIIIIILjava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 32
    .line 33
    return-object p0
.end method

.method public static final d4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 12

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    sget v0, Lpb/k;->max_sum:I

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    new-array v1, v1, [Ljava/lang/Object;

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    aput-object p1, v1, v2

    .line 10
    .line 11
    invoke-interface {p0, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    sget v7, Lpb/e;->red_soft:I

    .line 16
    .line 17
    const/16 v10, 0x36

    .line 18
    .line 19
    const/4 v11, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v9, 0x0

    .line 24
    move-object v3, p2

    .line 25
    invoke-static/range {v3 .. v11}, LvX0/g;->b(LvX0/f;Ljava/lang/String;FIIIIILjava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 29
    .line 30
    return-object p0
.end method

.method public static final f4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 12

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    sget v0, Lpb/k;->min_sum:I

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    new-array v1, v1, [Ljava/lang/Object;

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    aput-object p1, v1, v2

    .line 10
    .line 11
    invoke-interface {p0, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v4

    .line 15
    sget v7, Lpb/e;->red_soft:I

    .line 16
    .line 17
    const/16 v10, 0x36

    .line 18
    .line 19
    const/4 v11, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v9, 0x0

    .line 24
    move-object v3, p2

    .line 25
    invoke-static/range {v3 .. v11}, LvX0/g;->b(LvX0/f;Ljava/lang/String;FIIIIILjava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 29
    .line 30
    return-object p0
.end method

.method private final h4(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object v0, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v0, Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    :try_start_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :catchall_0
    move-exception p1

    .line 47
    goto :goto_2

    .line 48
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw p1

    .line 56
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    :try_start_1
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 60
    .line 61
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I2:Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;

    .line 62
    .line 63
    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->L$0:Ljava/lang/Object;

    .line 64
    .line 65
    iput v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$getTaxStatus$1;->label:I

    .line 66
    .line 67
    invoke-virtual {v2, v0}, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    if-ne v0, v1, :cond_3

    .line 72
    .line 73
    return-object v1

    .line 74
    :cond_3
    move-object v4, v0

    .line 75
    move-object v0, p1

    .line 76
    move-object p1, v4

    .line 77
    :goto_1
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 78
    .line 79
    .line 80
    goto :goto_3

    .line 81
    :goto_2
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 82
    .line 83
    .line 84
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 85
    .line 86
    return-object p1
.end method

.method private final j4(Lcom/xbet/onexcore/data/model/ServerException;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorSource()Lcom/xbet/onexcore/data/errors/ServerError;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    invoke-virtual {v0}, Lcom/xbet/onexcore/data/errors/ServerError;->getTitle()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v1

    .line 14
    :goto_0
    if-nez v0, :cond_1

    .line 15
    .line 16
    const-string v0, ""

    .line 17
    .line 18
    :cond_1
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/model/ServerException;->getErrorSource()Lcom/xbet/onexcore/data/errors/ServerError;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    if-eqz p1, :cond_2

    .line 23
    .line 24
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/errors/ServerError;->getErrorCode()Lcom/xbet/onexcore/data/errors/ErrorsCode;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-eqz p1, :cond_2

    .line 29
    .line 30
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/errors/ErrorsCode;->getErrorCode()I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    :cond_2
    if-nez v1, :cond_3

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_3
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    const/16 v1, 0x67

    .line 46
    .line 47
    if-ne p1, v1, :cond_4

    .line 48
    .line 49
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 50
    .line 51
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;

    .line 52
    .line 53
    invoke-direct {v1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    return-void

    .line 60
    :cond_4
    :goto_1
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 61
    .line 62
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;

    .line 63
    .line 64
    invoke-direct {v1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;-><init>(Ljava/lang/String;)V

    .line 65
    .line 66
    .line 67
    invoke-interface {p1, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 68
    .line 69
    .line 70
    return-void
.end method

.method public static final l4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 11

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    sget v0, Lpb/k;->min_max_bet_input:I

    .line 4
    .line 5
    const/4 v1, 0x2

    .line 6
    new-array v1, v1, [Ljava/lang/Object;

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    aput-object p1, v1, v2

    .line 10
    .line 11
    const/4 p1, 0x1

    .line 12
    aput-object p2, v1, p1

    .line 13
    .line 14
    invoke-interface {p0, v0, v1}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    sget v8, Lpb/c;->textColorSecondary:I

    .line 19
    .line 20
    const/16 v9, 0x1e

    .line 21
    .line 22
    const/4 v10, 0x0

    .line 23
    const/4 v4, 0x0

    .line 24
    const/4 v5, 0x0

    .line 25
    const/4 v6, 0x0

    .line 26
    const/4 v7, 0x0

    .line 27
    move-object v2, p3

    .line 28
    invoke-static/range {v2 .. v10}, LvX0/g;->b(LvX0/f;Ljava/lang/String;FIIIIILjava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 32
    .line 33
    return-object p0
.end method

.method private final m4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->V2:Lll/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lll/a;->r()Lnl/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Lnl/e;->invoke()Lml/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lml/a;->a()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-lez v1, :cond_0

    .line 20
    .line 21
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->F3:Lzg/a;

    .line 22
    .line 23
    invoke-virtual {v0}, Lml/a;->a()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-virtual {v1, v0}, Lzg/a;->k(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    :cond_0
    return-void
.end method

.method private final o4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->F2:Lorg/xbet/toto_bet/core/domain/usecase/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/core/domain/usecase/a;->a()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$observeTaxStatus$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$observeTaxStatus$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y2:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->getDefault()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    sget-object v2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$observeTaxStatus$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$observeTaxStatus$2;

    .line 32
    .line 33
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public static synthetic p3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic p4(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->l4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->t4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final r4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->w4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic s3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->f4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->x3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final t4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->w4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static synthetic u3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->r4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic v3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->d4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic w3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->Z3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final x3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->w4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final y4(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->w4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final X3()V
    .locals 1

    .line 1
    const-string v0, ""

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->n4(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->k4()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final Y3(D)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->a()Ljava/math/BigDecimal;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Ljava/math/BigDecimal;->doubleValue()D

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;

    .line 12
    .line 13
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/domain/usecase/a;->a()Ljava/math/BigDecimal;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    invoke-virtual {v2}, Ljava/math/BigDecimal;->doubleValue()D

    .line 18
    .line 19
    .line 20
    move-result-wide v2

    .line 21
    sget-object v4, Ll8/j;->a:Ll8/j;

    .line 22
    .line 23
    iget-object v5, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    invoke-interface {v5}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    check-cast v5, LaV0/c;

    .line 30
    .line 31
    invoke-virtual {v5}, LaV0/c;->e()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    sget-object v6, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 36
    .line 37
    invoke-virtual {v4, v0, v1, v5, v6}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v5

    .line 41
    iget-object v7, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 42
    .line 43
    invoke-interface {v7}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v7

    .line 47
    check-cast v7, LaV0/c;

    .line 48
    .line 49
    invoke-virtual {v7}, LaV0/c;->e()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v7

    .line 53
    invoke-virtual {v4, v2, v3, v7, v6}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    cmpg-double v6, p1, v0

    .line 58
    .line 59
    if-gez v6, :cond_0

    .line 60
    .line 61
    invoke-virtual {p0, v5}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->e4(Ljava/lang/String;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    return-object p1

    .line 66
    :cond_0
    cmpl-double v0, p1, v2

    .line 67
    .line 68
    if-lez v0, :cond_1

    .line 69
    .line 70
    invoke-virtual {p0, v4}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->c4(Ljava/lang/String;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    return-object p1

    .line 75
    :cond_1
    new-instance p1, LvX0/a;

    .line 76
    .line 77
    invoke-direct {p1}, LvX0/a;-><init>()V

    .line 78
    .line 79
    .line 80
    new-instance p2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/e;

    .line 81
    .line 82
    invoke-direct {p2, p0, v5, v4}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/e;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p1, p2}, LvX0/a;->b(Lkotlin/jvm/functions/Function1;)V

    .line 86
    .line 87
    .line 88
    invoke-virtual {p1}, LvX0/a;->a()LvX0/e;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    new-instance p2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 93
    .line 94
    const/4 v0, 0x1

    .line 95
    invoke-direct {p2, p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;-><init>(LvX0/e;Z)V

    .line 96
    .line 97
    .line 98
    return-object p2
.end method

.method public final a4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->H4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c4(Ljava/lang/String;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;
    .locals 2

    .line 1
    new-instance v0, LvX0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LvX0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/j;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/j;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {v0, v1}, LvX0/a;->b(Lkotlin/jvm/functions/Function1;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0}, LvX0/a;->a()LvX0/e;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    invoke-direct {v0, p1, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;-><init>(LvX0/e;Z)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method public final e4(Ljava/lang/String;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;
    .locals 2

    .line 1
    new-instance v0, LvX0/a;

    .line 2
    .line 3
    invoke-direct {v0}, LvX0/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/h;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/h;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {v0, v1}, LvX0/a;->b(Lkotlin/jvm/functions/Function1;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0}, LvX0/a;->a()LvX0/e;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    invoke-direct {v0, p1, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;-><init>(LvX0/e;Z)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method public final g4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LaV0/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "LIU0/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->X4:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->K(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final k4()V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S1:Lorg/xbet/toto_bet/makebet/domain/usecase/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->a()Ljava/math/BigDecimal;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Ljava/math/BigDecimal;->doubleValue()D

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->P1:Lorg/xbet/toto_bet/makebet/domain/usecase/a;

    .line 12
    .line 13
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/domain/usecase/a;->a()Ljava/math/BigDecimal;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    invoke-virtual {v2}, Ljava/math/BigDecimal;->doubleValue()D

    .line 18
    .line 19
    .line 20
    move-result-wide v2

    .line 21
    sget-object v4, Ll8/j;->a:Ll8/j;

    .line 22
    .line 23
    iget-object v5, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 24
    .line 25
    invoke-interface {v5}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v5

    .line 29
    check-cast v5, LaV0/c;

    .line 30
    .line 31
    invoke-virtual {v5}, LaV0/c;->e()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v5

    .line 35
    sget-object v6, Lcom/xbet/onexcore/utils/ValueType;->AMOUNT:Lcom/xbet/onexcore/utils/ValueType;

    .line 36
    .line 37
    invoke-virtual {v4, v0, v1, v5, v6}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 42
    .line 43
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    check-cast v1, LaV0/c;

    .line 48
    .line 49
    invoke-virtual {v1}, LaV0/c;->e()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-virtual {v4, v2, v3, v1, v6}, Ll8/j;->e(DLjava/lang/String;Lcom/xbet/onexcore/utils/ValueType;)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    new-instance v3, LvX0/a;

    .line 60
    .line 61
    invoke-direct {v3}, LvX0/a;-><init>()V

    .line 62
    .line 63
    .line 64
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;

    .line 65
    .line 66
    invoke-direct {v4, p0, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {v3, v4}, LvX0/a;->b(Lkotlin/jvm/functions/Function1;)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {v3}, LvX0/a;->a()LvX0/e;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 77
    .line 78
    const/4 v3, 0x0

    .line 79
    invoke-direct {v1, v0, v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;-><init>(LvX0/e;Z)V

    .line 80
    .line 81
    .line 82
    invoke-interface {v2, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 83
    .line 84
    .line 85
    return-void
.end method

.method public final n4(Ljava/lang/String;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->k4()V

    .line 8
    .line 9
    .line 10
    goto :goto_1

    .line 11
    :cond_0
    invoke-static {p1}, Lkotlin/text/u;->u(Ljava/lang/String;)Ljava/lang/Double;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Ljava/lang/Double;->doubleValue()D

    .line 18
    .line 19
    .line 20
    move-result-wide v0

    .line 21
    goto :goto_0

    .line 22
    :cond_1
    const-wide/16 v0, 0x0

    .line 23
    .line 24
    :goto_0
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    invoke-virtual {p0, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->Y3(D)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-interface {v2, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    :goto_1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    :goto_2
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    move-object v2, v1

    .line 40
    check-cast v2, LaV0/c;

    .line 41
    .line 42
    const/16 v7, 0xd

    .line 43
    .line 44
    const/4 v8, 0x0

    .line 45
    const/4 v3, 0x0

    .line 46
    const/4 v5, 0x0

    .line 47
    const/4 v6, 0x0

    .line 48
    move-object v4, p1

    .line 49
    invoke-static/range {v2 .. v8}, LaV0/c;->b(LaV0/c;ZLjava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)LaV0/c;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    invoke-interface {v0, v1, p1}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    if-eqz p1, :cond_2

    .line 58
    .line 59
    return-void

    .line 60
    :cond_2
    move-object p1, v4

    .line 61
    goto :goto_2
.end method

.method public final q4()V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$c;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$c;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/k;

    .line 13
    .line 14
    invoke-direct {v3, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/k;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y2:Lm8/a;

    .line 18
    .line 19
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 20
    .line 21
    .line 22
    move-result-object v5

    .line 23
    new-instance v7, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$onMakeBet$2;

    .line 24
    .line 25
    const/4 v0, 0x0

    .line 26
    invoke-direct {v7, p0, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$onMakeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    const/16 v8, 0xa

    .line 30
    .line 31
    const/4 v9, 0x0

    .line 32
    const/4 v4, 0x0

    .line 33
    const/4 v6, 0x0

    .line 34
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final s4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/i;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/i;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V

    .line 8
    .line 9
    .line 10
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$onPaymentClicked$2;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-direct {v5, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$onPaymentClicked$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/16 v6, 0xe

    .line 17
    .line 18
    const/4 v7, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final u4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final v4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->S3:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LaV0/c;

    .line 8
    .line 9
    invoke-virtual {v0}, LaV0/c;->c()Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    const-string v1, "0.0"

    .line 14
    .line 15
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-nez v1, :cond_0

    .line 20
    .line 21
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-lez v1, :cond_0

    .line 26
    .line 27
    invoke-virtual {p0, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->n4(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    :cond_0
    return-void
.end method

.method public final w4(Ljava/lang/Throwable;)V
    .locals 4

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lcom/xbet/onexcore/data/model/ServerException;

    .line 6
    .line 7
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->j4(Lcom/xbet/onexcore/data/model/ServerException;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 12
    .line 13
    if-nez v0, :cond_3

    .line 14
    .line 15
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 21
    .line 22
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;

    .line 23
    .line 24
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-nez p1, :cond_2

    .line 29
    .line 30
    const-string p1, ""

    .line 31
    .line 32
    :cond_2
    invoke-direct {v1, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    return-void

    .line 39
    :cond_3
    :goto_0
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->I3:Lkotlinx/coroutines/flow/V;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;

    .line 42
    .line 43
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y1:LHX0/e;

    .line 44
    .line 45
    sget v2, Lpb/k;->no_connection_check_network:I

    .line 46
    .line 47
    const/4 v3, 0x0

    .line 48
    new-array v3, v3, [Ljava/lang/Object;

    .line 49
    .line 50
    invoke-interface {v1, v2, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;-><init>(Ljava/lang/String;)V

    .line 55
    .line 56
    .line 57
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 58
    .line 59
    .line 60
    return-void
.end method

.method public final x4(Lorg/xbet/balance/model/BalanceModel;)V
    .locals 8
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/g;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/g;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->y2:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$updateBalance$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$updateBalance$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method
