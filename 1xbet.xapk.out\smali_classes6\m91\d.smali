.class public final synthetic Lm91/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lm91/e;


# direct methods
.method public synthetic constructor <init>(Lm91/e;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm91/d;->a:Lm91/e;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lm91/d;->a:Lm91/e;

    invoke-static {v0}, Lm91/e;->a(Lm91/e;)Ll91/b;

    move-result-object v0

    return-object v0
.end method
