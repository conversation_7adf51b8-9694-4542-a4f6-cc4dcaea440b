.class public final Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LN31/g;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\r\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u001b\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 V2\u00020\u00012\u00020\u0002:\u0001(B?\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\n\u0008\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\u0008\u0008\u0002\u0010\u000c\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\r\u0010\u000eB\u001b\u0008\u0016\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0008\u0010\u000b\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\u0008\r\u0010\u000fJ\u000f\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J3\u0010\u0019\u001a\u00020\u00172\"\u0010\u0018\u001a\u001e\u0012\u0004\u0012\u00020\u0014\u0012\u0006\u0012\u0004\u0018\u00010\u0015\u0012\u0006\u0012\u0004\u0018\u00010\u0016\u0012\u0004\u0012\u00020\u00170\u0013H\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u001c\u001a\u00020\u00172\u0006\u0010\u001b\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\'\u0010#\u001a\u00020\u00172\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u001f0\u001e2\u0008\u0010\"\u001a\u0004\u0018\u00010!H\u0016\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\u0017H\u0014\u00a2\u0006\u0004\u0008%\u0010&J\u000f\u0010\'\u001a\u00020\u0017H\u0016\u00a2\u0006\u0004\u0008\'\u0010&J\u000f\u0010(\u001a\u00020\u0017H\u0016\u00a2\u0006\u0004\u0008(\u0010&J\u0017\u0010*\u001a\u00020\u00172\u0006\u0010)\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008*\u0010\u001dR4\u0010,\u001a \u0012\u0004\u0012\u00020\u0014\u0012\u0006\u0012\u0004\u0018\u00010\u0015\u0012\u0006\u0012\u0004\u0018\u00010\u0016\u0012\u0004\u0012\u00020\u0017\u0018\u00010\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008(\u0010+R\u0014\u0010.\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010-R\u0014\u0010/\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010-R\u0014\u00100\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010-R\u0014\u00102\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u0010-R\u0014\u00104\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u0010-R\u0014\u00106\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u0010-R\u0014\u00108\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00087\u0010-R\u0014\u0010:\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010-R\u0014\u0010<\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008;\u0010-R\u0014\u0010@\u001a\u00020=8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010D\u001a\u00020A8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010H\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010L\u001a\u00020I8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010P\u001a\u00020M8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR \u0010U\u001a\u000e\u0012\u0004\u0012\u00020R\u0012\u0004\u0012\u00020\u00170Q8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010T\u00a8\u0006W"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;",
        "Landroid/widget/FrameLayout;",
        "LN31/g;",
        "Landroid/content/Context;",
        "context",
        "",
        "headerTitle",
        "buttonAllTitle",
        "",
        "iconFilterResId",
        "Landroid/util/AttributeSet;",
        "attrs",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILandroid/util/AttributeSet;I)V",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "getRecyclerView",
        "()Landroidx/recyclerview/widget/RecyclerView;",
        "Lkotlin/Function3;",
        "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
        "",
        "",
        "",
        "listener",
        "setSportCollectionClickListener",
        "(LOc/n;)V",
        "style",
        "setStyle",
        "(I)V",
        "",
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        "list",
        "Ljava/lang/Runnable;",
        "commitCallback",
        "b",
        "(Ljava/util/List;Ljava/lang/Runnable;)V",
        "onDetachedFromWindow",
        "()V",
        "d",
        "a",
        "marginHorizontal",
        "c",
        "LOc/n;",
        "headerButtonsClickListener",
        "I",
        "headerTitlePaddingStart",
        "headerShimmerMarginTop",
        "headerTitlePaddingBottom",
        "e",
        "headerMarginTop",
        "f",
        "btFilterPadding",
        "g",
        "btFilterMarginEnd",
        "h",
        "btAllMarginEnd",
        "i",
        "headerSize",
        "j",
        "btFilterSize",
        "Landroid/widget/FrameLayout$LayoutParams;",
        "k",
        "Landroid/widget/FrameLayout$LayoutParams;",
        "recyclerViewLayoutParams",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "l",
        "Lorg/xbet/uikit/components/header/DSHeader;",
        "headerView",
        "Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;",
        "m",
        "Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;",
        "recyclerView",
        "Landroid/widget/ImageView;",
        "n",
        "Landroid/widget/ImageView;",
        "buttonFilterView",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "o",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "titleShimmerView",
        "Lkotlin/Function1;",
        "Landroid/content/res/TypedArray;",
        "p",
        "Lkotlin/jvm/functions/Function1;",
        "applyAttrs",
        "q",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final q:Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final r:I


# instance fields
.field public a:LOc/n;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LOc/n<",
            "-",
            "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:Landroid/widget/FrameLayout$LayoutParams;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lorg/xbet/uikit/components/header/DSHeader;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/content/res/TypedArray;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->q:Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->r:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 9
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    const/16 v7, 0x20

    const/4 v8, 0x0

    .line 28
    const-string v2, ""

    const-string v3, ""

    const/4 v4, 0x0

    const/4 v6, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v5, p2

    invoke-direct/range {v0 .. v8}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;-><init>(Landroid/content/Context;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILandroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILandroid/util/AttributeSet;I)V
    .locals 25
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/CharSequence;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/CharSequence;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move-object/from16 v0, p0

    move-object/from16 v2, p1

    move/from16 v7, p4

    move-object/from16 v8, p5

    move/from16 v9, p6

    .line 2
    invoke-direct {v0, v2, v8, v9}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->extra_large_horizontal_margin_dynamic:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->b:I

    .line 4
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->space_10:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->c:I

    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->space_8:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->d:I

    .line 6
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->space_8:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->e:I

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->space_4:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->f:I

    .line 8
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->extra_large_horizontal_margin_dynamic:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->g:I

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->space_16:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->h:I

    .line 10
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->size_40:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->i:I

    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_24:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->j:I

    .line 12
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v4, -0x1

    const/4 v5, -0x2

    invoke-direct {v3, v4, v5}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 13
    iput v1, v3, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 14
    iput-object v3, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->k:Landroid/widget/FrameLayout$LayoutParams;

    .line 15
    new-instance v1, Lorg/xbet/uikit/components/header/DSHeader;

    const/4 v5, 0x6

    const/4 v6, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/header/DSHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object v10, v1

    iput-object v10, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->l:Lorg/xbet/uikit/components/header/DSHeader;

    .line 16
    new-instance v1, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object v11, v1

    iput-object v11, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 17
    new-instance v12, Landroid/widget/ImageView;

    invoke-direct {v12, v2}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    iput-object v12, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->n:Landroid/widget/ImageView;

    .line 18
    new-instance v1, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->o:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 19
    new-instance v3, LR31/a;

    invoke-direct {v3, v0, v2}, LR31/a;-><init>(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/content/Context;)V

    iput-object v3, v0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->p:Lkotlin/jvm/functions/Function1;

    .line 20
    sget-object v4, Lm31/g;->SportCollectionWithHeader:[I

    const/4 v5, 0x0

    .line 21
    invoke-virtual {v2, v8, v4, v9, v5}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v2

    invoke-interface {v3, v2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {v2}, Landroid/content/res/TypedArray;->recycle()V

    .line 22
    invoke-virtual {v0, v10}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 23
    invoke-virtual {v0, v11}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 24
    invoke-virtual {v0, v12}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 25
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 26
    new-instance v13, Lorg/xbet/uikit/components/header/a$a;

    const/16 v23, 0x1f6

    const/16 v24, 0x0

    const/4 v15, 0x0

    const/16 v16, 0x0

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x0

    const/16 v21, 0x0

    const/16 v22, 0x0

    move-object/from16 v14, p2

    move-object/from16 v17, p3

    invoke-direct/range {v13 .. v24}, Lorg/xbet/uikit/components/header/a$a;-><init>(Ljava/lang/CharSequence;ZLjava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/Integer;Landroid/graphics/drawable/Drawable;LL11/c;LL11/c;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    invoke-virtual {v10, v13}, Lorg/xbet/uikit/components/header/DSHeader;->setModel(Lorg/xbet/uikit/components/header/a;)V

    if-eqz v7, :cond_0

    .line 27
    invoke-virtual {v12, v7}, Landroid/widget/ImageView;->setImageResource(I)V

    :cond_0
    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILandroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 7

    and-int/lit8 p8, p7, 0x10

    if-eqz p8, :cond_0

    const/4 p5, 0x0

    :cond_0
    move-object v5, p5

    and-int/lit8 p5, p7, 0x20

    if-eqz p5, :cond_1

    const/4 p6, 0x0

    const/4 v6, 0x0

    :goto_0
    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move v4, p4

    goto :goto_1

    :cond_1
    move v6, p6

    goto :goto_0

    .line 1
    :goto_1
    invoke-direct/range {v0 .. v6}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;-><init>(Landroid/content/Context;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILandroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->i(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->h(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->j(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/view/View;)V

    return-void
.end method

.method public static final h(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 7

    .line 1
    sget v0, Lm31/g;->SportCollectionWithHeader_iconFilterColor:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    sget v2, Lm31/g;->SportCollectionWithHeader_buttonFilterBackground:I

    .line 9
    .line 10
    invoke-virtual {p2, v2, v1}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    sget v3, Lm31/g;->SportCollectionWithHeader_headerShimmerHeight:I

    .line 15
    .line 16
    invoke-virtual {p2, v3, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 17
    .line 18
    .line 19
    move-result v3

    .line 20
    sget v4, Lm31/g;->SportCollectionWithHeader_headerShimmerWidth:I

    .line 21
    .line 22
    invoke-virtual {p2, v4, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 23
    .line 24
    .line 25
    move-result v4

    .line 26
    sget v5, Lm31/g;->SportCollectionWithHeader_headerShimmerCornerRadius:I

    .line 27
    .line 28
    const/4 v6, 0x0

    .line 29
    invoke-virtual {p2, v5, v6}, Landroid/content/res/TypedArray;->getDimension(IF)F

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->n:Landroid/widget/ImageView;

    .line 34
    .line 35
    invoke-virtual {v5, v0}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 36
    .line 37
    .line 38
    new-instance v0, Landroid/widget/FrameLayout$LayoutParams;

    .line 39
    .line 40
    iget v6, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->j:I

    .line 41
    .line 42
    invoke-direct {v0, v6, v6}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 43
    .line 44
    .line 45
    const v6, 0x800035

    .line 46
    .line 47
    .line 48
    iput v6, v0, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 49
    .line 50
    iget v6, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->g:I

    .line 51
    .line 52
    invoke-virtual {v0, v6}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 53
    .line 54
    .line 55
    iget v6, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->g:I

    .line 56
    .line 57
    invoke-virtual {v0, v6}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 58
    .line 59
    .line 60
    iget v6, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->e:I

    .line 61
    .line 62
    iput v6, v0, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 63
    .line 64
    invoke-virtual {v5, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 65
    .line 66
    .line 67
    iget v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->f:I

    .line 68
    .line 69
    invoke-virtual {v5, v0, v0, v0, v0}, Landroid/view/View;->setPadding(IIII)V

    .line 70
    .line 71
    .line 72
    new-instance v0, LR31/b;

    .line 73
    .line 74
    invoke-direct {v0, p0}, LR31/b;-><init>(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;)V

    .line 75
    .line 76
    .line 77
    invoke-virtual {v5, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 78
    .line 79
    .line 80
    if-eqz v2, :cond_0

    .line 81
    .line 82
    invoke-virtual {v5, v2}, Landroid/view/View;->setBackgroundResource(I)V

    .line 83
    .line 84
    .line 85
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->l:Lorg/xbet/uikit/components/header/DSHeader;

    .line 86
    .line 87
    new-instance v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 88
    .line 89
    const/4 v5, -0x1

    .line 90
    const/4 v6, -0x2

    .line 91
    invoke-direct {v2, v5, v6}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {v0, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 95
    .line 96
    .line 97
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->d(I)V

    .line 98
    .line 99
    .line 100
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->n:Landroid/widget/ImageView;

    .line 101
    .line 102
    invoke-virtual {v2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    iget v2, v2, Landroid/view/ViewGroup$LayoutParams;->width:I

    .line 107
    .line 108
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->n:Landroid/widget/ImageView;

    .line 109
    .line 110
    invoke-virtual {v5}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 111
    .line 112
    .line 113
    move-result-object v5

    .line 114
    instance-of v6, v5, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 115
    .line 116
    if-eqz v6, :cond_1

    .line 117
    .line 118
    check-cast v5, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 119
    .line 120
    invoke-virtual {v5}, Landroid/view/ViewGroup$MarginLayoutParams;->getMarginEnd()I

    .line 121
    .line 122
    .line 123
    move-result v5

    .line 124
    goto :goto_0

    .line 125
    :cond_1
    const/4 v5, 0x0

    .line 126
    :goto_0
    add-int/2addr v2, v5

    .line 127
    iget v5, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->h:I

    .line 128
    .line 129
    add-int/2addr v2, v5

    .line 130
    iget v5, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->b:I

    .line 131
    .line 132
    invoke-static {v0, v5, v1, v2, v1}, Lorg/xbet/uikit/utils/S;->o(Landroid/view/View;IIII)V

    .line 133
    .line 134
    .line 135
    new-instance v1, LR31/c;

    .line 136
    .line 137
    invoke-direct {v1, p0}, LR31/c;-><init>(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;)V

    .line 138
    .line 139
    .line 140
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/DSHeader;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 141
    .line 142
    .line 143
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 144
    .line 145
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->k:Landroid/widget/FrameLayout$LayoutParams;

    .line 146
    .line 147
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 148
    .line 149
    .line 150
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->o:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 151
    .line 152
    const/16 v1, 0x8

    .line 153
    .line 154
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 155
    .line 156
    .line 157
    iget v1, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->b:I

    .line 158
    .line 159
    invoke-virtual {v0}, Landroid/view/View;->getTop()I

    .line 160
    .line 161
    .line 162
    move-result v2

    .line 163
    invoke-virtual {v0}, Landroid/view/View;->getRight()I

    .line 164
    .line 165
    .line 166
    move-result v5

    .line 167
    iget v6, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->d:I

    .line 168
    .line 169
    invoke-virtual {v0, v1, v2, v5, v6}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 170
    .line 171
    .line 172
    new-instance v1, Landroid/widget/FrameLayout$LayoutParams;

    .line 173
    .line 174
    invoke-direct {v1, v4, v3}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 175
    .line 176
    .line 177
    const v2, 0x800033

    .line 178
    .line 179
    .line 180
    iput v2, v1, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 181
    .line 182
    iget v2, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->b:I

    .line 183
    .line 184
    invoke-virtual {v1, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 185
    .line 186
    .line 187
    iget v2, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->b:I

    .line 188
    .line 189
    invoke-virtual {v1, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 190
    .line 191
    .line 192
    iget p0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->c:I

    .line 193
    .line 194
    iput p0, v1, Landroid/widget/FrameLayout$LayoutParams;->topMargin:I

    .line 195
    .line 196
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 197
    .line 198
    .line 199
    new-instance p0, Landroid/graphics/drawable/GradientDrawable;

    .line 200
    .line 201
    invoke-direct {p0}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 202
    .line 203
    .line 204
    invoke-virtual {p0, p2}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 205
    .line 206
    .line 207
    sget p2, LlZ0/d;->uikitSecondary20:I

    .line 208
    .line 209
    const/4 v1, 0x2

    .line 210
    const/4 v2, 0x0

    .line 211
    invoke-static {p1, p2, v2, v1, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 212
    .line 213
    .line 214
    move-result p1

    .line 215
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 216
    .line 217
    .line 218
    move-result-object p1

    .line 219
    invoke-virtual {p0, p1}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 220
    .line 221
    .line 222
    invoke-virtual {v0, p0}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 223
    .line 224
    .line 225
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 226
    .line 227
    return-object p0
.end method

.method public static final i(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->a:LOc/n;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->FILTER:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    invoke-interface {p0, p1, v0, v0}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public static final j(Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->a:LOc/n;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;->ALL:Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    invoke-interface {p0, p1, v0, v0}, LOc/n;->invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method


# virtual methods
.method public a()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->o:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->l:Lorg/xbet/uikit/components/header/DSHeader;

    .line 9
    .line 10
    const/4 v1, 0x0

    .line 11
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->n:Landroid/widget/ImageView;

    .line 15
    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public b(Ljava/util/List;Ljava/lang/Runnable;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lorg/xbet/uikit_sport/sport_collection/b;",
            ">;",
            "Ljava/lang/Runnable;",
            ")V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;->f(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->a()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public c(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;->c(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public d()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->o:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 5
    .line 6
    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->l:Lorg/xbet/uikit/components/header/DSHeader;

    .line 8
    .line 9
    const/16 v1, 0x8

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->n:Landroid/widget/ImageView;

    .line 15
    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 20
    .line 21
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public getRecyclerView()Landroidx/recyclerview/widget/RecyclerView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 2
    .line 3
    return-object v0
.end method

.method public onDetachedFromWindow()V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 2
    .line 3
    .line 4
    invoke-super {p0}, Landroid/widget/FrameLayout;->onDetachedFromWindow()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public setSportCollectionClickListener(LOc/n;)V
    .locals 1
    .param p1    # LOc/n;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LOc/n<",
            "-",
            "Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;",
            "-",
            "Ljava/lang/Long;",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;->setSportCollectionActionType(LOc/n;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->a:LOc/n;

    .line 7
    .line 8
    return-void
.end method

.method public setStyle(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_collection/views/withHeader/SportsCollectionWithHeader;->m:Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_collection/views/simple/SportsCollectionSimple;->setViewStyle(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
