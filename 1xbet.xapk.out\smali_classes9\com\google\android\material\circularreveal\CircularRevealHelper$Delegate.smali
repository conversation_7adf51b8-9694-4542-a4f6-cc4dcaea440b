.class public interface abstract Lcom/google/android/material/circularreveal/CircularRevealHelper$Delegate;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/material/circularreveal/CircularRevealHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Delegate"
.end annotation


# virtual methods
.method public abstract actualDraw(Landroid/graphics/Canvas;)V
.end method

.method public abstract actualIsOpaque()Z
.end method
