.class final Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tvbet.presentation.TvBetJackpotTableViewModel$getTableInfoByDate$2"
    f = "TvBetJackpotTableViewModel.kt"
    l = {
        0x50,
        0x56
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->K3(Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $date:Ljava/lang/String;

.field L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field L$5:Ljava/lang/Object;

.field L$6:Ljava/lang/Object;

.field L$7:Ljava/lang/Object;

.field L$8:Ljava/lang/Object;

.field L$9:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->$date:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->$date:Ljava/lang/String;

    invoke-direct {p1, v0, v1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x2

    .line 10
    const/4 v4, 0x1

    .line 11
    if-eqz v2, :cond_2

    .line 12
    .line 13
    if-eq v2, v4, :cond_1

    .line 14
    .line 15
    if-ne v2, v3, :cond_0

    .line 16
    .line 17
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$9:Ljava/lang/Object;

    .line 18
    .line 19
    check-cast v2, Ljava/lang/String;

    .line 20
    .line 21
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$8:Ljava/lang/Object;

    .line 22
    .line 23
    check-cast v4, Ljava/util/List;

    .line 24
    .line 25
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$7:Ljava/lang/Object;

    .line 26
    .line 27
    check-cast v6, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 28
    .line 29
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$6:Ljava/lang/Object;

    .line 30
    .line 31
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$5:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast v8, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 34
    .line 35
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$4:Ljava/lang/Object;

    .line 36
    .line 37
    check-cast v9, Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    iget-object v10, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$3:Ljava/lang/Object;

    .line 40
    .line 41
    check-cast v10, Lorg/xbet/balance/model/BalanceModel;

    .line 42
    .line 43
    iget-object v11, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$2:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast v11, Ljava/util/List;

    .line 46
    .line 47
    iget-object v12, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$1:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast v12, Ljava/lang/String;

    .line 50
    .line 51
    iget-object v13, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$0:Ljava/lang/Object;

    .line 52
    .line 53
    check-cast v13, Lvb1/b;

    .line 54
    .line 55
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    move-object v14, v13

    .line 59
    move-object v13, v12

    .line 60
    move-object v12, v11

    .line 61
    move-object v11, v10

    .line 62
    move-object v10, v9

    .line 63
    move-object v9, v8

    .line 64
    move-object/from16 v8, p1

    .line 65
    .line 66
    goto/16 :goto_8

    .line 67
    .line 68
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 69
    .line 70
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 71
    .line 72
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    throw v1

    .line 76
    :cond_1
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$2:Ljava/lang/Object;

    .line 77
    .line 78
    check-cast v2, Ljava/util/List;

    .line 79
    .line 80
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$1:Ljava/lang/Object;

    .line 81
    .line 82
    check-cast v4, Ljava/lang/String;

    .line 83
    .line 84
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$0:Ljava/lang/Object;

    .line 85
    .line 86
    check-cast v6, Lvb1/b;

    .line 87
    .line 88
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 89
    .line 90
    .line 91
    move-object v7, v6

    .line 92
    move-object v6, v4

    .line 93
    move-object/from16 v4, p1

    .line 94
    .line 95
    goto/16 :goto_3

    .line 96
    .line 97
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 98
    .line 99
    .line 100
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 101
    .line 102
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->$date:Ljava/lang/String;

    .line 103
    .line 104
    invoke-static {v2, v6}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->E3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;)V

    .line 105
    .line 106
    .line 107
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 108
    .line 109
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->$date:Ljava/lang/String;

    .line 110
    .line 111
    invoke-static {v2, v6}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->y3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;)Lvb1/b;

    .line 112
    .line 113
    .line 114
    move-result-object v6

    .line 115
    if-eqz v6, :cond_3

    .line 116
    .line 117
    invoke-virtual {v6}, Lvb1/b;->d()Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v2

    .line 121
    goto :goto_0

    .line 122
    :cond_3
    const/4 v2, 0x0

    .line 123
    :goto_0
    if-eqz v6, :cond_4

    .line 124
    .line 125
    invoke-virtual {v6}, Lvb1/b;->c()Ljava/lang/String;

    .line 126
    .line 127
    .line 128
    move-result-object v7

    .line 129
    goto :goto_1

    .line 130
    :cond_4
    const/4 v7, 0x0

    .line 131
    :goto_1
    new-instance v8, Ljava/lang/StringBuilder;

    .line 132
    .line 133
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 134
    .line 135
    .line 136
    invoke-virtual {v8, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 137
    .line 138
    .line 139
    const-string v2, " - "

    .line 140
    .line 141
    invoke-virtual {v8, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 142
    .line 143
    .line 144
    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 145
    .line 146
    .line 147
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 148
    .line 149
    .line 150
    move-result-object v2

    .line 151
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 152
    .line 153
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->z3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lvb1/a;

    .line 154
    .line 155
    .line 156
    move-result-object v7

    .line 157
    if-eqz v7, :cond_5

    .line 158
    .line 159
    invoke-virtual {v7}, Lvb1/a;->d()Ljava/util/List;

    .line 160
    .line 161
    .line 162
    move-result-object v7

    .line 163
    goto :goto_2

    .line 164
    :cond_5
    const/4 v7, 0x0

    .line 165
    :goto_2
    if-nez v7, :cond_6

    .line 166
    .line 167
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 168
    .line 169
    .line 170
    move-result-object v7

    .line 171
    :cond_6
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 172
    .line 173
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lfk/l;

    .line 174
    .line 175
    .line 176
    move-result-object v8

    .line 177
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 178
    .line 179
    invoke-static {v9}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->r3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lorg/xbet/balance/model/BalanceScreenType;

    .line 180
    .line 181
    .line 182
    move-result-object v9

    .line 183
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$0:Ljava/lang/Object;

    .line 184
    .line 185
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$1:Ljava/lang/Object;

    .line 186
    .line 187
    iput-object v7, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$2:Ljava/lang/Object;

    .line 188
    .line 189
    iput v4, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->label:I

    .line 190
    .line 191
    invoke-interface {v8, v9, v0}, Lfk/l;->a(Lorg/xbet/balance/model/BalanceScreenType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 192
    .line 193
    .line 194
    move-result-object v4

    .line 195
    if-ne v4, v1, :cond_7

    .line 196
    .line 197
    goto/16 :goto_7

    .line 198
    .line 199
    :cond_7
    move-object/from16 v17, v6

    .line 200
    .line 201
    move-object v6, v2

    .line 202
    move-object v2, v7

    .line 203
    move-object/from16 v7, v17

    .line 204
    .line 205
    :goto_3
    check-cast v4, Lorg/xbet/balance/model/BalanceModel;

    .line 206
    .line 207
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 208
    .line 209
    invoke-static {v8}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->A3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lkotlinx/coroutines/flow/V;

    .line 210
    .line 211
    .line 212
    move-result-object v8

    .line 213
    iget-object v9, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 214
    .line 215
    move-object v11, v2

    .line 216
    move-object v10, v4

    .line 217
    move-object v2, v6

    .line 218
    move-object v13, v7

    .line 219
    move-object v6, v9

    .line 220
    move-object v9, v8

    .line 221
    :goto_4
    invoke-interface {v9}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 222
    .line 223
    .line 224
    move-result-object v7

    .line 225
    move-object v4, v7

    .line 226
    check-cast v4, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;

    .line 227
    .line 228
    if-eqz v13, :cond_8

    .line 229
    .line 230
    invoke-virtual {v13}, Lvb1/b;->e()Ljava/util/List;

    .line 231
    .line 232
    .line 233
    move-result-object v4

    .line 234
    goto :goto_5

    .line 235
    :cond_8
    const/4 v4, 0x0

    .line 236
    :goto_5
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->z3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lvb1/a;

    .line 237
    .line 238
    .line 239
    move-result-object v8

    .line 240
    if-eqz v8, :cond_9

    .line 241
    .line 242
    invoke-virtual {v8}, Lvb1/a;->c()D

    .line 243
    .line 244
    .line 245
    move-result-wide v14

    .line 246
    move-wide/from16 v17, v14

    .line 247
    .line 248
    move-object v12, v11

    .line 249
    move-object v14, v13

    .line 250
    move-object v13, v2

    .line 251
    move-object v11, v10

    .line 252
    move-object v10, v9

    .line 253
    move-object v9, v6

    .line 254
    :goto_6
    move-object v15, v4

    .line 255
    move-wide/from16 v3, v17

    .line 256
    .line 257
    goto :goto_9

    .line 258
    :cond_9
    iput-object v13, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$0:Ljava/lang/Object;

    .line 259
    .line 260
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$1:Ljava/lang/Object;

    .line 261
    .line 262
    iput-object v11, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$2:Ljava/lang/Object;

    .line 263
    .line 264
    iput-object v10, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$3:Ljava/lang/Object;

    .line 265
    .line 266
    iput-object v9, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$4:Ljava/lang/Object;

    .line 267
    .line 268
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$5:Ljava/lang/Object;

    .line 269
    .line 270
    iput-object v7, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$6:Ljava/lang/Object;

    .line 271
    .line 272
    iput-object v6, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$7:Ljava/lang/Object;

    .line 273
    .line 274
    iput-object v4, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$8:Ljava/lang/Object;

    .line 275
    .line 276
    iput-object v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->L$9:Ljava/lang/Object;

    .line 277
    .line 278
    iput v3, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;->label:I

    .line 279
    .line 280
    invoke-static {v6, v10, v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->w3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 281
    .line 282
    .line 283
    move-result-object v8

    .line 284
    if-ne v8, v1, :cond_a

    .line 285
    .line 286
    :goto_7
    return-object v1

    .line 287
    :cond_a
    move-object v12, v11

    .line 288
    move-object v14, v13

    .line 289
    move-object v13, v2

    .line 290
    move-object v11, v10

    .line 291
    move-object v10, v9

    .line 292
    move-object v9, v6

    .line 293
    :goto_8
    check-cast v8, Lvb1/a;

    .line 294
    .line 295
    invoke-virtual {v8}, Lvb1/a;->c()D

    .line 296
    .line 297
    .line 298
    move-result-wide v15

    .line 299
    move-wide/from16 v17, v15

    .line 300
    .line 301
    goto :goto_6

    .line 302
    :goto_9
    invoke-virtual {v11}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 303
    .line 304
    .line 305
    move-result-object v5

    .line 306
    invoke-static {v6, v3, v4, v5}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->s3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;DLjava/lang/String;)Ljava/lang/String;

    .line 307
    .line 308
    .line 309
    move-result-object v3

    .line 310
    new-instance v4, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;

    .line 311
    .line 312
    move-object v5, v15

    .line 313
    invoke-direct {v4, v2, v5, v3, v12}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;-><init>(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V

    .line 314
    .line 315
    .line 316
    invoke-interface {v10, v7, v4}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 317
    .line 318
    .line 319
    move-result v2

    .line 320
    if-eqz v2, :cond_b

    .line 321
    .line 322
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 323
    .line 324
    return-object v1

    .line 325
    :cond_b
    move-object v6, v9

    .line 326
    move-object v9, v10

    .line 327
    move-object v10, v11

    .line 328
    move-object v11, v12

    .line 329
    move-object v2, v13

    .line 330
    move-object v13, v14

    .line 331
    const/4 v3, 0x2

    .line 332
    goto :goto_4
.end method
