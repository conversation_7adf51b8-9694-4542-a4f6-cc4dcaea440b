.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->m0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lck/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->o0:Lck/a;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->n0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Landroidx/lifecycle/e0$c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->i0:Landroidx/lifecycle/e0$c;

    .line 2
    .line 3
    return-void
.end method
