.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u008e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\t\n\u0002\u0008\u0013\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u0000 f2\u00020\u0001:\u0001gB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u001d\u0010\u000c\u001a\u00020\u00062\u000c\u0010\u000b\u001a\u0008\u0012\u0004\u0012\u00020\n0\tH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ/\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0017\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u0019H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001cJ\u000f\u0010\u001e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0003J\u0017\u0010!\u001a\u00020\u00062\u0006\u0010 \u001a\u00020\u001fH\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u000f\u0010#\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008#\u0010\u0003J\u0019\u0010&\u001a\u00020\u00062\u0008\u0010%\u001a\u0004\u0018\u00010$H\u0014\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010(\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008(\u0010\u0003R\"\u00100\u001a\u00020)8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008*\u0010+\u001a\u0004\u0008,\u0010-\"\u0004\u0008.\u0010/R\"\u00108\u001a\u0002018\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105\"\u0004\u00086\u00107R\"\u0010@\u001a\u0002098\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008:\u0010;\u001a\u0004\u0008<\u0010=\"\u0004\u0008>\u0010?R+\u0010I\u001a\u00020A2\u0006\u0010B\u001a\u00020A8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010F\"\u0004\u0008G\u0010HR+\u0010P\u001a\u00020\u000e2\u0006\u0010B\u001a\u00020\u000e8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008J\u0010K\u001a\u0004\u0008L\u0010M\"\u0004\u0008N\u0010OR+\u0010T\u001a\u00020\u000e2\u0006\u0010B\u001a\u00020\u000e8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008Q\u0010K\u001a\u0004\u0008R\u0010M\"\u0004\u0008S\u0010OR\u001b\u0010Z\u001a\u00020U8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008V\u0010W\u001a\u0004\u0008X\u0010YR\u001b\u0010`\u001a\u00020[8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\\\u0010]\u001a\u0004\u0008^\u0010_R\u001b\u0010e\u001a\u00020a8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008b\u0010]\u001a\u0004\u0008c\u0010d\u00a8\u0006h"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;",
        "state",
        "",
        "U2",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;)V",
        "",
        "Lkb1/A;",
        "data",
        "c3",
        "(Ljava/util/List;)V",
        "",
        "title",
        "description",
        "positiveButtonTitle",
        "Lorg/xbet/uikit/components/dialog/AlertType;",
        "alertType",
        "d3",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;",
        "X2",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "e3",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "f3",
        "T2",
        "",
        "isBottomBarVisible",
        "J2",
        "(Z)V",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "S2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LSX0/c;",
        "j0",
        "LSX0/c;",
        "M2",
        "()LSX0/c;",
        "setLottieEmptyConfigurator",
        "(LSX0/c;)V",
        "lottieEmptyConfigurator",
        "LTZ0/a;",
        "k0",
        "LTZ0/a;",
        "K2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "",
        "<set-?>",
        "l0",
        "LeX0/f;",
        "N2",
        "()J",
        "Z2",
        "(J)V",
        "tournamentID",
        "m0",
        "LeX0/k;",
        "O2",
        "()Ljava/lang/String;",
        "a3",
        "(Ljava/lang/String;)V",
        "tournamentTitle",
        "n0",
        "P2",
        "b3",
        "tournamentType",
        "LS91/d0;",
        "o0",
        "LRc/c;",
        "Q2",
        "()LS91/d0;",
        "viewBinding",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;",
        "b1",
        "Lkotlin/j;",
        "R2",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;",
        "viewModel",
        "Lhb1/b;",
        "k1",
        "L2",
        "()Lhb1/b;",
        "adapter",
        "v1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final v1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic x1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public j0:LSX0/c;

.field public k0:LTZ0/a;

.field public final k1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 8

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;

    .line 4
    .line 5
    const-string v2, "tournamentID"

    .line 6
    .line 7
    const-string v3, "getTournamentID()J"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "tournamentTitle"

    .line 20
    .line 21
    const-string v5, "getTournamentTitle()Ljava/lang/String;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "tournamentType"

    .line 33
    .line 34
    const-string v6, "getTournamentType()Ljava/lang/String;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "viewBinding"

    .line 46
    .line 47
    const-string v7, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTournamentStagesAltDesignBinding;"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    const/4 v5, 0x4

    .line 57
    new-array v5, v5, [Lkotlin/reflect/m;

    .line 58
    .line 59
    aput-object v0, v5, v4

    .line 60
    .line 61
    const/4 v0, 0x1

    .line 62
    aput-object v2, v5, v0

    .line 63
    .line 64
    const/4 v0, 0x2

    .line 65
    aput-object v3, v5, v0

    .line 66
    .line 67
    const/4 v0, 0x3

    .line 68
    aput-object v1, v5, v0

    .line 69
    .line 70
    sput-object v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 71
    .line 72
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$a;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->v1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$a;

    .line 79
    .line 80
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, Lu91/c;->fragment_tournament_stages_alt_design:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v1, LeX0/f;

    .line 7
    .line 8
    const/4 v5, 0x2

    .line 9
    const/4 v6, 0x0

    .line 10
    const-string v2, "TOURNAMENT_ID"

    .line 11
    .line 12
    const-wide/16 v3, 0x0

    .line 13
    .line 14
    invoke-direct/range {v1 .. v6}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 15
    .line 16
    .line 17
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->l0:LeX0/f;

    .line 18
    .line 19
    new-instance v0, LeX0/k;

    .line 20
    .line 21
    const-string v1, "TOURNAMENT_TITLE"

    .line 22
    .line 23
    const/4 v2, 0x0

    .line 24
    const/4 v3, 0x2

    .line 25
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 26
    .line 27
    .line 28
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->m0:LeX0/k;

    .line 29
    .line 30
    new-instance v0, LeX0/k;

    .line 31
    .line 32
    const-string v1, "TOURNAMENT_TYPE"

    .line 33
    .line 34
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->n0:LeX0/k;

    .line 38
    .line 39
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$viewBinding$2;

    .line 40
    .line 41
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->o0:LRc/c;

    .line 46
    .line 47
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/a;

    .line 48
    .line 49
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/a;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)V

    .line 50
    .line 51
    .line 52
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$1;

    .line 53
    .line 54
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 55
    .line 56
    .line 57
    sget-object v3, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 58
    .line 59
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$2;

    .line 60
    .line 61
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 62
    .line 63
    .line 64
    invoke-static {v3, v4}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    const-class v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 69
    .line 70
    invoke-static {v4}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 71
    .line 72
    .line 73
    move-result-object v4

    .line 74
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$3;

    .line 75
    .line 76
    invoke-direct {v5, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 77
    .line 78
    .line 79
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$4;

    .line 80
    .line 81
    invoke-direct {v6, v2, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 82
    .line 83
    .line 84
    invoke-static {p0, v4, v5, v6, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->b1:Lkotlin/j;

    .line 89
    .line 90
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/b;

    .line 91
    .line 92
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/b;-><init>()V

    .line 93
    .line 94
    .line 95
    invoke-static {v3, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->k1:Lkotlin/j;

    .line 100
    .line 101
    return-void
.end method

.method public static synthetic A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic B2()Lhb1/b;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->I2()Lhb1/b;

    move-result-object v0

    return-object v0
.end method

.method public static synthetic C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->g3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic D2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->U2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Z2(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->a3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->b3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->d3(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final I2()Lhb1/b;
    .locals 1

    .line 1
    new-instance v0, Lhb1/b;

    .line 2
    .line 3
    invoke-direct {v0}, Lhb1/b;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method private final J2(Z)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/d0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    sget v1, Lpb/f;->space_108:I

    .line 14
    .line 15
    invoke-virtual {p1, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 16
    .line 17
    .line 18
    move-result p1

    .line 19
    goto :goto_0

    .line 20
    :cond_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    sget v1, Lpb/f;->size_42:I

    .line 25
    .line 26
    invoke-virtual {p1, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 27
    .line 28
    .line 29
    move-result p1

    .line 30
    :goto_0
    invoke-virtual {v0}, Landroid/view/View;->getPaddingStart()I

    .line 31
    .line 32
    .line 33
    move-result v1

    .line 34
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    invoke-virtual {v0}, Landroid/view/View;->getPaddingEnd()I

    .line 39
    .line 40
    .line 41
    move-result v3

    .line 42
    invoke-virtual {v0, v1, v2, v3, p1}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method private final N2()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->l0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method private final O2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->m0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method private final T2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const-string v1, "REQUEST_KEY_CLOSE_OTHER_TOURNAMENTS_FRAGMENTS"

    .line 10
    .line 11
    invoke-static {}, Landroidx/core/os/d;->a()Landroid/os/Bundle;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {v0, v1, v2}, Landroidx/fragment/app/FragmentManager;->K1(Ljava/lang/String;Landroid/os/Bundle;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->R2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->onBackPressed()V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static final V2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->T2()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final W2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->T2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final Y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;Landroid/view/View;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->R2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->b()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->P2()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;

    .line 22
    .line 23
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {p2, v0, p1, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->H3(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final Z2(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->l0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final a3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->m0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final c3(Ljava/util/List;)V
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "Lkb1/A;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->M2()LSX0/c;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    sget-object v2, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 12
    .line 13
    sget v7, Lpb/k;->data_retrieval_error:I

    .line 14
    .line 15
    const/16 v11, 0x1de

    .line 16
    .line 17
    const/4 v12, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    const/4 v4, 0x0

    .line 20
    const/4 v5, 0x0

    .line 21
    const/4 v6, 0x0

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v9, 0x0

    .line 24
    const/4 v10, 0x0

    .line 25
    invoke-static/range {v1 .. v12}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->e3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 30
    .line 31
    .line 32
    return-void

    .line 33
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    iget-object v0, v0, LS91/d0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 38
    .line 39
    const/4 v1, 0x0

    .line 40
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iget-object v0, v0, LS91/d0;->d:Landroid/widget/LinearLayout;

    .line 48
    .line 49
    const/16 v1, 0x8

    .line 50
    .line 51
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->L2()Lhb1/b;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 59
    .line 60
    .line 61
    return-void
.end method

.method private final d3(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V
    .locals 16

    .line 1
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->K2()LTZ0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 6
    .line 7
    const/16 v14, 0xbf8

    .line 8
    .line 9
    const/4 v15, 0x0

    .line 10
    const/4 v5, 0x0

    .line 11
    const/4 v6, 0x0

    .line 12
    const/4 v7, 0x0

    .line 13
    const/4 v8, 0x0

    .line 14
    const/4 v9, 0x0

    .line 15
    const/4 v10, 0x0

    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v13, 0x0

    .line 18
    move-object/from16 v2, p1

    .line 19
    .line 20
    move-object/from16 v3, p2

    .line 21
    .line 22
    move-object/from16 v4, p3

    .line 23
    .line 24
    move-object/from16 v12, p4

    .line 25
    .line 26
    invoke-direct/range {v1 .. v15}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-virtual {v0, v1, v2}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method private final e3(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/d0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iget-object v0, v0, LS91/d0;->d:Landroid/widget/LinearLayout;

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->f3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method private final f3(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/d0;->e:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public static final g3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->S2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->W2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->V2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final K2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final L2()Lhb1/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->k1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lhb1/b;

    .line 8
    .line 9
    return-object v0
.end method

.method public final M2()LSX0/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->j0:LSX0/c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final P2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public final Q2()LS91/d0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/d0;

    .line 13
    .line 14
    return-object v0
.end method

.method public final R2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->b1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final S2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final U2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;)V
    .locals 1

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$c;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$c;->a()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->c3(Ljava/util/List;)V

    .line 12
    .line 13
    .line 14
    return-void

    .line 15
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    .line 16
    .line 17
    if-eqz v0, :cond_2

    .line 18
    .line 19
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    .line 20
    .line 21
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->a()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->c3(Ljava/util/List;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->X2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    sget-object v0, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 40
    .line 41
    if-eq p1, v0, :cond_1

    .line 42
    .line 43
    const/4 p1, 0x1

    .line 44
    goto :goto_0

    .line 45
    :cond_1
    const/4 p1, 0x0

    .line 46
    :goto_0
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->J2(Z)V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :cond_2
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$b;

    .line 51
    .line 52
    if-eqz v0, :cond_3

    .line 53
    .line 54
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$b;

    .line 55
    .line 56
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->e3(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 61
    .line 62
    .line 63
    return-void

    .line 64
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 65
    .line 66
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 67
    .line 68
    .line 69
    throw p1
.end method

.method public final X2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/d0;->b:Lorg/xbet/uikit/components/bottombar/BottomBar;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    sget-object v2, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 16
    .line 17
    const/4 v3, 0x0

    .line 18
    if-eq v1, v2, :cond_0

    .line 19
    .line 20
    const/4 v1, 0x1

    .line 21
    goto :goto_0

    .line 22
    :cond_0
    const/4 v1, 0x0

    .line 23
    :goto_0
    if-eqz v1, :cond_1

    .line 24
    .line 25
    goto :goto_1

    .line 26
    :cond_1
    const/16 v3, 0x8

    .line 27
    .line 28
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    iget-object v0, v0, LS91/d0;->b:Lorg/xbet/uikit/components/bottombar/BottomBar;

    .line 36
    .line 37
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;

    .line 38
    .line 39
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/bottombar/BottomBar;->setFirstButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 46
    .line 47
    .line 48
    move-result-object v0

    .line 49
    iget-object v0, v0, LS91/d0;->b:Lorg/xbet/uikit/components/bottombar/BottomBar;

    .line 50
    .line 51
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;->c()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->a()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/bottombar/BottomBar;->setFirstButtonText(Ljava/lang/CharSequence;)V

    .line 60
    .line 61
    .line 62
    return-void
.end method

.method public final b3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->x1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget-object v0, v0, LS91/d0;->g:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 10
    .line 11
    sget v1, Lpb/k;->tournament_stages:I

    .line 12
    .line 13
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setTitle(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/d;

    .line 21
    .line 22
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/d;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)V

    .line 23
    .line 24
    .line 25
    invoke-static {p0, v0}, LXW0/d;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function0;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    iget-object v0, v0, LS91/d0;->g:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 33
    .line 34
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/e;

    .line 35
    .line 36
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/e;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->Q2()LS91/d0;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    iget-object v0, v0, LS91/d0;->g:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 47
    .line 48
    invoke-virtual {v0}, Landroidx/appcompat/widget/Toolbar;->getNavigationIcon()Landroid/graphics/drawable/Drawable;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    if-eqz v0, :cond_0

    .line 53
    .line 54
    sget-object v1, Lub/b;->a:Lub/b;

    .line 55
    .line 56
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 57
    .line 58
    .line 59
    move-result-object v2

    .line 60
    sget v3, Lpb/c;->textColorSecondary:I

    .line 61
    .line 62
    const/4 v5, 0x4

    .line 63
    const/4 v6, 0x0

    .line 64
    const/4 v4, 0x0

    .line 65
    invoke-static/range {v1 .. v6}, Lub/b;->f(Lub/b;Landroid/content/Context;IZILjava/lang/Object;)I

    .line 66
    .line 67
    .line 68
    move-result v1

    .line 69
    sget-object v2, Landroid/graphics/PorterDuff$Mode;->SRC_ATOP:Landroid/graphics/PorterDuff$Mode;

    .line 70
    .line 71
    invoke-virtual {v0, v1, v2}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    .line 72
    .line 73
    .line 74
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->R2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->N2()J

    .line 79
    .line 80
    .line 81
    move-result-wide v1

    .line 82
    const/4 v3, 0x1

    .line 83
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->P2()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v4

    .line 87
    invoke-virtual {v0, v1, v2, v3, v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->J3(JZLjava/lang/String;)V

    .line 88
    .line 89
    .line 90
    iget-object p1, p1, LS91/d0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 91
    .line 92
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->L2()Lhb1/b;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 97
    .line 98
    .line 99
    return-void
.end method

.method public u2()V
    .locals 15

    .line 1
    sget-object v0, LVa1/o;->a:LVa1/o;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->N2()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->O2()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v4

    .line 11
    sget-object v3, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 14
    .line 15
    .line 16
    move-result-object v5

    .line 17
    invoke-virtual {v5}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    new-instance v6, LVa1/e;

    .line 22
    .line 23
    const/16 v13, 0x3f

    .line 24
    .line 25
    const/4 v14, 0x0

    .line 26
    const/4 v7, 0x0

    .line 27
    const/4 v8, 0x0

    .line 28
    const/4 v9, 0x0

    .line 29
    const/4 v10, 0x0

    .line 30
    const/4 v11, 0x0

    .line 31
    const/4 v12, 0x0

    .line 32
    invoke-direct/range {v6 .. v14}, LVa1/e;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual/range {v0 .. v6}, LVa1/o;->e(JLorg/xplatform/aggregator/api/navigation/TournamentsPage;Ljava/lang/String;Landroid/app/Application;LVa1/e;)LVa1/l;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-interface {v0, p0}, LVa1/l;->k(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->R2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->F3()Lkotlinx/coroutines/flow/f0;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v8

    .line 27
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/4 v11, 0x3

    .line 34
    const/4 v12, 0x0

    .line 35
    move-object v7, v8

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x0

    .line 38
    move-object v10, v2

    .line 39
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->R2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->E3()Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$2;

    .line 51
    .line 52
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lkotlin/coroutines/e;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 56
    .line 57
    .line 58
    move-result-object v9

    .line 59
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    new-instance v15, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 64
    .line 65
    move-object v10, v5

    .line 66
    move-object v7, v15

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/16 v16, 0x3

    .line 71
    .line 72
    const/16 v17, 0x0

    .line 73
    .line 74
    const/4 v13, 0x0

    .line 75
    const/4 v14, 0x0

    .line 76
    move-object v12, v1

    .line 77
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    return-void
.end method
