.class public final synthetic Lm11/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:Lm11/H$p;

.field public final synthetic c:Z

.field public final synthetic d:I

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;Lm11/H$p;ZII)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm11/q;->a:Landroidx/compose/ui/l;

    iput-object p2, p0, Lm11/q;->b:Lm11/H$p;

    iput-boolean p3, p0, Lm11/q;->c:Z

    iput p4, p0, Lm11/q;->d:I

    iput p5, p0, Lm11/q;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, Lm11/q;->a:Landroidx/compose/ui/l;

    iget-object v1, p0, Lm11/q;->b:Lm11/H$p;

    iget-boolean v2, p0, Lm11/q;->c:Z

    iget v3, p0, Lm11/q;->d:I

    iget v4, p0, Lm11/q;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, Lm11/G;->o(Landroidx/compose/ui/l;Lm11/H$p;ZIILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
