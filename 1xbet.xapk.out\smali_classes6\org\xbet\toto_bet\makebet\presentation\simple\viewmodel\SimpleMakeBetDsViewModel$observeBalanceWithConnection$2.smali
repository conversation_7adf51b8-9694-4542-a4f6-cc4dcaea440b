.class final Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.simple.viewmodel.SimpleMakeBetDsViewModel$observeBalanceWithConnection$2"
    f = "SimpleMakeBetDsViewModel.kt"
    l = {
        0xef,
        0xeb
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->e4()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xbet/balance/model/BalanceModel;",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "balance",
        "Lorg/xbet/balance/model/BalanceModel;",
        "isConnected",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->invoke(Lorg/xbet/balance/model/BalanceModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/balance/model/BalanceModel;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/balance/model/BalanceModel;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$0:Ljava/lang/Object;

    iput-boolean p2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->Z$0:Z

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x2

    .line 10
    const/4 v4, 0x1

    .line 11
    const/4 v5, 0x0

    .line 12
    if-eqz v2, :cond_2

    .line 13
    .line 14
    if-eq v2, v4, :cond_1

    .line 15
    .line 16
    if-ne v2, v3, :cond_0

    .line 17
    .line 18
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$0:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v1, Lorg/xbet/balance/model/BalanceModel;

    .line 21
    .line 22
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    goto/16 :goto_3

    .line 26
    .line 27
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 28
    .line 29
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 30
    .line 31
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw v1

    .line 35
    :cond_1
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$2:Ljava/lang/Object;

    .line 36
    .line 37
    check-cast v2, Ljava/lang/String;

    .line 38
    .line 39
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$1:Ljava/lang/Object;

    .line 40
    .line 41
    check-cast v4, Lorg/xbet/toto_bet/makebet/domain/usecase/p;

    .line 42
    .line 43
    iget-object v6, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast v6, Lorg/xbet/balance/model/BalanceModel;

    .line 46
    .line 47
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    move-object v7, v6

    .line 51
    move-object v6, v4

    .line 52
    move-object/from16 v4, p1

    .line 53
    .line 54
    goto/16 :goto_1

    .line 55
    .line 56
    :cond_2
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    move-object v6, v2

    .line 62
    check-cast v6, Lorg/xbet/balance/model/BalanceModel;

    .line 63
    .line 64
    iget-boolean v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->Z$0:Z

    .line 65
    .line 66
    iget-object v7, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 67
    .line 68
    invoke-static {v7, v6}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->T3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;Lorg/xbet/balance/model/BalanceModel;)V

    .line 69
    .line 70
    .line 71
    iget-object v7, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 72
    .line 73
    invoke-static {v7}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 74
    .line 75
    .line 76
    move-result-object v7

    .line 77
    invoke-interface {v7}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v7

    .line 81
    check-cast v7, Ljava/lang/Number;

    .line 82
    .line 83
    invoke-virtual {v7}, Ljava/lang/Number;->longValue()J

    .line 84
    .line 85
    .line 86
    move-result-wide v7

    .line 87
    invoke-virtual {v6}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 88
    .line 89
    .line 90
    move-result-wide v9

    .line 91
    cmp-long v11, v7, v9

    .line 92
    .line 93
    if-eqz v11, :cond_9

    .line 94
    .line 95
    iget-object v7, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 96
    .line 97
    invoke-static {v7}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->u3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 98
    .line 99
    .line 100
    move-result-object v7

    .line 101
    :cond_3
    invoke-interface {v7}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 102
    .line 103
    .line 104
    move-result-object v8

    .line 105
    move-object v9, v8

    .line 106
    check-cast v9, LYU0/a;

    .line 107
    .line 108
    new-instance v10, Ljava/math/BigDecimal;

    .line 109
    .line 110
    const-wide/high16 v11, -0x4010000000000000L    # -1.0

    .line 111
    .line 112
    invoke-static {v11, v12}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    .line 113
    .line 114
    .line 115
    move-result-object v11

    .line 116
    invoke-direct {v10, v11}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 117
    .line 118
    .line 119
    const/16 v15, 0x16

    .line 120
    .line 121
    const/16 v16, 0x0

    .line 122
    .line 123
    const/4 v11, 0x0

    .line 124
    const/4 v12, 0x0

    .line 125
    const/4 v13, 0x1

    .line 126
    const/4 v14, 0x0

    .line 127
    invoke-static/range {v9 .. v16}, LYU0/a;->b(LYU0/a;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;ZLjava/lang/String;ILjava/lang/Object;)LYU0/a;

    .line 128
    .line 129
    .line 130
    move-result-object v9

    .line 131
    invoke-interface {v7, v8, v9}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 132
    .line 133
    .line 134
    move-result v8

    .line 135
    if-eqz v8, :cond_3

    .line 136
    .line 137
    if-eqz v2, :cond_9

    .line 138
    .line 139
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 140
    .line 141
    invoke-static {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->x3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 142
    .line 143
    .line 144
    move-result-object v2

    .line 145
    invoke-virtual {v6}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 146
    .line 147
    .line 148
    move-result-wide v7

    .line 149
    invoke-static {v7, v8}, LHc/a;->f(J)Ljava/lang/Long;

    .line 150
    .line 151
    .line 152
    move-result-object v7

    .line 153
    invoke-interface {v2, v7}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 154
    .line 155
    .line 156
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 157
    .line 158
    invoke-static {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->I3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/p;

    .line 159
    .line 160
    .line 161
    move-result-object v2

    .line 162
    invoke-virtual {v6}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 163
    .line 164
    .line 165
    move-result-object v7

    .line 166
    invoke-virtual {v6}, Lorg/xbet/balance/model/BalanceModel;->getCurrencyIsoCode()Ljava/lang/String;

    .line 167
    .line 168
    .line 169
    move-result-object v8

    .line 170
    invoke-interface {v8}, Ljava/lang/CharSequence;->length()I

    .line 171
    .line 172
    .line 173
    move-result v9

    .line 174
    if-lez v9, :cond_4

    .line 175
    .line 176
    goto :goto_0

    .line 177
    :cond_4
    move-object v8, v5

    .line 178
    :goto_0
    if-nez v8, :cond_6

    .line 179
    .line 180
    iget-object v8, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 181
    .line 182
    invoke-static {v8}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->y3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lgk/b;

    .line 183
    .line 184
    .line 185
    move-result-object v8

    .line 186
    invoke-virtual {v6}, Lorg/xbet/balance/model/BalanceModel;->getCurrencyId()J

    .line 187
    .line 188
    .line 189
    move-result-wide v9

    .line 190
    iput-object v6, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$0:Ljava/lang/Object;

    .line 191
    .line 192
    iput-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$1:Ljava/lang/Object;

    .line 193
    .line 194
    iput-object v7, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$2:Ljava/lang/Object;

    .line 195
    .line 196
    iput v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->label:I

    .line 197
    .line 198
    invoke-interface {v8, v9, v10, v0}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    move-result-object v4

    .line 202
    if-ne v4, v1, :cond_5

    .line 203
    .line 204
    goto :goto_2

    .line 205
    :cond_5
    move-object/from16 v17, v6

    .line 206
    .line 207
    move-object v6, v2

    .line 208
    move-object v2, v7

    .line 209
    move-object/from16 v7, v17

    .line 210
    .line 211
    :goto_1
    check-cast v4, Lbk/a;

    .line 212
    .line 213
    invoke-virtual {v4}, Lbk/a;->c()Ljava/lang/String;

    .line 214
    .line 215
    .line 216
    move-result-object v8

    .line 217
    move-object/from16 v17, v7

    .line 218
    .line 219
    move-object v7, v2

    .line 220
    move-object v2, v6

    .line 221
    move-object/from16 v6, v17

    .line 222
    .line 223
    :cond_6
    iput-object v6, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$0:Ljava/lang/Object;

    .line 224
    .line 225
    iput-object v5, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$1:Ljava/lang/Object;

    .line 226
    .line 227
    iput-object v5, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->L$2:Ljava/lang/Object;

    .line 228
    .line 229
    iput v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->label:I

    .line 230
    .line 231
    invoke-virtual {v2, v7, v8, v0}, Lorg/xbet/toto_bet/makebet/domain/usecase/p;->a(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 232
    .line 233
    .line 234
    move-result-object v2

    .line 235
    if-ne v2, v1, :cond_7

    .line 236
    .line 237
    :goto_2
    return-object v1

    .line 238
    :cond_7
    move-object v1, v6

    .line 239
    :goto_3
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 240
    .line 241
    invoke-static {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->u3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 242
    .line 243
    .line 244
    move-result-object v2

    .line 245
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel$observeBalanceWithConnection$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 246
    .line 247
    :cond_8
    invoke-interface {v2}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 248
    .line 249
    .line 250
    move-result-object v4

    .line 251
    move-object v5, v4

    .line 252
    check-cast v5, LYU0/a;

    .line 253
    .line 254
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->B3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/c;

    .line 255
    .line 256
    .line 257
    move-result-object v6

    .line 258
    invoke-virtual {v6}, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->a()Ljava/math/BigDecimal;

    .line 259
    .line 260
    .line 261
    move-result-object v7

    .line 262
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->A3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/a;

    .line 263
    .line 264
    .line 265
    move-result-object v6

    .line 266
    invoke-virtual {v6}, Lorg/xbet/toto_bet/makebet/domain/usecase/a;->a()Ljava/math/BigDecimal;

    .line 267
    .line 268
    .line 269
    move-result-object v8

    .line 270
    invoke-virtual {v1}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 271
    .line 272
    .line 273
    move-result-object v10

    .line 274
    const/4 v11, 0x1

    .line 275
    const/4 v12, 0x0

    .line 276
    const/4 v6, 0x0

    .line 277
    const/4 v9, 0x0

    .line 278
    invoke-static/range {v5 .. v12}, LYU0/a;->b(LYU0/a;Ljava/math/BigDecimal;Ljava/math/BigDecimal;Ljava/math/BigDecimal;ZLjava/lang/String;ILjava/lang/Object;)LYU0/a;

    .line 279
    .line 280
    .line 281
    move-result-object v5

    .line 282
    invoke-interface {v2, v4, v5}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 283
    .line 284
    .line 285
    move-result v4

    .line 286
    if-eqz v4, :cond_8

    .line 287
    .line 288
    :cond_9
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 289
    .line 290
    return-object v1
.end method
