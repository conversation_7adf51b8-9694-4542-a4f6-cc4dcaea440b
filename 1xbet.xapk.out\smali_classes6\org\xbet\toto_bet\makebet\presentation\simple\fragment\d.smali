.class public final synthetic Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/d;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/d;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->A2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Landroidx/lifecycle/e0$c;

    move-result-object v0

    return-object v0
.end method
