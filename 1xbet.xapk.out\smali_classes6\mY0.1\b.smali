.class public final LmY0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a9\u0010\u0006\u001a\u00028\u0001\"\u0008\u0008\u0000\u0010\u0001*\u00020\u0000\"\u000e\u0008\u0001\u0010\u0003*\u0008\u0012\u0004\u0012\u00028\u00000\u0002*\u0008\u0012\u0004\u0012\u00028\u00000\u00042\u0006\u0010\u0005\u001a\u00028\u0001\u00a2\u0006\u0004\u0008\u0006\u0010\u0007\u00a8\u0006\u0008"
    }
    d2 = {
        "LmY0/d;",
        "Position",
        "LmY0/a;",
        "A",
        "LmY0/a$a;",
        "axis",
        "a",
        "(LmY0/a$a;LmY0/a;)LmY0/a;",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LmY0/a$a;LmY0/a;)LmY0/a;
    .locals 1
    .param p0    # LmY0/a$a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # LmY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<Position:",
            "LmY0/d;",
            "A:",
            "LmY0/a<",
            "TPosition;>;>(",
            "LmY0/a$a<",
            "TPosition;>;TA;)TA;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, LmY0/a$a;->a()LDY0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p1, v0}, LmY0/a;->E(LDY0/a;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, LmY0/a$a;->f()LDY0/a;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p1, v0}, LmY0/a;->J(LDY0/a;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, LmY0/a$a;->b()LDY0/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, LmY0/a;->F(LDY0/a;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LmY0/a$a;->c()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p1, v0}, LmY0/a;->G(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p0}, LmY0/a$a;->g()F

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    invoke-virtual {p1, v0}, LmY0/a;->K(F)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p0}, LmY0/a$a;->j()LnY0/a;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p1, v0}, LmY0/a;->N(LnY0/a;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, LmY0/a$a;->e()LmY0/a$b;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p1, v0}, LmY0/a;->I(LmY0/a$b;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p0}, LmY0/a$a;->i()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {p1, v0}, LmY0/a;->M(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p0}, LmY0/a$a;->h()Ljava/lang/CharSequence;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {p1, v0}, LmY0/a;->L(Ljava/lang/CharSequence;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, LmY0/a$a;->d()F

    .line 65
    .line 66
    .line 67
    move-result p0

    .line 68
    invoke-virtual {p1, p0}, LmY0/a;->H(F)V

    .line 69
    .line 70
    .line 71
    return-object p1
.end method
