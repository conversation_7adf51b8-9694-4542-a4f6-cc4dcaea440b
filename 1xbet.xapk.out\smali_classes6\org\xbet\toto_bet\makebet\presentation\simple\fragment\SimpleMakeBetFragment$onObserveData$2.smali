.class final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.simple.fragment.SimpleMakeBetFragment$onObserveData$2"
    f = "SimpleMakeBetFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "L<PERSON>lin/jvm/functions/Function2<",
        "LIU0/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "LIU0/a;",
        "taxStatusModel",
        "",
        "<anonymous>",
        "(LIU0/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(LIU0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LIU0/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, LIU0/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->invoke(LIU0/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, LIU0/a;

    .line 14
    .line 15
    invoke-virtual {p1}, LIU0/a;->d()Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 22
    .line 23
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    iget-object v0, v0, LOU0/g;->h:Lorg/xbet/ui_common/viewcomponents/views/tax/TaxExpandableLinearLayout;

    .line 28
    .line 29
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 30
    .line 31
    const/4 v2, 0x0

    .line 32
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    sget v2, Lpb/g;->ic_info_new:I

    .line 36
    .line 37
    invoke-virtual {v0, v2}, Lorg/xbet/ui_common/viewcomponents/views/tax/TaxExpandableLinearLayout;->setHeaderIcon(I)V

    .line 38
    .line 39
    .line 40
    sget v2, Lpb/k;->tax_bonus_empty:I

    .line 41
    .line 42
    invoke-virtual {v1, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/tax/TaxExpandableLinearLayout;->setHeaderTitle(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p1}, LIU0/a;->a()Ljava/lang/String;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    if-lez v1, :cond_0

    .line 58
    .line 59
    invoke-virtual {p1}, LIU0/a;->a()Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/viewcomponents/views/tax/TaxExpandableLinearLayout;->setContentText(Ljava/lang/String;)V

    .line 64
    .line 65
    .line 66
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 67
    .line 68
    return-object p1

    .line 69
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 70
    .line 71
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 72
    .line 73
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw p1
.end method
