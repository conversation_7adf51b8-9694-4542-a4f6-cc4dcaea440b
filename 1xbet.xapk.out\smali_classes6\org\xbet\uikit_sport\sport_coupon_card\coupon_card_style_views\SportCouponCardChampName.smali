.class public final Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LU31/a;
.implements LU31/i;
.implements LU31/e;
.implements LU31/d;
.implements LU31/j;
.implements LU31/f;
.implements LU31/h;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00cc\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\r\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008+\n\u0002\u0010\u0007\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 \u00b1\u00012\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u00042\u00020\u00052\u00020\u00062\u00020\u00072\u00020\u0008:\u00013B\'\u0008\u0007\u0012\u0006\u0010\n\u001a\u00020\t\u0012\n\u0008\u0002\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u0012\u0008\u0008\u0002\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0011\u0010\u0012\u001a\u0004\u0018\u00010\u0011H\u0016\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0014\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J7\u0010\u001f\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\r2\u0006\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001e\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010#\u001a\u00020\u00162\u0006\u0010\"\u001a\u00020!H\u0014\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010\'\u001a\u00020\u00162\u0006\u0010&\u001a\u00020%H\u0016\u00a2\u0006\u0004\u0008\'\u0010(J%\u0010,\u001a\u00020\u00162\u0014\u0010+\u001a\u0010\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u0016\u0018\u00010)H\u0016\u00a2\u0006\u0004\u0008,\u0010-J%\u0010.\u001a\u00020\u00162\u0014\u0010+\u001a\u0010\u0012\u0004\u0012\u00020*\u0012\u0004\u0012\u00020\u0016\u0018\u00010)H\u0016\u00a2\u0006\u0004\u0008.\u0010-J\u0017\u00101\u001a\u00020\u00162\u0006\u00100\u001a\u00020/H\u0016\u00a2\u0006\u0004\u00081\u00102J\u000f\u00103\u001a\u00020\u0016H\u0016\u00a2\u0006\u0004\u00083\u00104J\u0019\u00107\u001a\u00020\u00162\u0008\u00106\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u00087\u00108J\u0019\u0010:\u001a\u00020\u00162\u0008\u00109\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008:\u00108J\u0017\u0010<\u001a\u00020\u00162\u0006\u0010;\u001a\u00020\u0019H\u0016\u00a2\u0006\u0004\u0008<\u0010=J\u0019\u0010?\u001a\u00020\u00162\u0008\u0010>\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008?\u00108J\u0019\u0010A\u001a\u00020\u00162\u0008\u0010@\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008A\u00108J!\u0010E\u001a\u00020\u00162\u0008\u0010B\u001a\u0004\u0018\u0001052\u0006\u0010D\u001a\u00020CH\u0016\u00a2\u0006\u0004\u0008E\u0010FJ\u0019\u0010H\u001a\u00020\u00162\u0008\u0010G\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008H\u00108J\u0017\u0010J\u001a\u00020\u00162\u0006\u0010I\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008J\u0010KJ\u0019\u0010M\u001a\u00020\u00162\u0008\u0010L\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008M\u00108J\u0019\u0010O\u001a\u00020\u00162\u0008\u0010N\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008O\u00108J\u0019\u0010Q\u001a\u00020\u00162\u0008\u0008\u0001\u0010P\u001a\u00020\rH\u0016\u00a2\u0006\u0004\u0008Q\u0010KJ\u0019\u0010S\u001a\u00020\u00162\u0008\u0010R\u001a\u0004\u0018\u000105H\u0016\u00a2\u0006\u0004\u0008S\u00108J\u001b\u0010U\u001a\u00020\u00162\n\u0008\u0001\u0010T\u001a\u0004\u0018\u00010\rH\u0016\u00a2\u0006\u0004\u0008U\u0010VJ\u0017\u00107\u001a\u00020\u00162\u0008\u0008\u0001\u00106\u001a\u00020\r\u00a2\u0006\u0004\u00087\u0010KJ\u0017\u0010:\u001a\u00020\u00162\u0008\u0008\u0001\u00109\u001a\u00020\r\u00a2\u0006\u0004\u0008:\u0010KJ\u0017\u0010?\u001a\u00020\u00162\u0008\u0008\u0001\u0010W\u001a\u00020\r\u00a2\u0006\u0004\u0008?\u0010KJ\u0017\u0010E\u001a\u00020\u00162\u0008\u0010X\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u0008E\u00108J\u0015\u0010Y\u001a\u00020\u00162\u0006\u0010D\u001a\u00020C\u00a2\u0006\u0004\u0008Y\u0010ZJ\u0017\u0010O\u001a\u00020\u00162\u0008\u0008\u0001\u0010[\u001a\u00020\r\u00a2\u0006\u0004\u0008O\u0010KJ\u0015\u0010Q\u001a\u00020\u00162\u0006\u0010P\u001a\u00020\u0011\u00a2\u0006\u0004\u0008Q\u0010\\J\u0017\u0010S\u001a\u00020\u00162\u0008\u0008\u0001\u0010R\u001a\u00020\r\u00a2\u0006\u0004\u0008S\u0010KJ+\u0010_\u001a\u00020\u00162\u0008\u00109\u001a\u0004\u0018\u0001052\u0008\u0010]\u001a\u0004\u0018\u0001052\u0008\u0010^\u001a\u0004\u0018\u000105\u00a2\u0006\u0004\u0008_\u0010`J\u001f\u0010a\u001a\u00020\u00162\u0006\u0010\u0014\u001a\u00020\r2\u0006\u0010\u0015\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008a\u0010\u0018J\u0017\u0010c\u001a\u00020\u00162\u0006\u0010b\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008c\u0010KJ\u000f\u0010d\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008d\u0010eJ\u000f\u0010f\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008f\u00104J\u000f\u0010g\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008g\u00104J\u000f\u0010h\u001a\u00020\u0016H\u0002\u00a2\u0006\u0004\u0008h\u00104J\u0017\u0010j\u001a\u00020\r2\u0006\u0010i\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008j\u0010kJ\u001f\u0010m\u001a\u00020\r2\u0006\u0010i\u001a\u00020\r2\u0006\u0010l\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008m\u0010nJ\u001f\u0010q\u001a\u00020o2\u0006\u0010\"\u001a\u00020!2\u0006\u0010p\u001a\u00020oH\u0002\u00a2\u0006\u0004\u0008q\u0010rJ\u001f\u0010s\u001a\u00020o2\u0006\u0010\"\u001a\u00020!2\u0006\u0010p\u001a\u00020oH\u0002\u00a2\u0006\u0004\u0008s\u0010rR\u001b\u0010x\u001a\u00020t8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00083\u0010u\u001a\u0004\u0008v\u0010wR\u0014\u0010z\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010yR\u0014\u0010|\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010yR\u0014\u0010~\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008}\u0010yR\u0015\u0010\u0080\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u007f\u0010yR\u0016\u0010\u0082\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0081\u0001\u0010yR\u0016\u0010\u0084\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0083\u0001\u0010yR\u0016\u0010\u0086\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0085\u0001\u0010yR\u0016\u0010\u0088\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0087\u0001\u0010yR\u0017\u0010\u008b\u0001\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0089\u0001\u0010\u008a\u0001R\u0015\u0010\u008c\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010yR\u0015\u0010\u008d\u0001\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010yR\u0018\u0010\u0091\u0001\u001a\u00030\u008e\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u008f\u0001\u0010\u0090\u0001R\u001f\u0010\u0094\u0001\u001a\u00030\u008e\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008j\u0010u\u001a\u0006\u0008\u0092\u0001\u0010\u0093\u0001R\u001f\u0010\u0096\u0001\u001a\u00030\u008e\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008m\u0010u\u001a\u0006\u0008\u0095\u0001\u0010\u0093\u0001R\u001f\u0010\u0098\u0001\u001a\u00030\u008e\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008h\u0010u\u001a\u0006\u0008\u0097\u0001\u0010\u0093\u0001R\u001f\u0010\u009c\u0001\u001a\u00030\u0099\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008g\u0010u\u001a\u0006\u0008\u009a\u0001\u0010\u009b\u0001R\u001f\u0010\u00a0\u0001\u001a\u00030\u009d\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008f\u0010u\u001a\u0006\u0008\u009e\u0001\u0010\u009f\u0001R\u0017\u0010\u00a3\u0001\u001a\u00030\u00a1\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u00081\u0010\u00a2\u0001R\u0016\u0010\u00a4\u0001\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008c\u0010\u008a\u0001R\u0016\u0010\u00a5\u0001\u001a\u00020o8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008d\u0010\u008a\u0001R\u0017\u0010\u00a8\u0001\u001a\u00030\u00a6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008a\u0010\u00a7\u0001R\u0018\u0010\u00ac\u0001\u001a\u00030\u00a9\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00aa\u0001\u0010\u00ab\u0001R\u0018\u0010\u00b0\u0001\u001a\u00030\u00ad\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ae\u0001\u0010\u00af\u0001\u00a8\u0006\u00b2\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;",
        "Landroid/widget/FrameLayout;",
        "LU31/a;",
        "LU31/i;",
        "LU31/e;",
        "LU31/d;",
        "LU31/j;",
        "LU31/f;",
        "LU31/h;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "LX31/c;",
        "couponCardUiModel",
        "setModel",
        "(LX31/c;)V",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "",
        "url",
        "s",
        "(Ljava/lang/String;)V",
        "a",
        "()V",
        "",
        "caption",
        "setCaption",
        "(Ljava/lang/CharSequence;)V",
        "title",
        "setTitle",
        "live",
        "b",
        "(Z)V",
        "subTitle",
        "setSubTitle",
        "marketHeader",
        "setMarketHeader",
        "coef",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "setMarketCoefficient",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "bonusTitle",
        "setCouponBonusTitle",
        "res",
        "setSportImage",
        "(I)V",
        "description",
        "setMarketDescription",
        "tagText",
        "setTagText",
        "tagColor",
        "setTagColor",
        "error",
        "setError",
        "marketStyle",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "subtitle",
        "marketCoefficient",
        "setMarketCoefficientState",
        "(Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "tag",
        "(Landroid/content/res/ColorStateList;)V",
        "header",
        "coefficient",
        "setMarket",
        "(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V",
        "v",
        "width",
        "t",
        "u",
        "()I",
        "r",
        "q",
        "p",
        "errorTextHeight",
        "n",
        "(I)I",
        "subTitleHeight",
        "o",
        "(II)I",
        "",
        "topOffset",
        "l",
        "(Landroid/graphics/Canvas;F)F",
        "k",
        "Lorg/xbet/uikit/utils/e;",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "I",
        "iconSize",
        "c",
        "liveIconSize",
        "d",
        "buttonSize",
        "e",
        "paddingHorizontal",
        "f",
        "paddingVertical",
        "g",
        "buttonHorizontalMargin",
        "h",
        "iconEndMargin",
        "i",
        "shimmerHeight",
        "j",
        "F",
        "captionMargin",
        "topElementsMargin",
        "marketVerticalMargin",
        "LW31/h;",
        "m",
        "LW31/h;",
        "titleDelegate",
        "getSubTitleDelegate",
        "()LW31/h;",
        "subTitleDelegate",
        "getCaptionDelegate",
        "captionDelegate",
        "getErrorDelegate",
        "errorDelegate",
        "LW31/f;",
        "getTagDelegate",
        "()LW31/f;",
        "tagDelegate",
        "LW31/e;",
        "getShimmerDelegate",
        "()LW31/e;",
        "shimmerDelegate",
        "LW31/i;",
        "LW31/i;",
        "buttonsDelegate",
        "shrinkCoefTextSize",
        "usualCoefTextSize",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "marketView",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "w",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "sportImageView",
        "Landroid/widget/ImageView;",
        "x",
        "Landroid/widget/ImageView;",
        "liveImageView",
        "y",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final y:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final z:I


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:F

.field public final k:I

.field public final l:I

.field public final m:LW31/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:LW31/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:F

.field public final u:F

.field public final v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Lorg/xbet/uikit/components/views/LoadableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->y:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->z:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 10
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct/range {p0 .. p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    new-instance v0, LV31/f;

    invoke-direct {v0, p0}, LV31/f;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->a:Lkotlin/j;

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_16:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_14:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->c:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_40:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->d:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->f:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_8:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->g:I

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->h:I

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_120:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->i:I

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->j:F

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_8:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->l:I

    .line 18
    new-instance v0, LW31/h;

    .line 19
    sget v4, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    const/4 v5, 0x4

    const/4 v6, 0x0

    const/4 v2, 0x3

    const/4 v3, 0x0

    move-object v1, p0

    .line 20
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 21
    new-instance v0, LV31/g;

    invoke-direct {v0, p0}, LV31/g;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->n:Lkotlin/j;

    .line 22
    new-instance v0, LV31/h;

    invoke-direct {v0, p0}, LV31/h;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->o:Lkotlin/j;

    .line 23
    new-instance v0, LV31/i;

    invoke-direct {v0, p0}, LV31/i;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->p:Lkotlin/j;

    .line 24
    new-instance v0, LV31/j;

    invoke-direct {v0, p0}, LV31/j;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->q:Lkotlin/j;

    .line 25
    new-instance v0, LV31/k;

    invoke-direct {v0, p0}, LV31/k;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->r:Lkotlin/j;

    .line 26
    new-instance v0, LW31/i;

    .line 27
    sget v2, LlZ0/h;->ic_glyph_move_vertical_large:I

    .line 28
    sget v3, LlZ0/h;->ic_glyph_cancel_small:I

    .line 29
    sget v4, LlZ0/g;->size_48:I

    .line 30
    sget v5, LlZ0/g;->size_40:I

    .line 31
    invoke-direct/range {v0 .. v5}, LW31/i;-><init>(Landroid/view/ViewGroup;IIII)V

    .line 32
    invoke-virtual {v0}, LW31/c;->e()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 33
    invoke-virtual {v0}, LW31/c;->c()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 34
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 35
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->text_12:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v7

    iput v7, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->t:F

    .line 36
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->text_18:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v8

    iput v8, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->u:F

    .line 37
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 38
    sget-object v1, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->START:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketBlockedIconPosition(Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;)V

    .line 39
    sget v1, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionTextStyle(I)V

    .line 40
    sget v1, LlZ0/n;->TextStyle_Caption_Regular_L_TextPrimary:I

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setHeaderTextStyle(I)V

    .line 41
    sget v1, LlZ0/n;->TextStyle_Headline_Medium_TextPrimary:I

    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientTextStyle(I)V

    const v1, 0x7fffffff

    .line 42
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionMaxLines(I)V

    const/4 v1, 0x1

    .line 43
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setHeaderMaxLines(I)V

    .line 44
    invoke-virtual {v0, v8}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMaxTextSize(F)V

    .line 45
    invoke-virtual {v0, v7}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMinTextSize(F)V

    .line 46
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 47
    new-instance v0, Lorg/xbet/uikit/components/views/LoadableImageView;

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/views/LoadableImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 48
    sget v2, LlZ0/d;->uikitSecondary:I

    const/4 v3, 0x2

    const/4 v4, 0x0

    invoke-static {p1, v2, v4, v3, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-virtual {v0, v2}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 49
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 50
    new-instance v2, Landroid/widget/ImageView;

    invoke-direct {v2, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 51
    sget v3, LlZ0/h;->ic_glyph_live_indicator:I

    invoke-static {p1, v3}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v3

    invoke-virtual {v2, v3}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 52
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    const/16 v3, 0x8

    .line 53
    invoke-virtual {v2, v3}, Landroid/view/View;->setVisibility(I)V

    .line 54
    iput-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 55
    sget-object v2, Lm31/g;->SportCouponCardView:[I

    const/4 v3, 0x0

    .line 56
    invoke-virtual {p1, p2, v2, p3, v3}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v2

    .line 57
    sget v5, Lm31/g;->SportCouponCardView_title:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v5

    if-eqz v5, :cond_0

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_0

    :cond_0
    move-object v5, v4

    :goto_0
    const-string v7, ""

    if-nez v5, :cond_1

    move-object v5, v7

    :cond_1
    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTitle(Ljava/lang/CharSequence;)V

    .line 58
    sget v5, Lm31/g;->SportCouponCardView_caption:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v5

    if-eqz v5, :cond_2

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_1

    :cond_2
    move-object v5, v4

    :goto_1
    if-nez v5, :cond_3

    move-object v5, v7

    :cond_3
    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setCaption(Ljava/lang/CharSequence;)V

    .line 59
    sget v5, Lm31/g;->SportCouponCardView_subtitle:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v5

    if-eqz v5, :cond_4

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_2

    :cond_4
    move-object v5, v4

    :goto_2
    if-nez v5, :cond_5

    move-object v5, v7

    :cond_5
    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 60
    sget v5, Lm31/g;->SportCouponCardView_tag:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v5

    if-eqz v5, :cond_6

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_3

    :cond_6
    move-object v5, v4

    :goto_3
    if-nez v5, :cond_7

    move-object v5, v7

    :cond_7
    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTagText(Ljava/lang/CharSequence;)V

    .line 61
    sget v5, Lm31/g;->SportCouponCardView_tagColor:I

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->c(Landroid/content/res/TypedArray;Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    move-result-object v5

    if-eqz v5, :cond_8

    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTagColor(Landroid/content/res/ColorStateList;)V

    .line 62
    :cond_8
    sget v5, Lm31/g;->SportCouponCardView_error:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v5

    if-eqz v5, :cond_9

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_4

    :cond_9
    move-object v5, v4

    :goto_4
    if-nez v5, :cond_a

    move-object v5, v7

    :cond_a
    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setError(Ljava/lang/CharSequence;)V

    .line 63
    sget v5, Lm31/g;->SportCouponCardView_couponMarketStyle:I

    invoke-virtual {v2, v5, v3}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result v5

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-virtual {p0, v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketStyle(Ljava/lang/Integer;)V

    .line 64
    sget v5, Lm31/g;->SportCouponCardView_marketTitle:I

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    invoke-static {v2, p1, v5}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v5

    if-eqz v5, :cond_b

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    goto :goto_5

    :cond_b
    move-object v5, v4

    :goto_5
    if-nez v5, :cond_c

    move-object v5, v7

    .line 65
    :cond_c
    sget v8, Lm31/g;->SportCouponCardView_marketHeader:I

    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    invoke-static {v2, p1, v8}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v8

    if-eqz v8, :cond_d

    invoke-virtual {v8}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v8

    goto :goto_6

    :cond_d
    move-object v8, v4

    :goto_6
    if-nez v8, :cond_e

    move-object v8, v7

    .line 66
    :cond_e
    sget v9, Lm31/g;->SportCouponCardView_marketCoefficient:I

    invoke-static {v9}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v9

    invoke-static {v2, p1, v9}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v1

    if-eqz v1, :cond_f

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v4

    :cond_f
    if-nez v4, :cond_10

    goto :goto_7

    :cond_10
    move-object v7, v4

    .line 67
    :goto_7
    invoke-virtual {p0, v5, v8, v7}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 68
    sget v1, Lm31/g;->SportCouponCardView_showSkeleton:I

    invoke-virtual {v2, v1, v3}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v1

    if-eqz v1, :cond_11

    .line 69
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->a()V

    .line 70
    :cond_11
    invoke-virtual {v2}, Landroid/content/res/TypedArray;->recycle()V

    .line 71
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 72
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCouponCardStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic c(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->i(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->y(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->j(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getCaptionDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->o:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getErrorDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->p:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getShimmerDelegate()LW31/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->r:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSubTitleDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->n:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTagDelegate()LW31/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->q:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic h(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/e;

    move-result-object p0

    return-object p0
.end method

.method public static final i(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final j(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    move-object v1, p0

    .line 10
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static final m(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Warning:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const v2, 0x7fffffff

    .line 8
    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    move-object v1, p0

    .line 12
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method private final u()I
    .locals 7

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, LW31/h;->c()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    int-to-float v0, v0

    .line 16
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->j:F

    .line 17
    .line 18
    add-float/2addr v0, v1

    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    const/4 v2, 0x0

    .line 32
    if-nez v1, :cond_0

    .line 33
    .line 34
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 47
    .line 48
    add-int/2addr v1, v3

    .line 49
    goto :goto_0

    .line 50
    :cond_0
    const/4 v1, 0x0

    .line 51
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 52
    .line 53
    .line 54
    move-result-object v3

    .line 55
    invoke-virtual {v3}, LW31/h;->e()Z

    .line 56
    .line 57
    .line 58
    move-result v3

    .line 59
    if-nez v3, :cond_1

    .line 60
    .line 61
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    invoke-virtual {v2}, LW31/h;->c()I

    .line 66
    .line 67
    .line 68
    move-result v2

    .line 69
    :cond_1
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->n(I)I

    .line 70
    .line 71
    .line 72
    move-result v3

    .line 73
    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->o(II)I

    .line 74
    .line 75
    .line 76
    move-result v4

    .line 77
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 78
    .line 79
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 80
    .line 81
    .line 82
    move-result v5

    .line 83
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->l:I

    .line 84
    .line 85
    mul-int/lit8 v6, v6, 0x2

    .line 86
    .line 87
    add-int/2addr v5, v6

    .line 88
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getShimmerDelegate()LW31/e;

    .line 89
    .line 90
    .line 91
    move-result-object v6

    .line 92
    invoke-virtual {v6}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 93
    .line 94
    .line 95
    move-result-object v6

    .line 96
    invoke-virtual {v6}, Landroid/view/View;->getVisibility()I

    .line 97
    .line 98
    .line 99
    move-result v6

    .line 100
    if-nez v6, :cond_2

    .line 101
    .line 102
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->i:I

    .line 103
    .line 104
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    goto :goto_1

    .line 109
    :cond_2
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->f:I

    .line 110
    .line 111
    add-int/2addr v6, v5

    .line 112
    add-int/2addr v6, v1

    .line 113
    add-int/2addr v6, v2

    .line 114
    add-int/2addr v6, v4

    .line 115
    add-int/2addr v6, v3

    .line 116
    int-to-float v1, v6

    .line 117
    add-float/2addr v1, v0

    .line 118
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    :goto_1
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 123
    .line 124
    .line 125
    move-result v0

    .line 126
    const/high16 v1, 0x40000000    # 2.0f

    .line 127
    .line 128
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 129
    .line 130
    .line 131
    move-result v0

    .line 132
    return v0
.end method

.method private final v(II)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0}, LW31/c;->e()Landroid/widget/ImageView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->d:I

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 17
    .line 18
    :goto_0
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 23
    .line 24
    sub-int/2addr p1, v1

    .line 25
    sub-int/2addr p1, v0

    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 27
    .line 28
    const/high16 v1, 0x40000000    # 2.0f

    .line 29
    .line 30
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public static final w(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/e;
    .locals 2

    .line 1
    new-instance v0, LW31/e;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->i:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/e;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final x(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    move-object v1, p0

    .line 10
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static final y(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;)LW31/f;
    .locals 2

    .line 1
    new-instance v0, LW31/f;

    .line 2
    .line 3
    sget v1, LlZ0/n;->Widget_Tag_RectangularS_Red:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/f;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method


# virtual methods
.method public a()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/e;->d()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public b(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    const/4 p1, 0x0

    .line 6
    goto :goto_0

    .line 7
    :cond_0
    const/16 p1, 0x8

    .line 8
    .line 9
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final k(Landroid/graphics/Canvas;F)F
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 10
    .line 11
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->c:I

    .line 12
    .line 13
    add-int/2addr v0, v1

    .line 14
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->h:I

    .line 15
    .line 16
    add-int/2addr v0, v1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 19
    .line 20
    :goto_0
    int-to-float v0, v0

    .line 21
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    invoke-virtual {v1, p1, v0, p2}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 26
    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    invoke-virtual {p1}, LW31/h;->e()Z

    .line 33
    .line 34
    .line 35
    move-result p1

    .line 36
    const/4 v0, 0x0

    .line 37
    if-nez p1, :cond_1

    .line 38
    .line 39
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    const/4 p1, 0x0

    .line 43
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    invoke-virtual {v1}, LW31/h;->e()Z

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    if-nez v1, :cond_2

    .line 52
    .line 53
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-virtual {v0}, LW31/h;->c()I

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    add-int/2addr v0, p1

    .line 62
    :cond_2
    int-to-float p1, v0

    .line 63
    add-float/2addr p2, p1

    .line 64
    return p2
.end method

.method public final l(Landroid/graphics/Canvas;F)F
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v1, 0x0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 29
    .line 30
    add-int/2addr v0, v2

    .line 31
    goto :goto_0

    .line 32
    :cond_0
    const/4 v0, 0x0

    .line 33
    :goto_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->j:F

    .line 34
    .line 35
    add-float/2addr p2, v2

    .line 36
    int-to-float v0, v0

    .line 37
    add-float/2addr p2, v0

    .line 38
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 39
    .line 40
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 41
    .line 42
    int-to-float v2, v2

    .line 43
    invoke-virtual {v0, p1, v2, p2}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 44
    .line 45
    .line 46
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 47
    .line 48
    invoke-virtual {p1}, LW31/h;->e()Z

    .line 49
    .line 50
    .line 51
    move-result p1

    .line 52
    if-nez p1, :cond_1

    .line 53
    .line 54
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 55
    .line 56
    invoke-virtual {p1}, LW31/h;->c()I

    .line 57
    .line 58
    .line 59
    move-result p1

    .line 60
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 61
    .line 62
    add-int v1, p1, v0

    .line 63
    .line 64
    :cond_1
    int-to-float p1, v1

    .line 65
    add-float/2addr p2, p1

    .line 66
    return p2
.end method

.method public final n(I)I
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    if-lez p1, :cond_0

    .line 3
    .line 4
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 5
    .line 6
    goto :goto_0

    .line 7
    :cond_0
    const/4 p1, 0x0

    .line 8
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v1}, LW31/h;->e()Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-nez v1, :cond_1

    .line 17
    .line 18
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, LW31/h;->c()I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    add-int/2addr v0, p1

    .line 27
    :cond_1
    return v0
.end method

.method public final o(II)I
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    if-gtz p2, :cond_1

    .line 3
    .line 4
    if-lez p1, :cond_0

    .line 5
    .line 6
    goto :goto_0

    .line 7
    :cond_0
    const/4 p1, 0x0

    .line 8
    goto :goto_1

    .line 9
    :cond_1
    :goto_0
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 10
    .line 11
    :goto_1
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 12
    .line 13
    invoke-virtual {p2}, LW31/h;->e()Z

    .line 14
    .line 15
    .line 16
    move-result p2

    .line 17
    if-nez p2, :cond_2

    .line 18
    .line 19
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 20
    .line 21
    invoke-virtual {p2}, LW31/h;->c()I

    .line 22
    .line 23
    .line 24
    move-result p2

    .line 25
    add-int/2addr p2, p1

    .line 26
    return p2

    .line 27
    :cond_2
    return v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->f:I

    .line 2
    .line 3
    int-to-float v0, v0

    .line 4
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 5
    .line 6
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 7
    .line 8
    add-int/2addr v1, v2

    .line 9
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->h:I

    .line 10
    .line 11
    add-int/2addr v1, v2

    .line 12
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 13
    .line 14
    .line 15
    move-result-object v2

    .line 16
    int-to-float v1, v1

    .line 17
    invoke-virtual {v2, p1, v1, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 18
    .line 19
    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v1}, LW31/h;->c()I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    int-to-float v1, v1

    .line 29
    add-float/2addr v0, v1

    .line 30
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->l(Landroid/graphics/Canvas;F)F

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k(Landroid/graphics/Canvas;F)F

    .line 35
    .line 36
    .line 37
    move-result v0

    .line 38
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 43
    .line 44
    int-to-float v2, v2

    .line 45
    invoke-virtual {v1, p1, v2, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 46
    .line 47
    .line 48
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-virtual {p1}, LW31/h;->e()Z

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    if-nez p1, :cond_0

    .line 57
    .line 58
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-virtual {p1}, LW31/h;->c()I

    .line 63
    .line 64
    .line 65
    :cond_0
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 6

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    if-nez p1, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getShimmerDelegate()LW31/e;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, LW31/e;->b()V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->l:I

    .line 28
    .line 29
    sub-int/2addr p1, p2

    .line 30
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 31
    .line 32
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 33
    .line 34
    .line 35
    move-result p2

    .line 36
    sub-int v3, p1, p2

    .line 37
    .line 38
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 39
    .line 40
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 41
    .line 42
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 43
    .line 44
    .line 45
    move-result p1

    .line 46
    add-int v4, v2, p1

    .line 47
    .line 48
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 49
    .line 50
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 51
    .line 52
    .line 53
    move-result p1

    .line 54
    add-int v5, v3, p1

    .line 55
    .line 56
    move-object v0, p0

    .line 57
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 58
    .line 59
    .line 60
    iget-object p1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 61
    .line 62
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 63
    .line 64
    .line 65
    move-result-object p2

    .line 66
    invoke-virtual {p2}, LW31/h;->c()I

    .line 67
    .line 68
    .line 69
    move-result p2

    .line 70
    div-int/lit8 p2, p2, 0x2

    .line 71
    .line 72
    iget p3, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->f:I

    .line 73
    .line 74
    add-int/2addr p2, p3

    .line 75
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 76
    .line 77
    .line 78
    move-result p3

    .line 79
    iget p4, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->l:I

    .line 80
    .line 81
    sub-int/2addr p3, p4

    .line 82
    iget-object p4, v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 83
    .line 84
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 85
    .line 86
    .line 87
    move-result p4

    .line 88
    div-int/lit8 p4, p4, 0x2

    .line 89
    .line 90
    sub-int/2addr p3, p4

    .line 91
    invoke-virtual {p1, p2, p3}, LW31/i;->l(II)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->r()V

    .line 95
    .line 96
    .line 97
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->q()V

    .line 98
    .line 99
    .line 100
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->p()V

    .line 101
    .line 102
    .line 103
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getShimmerDelegate()LW31/e;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getShimmerDelegate()LW31/e;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-virtual {p2, v0}, LW31/e;->c(I)V

    .line 24
    .line 25
    .line 26
    goto :goto_1

    .line 27
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v(II)V

    .line 28
    .line 29
    .line 30
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 35
    .line 36
    mul-int/lit8 v1, v1, 0x2

    .line 37
    .line 38
    sub-int v1, v0, v1

    .line 39
    .line 40
    invoke-virtual {p2, v1}, LW31/f;->c(I)V

    .line 41
    .line 42
    .line 43
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    .line 44
    .line 45
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 46
    .line 47
    mul-int/lit8 v1, v1, 0x2

    .line 48
    .line 49
    sub-int v1, v0, v1

    .line 50
    .line 51
    invoke-virtual {p2, v1}, LW31/h;->f(I)V

    .line 52
    .line 53
    .line 54
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 55
    .line 56
    .line 57
    move-result-object p2

    .line 58
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 59
    .line 60
    mul-int/lit8 v1, v1, 0x2

    .line 61
    .line 62
    sub-int v1, v0, v1

    .line 63
    .line 64
    invoke-virtual {p2, v1}, LW31/h;->f(I)V

    .line 65
    .line 66
    .line 67
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 68
    .line 69
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 70
    .line 71
    .line 72
    move-result p2

    .line 73
    if-nez p2, :cond_1

    .line 74
    .line 75
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 76
    .line 77
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->h:I

    .line 78
    .line 79
    add-int/2addr p2, v1

    .line 80
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->c:I

    .line 81
    .line 82
    add-int/2addr p2, v1

    .line 83
    goto :goto_0

    .line 84
    :cond_1
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 85
    .line 86
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 91
    .line 92
    sub-int v2, v0, v2

    .line 93
    .line 94
    sub-int/2addr v2, p2

    .line 95
    invoke-virtual {v1, v2}, LW31/h;->f(I)V

    .line 96
    .line 97
    .line 98
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->t(I)V

    .line 99
    .line 100
    .line 101
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 102
    .line 103
    invoke-virtual {p2}, LW31/c;->g()V

    .line 104
    .line 105
    .line 106
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 107
    .line 108
    invoke-virtual {p2}, Landroid/view/View;->getVisibility()I

    .line 109
    .line 110
    .line 111
    move-result p2

    .line 112
    if-nez p2, :cond_2

    .line 113
    .line 114
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 115
    .line 116
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->c:I

    .line 117
    .line 118
    const/high16 v1, 0x40000000    # 2.0f

    .line 119
    .line 120
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 121
    .line 122
    .line 123
    move-result v0

    .line 124
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->c:I

    .line 125
    .line 126
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 127
    .line 128
    .line 129
    move-result v1

    .line 130
    invoke-virtual {p2, v0, v1}, Landroid/view/View;->measure(II)V

    .line 131
    .line 132
    .line 133
    :cond_2
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->u()I

    .line 134
    .line 135
    .line 136
    move-result p2

    .line 137
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 138
    .line 139
    .line 140
    return-void
.end method

.method public final p()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 10
    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->l:I

    .line 16
    .line 17
    mul-int/lit8 v1, v1, 0x2

    .line 18
    .line 19
    add-int/2addr v0, v1

    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v1}, LW31/h;->e()Z

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-nez v1, :cond_0

    .line 29
    .line 30
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    invoke-virtual {v1}, LW31/h;->c()I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->k:I

    .line 39
    .line 40
    add-int/2addr v1, v2

    .line 41
    goto :goto_0

    .line 42
    :cond_0
    const/4 v1, 0x0

    .line 43
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 44
    .line 45
    .line 46
    move-result v2

    .line 47
    sub-int/2addr v2, v0

    .line 48
    sub-int/2addr v2, v1

    .line 49
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    invoke-virtual {v0}, LW31/h;->c()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    div-int/lit8 v0, v0, 0x2

    .line 58
    .line 59
    sub-int/2addr v2, v0

    .line 60
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->c:I

    .line 61
    .line 62
    div-int/lit8 v1, v0, 0x2

    .line 63
    .line 64
    sub-int v6, v2, v1

    .line 65
    .line 66
    div-int/lit8 v1, v0, 0x2

    .line 67
    .line 68
    add-int v8, v2, v1

    .line 69
    .line 70
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->x:Landroid/widget/ImageView;

    .line 71
    .line 72
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 73
    .line 74
    add-int v7, v5, v0

    .line 75
    .line 76
    move-object v3, p0

    .line 77
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 78
    .line 79
    .line 80
    :cond_1
    return-void
.end method

.method public final q()V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->f:I

    .line 10
    .line 11
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LW31/h;->c()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    div-int/lit8 v1, v1, 0x2

    .line 20
    .line 21
    add-int/2addr v0, v1

    .line 22
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 23
    .line 24
    div-int/lit8 v2, v1, 0x2

    .line 25
    .line 26
    sub-int v6, v0, v2

    .line 27
    .line 28
    div-int/lit8 v2, v1, 0x2

    .line 29
    .line 30
    add-int v8, v0, v2

    .line 31
    .line 32
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 33
    .line 34
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 35
    .line 36
    add-int v7, v5, v1

    .line 37
    .line 38
    move-object v3, p0

    .line 39
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 40
    .line 41
    .line 42
    :cond_0
    return-void
.end method

.method public final r()V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->f:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, LW31/h;->c()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 12
    .line 13
    invoke-virtual {v2}, Landroid/view/View;->getHeight()I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    .line 18
    .line 19
    .line 20
    move-result v1

    .line 21
    add-int/2addr v0, v1

    .line 22
    int-to-float v0, v0

    .line 23
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->j:F

    .line 24
    .line 25
    add-float/2addr v0, v1

    .line 26
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 31
    .line 32
    float-to-int v0, v0

    .line 33
    invoke-virtual {v1, v2, v2, v0}, LW31/f;->b(III)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public s(Ljava/lang/String;)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    const/16 v5, 0xe

    .line 4
    .line 5
    const/4 v6, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    move-object v1, p1

    .line 10
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->X(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->h(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setCaption(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setCaption(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setCaption(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setCouponBonusTitle(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setCaption(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final setError(I)V
    .locals 1

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->g(I)V

    return-void
.end method

.method public setError(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final setMarketCoefficient(Ljava/lang/CharSequence;)V
    .locals 1

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 0
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    return-void
.end method

.method public final setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMarketHeader(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMarketStyle(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->v:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketStyle(Ljava/lang/Integer;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setModel(LX31/c;)V
    .locals 3
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LX31/c;->j()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, LX31/c;->a()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setCaption(Ljava/lang/CharSequence;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1}, LX31/c;->m()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTagText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1}, LX31/c;->l()I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTagColor(I)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/c;->o()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTitle(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1}, LX31/c;->k()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p1}, LX31/c;->c()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setError(Ljava/lang/CharSequence;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p1}, LX31/c;->f()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-virtual {p1}, LX31/c;->g()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {p1}, LX31/c;->e()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v2

    .line 62
    invoke-virtual {p0, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p1}, LX31/c;->h()I

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketStyle(Ljava/lang/Integer;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p1}, LX31/c;->b()Lorg/xbet/uikit/components/market/base/CoefficientState;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 81
    .line 82
    .line 83
    invoke-virtual {p1}, LX31/c;->g()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {p1}, LX31/c;->d()Z

    .line 91
    .line 92
    .line 93
    move-result p1

    .line 94
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b(Z)V

    .line 95
    .line 96
    .line 97
    return-void
.end method

.method public setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->j(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setSportImage(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setSubTitle(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setSubTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setSubTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getSubTitleDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setTagColor(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->d(I)V

    return-void
.end method

.method public final setTagColor(Landroid/content/res/ColorStateList;)V
    .locals 1
    .param p1    # Landroid/content/res/ColorStateList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->e(Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public final setTagText(I)V
    .locals 1

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->f(I)V

    return-void
.end method

.method public setTagText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->g(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->m:LW31/h;

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final t(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->s:LW31/i;

    .line 2
    .line 3
    invoke-virtual {v0}, LW31/c;->c()Landroid/widget/ImageView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->d:I

    .line 14
    .line 15
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->g:I

    .line 16
    .line 17
    mul-int/lit8 v1, v1, 0x2

    .line 18
    .line 19
    add-int/2addr v0, v1

    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 v0, 0x0

    .line 22
    :goto_0
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 23
    .line 24
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    if-nez v1, :cond_1

    .line 29
    .line 30
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 31
    .line 32
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 33
    .line 34
    add-int/2addr v1, v2

    .line 35
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->h:I

    .line 36
    .line 37
    add-int/2addr v1, v2

    .line 38
    goto :goto_1

    .line 39
    :cond_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->e:I

    .line 40
    .line 41
    :goto_1
    sub-int/2addr p1, v1

    .line 42
    sub-int/2addr p1, v0

    .line 43
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->getCaptionDelegate()LW31/h;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {v0, p1}, LW31/h;->f(I)V

    .line 48
    .line 49
    .line 50
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->w:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 51
    .line 52
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 53
    .line 54
    const/high16 v1, 0x40000000    # 2.0f

    .line 55
    .line 56
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 57
    .line 58
    .line 59
    move-result v0

    .line 60
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardChampName;->b:I

    .line 61
    .line 62
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 63
    .line 64
    .line 65
    move-result v1

    .line 66
    invoke-virtual {p1, v0, v1}, Landroid/view/View;->measure(II)V

    .line 67
    .line 68
    .line 69
    return-void
.end method
