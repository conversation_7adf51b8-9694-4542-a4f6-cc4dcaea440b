.class public final synthetic Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;


# direct methods
.method public synthetic constructor <init>(ZLorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;->a:Z

    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;->a:Z

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    invoke-static {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->B2(ZLorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
