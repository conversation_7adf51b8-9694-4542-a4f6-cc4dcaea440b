.class public final LnZ0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0010\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "",
        "Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;",
        "a",
        "(I)Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(I)Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p0, :cond_5

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    if-eq p0, v0, :cond_4

    .line 5
    .line 6
    const/4 v0, 0x2

    .line 7
    if-eq p0, v0, :cond_3

    .line 8
    .line 9
    const/4 v0, 0x3

    .line 10
    if-eq p0, v0, :cond_2

    .line 11
    .line 12
    const/4 v0, 0x4

    .line 13
    if-eq p0, v0, :cond_1

    .line 14
    .line 15
    const/4 v0, 0x5

    .line 16
    if-eq p0, v0, :cond_0

    .line 17
    .line 18
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->PRIMARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 19
    .line 20
    return-object p0

    .line 21
    :cond_0
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->SENARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 22
    .line 23
    return-object p0

    .line 24
    :cond_1
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->QUINARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 25
    .line 26
    return-object p0

    .line 27
    :cond_2
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->QUATERNARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 28
    .line 29
    return-object p0

    .line 30
    :cond_3
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->TERTIARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 31
    .line 32
    return-object p0

    .line 33
    :cond_4
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->SECONDARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 34
    .line 35
    return-object p0

    .line 36
    :cond_5
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->PRIMARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 37
    .line 38
    return-object p0
.end method
