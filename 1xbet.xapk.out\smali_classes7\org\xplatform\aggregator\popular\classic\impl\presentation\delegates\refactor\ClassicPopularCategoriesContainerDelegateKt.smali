.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a3\u0010\t\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00080\u00070\u00062\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LUX0/k;",
        "nestedRecyclerViewScrollKeeper",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
        "aggregatorPopularClassicCommonClickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "h",
        "(LUX0/k;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->l(LB4/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->o(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/b;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/b;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->k(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->j(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->n(LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LHZ0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt;->m(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LHZ0/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final h(LUX0/k;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;
    .locals 2
    .param p0    # LUX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LUX0/k;",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, LOb1/a;

    .line 2
    .line 3
    invoke-direct {v0}, LOb1/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, LOb1/b;

    .line 7
    .line 8
    invoke-direct {v1, p1, p2, p0}, LOb1/b;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LUX0/k;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt$classicPopularCategoriesContainerDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt$classicPopularCategoriesContainerDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt$classicPopularCategoriesContainerDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/refactor/ClassicPopularCategoriesContainerDelegateKt$classicPopularCategoriesContainerDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance p2, LB4/b;

    .line 19
    .line 20
    invoke-direct {p2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object p2
.end method

.method public static final i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/b;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/b;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/b;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final j(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p3}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LIb1/b;

    .line 6
    .line 7
    iget-object v0, v0, LIb1/b;->b:Lorg/xbet/uikit/components/header/HeaderLarge;

    .line 8
    .line 9
    new-instance v1, LOb1/c;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1, p3}, LOb1/c;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)V

    .line 12
    .line 13
    .line 14
    const/4 v2, 0x1

    .line 15
    const/4 v3, 0x0

    .line 16
    invoke-static {v3, v1, v2, v3}, LN11/f;->k(Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/header/HeaderLarge;->setButtonClickListener(Landroid/view/View$OnClickListener;)V

    .line 21
    .line 22
    .line 23
    new-instance v0, LOb1/d;

    .line 24
    .line 25
    invoke-direct {v0, p3, p0, p1}, LOb1/d;-><init>(LB4/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)V

    .line 26
    .line 27
    .line 28
    invoke-virtual {p3, v0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 29
    .line 30
    .line 31
    new-instance p0, LOb1/e;

    .line 32
    .line 33
    invoke-direct {p0, p2, p3}, LOb1/e;-><init>(LUX0/k;LB4/a;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p3, p0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 37
    .line 38
    .line 39
    new-instance p0, LOb1/f;

    .line 40
    .line 41
    invoke-direct {p0, p2, p3}, LOb1/f;-><init>(LUX0/k;LB4/a;)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p3, p0}, LB4/a;->t(Lkotlin/jvm/functions/Function0;)V

    .line 45
    .line 46
    .line 47
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 48
    .line 49
    return-object p0
.end method

.method public static final k(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-interface {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;->l(Ljava/lang/String;Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final l(LB4/a;Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;Ljava/util/List;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    check-cast p3, LIb1/b;

    .line 6
    .line 7
    iget-object p3, p3, LIb1/b;->c:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, LQb1/a;

    .line 14
    .line 15
    invoke-virtual {v0}, LQb1/a;->d()LHZ0/f;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    const/4 v1, 0x0

    .line 20
    const/4 v2, 0x2

    .line 21
    invoke-static {p3, v0, v1, v2, v1}, Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;->setItems$default(Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;LHZ0/f;Ljava/lang/Runnable;ILjava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 25
    .line 26
    .line 27
    move-result-object p0

    .line 28
    check-cast p0, LIb1/b;

    .line 29
    .line 30
    iget-object p0, p0, LIb1/b;->c:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 31
    .line 32
    new-instance p3, LOb1/g;

    .line 33
    .line 34
    invoke-direct {p3, p1, p2}, LOb1/g;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0, p3}, Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;->setOnItemClickListener(Lkotlin/jvm/functions/Function1;)V

    .line 38
    .line 39
    .line 40
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 41
    .line 42
    return-object p0
.end method

.method public static final m(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LHZ0/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;->i(Ljava/lang/String;LHZ0/a;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final n(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LIb1/b;

    .line 14
    .line 15
    iget-object p1, p1, LIb1/b;->c:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->c(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method

.method public static final o(LUX0/k;LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView$D;->getAbsoluteAdapterPosition()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    check-cast p1, LIb1/b;

    .line 14
    .line 15
    iget-object p1, p1, LIb1/b;->c:Lorg/xbet/uikit/components/categorycardcollection/CategoryCardCollection;

    .line 16
    .line 17
    invoke-virtual {p0, v0, p1}, LUX0/k;->e(Ljava/lang/String;Landroidx/recyclerview/widget/RecyclerView;)V

    .line 18
    .line 19
    .line 20
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 21
    .line 22
    return-object p0
.end method
