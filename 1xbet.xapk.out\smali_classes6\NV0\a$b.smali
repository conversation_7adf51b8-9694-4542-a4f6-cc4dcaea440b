.class public final LNV0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LNV0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LNV0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LNV0/a$b$a;,
        LNV0/a$b$b;
    }
.end annotation


# instance fields
.field public final a:LNV0/a$b;

.field public b:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public c:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tiragecategoryresult/data/c;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tiragecategoryresult/data/TirageCategoryResultRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LOV0/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/TirageCategoryResultViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/TirageCategoryViewModel;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lak/a;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LNV0/a$b;->a:LNV0/a$b;

    .line 4
    invoke-virtual/range {p0 .. p11}, LNV0/a$b;->c(LQW0/c;Lak/a;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lak/a;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;LNV0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p11}, LNV0/a$b;-><init>(LQW0/c;Lak/a;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LNV0/a$b;->d(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryFragment;)Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryResultOldFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LNV0/a$b;->e(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryResultOldFragment;)Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryResultOldFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;Lak/a;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;)V
    .locals 0

    .line 1
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p5

    .line 5
    iput-object p5, p0, LNV0/a$b;->b:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p4

    .line 11
    iput-object p4, p0, LNV0/a$b;->c:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p4

    .line 17
    iput-object p4, p0, LNV0/a$b;->d:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p4, LNV0/a$b$a;

    .line 20
    .line 21
    invoke-direct {p4, p1}, LNV0/a$b$a;-><init>(LQW0/c;)V

    .line 22
    .line 23
    .line 24
    iput-object p4, p0, LNV0/a$b;->e:Ldagger/internal/h;

    .line 25
    .line 26
    new-instance p1, LNV0/a$b$b;

    .line 27
    .line 28
    invoke-direct {p1, p2}, LNV0/a$b$b;-><init>(Lak/a;)V

    .line 29
    .line 30
    .line 31
    iput-object p1, p0, LNV0/a$b;->f:Ldagger/internal/h;

    .line 32
    .line 33
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput-object p1, p0, LNV0/a$b;->g:Ldagger/internal/h;

    .line 38
    .line 39
    invoke-static {p1}, Lorg/xbet/toto_bet/tiragecategoryresult/data/d;->a(LBc/a;)Lorg/xbet/toto_bet/tiragecategoryresult/data/d;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iput-object p1, p0, LNV0/a$b;->h:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static {p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    iput-object p1, p0, LNV0/a$b;->i:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object p2, p0, LNV0/a$b;->e:Ldagger/internal/h;

    .line 52
    .line 53
    iget-object p4, p0, LNV0/a$b;->h:Ldagger/internal/h;

    .line 54
    .line 55
    invoke-static {p2, p4, p1}, Lorg/xbet/toto_bet/tiragecategoryresult/data/e;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/tiragecategoryresult/data/e;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iput-object p1, p0, LNV0/a$b;->j:Ldagger/internal/h;

    .line 60
    .line 61
    invoke-static {p1}, LOV0/b;->a(LBc/a;)LOV0/b;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iput-object p1, p0, LNV0/a$b;->k:Ldagger/internal/h;

    .line 66
    .line 67
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    iput-object p1, p0, LNV0/a$b;->l:Ldagger/internal/h;

    .line 72
    .line 73
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    iput-object p1, p0, LNV0/a$b;->m:Ldagger/internal/h;

    .line 78
    .line 79
    invoke-static {p3}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    iput-object p1, p0, LNV0/a$b;->n:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 86
    .line 87
    .line 88
    move-result-object p11

    .line 89
    iput-object p11, p0, LNV0/a$b;->o:Ldagger/internal/h;

    .line 90
    .line 91
    iget-object p2, p0, LNV0/a$b;->b:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object p3, p0, LNV0/a$b;->c:Ldagger/internal/h;

    .line 94
    .line 95
    iget-object p4, p0, LNV0/a$b;->d:Ldagger/internal/h;

    .line 96
    .line 97
    iget-object p5, p0, LNV0/a$b;->e:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object p6, p0, LNV0/a$b;->f:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object p7, p0, LNV0/a$b;->k:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object p8, p0, LNV0/a$b;->l:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object p9, p0, LNV0/a$b;->m:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object p10, p0, LNV0/a$b;->n:Ldagger/internal/h;

    .line 108
    .line 109
    invoke-static/range {p2 .. p11}, Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/e;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/e;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    iput-object p1, p0, LNV0/a$b;->p:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object p2, p0, LNV0/a$b;->b:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p3, p0, LNV0/a$b;->c:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object p4, p0, LNV0/a$b;->d:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object p5, p0, LNV0/a$b;->e:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p6, p0, LNV0/a$b;->f:Ldagger/internal/h;

    .line 124
    .line 125
    iget-object p7, p0, LNV0/a$b;->k:Ldagger/internal/h;

    .line 126
    .line 127
    iget-object p8, p0, LNV0/a$b;->l:Ldagger/internal/h;

    .line 128
    .line 129
    iget-object p9, p0, LNV0/a$b;->m:Ldagger/internal/h;

    .line 130
    .line 131
    iget-object p10, p0, LNV0/a$b;->n:Ldagger/internal/h;

    .line 132
    .line 133
    iget-object p11, p0, LNV0/a$b;->o:Ldagger/internal/h;

    .line 134
    .line 135
    invoke-static/range {p2 .. p11}, Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/l;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/l;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    iput-object p1, p0, LNV0/a$b;->q:Ldagger/internal/h;

    .line 140
    .line 141
    return-void
.end method

.method public final d(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryFragment;)Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNV0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/c;->a(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final e(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryResultOldFragment;)Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryResultOldFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LNV0/a$b;->g()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/f;->a(Lorg/xbet/toto_bet/tiragecategoryresult/presentation/fragment/TirageCategoryResultOldFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final f()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-static {v0}, Ldagger/internal/f;->b(I)Ldagger/internal/f;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    const-class v1, Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/TirageCategoryResultViewModel;

    .line 7
    .line 8
    iget-object v2, p0, LNV0/a$b;->p:Ldagger/internal/h;

    .line 9
    .line 10
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    const-class v1, Lorg/xbet/toto_bet/tiragecategoryresult/presentation/viewmodel/TirageCategoryViewModel;

    .line 15
    .line 16
    iget-object v2, p0, LNV0/a$b;->q:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-virtual {v0, v1, v2}, Ldagger/internal/f;->c(Ljava/lang/Object;Ljava/lang/Object;)Ldagger/internal/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Ldagger/internal/f;->a()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    return-object v0
.end method

.method public final g()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LNV0/a$b;->f()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
