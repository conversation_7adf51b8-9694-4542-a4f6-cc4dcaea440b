.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info_alt_design.TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1"
    f = "TournamentsFullInfoAltDesignSharedViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;Leu/i;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Lorg/xplatform/aggregator/impl/core/domain/usecases/d;Lorg/xbet/onexlocalization/f;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xbet/remoteconfig/domain/usecases/i;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;",
        "Lkb1/E;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkb1/F<",
        "+",
        "Lkb1/m;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;",
        "fullInfoSate",
        "Lkb1/E;",
        "error",
        "Lkb1/F;",
        "Lkb1/m;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;Lkb1/E;)Lkb1/F;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;

    check-cast p2, Lkb1/E;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;",
            "Lkb1/E;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkb1/F<",
            "Lkb1/m;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_9

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Lkb1/E;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentsGamesScreenState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 20
    .line 21
    sget-object v2, Lkb1/F$e;->a:Lkb1/F$e;

    .line 22
    .line 23
    instance-of v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$a;

    .line 24
    .line 25
    if-eqz v3, :cond_6

    .line 26
    .line 27
    :try_start_0
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 28
    .line 29
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$a;

    .line 30
    .line 31
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$a;->a()Li81/a;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-virtual {p1}, Li81/a;->m()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    new-instance v3, Ljava/util/ArrayList;

    .line 40
    .line 41
    const/16 v4, 0xa

    .line 42
    .line 43
    invoke-static {v2, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 44
    .line 45
    .line 46
    move-result v4

    .line 47
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 48
    .line 49
    .line 50
    invoke-interface {v2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 55
    .line 56
    .line 57
    move-result v4

    .line 58
    if-eqz v4, :cond_0

    .line 59
    .line 60
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v4

    .line 64
    check-cast v4, Lorg/xplatform/aggregator/api/model/Game;

    .line 65
    .line 66
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->M3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Ljava/util/Map;

    .line 67
    .line 68
    .line 69
    move-result-object v5

    .line 70
    invoke-virtual {v4}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 71
    .line 72
    .line 73
    move-result-wide v6

    .line 74
    invoke-static {v6, v7}, LHc/a;->f(J)Ljava/lang/Long;

    .line 75
    .line 76
    .line 77
    move-result-object v6

    .line 78
    invoke-interface {v5, v6, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    sget-object v4, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 82
    .line 83
    invoke-interface {v3, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 84
    .line 85
    .line 86
    goto :goto_0

    .line 87
    :catchall_0
    move-exception p1

    .line 88
    goto :goto_2

    .line 89
    :cond_0
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LHX0/e;

    .line 90
    .line 91
    .line 92
    move-result-object v2

    .line 93
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->e4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Z

    .line 94
    .line 95
    .line 96
    move-result v3

    .line 97
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->U3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lek0/o;

    .line 98
    .line 99
    .line 100
    move-result-object v4

    .line 101
    invoke-virtual {v4}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 102
    .line 103
    .line 104
    move-result-object v4

    .line 105
    invoke-static {p1, v2, v3, v4}, Ljb1/r;->h(Li81/a;LHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)Lkb1/m;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    invoke-virtual {p1}, Lkb1/m;->b()Ljava/util/List;

    .line 110
    .line 111
    .line 112
    move-result-object v2

    .line 113
    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    .line 114
    .line 115
    .line 116
    move-result v2

    .line 117
    instance-of v3, v0, Lkb1/E$c;

    .line 118
    .line 119
    if-eqz v3, :cond_2

    .line 120
    .line 121
    if-eqz v2, :cond_1

    .line 122
    .line 123
    new-instance p1, Lkb1/F$c;

    .line 124
    .line 125
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-direct {p1, v0}, Lkb1/F$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 130
    .line 131
    .line 132
    goto :goto_1

    .line 133
    :cond_1
    new-instance v0, Lkb1/F$d;

    .line 134
    .line 135
    invoke-direct {v0, p1}, Lkb1/F$d;-><init>(Ljava/lang/Object;)V

    .line 136
    .line 137
    .line 138
    move-object p1, v0

    .line 139
    goto :goto_1

    .line 140
    :cond_2
    instance-of p1, v0, Lkb1/E$b;

    .line 141
    .line 142
    if-eqz p1, :cond_3

    .line 143
    .line 144
    new-instance p1, Lkb1/F$b;

    .line 145
    .line 146
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 147
    .line 148
    .line 149
    move-result-object v0

    .line 150
    invoke-direct {p1, v0}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 151
    .line 152
    .line 153
    goto :goto_1

    .line 154
    :cond_3
    instance-of p1, v0, Lkb1/E$a;

    .line 155
    .line 156
    if-eqz p1, :cond_4

    .line 157
    .line 158
    new-instance p1, Lkb1/F$a;

    .line 159
    .line 160
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    invoke-direct {p1, v0}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 165
    .line 166
    .line 167
    :goto_1
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 168
    .line 169
    .line 170
    move-result-object p1

    .line 171
    goto :goto_3

    .line 172
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 173
    .line 174
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 175
    .line 176
    .line 177
    throw p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 178
    :goto_2
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 179
    .line 180
    invoke-static {p1}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 181
    .line 182
    .line 183
    move-result-object p1

    .line 184
    invoke-static {p1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 185
    .line 186
    .line 187
    move-result-object p1

    .line 188
    :goto_3
    invoke-static {p1}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 189
    .line 190
    .line 191
    move-result-object v0

    .line 192
    if-nez v0, :cond_5

    .line 193
    .line 194
    goto :goto_4

    .line 195
    :cond_5
    invoke-static {v1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->i4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;)V

    .line 196
    .line 197
    .line 198
    new-instance p1, Lkb1/F$c;

    .line 199
    .line 200
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    invoke-direct {p1, v0}, Lkb1/F$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 205
    .line 206
    .line 207
    :goto_4
    check-cast p1, Lkb1/F;

    .line 208
    .line 209
    return-object p1

    .line 210
    :cond_6
    instance-of p1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$b;

    .line 211
    .line 212
    if-eqz p1, :cond_7

    .line 213
    .line 214
    instance-of v3, v0, Lkb1/E$a;

    .line 215
    .line 216
    if-eqz v3, :cond_7

    .line 217
    .line 218
    new-instance p1, Lkb1/F$a;

    .line 219
    .line 220
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 221
    .line 222
    .line 223
    move-result-object v0

    .line 224
    invoke-direct {p1, v0}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 225
    .line 226
    .line 227
    return-object p1

    .line 228
    :cond_7
    if-eqz p1, :cond_8

    .line 229
    .line 230
    instance-of p1, v0, Lkb1/E$b;

    .line 231
    .line 232
    if-eqz p1, :cond_8

    .line 233
    .line 234
    new-instance p1, Lkb1/F$b;

    .line 235
    .line 236
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 237
    .line 238
    .line 239
    move-result-object v0

    .line 240
    invoke-direct {p1, v0}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 241
    .line 242
    .line 243
    return-object p1

    .line 244
    :cond_8
    return-object v2

    .line 245
    :cond_9
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 246
    .line 247
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 248
    .line 249
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 250
    .line 251
    .line 252
    throw p1
.end method
