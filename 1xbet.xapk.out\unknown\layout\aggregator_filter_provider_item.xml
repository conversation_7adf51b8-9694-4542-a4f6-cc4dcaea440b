<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clImageContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/space_8"
    android:layout_marginBottom="@dimen/space_8"
    android:background="@drawable/rounded_background_16_stroke"
    android:backgroundTint="?attr/uikitPrimary">

    <ImageView
        android:id="@+id/ivDelete"
        android:layout_width="@dimen/size_34"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/space_8"
        android:scaleType="centerInside"
        android:src="@drawable/ic_glyph_clear_ios"
        android:importantForAccessibility="no"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/image"
        app:tint="?attr/uikitPrimary" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="?attr/contentBackground"
        android:scaleType="fitXY"
        android:importantForAccessibility="no"
        app:layout_constraintDimensionRatio="70:45"
        app:layout_constraintEnd_toStartOf="@id/ivDelete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/background_gradient" />

</androidx.constraintlayout.widget.ConstraintLayout>
