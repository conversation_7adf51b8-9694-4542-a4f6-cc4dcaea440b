.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/fragment/app/J;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/c;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/c;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->z2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method
