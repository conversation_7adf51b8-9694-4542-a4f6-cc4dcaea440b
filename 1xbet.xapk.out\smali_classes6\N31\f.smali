.class public final synthetic LN31/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN31/f;->a:Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LN31/f;->a:Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;

    invoke-static {v0}, Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;->a(Lorg/xbet/uikit_sport/sport_collection/SportsCollectionMain;)LP31/a;

    move-result-object v0

    return-object v0
.end method
