.class public final synthetic Lorg/xbet/ui_common/dialogs/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/widget/CalendarView$OnDateChangeListener;


# instance fields
.field public final synthetic a:Ljava/util/Calendar;

.field public final synthetic b:Lorg/xbet/ui_common/dialogs/PeriodDatePicker;


# direct methods
.method public synthetic constructor <init>(Ljava/util/Calendar;Lorg/xbet/ui_common/dialogs/PeriodDatePicker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/ui_common/dialogs/e;->a:Ljava/util/Calendar;

    iput-object p2, p0, Lorg/xbet/ui_common/dialogs/e;->b:Lorg/xbet/ui_common/dialogs/PeriodDatePicker;

    return-void
.end method


# virtual methods
.method public final onSelectedDayChange(Landroid/widget/CalendarView;III)V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/dialogs/e;->a:Ljava/util/Calendar;

    iget-object v1, p0, Lorg/xbet/ui_common/dialogs/e;->b:Lorg/xbet/ui_common/dialogs/PeriodDatePicker;

    move-object v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    invoke-static/range {v0 .. v5}, Lorg/xbet/ui_common/dialogs/PeriodDatePicker;->d3(Ljava/util/Calendar;Lorg/xbet/ui_common/dialogs/PeriodDatePicker;Landroid/widget/CalendarView;III)V

    return-void
.end method
