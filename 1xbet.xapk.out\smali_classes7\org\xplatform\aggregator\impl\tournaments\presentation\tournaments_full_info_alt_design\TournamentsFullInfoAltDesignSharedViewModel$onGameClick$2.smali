.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info_alt_design.TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2"
    f = "TournamentsFullInfoAltDesignSharedViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->M4(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field final synthetic $screenName:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Lorg/xplatform/aggregator/api/model/Game;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$screenName:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->i4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$screenName:Ljava/lang/String;

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Lorg/xplatform/aggregator/api/model/Game;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->G3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lkotlinx/coroutines/flow/V;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    check-cast p1, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 22
    .line 23
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2$a;->a:[I

    .line 24
    .line 25
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    aget p1, v0, p1

    .line 30
    .line 31
    const/4 v0, 0x1

    .line 32
    if-eq p1, v0, :cond_1

    .line 33
    .line 34
    const/4 v0, 0x2

    .line 35
    if-eq p1, v0, :cond_0

    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 39
    .line 40
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->R3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/analytics/domain/scope/g0;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 47
    .line 48
    .line 49
    move-result-wide v0

    .line 50
    invoke-virtual {p1, v0, v1}, Lorg/xbet/analytics/domain/scope/g0;->Q(J)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 54
    .line 55
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LnR/a;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$screenName:Ljava/lang/String;

    .line 60
    .line 61
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 62
    .line 63
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 64
    .line 65
    .line 66
    move-result-wide v1

    .line 67
    long-to-int v2, v1

    .line 68
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->AGGREGATOR_TOURNAMENT_GAMES:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 69
    .line 70
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v1

    .line 74
    invoke-interface {p1, v0, v2, v1}, LnR/a;->h(Ljava/lang/String;ILjava/lang/String;)V

    .line 75
    .line 76
    .line 77
    goto :goto_0

    .line 78
    :cond_1
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 79
    .line 80
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->R3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/analytics/domain/scope/g0;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 85
    .line 86
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 87
    .line 88
    .line 89
    move-result-wide v0

    .line 90
    invoke-virtual {p1, v0, v1}, Lorg/xbet/analytics/domain/scope/g0;->W(J)V

    .line 91
    .line 92
    .line 93
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 94
    .line 95
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LnR/a;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$screenName:Ljava/lang/String;

    .line 100
    .line 101
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 102
    .line 103
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 104
    .line 105
    .line 106
    move-result-wide v1

    .line 107
    long-to-int v2, v1

    .line 108
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->AGGREGATOR_TOURNAMENT_PAGE:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 109
    .line 110
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    invoke-interface {p1, v0, v2, v1}, LnR/a;->h(Ljava/lang/String;ILjava/lang/String;)V

    .line 115
    .line 116
    .line 117
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 118
    .line 119
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->S3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 120
    .line 121
    .line 122
    move-result-object p1

    .line 123
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 124
    .line 125
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$onGameClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 126
    .line 127
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/q;

    .line 128
    .line 129
    invoke-direct {v2, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/q;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)V

    .line 130
    .line 131
    .line 132
    const/4 v1, 0x0

    .line 133
    invoke-virtual {p1, v0, v1, v2}, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlin/jvm/functions/Function1;)V

    .line 134
    .line 135
    .line 136
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 137
    .line 138
    return-object p1

    .line 139
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 140
    .line 141
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 142
    .line 143
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 144
    .line 145
    .line 146
    throw p1
.end method
