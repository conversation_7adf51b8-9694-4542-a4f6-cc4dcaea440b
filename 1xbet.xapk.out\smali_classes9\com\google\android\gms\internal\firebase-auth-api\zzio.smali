.class public final synthetic Lcom/google/android/gms/internal/firebase-auth-api/zzio;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/internal/firebase-auth-api/zznn;


# static fields
.field public static final synthetic zza:Lcom/google/android/gms/internal/firebase-auth-api/zzio;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/firebase-auth-api/zzio;

    invoke-direct {v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzio;-><init>()V

    sput-object v0, Lcom/google/android/gms/internal/firebase-auth-api/zzio;->zza:Lcom/google/android/gms/internal/firebase-auth-api/zzio;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON><PERSON><PERSON>/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final zza(Lcom/google/android/gms/internal/firebase-auth-api/zzci;Ljava/lang/Integer;)Lcom/google/android/gms/internal/firebase-auth-api/zzbu;
    .locals 0

    check-cast p1, Lcom/google/android/gms/internal/firebase-auth-api/zziq;

    const/4 p2, 0x0

    invoke-static {p1, p2}, Lcom/google/android/gms/internal/firebase-auth-api/zzin;->zza(Lcom/google/android/gms/internal/firebase-auth-api/zziq;Ljava/lang/Integer;)Lcom/google/android/gms/internal/firebase-auth-api/zzij;

    move-result-object p1

    return-object p1
.end method
