.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizeItemAltDesignFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizeItemAltDesignFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizeItemAltDesignFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizeItemAltDesignFragment;

    check-cast p1, Lkb1/x$b;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizeItemAltDesignFragment;->z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizeItemAltDesignFragment;Lkb1/x$b;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
