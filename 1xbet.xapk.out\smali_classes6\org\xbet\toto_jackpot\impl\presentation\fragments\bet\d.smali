.class public final Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0018\u00002\u00020\u0001:\u0001&B\u001b\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0013\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0013\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\r0\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0013\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u00100\u000c\u00a2\u0006\u0004\u0008\u0011\u0010\u000fJ\r\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\r\u0010\u0015\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J\r\u0010\u0016\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0016\u0010\u0014J\r\u0010\u0017\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0017\u0010\u0014R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001bR\u001a\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\t0\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR\u001a\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\r0 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010\"R\u001a\u0010%\u001a\u0008\u0012\u0004\u0012\u00020\u00100 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010\"\u00a8\u0006\'"
    }
    d2 = {
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "LwW0/k;",
        "getVariantsAmountUseCase",
        "LwX0/c;",
        "router",
        "<init>",
        "(LwW0/k;LwX0/c;)V",
        "Lkotlinx/coroutines/flow/Z;",
        "",
        "r3",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a;",
        "q3",
        "()Lkotlinx/coroutines/flow/f0;",
        "",
        "s3",
        "",
        "v3",
        "()V",
        "p3",
        "t3",
        "u3",
        "v1",
        "LwW0/k;",
        "x1",
        "LwX0/c;",
        "Lkotlinx/coroutines/flow/U;",
        "y1",
        "Lkotlinx/coroutines/flow/U;",
        "dialogState",
        "Lkotlinx/coroutines/flow/V;",
        "F1",
        "Lkotlinx/coroutines/flow/V;",
        "betTypeState",
        "H1",
        "titleState",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:LwW0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lkotlinx/coroutines/flow/U;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/U<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LwW0/k;LwX0/c;)V
    .locals 0
    .param p1    # LwW0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->v1:LwW0/k;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->x1:LwX0/c;

    .line 7
    .line 8
    invoke-static {}, Lorg/xbet/ui_common/utils/flows/c;->a()Lkotlinx/coroutines/flow/U;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->y1:Lkotlinx/coroutines/flow/U;

    .line 13
    .line 14
    sget-object p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a$b;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a$b;

    .line 15
    .line 16
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->F1:Lkotlinx/coroutines/flow/V;

    .line 21
    .line 22
    const-string p1, ""

    .line 23
    .line 24
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->H1:Lkotlinx/coroutines/flow/V;

    .line 29
    .line 30
    return-void
.end method


# virtual methods
.method public final p3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->F1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a;

    .line 9
    .line 10
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a$b;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a$b;

    .line 11
    .line 12
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    return-void
.end method

.method public final q3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->F1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final r3()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->y1:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->d(Lkotlinx/coroutines/flow/U;)Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final s3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->H1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->e(Lkotlinx/coroutines/flow/V;)Lkotlinx/coroutines/flow/f0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final t3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->y1:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    sget-object v1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final u3()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->y1:Lkotlinx/coroutines/flow/U;

    .line 2
    .line 3
    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 4
    .line 5
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final v3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->F1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a;

    .line 9
    .line 10
    sget-object v2, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a$a;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d$a$a;

    .line 11
    .line 12
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    if-eqz v1, :cond_0

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->H1:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/d;->v1:LwW0/k;

    .line 21
    .line 22
    invoke-virtual {v1}, LwW0/k;->a()J

    .line 23
    .line 24
    .line 25
    move-result-wide v1

    .line 26
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-interface {v0, v1}, Lkotlinx/coroutines/flow/U;->d(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    return-void
.end method
