.class final Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.data.repository.TotoBetMakeBetRepositoryImpl$makeBet$2"
    f = "TotoBetMakeBetRepositoryImpl.kt"
    l = {
        0x1f
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;->b(Ljava/lang/String;DJLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/String;",
        "Lkotlin/coroutines/e<",
        "-",
        "LUU0/a;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "",
        "token",
        "LUU0/a;",
        "<anonymous>",
        "(Ljava/lang/String;)LUU0/a;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $lastBalanceId:J

.field final synthetic $promo:Ljava/lang/String;

.field final synthetic $sum:D

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;JDLjava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;",
            "JD",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;

    iput-wide p2, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$lastBalanceId:J

    iput-wide p4, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$sum:D

    iput-object p6, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$promo:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;

    iget-wide v2, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$lastBalanceId:J

    iget-wide v4, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$sum:D

    iget-object v6, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$promo:Ljava/lang/String;

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;JDLjava/lang/String;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/String;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "LUU0/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto/16 :goto_1

    .line 16
    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->L$0:Ljava/lang/Object;

    .line 29
    .line 30
    check-cast p1, Ljava/lang/String;

    .line 31
    .line 32
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;

    .line 33
    .line 34
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;->i(Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;)LPU0/b;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    iget-wide v3, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$lastBalanceId:J

    .line 39
    .line 40
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 41
    .line 42
    .line 43
    move-result-object v6

    .line 44
    iget-object v3, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;

    .line 45
    .line 46
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;->h(Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;)LVV0/b;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v3}, LVV0/b;->d()LZV0/g;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    invoke-virtual {v3}, LZV0/g;->c()I

    .line 55
    .line 56
    .line 57
    move-result v3

    .line 58
    invoke-static {v3}, LHc/a;->e(I)Ljava/lang/Integer;

    .line 59
    .line 60
    .line 61
    move-result-object v7

    .line 62
    iget-object v3, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;

    .line 63
    .line 64
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;->h(Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;)LVV0/b;

    .line 65
    .line 66
    .line 67
    move-result-object v3

    .line 68
    invoke-virtual {v3}, LVV0/b;->e()J

    .line 69
    .line 70
    .line 71
    move-result-wide v3

    .line 72
    invoke-static {v3, v4}, LHc/a;->f(J)Ljava/lang/Long;

    .line 73
    .line 74
    .line 75
    move-result-object v8

    .line 76
    iget-object v3, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;

    .line 77
    .line 78
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;->c(Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl;)LVV0/a;

    .line 79
    .line 80
    .line 81
    move-result-object v3

    .line 82
    invoke-virtual {v3}, LVV0/a;->c()Ljava/util/Map;

    .line 83
    .line 84
    .line 85
    move-result-object v3

    .line 86
    new-instance v9, Ljava/util/ArrayList;

    .line 87
    .line 88
    invoke-interface {v3}, Ljava/util/Map;->size()I

    .line 89
    .line 90
    .line 91
    move-result v4

    .line 92
    invoke-direct {v9, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 93
    .line 94
    .line 95
    invoke-interface {v3}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 96
    .line 97
    .line 98
    move-result-object v3

    .line 99
    invoke-interface {v3}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 100
    .line 101
    .line 102
    move-result-object v3

    .line 103
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 104
    .line 105
    .line 106
    move-result v4

    .line 107
    if-eqz v4, :cond_2

    .line 108
    .line 109
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 110
    .line 111
    .line 112
    move-result-object v4

    .line 113
    check-cast v4, Ljava/util/Map$Entry;

    .line 114
    .line 115
    invoke-static {v4}, LWV0/b;->a(Ljava/util/Map$Entry;)LRU0/a;

    .line 116
    .line 117
    .line 118
    move-result-object v4

    .line 119
    invoke-interface {v9, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 120
    .line 121
    .line 122
    goto :goto_0

    .line 123
    :cond_2
    iget-wide v3, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$sum:D

    .line 124
    .line 125
    invoke-static {v3, v4}, LHc/a;->c(D)Ljava/lang/Double;

    .line 126
    .line 127
    .line 128
    move-result-object v10

    .line 129
    iget-object v11, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->$promo:Ljava/lang/String;

    .line 130
    .line 131
    new-instance v5, LRU0/b;

    .line 132
    .line 133
    invoke-direct/range {v5 .. v11}, LRU0/b;-><init>(Ljava/lang/Long;Ljava/lang/Integer;Ljava/lang/Long;Ljava/util/List;Ljava/lang/Double;Ljava/lang/String;)V

    .line 134
    .line 135
    .line 136
    iput v2, p0, Lorg/xbet/toto_bet/makebet/data/repository/TotoBetMakeBetRepositoryImpl$makeBet$2;->label:I

    .line 137
    .line 138
    invoke-virtual {v1, p1, v5, p0}, LPU0/b;->e(Ljava/lang/String;LRU0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 139
    .line 140
    .line 141
    move-result-object p1

    .line 142
    if-ne p1, v0, :cond_3

    .line 143
    .line 144
    return-object v0

    .line 145
    :cond_3
    :goto_1
    check-cast p1, Le8/b;

    .line 146
    .line 147
    invoke-virtual {p1}, Le8/b;->a()Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    check-cast p1, LSU0/a;

    .line 152
    .line 153
    invoke-static {p1}, LQU0/a;->a(LSU0/a;)LUU0/a;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    return-object p1
.end method
