.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;,
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;,
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\r\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 H2\u00020\u00012\u00020\u0002:\u0002/2B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0019\u0010\u000e\u001a\u0004\u0018\u00010\r2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0017\u0010\u0012\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0015J\u0017\u0010\u0017\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0017\u0010\u0013J\u0017\u0010\u0017\u001a\u00020\u00112\u0008\u0010\u0018\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\u0008\u0017\u0010\u0019J!\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u001b\u001a\u00020\u001a2\n\u0008\u0002\u0010\u001c\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\u0008\u0017\u0010\u001dJ\u0017\u0010\u001e\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001e\u0010\u0013J\u0017\u0010\u001e\u001a\u00020\u00112\u0008\u0010\u0018\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\u0008\u001e\u0010\u0019J!\u0010\u001e\u001a\u00020\u00112\u0006\u0010\u001b\u001a\u00020\u001a2\n\u0008\u0002\u0010\u001c\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0004\u0008\u001e\u0010\u001dJ\u0017\u0010\u001f\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001f\u0010\u0013J\u0017\u0010\u001f\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0004\u0008\u001f\u0010\u0015J\u0017\u0010 \u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\u0004\u0008 \u0010\u0013J\u0017\u0010 \u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0004\u0008 \u0010\u0015J\u001b\u0010$\u001a\u00020\u00112\u000c\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\"0!\u00a2\u0006\u0004\u0008$\u0010%J\u0017\u0010&\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\u0004\u0008&\u0010\u0013J\u0017\u0010&\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\u0004\u0008&\u0010\u0015J!\u0010)\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u00072\u0008\u0008\u0002\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008)\u0010*J!\u0010)\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u00142\u0008\u0008\u0002\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008)\u0010+J\u0015\u0010)\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u000b\u00a2\u0006\u0004\u0008)\u0010,J!\u0010-\u001a\u00020\u00112\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u00072\u0008\u0008\u0002\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008-\u0010*J!\u0010-\u001a\u00020\u00112\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u00142\u0008\u0008\u0002\u0010(\u001a\u00020\'\u00a2\u0006\u0004\u0008-\u0010+J\u0015\u0010-\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u000b\u00a2\u0006\u0004\u0008-\u0010,R\u0014\u00101\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u001d\u00106\u001a\u0004\u0018\u00010\r8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105R\u001d\u00109\u001a\u0004\u0018\u00010\r8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00087\u00103\u001a\u0004\u00088\u00105R\u001d\u0010<\u001a\u0004\u0018\u00010\r8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008:\u00103\u001a\u0004\u0008;\u00105R\u001d\u0010?\u001a\u0004\u0018\u00010\r8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008=\u00103\u001a\u0004\u0008>\u00105R\u001d\u0010B\u001a\u0004\u0018\u00010\r8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008@\u00103\u001a\u0004\u0008A\u00105R\u001a\u0010G\u001a\u0008\u0012\u0004\u0012\u00020D0C8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008E\u0010F\u00a8\u0006I"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;",
        "fightCellDrawable",
        "Landroid/graphics/drawable/Drawable;",
        "C",
        "(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;",
        "text",
        "",
        "setInfoText",
        "(I)V",
        "",
        "(Ljava/lang/CharSequence;)V",
        "resId",
        "setTopLogo",
        "drawable",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "url",
        "placeholder",
        "(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V",
        "setBotLogo",
        "setTopTeamName",
        "setBotTeamName",
        "",
        "LI11/b;",
        "results",
        "setResults",
        "(Ljava/util/List;)V",
        "setResultGameTitle",
        "Lorg/xbet/uikit/core/eventcard/ScoreState;",
        "scoreState",
        "setTopResultGameScore",
        "(ILorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V",
        "(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)V",
        "setBotResultGameScore",
        "LC31/x;",
        "a",
        "LC31/x;",
        "binding",
        "b",
        "Lkotlin/j;",
        "getBDrawable",
        "()Landroid/graphics/drawable/Drawable;",
        "bDrawable",
        "c",
        "getFDrawable",
        "fDrawable",
        "d",
        "getFsDrawable",
        "fsDrawable",
        "e",
        "getKoDrawable",
        "koDrawable",
        "f",
        "getRDrawable",
        "rDrawable",
        "Lkotlin/sequences/Sequence;",
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;",
        "getFightResultViews",
        "()Lkotlin/sequences/Sequence;",
        "fightResultViews",
        "g",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final h:I


# instance fields
.field public final a:LC31/x;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->h:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p2

    invoke-static {p2, p0}, LC31/x;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/x;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 7
    new-instance p2, Lorg/xbet/uikit_sport/eventcard/middle/c;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_sport/eventcard/middle/c;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->b:Lkotlin/j;

    .line 8
    new-instance p2, Lorg/xbet/uikit_sport/eventcard/middle/d;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_sport/eventcard/middle/d;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->c:Lkotlin/j;

    .line 9
    new-instance p2, Lorg/xbet/uikit_sport/eventcard/middle/e;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_sport/eventcard/middle/e;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->d:Lkotlin/j;

    .line 10
    new-instance p2, Lorg/xbet/uikit_sport/eventcard/middle/f;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_sport/eventcard/middle/f;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->e:Lkotlin/j;

    .line 11
    new-instance p2, Lorg/xbet/uikit_sport/eventcard/middle/g;

    invoke-direct {p2, p1}, Lorg/xbet/uikit_sport/eventcard/middle/g;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->f:Lkotlin/j;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleFightingStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final A(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    sget v1, LlZ0/h;->ic_glyph_fighting_f:I

    .line 4
    .line 5
    invoke-virtual {v0, p0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->a(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static final B(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    sget v1, LlZ0/h;->ic_glyph_fighting_fs:I

    .line 4
    .line 5
    invoke-virtual {v0, p0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->a(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static final D(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    sget v1, LlZ0/h;->ic_glyph_fighting_ko:I

    .line 4
    .line 5
    invoke-virtual {v0, p0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->a(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method public static final E(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    sget v1, LlZ0/h;->ic_glyph_fighting_r:I

    .line 4
    .line 5
    invoke-virtual {v0, p0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->a(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method

.method private final getBDrawable()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getFDrawable()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getFightResultViews()Lkotlin/sequences/Sequence;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/sequences/Sequence<",
            "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 4
    .line 5
    iget-object v2, v1, LC31/x;->l:Landroid/widget/TextView;

    .line 6
    .line 7
    iget-object v3, v1, LC31/x;->s:Landroid/widget/TextView;

    .line 8
    .line 9
    iget-object v1, v1, LC31/x;->d:Landroid/widget/TextView;

    .line 10
    .line 11
    invoke-direct {v0, v2, v3, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;-><init>(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 12
    .line 13
    .line 14
    new-instance v1, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 15
    .line 16
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 17
    .line 18
    iget-object v3, v2, LC31/x;->p:Landroid/widget/TextView;

    .line 19
    .line 20
    iget-object v4, v2, LC31/x;->w:Landroid/widget/TextView;

    .line 21
    .line 22
    iget-object v2, v2, LC31/x;->h:Landroid/widget/TextView;

    .line 23
    .line 24
    invoke-direct {v1, v3, v4, v2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;-><init>(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 25
    .line 26
    .line 27
    new-instance v2, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 28
    .line 29
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 30
    .line 31
    iget-object v4, v3, LC31/x;->q:Landroid/widget/TextView;

    .line 32
    .line 33
    iget-object v5, v3, LC31/x;->y:Landroid/widget/TextView;

    .line 34
    .line 35
    iget-object v3, v3, LC31/x;->j:Landroid/widget/TextView;

    .line 36
    .line 37
    invoke-direct {v2, v4, v5, v3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;-><init>(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 38
    .line 39
    .line 40
    new-instance v3, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 41
    .line 42
    iget-object v4, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 43
    .line 44
    iget-object v5, v4, LC31/x;->m:Landroid/widget/TextView;

    .line 45
    .line 46
    iget-object v6, v4, LC31/x;->t:Landroid/widget/TextView;

    .line 47
    .line 48
    iget-object v4, v4, LC31/x;->e:Landroid/widget/TextView;

    .line 49
    .line 50
    invoke-direct {v3, v5, v6, v4}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;-><init>(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 51
    .line 52
    .line 53
    new-instance v4, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 54
    .line 55
    iget-object v5, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 56
    .line 57
    iget-object v6, v5, LC31/x;->k:Landroid/widget/TextView;

    .line 58
    .line 59
    iget-object v7, v5, LC31/x;->r:Landroid/widget/TextView;

    .line 60
    .line 61
    iget-object v5, v5, LC31/x;->c:Landroid/widget/TextView;

    .line 62
    .line 63
    invoke-direct {v4, v6, v7, v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;-><init>(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 64
    .line 65
    .line 66
    const/4 v5, 0x5

    .line 67
    new-array v5, v5, [Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 68
    .line 69
    const/4 v6, 0x0

    .line 70
    aput-object v0, v5, v6

    .line 71
    .line 72
    const/4 v0, 0x1

    .line 73
    aput-object v1, v5, v0

    .line 74
    .line 75
    const/4 v0, 0x2

    .line 76
    aput-object v2, v5, v0

    .line 77
    .line 78
    const/4 v0, 0x3

    .line 79
    aput-object v3, v5, v0

    .line 80
    .line 81
    const/4 v0, 0x4

    .line 82
    aput-object v4, v5, v0

    .line 83
    .line 84
    invoke-static {v5}, Lkotlin/sequences/s;->v([Ljava/lang/Object;)Lkotlin/sequences/Sequence;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    return-object v0
.end method

.method private final getFsDrawable()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getKoDrawable()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->e:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getRDrawable()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->f:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic s(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->D(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic setBotLogo$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;Ljava/lang/String;Landroid/graphics/drawable/Drawable;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 6
    .line 7
    iget-object p2, p2, LC31/x;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 8
    .line 9
    invoke-virtual {p2}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->getPlaceholder()Landroid/graphics/drawable/Drawable;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setBotLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public static synthetic setBotResultGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setBotResultGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setBotResultGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setBotResultGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopLogo$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;Ljava/lang/String;Landroid/graphics/drawable/Drawable;ILjava/lang/Object;)V
    .locals 0

    .line 1
    and-int/lit8 p3, p3, 0x2

    .line 2
    .line 3
    if-eqz p3, :cond_0

    .line 4
    .line 5
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    .line 6
    .line 7
    iget-object p2, p2, LC31/x;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 8
    .line 9
    invoke-virtual {p2}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->getPlaceholder()Landroid/graphics/drawable/Drawable;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setTopLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public static synthetic setTopResultGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;ILorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 1
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setTopResultGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic setTopResultGameScore$default(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 2
    sget-object p2, Lorg/xbet/uikit/core/eventcard/ScoreState;->ACTIVE_DEFAULT:Lorg/xbet/uikit/core/eventcard/ScoreState;

    :cond_0
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setTopResultGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public static synthetic t(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->B(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->E(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic v(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->z(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic w(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->A(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic x()Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic y(Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->C(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final z(Landroid/content/Context;)Landroid/graphics/drawable/Drawable;
    .locals 2

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    sget v1, LlZ0/h;->ic_glyph_fighting_b:I

    .line 4
    .line 5
    invoke-virtual {v0, p0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->a(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    return-object p0
.end method


# virtual methods
.method public final C(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$c;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_4

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_3

    .line 14
    .line 15
    const/4 v0, 0x3

    .line 16
    if-eq p1, v0, :cond_2

    .line 17
    .line 18
    const/4 v0, 0x4

    .line 19
    if-eq p1, v0, :cond_1

    .line 20
    .line 21
    const/4 v0, 0x5

    .line 22
    if-ne p1, v0, :cond_0

    .line 23
    .line 24
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->getRDrawable()Landroid/graphics/drawable/Drawable;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    return-object p1

    .line 29
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 30
    .line 31
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->getKoDrawable()Landroid/graphics/drawable/Drawable;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    return-object p1

    .line 40
    :cond_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->getFsDrawable()Landroid/graphics/drawable/Drawable;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    return-object p1

    .line 45
    :cond_3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->getFDrawable()Landroid/graphics/drawable/Drawable;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    return-object p1

    .line 50
    :cond_4
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->getBDrawable()Landroid/graphics/drawable/Drawable;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    return-object p1
.end method

.method public final setBotLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setBotLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v1, v0, LC31/x;->f:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setBotResultGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setBotResultGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotResultGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 2
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v1, v1, LC31/x;->g:Landroid/widget/TextView;

    invoke-virtual {v0, v1, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->c(Landroid/widget/TextView;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setBotResultGameScore(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)V
    .locals 2
    .param p1    # Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v1, v1, LC31/x;->g:Landroid/widget/TextView;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->C(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->b(Landroid/widget/TextView;Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setBotTeamName(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setBotTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setBotTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->i:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->i:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setInfoText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoText(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->n:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->n:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResultGameTitle(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setResultGameTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResultGameTitle(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->o:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->o:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setResults(Ljava/util/List;)V
    .locals 7
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LI11/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->getFightResultViews()Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const/4 v1, 0x0

    .line 10
    const/4 v2, 0x0

    .line 11
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    if-eqz v3, :cond_3

    .line 16
    .line 17
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    add-int/lit8 v4, v2, 0x1

    .line 22
    .line 23
    if-gez v2, :cond_0

    .line 24
    .line 25
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 26
    .line 27
    .line 28
    :cond_0
    check-cast v3, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    .line 29
    .line 30
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 31
    .line 32
    .line 33
    move-result v5

    .line 34
    if-ge v2, v5, :cond_1

    .line 35
    .line 36
    invoke-interface {p1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 37
    .line 38
    .line 39
    move-result-object v5

    .line 40
    check-cast v5, LI11/b;

    .line 41
    .line 42
    new-instance v6, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$setResults$1$1;

    .line 43
    .line 44
    invoke-direct {v6, p0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$setResults$1$1;-><init>(Ljava/lang/Object;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {v3, v5, v6}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b(LI11/b;Lkotlin/jvm/functions/Function1;)V

    .line 48
    .line 49
    .line 50
    :cond_1
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 51
    .line 52
    .line 53
    move-result v5

    .line 54
    if-ge v2, v5, :cond_2

    .line 55
    .line 56
    const/4 v2, 0x1

    .line 57
    goto :goto_1

    .line 58
    :cond_2
    const/4 v2, 0x0

    .line 59
    :goto_1
    invoke-virtual {v3, v2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->d(Z)V

    .line 60
    .line 61
    .line 62
    move v2, v4

    .line 63
    goto :goto_0

    .line 64
    :cond_3
    return-void
.end method

.method public final setTopLogo(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setTopLogo(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopLogo(Landroid/graphics/drawable/Drawable;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/4 v1, 0x0

    if-nez p1, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    const/16 v1, 0x8

    .line 3
    :cond_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopLogo(Ljava/lang/String;Landroid/graphics/drawable/Drawable;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v1, v0, LC31/x;->u:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    const/16 v6, 0xc

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v2, p1

    move-object v3, p2

    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    return-void
.end method

.method public final setTopResultGameScore(ILorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setTopResultGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopResultGameScore(Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V
    .locals 2
    .param p2    # Lorg/xbet/uikit/core/eventcard/ScoreState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v1, v1, LC31/x;->v:Landroid/widget/TextView;

    invoke-virtual {v0, v1, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->c(Landroid/widget/TextView;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    return-void
.end method

.method public final setTopResultGameScore(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)V
    .locals 2
    .param p1    # Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    sget-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->g:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v1, v1, LC31/x;->v:Landroid/widget/TextView;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->C(Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;)Landroid/graphics/drawable/Drawable;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->b(Landroid/widget/TextView;Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setTopTeamName(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->setTopTeamName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTopTeamName(Ljava/lang/CharSequence;)V
    .locals 3

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->x:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 3
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->a:LC31/x;

    iget-object v0, v0, LC31/x;->x:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
