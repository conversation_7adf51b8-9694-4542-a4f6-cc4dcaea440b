.class public final Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
.super Landroid/widget/LinearLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$a;,
        Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00ac\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0013\n\u0002\u0018\u0002\n\u0002\u0008\u0010\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 \u0083\u00012\u00020\u0001:\u0001VB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0013\u0010\u000e\u001a\u00020\n*\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0013\u0010\u0010\u001a\u00020\n*\u00020\rH\u0003\u00a2\u0006\u0004\u0008\u0010\u0010\u000fJ\u0013\u0010\u0011\u001a\u00020\n*\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u000fJ\u0013\u0010\u0012\u001a\u00020\n*\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u000fJ\u0013\u0010\u0013\u001a\u00020\n*\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u000fJ\u0013\u0010\u0014\u001a\u00020\n*\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u000fJ\u0017\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0015\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001e\u001a\u00020\n2\u0008\u0010\u001d\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0017\u0010!\u001a\u00020\n2\u0008\u0008\u0001\u0010 \u001a\u00020\u0006\u00a2\u0006\u0004\u0008!\u0010\"J\u0017\u0010!\u001a\u00020\n2\u0008\u0010$\u001a\u0004\u0018\u00010#\u00a2\u0006\u0004\u0008!\u0010%J\u0017\u0010(\u001a\u00020\n2\u0008\u0010\'\u001a\u0004\u0018\u00010&\u00a2\u0006\u0004\u0008(\u0010)J\u0017\u0010,\u001a\u00020\n2\u0008\u0010+\u001a\u0004\u0018\u00010*\u00a2\u0006\u0004\u0008,\u0010-J\u0017\u0010.\u001a\u00020\n2\u0008\u0010+\u001a\u0004\u0018\u00010*\u00a2\u0006\u0004\u0008.\u0010-J\u0017\u00100\u001a\u00020\n2\u0008\u0010+\u001a\u0004\u0018\u00010/\u00a2\u0006\u0004\u00080\u00101J\u0017\u00102\u001a\u00020\n2\u0008\u0010+\u001a\u0004\u0018\u00010*\u00a2\u0006\u0004\u00082\u0010-J\u0019\u00104\u001a\u00020\n2\u0008\u0010+\u001a\u0004\u0018\u000103H\u0007\u00a2\u0006\u0004\u00084\u00105J\u0015\u00108\u001a\u00020\n2\u0006\u00107\u001a\u000206\u00a2\u0006\u0004\u00088\u00109J\u0015\u0010;\u001a\u00020\n2\u0006\u0010:\u001a\u000206\u00a2\u0006\u0004\u0008;\u00109J\u0019\u0010=\u001a\u00020\n2\n\u0008\u0001\u0010<\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0004\u0008=\u0010\u001fJ\u001d\u0010@\u001a\u00020\n2\u0006\u0010>\u001a\u0002062\u0006\u0010?\u001a\u000206\u00a2\u0006\u0004\u0008@\u0010AJ\u0015\u0010@\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020B\u00a2\u0006\u0004\u0008@\u0010CJ\u0015\u0010E\u001a\u00020\n2\u0006\u0010D\u001a\u000206\u00a2\u0006\u0004\u0008E\u00109J\u0015\u0010G\u001a\u00020\n2\u0006\u0010F\u001a\u000206\u00a2\u0006\u0004\u0008G\u00109J\r\u0010I\u001a\u00020H\u00a2\u0006\u0004\u0008I\u0010JJ\r\u0010L\u001a\u00020K\u00a2\u0006\u0004\u0008L\u0010MJ\r\u0010O\u001a\u00020N\u00a2\u0006\u0004\u0008O\u0010PJ\r\u0010R\u001a\u00020Q\u00a2\u0006\u0004\u0008R\u0010SJ\u0015\u0010U\u001a\u00020\n2\u0006\u0010T\u001a\u000206\u00a2\u0006\u0004\u0008U\u00109R\u0014\u0010X\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010Z\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010WR\u0014\u0010\\\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008[\u0010WR\u0014\u0010^\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010WR\u0014\u0010`\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010WR\u001b\u0010d\u001a\u00020H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008a\u0010b\u001a\u0004\u0008c\u0010JR\u0016\u0010h\u001a\u00020e8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u0016\u0010k\u001a\u00020Q8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0016\u0010n\u001a\u00020K8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008l\u0010mR\u0016\u0010q\u001a\u00020N8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008o\u0010pR\u001b\u0010s\u001a\u00020H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0017\u0010b\u001a\u0004\u0008r\u0010JR\u001b\u0010u\u001a\u00020H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010b\u001a\u0004\u0008t\u0010JR\u001b\u0010z\u001a\u00020v8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008w\u0010b\u001a\u0004\u0008x\u0010yR$\u0010\u001a\u001a\u00020\u00192\u0006\u0010{\u001a\u00020\u00198\u0002@BX\u0082\u000e\u00a2\u0006\u000c\n\u0004\u0008|\u0010}\"\u0004\u0008~\u0010\u001cR#\u0010\u0082\u0001\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n0\u007f8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0080\u0001\u0010\u0081\u0001\u00a8\u0006\u0084\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;",
        "Landroid/widget/LinearLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "l",
        "()V",
        "Landroid/content/res/TypedArray;",
        "setupAccordion",
        "(Landroid/content/res/TypedArray;)V",
        "setupTag",
        "setupCounter",
        "setupCustomBadge",
        "setupActionIcon",
        "setupListCheckBox",
        "space",
        "Landroid/widget/Space;",
        "k",
        "(I)Landroid/widget/Space;",
        "Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;",
        "type",
        "setStyle",
        "(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V",
        "count",
        "setCounterNumber",
        "(Ljava/lang/Integer;)V",
        "resId",
        "setActionIcon",
        "(I)V",
        "Landroid/graphics/drawable/Drawable;",
        "actionIcon",
        "(Landroid/graphics/drawable/Drawable;)V",
        "Landroid/content/res/ColorStateList;",
        "imageTint",
        "setActionIconTint",
        "(Landroid/content/res/ColorStateList;)V",
        "Landroid/view/View$OnClickListener;",
        "listener",
        "setActionIconClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "setListCheckBoxClickListener",
        "Lc01/m;",
        "setListCheckboxCheckedChangeListener",
        "(Lc01/m;)V",
        "setAccordionClickListener",
        "Landroid/view/View$OnTouchListener;",
        "setDragAndDrapTouchListener",
        "(Landroid/view/View$OnTouchListener;)V",
        "",
        "expanded",
        "setAccordionExpanded",
        "(Z)V",
        "favorite",
        "setFavoriteIcon",
        "iconTintColorAttr",
        "setActionIconTintByColorAttr",
        "top",
        "new",
        "setCustomBadgeType",
        "(ZZ)V",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;",
        "(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V",
        "checked",
        "setListCheckboxChecked",
        "enabled",
        "setListCheckBoxEnabledWithAlpha",
        "Landroidx/appcompat/widget/AppCompatImageView;",
        "getActionIconView",
        "()Landroidx/appcompat/widget/AppCompatImageView;",
        "Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;",
        "getListCheckBox",
        "()Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;",
        "Lorg/xbet/uikit/components/accordion/Accordion;",
        "getAccordion",
        "()Lorg/xbet/uikit/components/accordion/Accordion;",
        "Lorg/xbet/uikit/components/counter/DSCounter;",
        "getCounter",
        "()Lorg/xbet/uikit/components/counter/DSCounter;",
        "visible",
        "setTagVisible",
        "a",
        "I",
        "space4",
        "b",
        "space8",
        "c",
        "space12",
        "d",
        "size20",
        "e",
        "size40",
        "f",
        "Lkotlin/j;",
        "getChevronImageView",
        "chevronImageView",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "g",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "tag",
        "h",
        "Lorg/xbet/uikit/components/counter/DSCounter;",
        "counterView",
        "i",
        "Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;",
        "listCheckboxView",
        "j",
        "Lorg/xbet/uikit/components/accordion/Accordion;",
        "accordionView",
        "getActionIconImageView",
        "actionIconImageView",
        "getDragAndDropImageView",
        "dragAndDropImageView",
        "Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;",
        "m",
        "getCustomBadgeView",
        "()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;",
        "customBadgeView",
        "value",
        "n",
        "Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;",
        "setType",
        "Lkotlin/Function1;",
        "o",
        "Lkotlin/jvm/functions/Function1;",
        "applyAttrs",
        "p",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final p:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final q:I


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:Lorg/xbet/uikit/components/tag/Tag;

.field public h:Lorg/xbet/uikit/components/counter/DSCounter;

.field public i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

.field public j:Lorg/xbet/uikit/components/accordion/Accordion;

.field public final k:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/content/res/TypedArray;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->p:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->q:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_4:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_8:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_12:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->c:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_20:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->d:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_40:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->e:I

    .line 11
    new-instance v0, LM31/a;

    invoke-direct {v0, p1, p0}, LM31/a;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V

    .line 12
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 13
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->f:Lkotlin/j;

    .line 14
    new-instance v0, LM31/b;

    invoke-direct {v0, p1, p0}, LM31/b;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V

    .line 15
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 16
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k:Lkotlin/j;

    .line 17
    new-instance v0, LM31/c;

    invoke-direct {v0, p1, p0}, LM31/c;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V

    .line 18
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 19
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->l:Lkotlin/j;

    .line 20
    new-instance v0, LM31/d;

    invoke-direct {v0, p1}, LM31/d;-><init>(Landroid/content/Context;)V

    .line 21
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 22
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->m:Lkotlin/j;

    .line 23
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->LIST_CHECKBOX:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->n:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 24
    new-instance v0, LM31/e;

    invoke-direct {v0, p0}, LM31/e;-><init>(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->o:Lkotlin/jvm/functions/Function1;

    .line 25
    sget-object v1, Lm31/g;->SportCellRightView:[I

    const/4 v2, 0x0

    .line 26
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    invoke-interface {v0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCellRightStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Landroid/content/Context;)Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i(Landroid/content/Context;)Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->f(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->g(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final f(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 2

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 7
    .line 8
    .line 9
    move-result p0

    .line 10
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 11
    .line 12
    .line 13
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 14
    .line 15
    iget v1, p1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->e:I

    .line 16
    .line 17
    invoke-direct {p0, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 21
    .line 22
    .line 23
    iget p0, p1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 24
    .line 25
    invoke-virtual {v0, p0, p0, p0, p0}, Landroid/view/View;->setPadding(IIII)V

    .line 26
    .line 27
    .line 28
    return-object v0
.end method

.method public static final g(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setupTag(Landroid/content/res/TypedArray;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setupCounter(Landroid/content/res/TypedArray;)V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setupListCheckBox(Landroid/content/res/TypedArray;)V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setupAccordion(Landroid/content/res/TypedArray;)V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setupCustomBadge(Landroid/content/res/TypedArray;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setupActionIcon(Landroid/content/res/TypedArray;)V

    .line 17
    .line 18
    .line 19
    sget v0, Lm31/g;->SportCellRightView_sportCellRightStyleType:I

    .line 20
    .line 21
    const/4 v1, 0x0

    .line 22
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-static {}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;->getEntries()Lkotlin/enums/a;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    check-cast p1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 35
    .line 36
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setType(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 37
    .line 38
    .line 39
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 40
    .line 41
    return-object p0
.end method

.method private final getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getChevronImageView()Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->f:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getCustomBadgeView()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->m:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getDragAndDropImageView()Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->l:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final h(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 3

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 7
    .line 8
    iget p1, p1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->d:I

    .line 9
    .line 10
    invoke-direct {v1, p1, p1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 14
    .line 15
    .line 16
    sget p1, LlZ0/h;->ic_glyph_chevron_right_small:I

    .line 17
    .line 18
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 19
    .line 20
    .line 21
    sget p1, LlZ0/d;->uikitSecondary:I

    .line 22
    .line 23
    const/4 v1, 0x0

    .line 24
    const/4 v2, 0x2

    .line 25
    invoke-static {p0, p1, v1, v2, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 26
    .line 27
    .line 28
    move-result p0

    .line 29
    invoke-static {p0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    invoke-virtual {v0, p0}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 34
    .line 35
    .line 36
    return-object v0
.end method

.method public static final i(Landroid/content/Context;)Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x2

    .line 5
    invoke-direct {v0, p0, v1, v2, v1}, Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public static final j(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;)Landroidx/appcompat/widget/AppCompatImageView;
    .locals 4

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 7
    .line 8
    iget v2, p1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->e:I

    .line 9
    .line 10
    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 14
    .line 15
    .line 16
    sget v1, LlZ0/h;->ic_glyph_reorder:I

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 19
    .line 20
    .line 21
    sget v1, LlZ0/d;->uikitSecondary:I

    .line 22
    .line 23
    const/4 v2, 0x0

    .line 24
    const/4 v3, 0x2

    .line 25
    invoke-static {p0, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 26
    .line 27
    .line 28
    move-result p0

    .line 29
    invoke-static {p0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    invoke-virtual {v0, p0}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 34
    .line 35
    .line 36
    iget p0, p1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 37
    .line 38
    invoke-virtual {v0, p0, p0, p0, p0}, Landroid/view/View;->setPadding(IIII)V

    .line 39
    .line 40
    .line 41
    return-object v0
.end method

.method private final setType(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->n:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->l()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method private final setupAccordion(Landroid/content/res/TypedArray;)V
    .locals 3

    .line 1
    sget v0, Lm31/g;->SportCellRightView_sportCellRightAccordionStyle:I

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit/components/accordion/AccordionType;->PRIMARY:Lorg/xbet/uikit/components/accordion/AccordionType;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    invoke-static {}, Lorg/xbet/uikit/components/accordion/AccordionType;->getEntries()Lkotlin/enums/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lorg/xbet/uikit/components/accordion/AccordionType;

    .line 22
    .line 23
    sget-object v1, Lorg/xbet/uikit/components/accordion/Accordion;->c:Lorg/xbet/uikit/components/accordion/Accordion$a;

    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-virtual {v1, v2, v0}, Lorg/xbet/uikit/components/accordion/Accordion$a;->a(Landroid/content/Context;Lorg/xbet/uikit/components/accordion/AccordionType;)Lorg/xbet/uikit/components/accordion/Accordion;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    invoke-virtual {v0, v1}, Landroid/view/View;->setId(I)V

    .line 38
    .line 39
    .line 40
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 41
    .line 42
    const/4 v2, -0x2

    .line 43
    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 47
    .line 48
    .line 49
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 50
    .line 51
    sget v0, Lm31/g;->SportCellRightView_sportCellRightAccordionExpanded:I

    .line 52
    .line 53
    const/4 v1, 0x0

    .line 54
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 55
    .line 56
    .line 57
    move-result p1

    .line 58
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 59
    .line 60
    if-nez v0, :cond_0

    .line 61
    .line 62
    const/4 v0, 0x0

    .line 63
    :cond_0
    invoke-virtual {v0, p1, v1}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded(ZZ)V

    .line 64
    .line 65
    .line 66
    return-void
.end method

.method private final setupActionIcon(Landroid/content/res/TypedArray;)V
    .locals 1

    .line 1
    sget v0, Lm31/g;->SportCellRightView_sportCellRightActionIcon:I

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIcon(Landroid/graphics/drawable/Drawable;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    sget v0, Lm31/g;->SportCellRightView_sportCellRightActionIconTint:I

    .line 13
    .line 14
    invoke-virtual {p1, v0}, Landroid/content/res/TypedArray;->getColorStateList(I)Landroid/content/res/ColorStateList;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    if-eqz p1, :cond_1

    .line 19
    .line 20
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIconTint(Landroid/content/res/ColorStateList;)V

    .line 21
    .line 22
    .line 23
    :cond_1
    return-void
.end method

.method private final setupCounter(Landroid/content/res/TypedArray;)V
    .locals 3

    .line 1
    sget-object v0, Lorg/xbet/uikit/components/counter/CounterType;->PRIMARY:Lorg/xbet/uikit/components/counter/CounterType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    sget v1, Lm31/g;->SportCellRightView_sportCellRightCounterStyle:I

    .line 8
    .line 9
    invoke-virtual {p1, v1, v0}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    invoke-static {}, Lorg/xbet/uikit/components/counter/CounterType;->getEntries()Lkotlin/enums/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lorg/xbet/uikit/components/counter/CounterType;

    .line 22
    .line 23
    sget-object v1, Lorg/xbet/uikit/components/counter/DSCounter;->e:Lorg/xbet/uikit/components/counter/DSCounter$a;

    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-virtual {v1, v2, v0}, Lorg/xbet/uikit/components/counter/DSCounter$a;->b(Landroid/content/Context;Lorg/xbet/uikit/components/counter/CounterType;)Lorg/xbet/uikit/components/counter/DSCounter;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    invoke-virtual {v0, v1}, Landroid/view/View;->setId(I)V

    .line 38
    .line 39
    .line 40
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 41
    .line 42
    const/4 v2, -0x2

    .line 43
    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 47
    .line 48
    .line 49
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 50
    .line 51
    sget v0, Lm31/g;->SportCellRightView_sportCellRightCounterNumber:I

    .line 52
    .line 53
    const/4 v1, -0x1

    .line 54
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 55
    .line 56
    .line 57
    move-result p1

    .line 58
    if-lez p1, :cond_0

    .line 59
    .line 60
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 65
    .line 66
    .line 67
    :cond_0
    return-void
.end method

.method private final setupCustomBadge(Landroid/content/res/TypedArray;)V
    .locals 3

    .line 1
    sget v0, Lm31/g;->SportCellRightView_sportCellRightCustomBadgeStyle:I

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->POPULAR:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    invoke-virtual {p1, v0, v2}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    invoke-static {}, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->getEntries()Lkotlin/enums/a;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    if-ltz p1, :cond_0

    .line 18
    .line 19
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    if-ge p1, v2, :cond_0

    .line 24
    .line 25
    invoke-interface {v0, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    :cond_0
    check-cast v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 30
    .line 31
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getCustomBadgeView()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-virtual {p1, v1}, Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;->setStyle(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method private final setupListCheckBox(Landroid/content/res/TypedArray;)V
    .locals 3

    .line 1
    sget-object v0, Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;->PRIMARY:Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    sget v1, Lm31/g;->SportCellRightView_sportCellRightListCheckboxStyle:I

    .line 8
    .line 9
    invoke-virtual {p1, v1, v0}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    invoke-static {}, Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;->getEntries()Lkotlin/enums/a;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;

    .line 22
    .line 23
    sget-object v1, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->g:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox$a;

    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-virtual {v1, v2, v0}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox$a;->a(Landroid/content/Context;Lorg/xbet/uikit/components/list_checkbox/ListCheckBoxType;)Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    invoke-virtual {v0, v1}, Landroid/view/View;->setId(I)V

    .line 38
    .line 39
    .line 40
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 41
    .line 42
    const/4 v2, -0x2

    .line 43
    invoke-direct {v1, v2, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 47
    .line 48
    .line 49
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 50
    .line 51
    sget v0, Lm31/g;->SportCellRightView_sportCellRightListCheckboxChecked:I

    .line 52
    .line 53
    const/4 v1, 0x0

    .line 54
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 55
    .line 56
    .line 57
    move-result p1

    .line 58
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 59
    .line 60
    if-nez v0, :cond_0

    .line 61
    .line 62
    const/4 v0, 0x0

    .line 63
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->setChecked(Z)V

    .line 64
    .line 65
    .line 66
    return-void
.end method

.method private final setupTag(Landroid/content/res/TypedArray;)V
    .locals 8
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "SetTextI18n"
        }
    .end annotation

    .line 1
    sget v0, Lm31/g;->SportCellRightView_sportCellRightTagVisible:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, v0, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    new-instance v2, Lorg/xbet/uikit/components/tag/Tag;

    .line 9
    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    const/4 v6, 0x6

    .line 15
    const/4 v7, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v5, 0x0

    .line 18
    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit/components/tag/Tag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 19
    .line 20
    .line 21
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    invoke-virtual {v2, v0}, Landroid/view/View;->setId(I)V

    .line 26
    .line 27
    .line 28
    new-instance v0, Landroid/widget/LinearLayout$LayoutParams;

    .line 29
    .line 30
    const/4 v3, -0x2

    .line 31
    invoke-direct {v0, v3, v3}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {v2, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 35
    .line 36
    .line 37
    sget v0, LlZ0/n;->Widget_Tag_Rounded_Red:I

    .line 38
    .line 39
    invoke-virtual {v2, v0}, Lorg/xbet/uikit/components/tag/Tag;->setStyle(I)V

    .line 40
    .line 41
    .line 42
    const-string v0, "Live"

    .line 43
    .line 44
    invoke-virtual {v2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 45
    .line 46
    .line 47
    const/16 v0, 0x11

    .line 48
    .line 49
    invoke-virtual {v2, v0}, Landroid/widget/TextView;->setGravity(I)V

    .line 50
    .line 51
    .line 52
    if-eqz p1, :cond_0

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_0
    const/16 v1, 0x8

    .line 56
    .line 57
    :goto_0
    invoke-virtual {v2, v1}, Landroid/view/View;->setVisibility(I)V

    .line 58
    .line 59
    .line 60
    iput-object v2, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->g:Lorg/xbet/uikit/components/tag/Tag;

    .line 61
    .line 62
    return-void
.end method


# virtual methods
.method public final getAccordion()Lorg/xbet/uikit/components/accordion/Accordion;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    return-object v0
.end method

.method public final getActionIconView()Landroidx/appcompat/widget/AppCompatImageView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getCounter()Lorg/xbet/uikit/components/counter/DSCounter;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    return-object v0
.end method

.method public final getListCheckBox()Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    return-object v0
.end method

.method public final k(I)Landroid/widget/Space;
    .locals 3

    .line 1
    new-instance v0, Landroid/widget/Space;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Landroid/widget/Space;-><init>(Landroid/content/Context;)V

    .line 8
    .line 9
    .line 10
    new-instance v1, Landroid/widget/LinearLayout$LayoutParams;

    .line 11
    .line 12
    const/4 v2, -0x1

    .line 13
    invoke-direct {v1, p1, v2}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 17
    .line 18
    .line 19
    return-object v0
.end method

.method public final l()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViewsInLayout()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->n:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;

    .line 5
    .line 6
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView$b;->a:[I

    .line 7
    .line 8
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    aget v0, v1, v0

    .line 13
    .line 14
    const/4 v1, 0x0

    .line 15
    packed-switch v0, :pswitch_data_0

    .line 16
    .line 17
    .line 18
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 19
    .line 20
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 21
    .line 22
    .line 23
    throw v0

    .line 24
    :pswitch_0
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 25
    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 31
    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getCustomBadgeView()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 38
    .line 39
    .line 40
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->c:I

    .line 41
    .line 42
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 47
    .line 48
    .line 49
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 50
    .line 51
    if-nez v0, :cond_0

    .line 52
    .line 53
    move-object v0, v1

    .line 54
    :cond_0
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 55
    .line 56
    .line 57
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 58
    .line 59
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 64
    .line 65
    .line 66
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 67
    .line 68
    if-nez v0, :cond_1

    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_1
    move-object v1, v0

    .line 72
    :goto_0
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 73
    .line 74
    .line 75
    return-void

    .line 76
    :pswitch_1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 77
    .line 78
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 83
    .line 84
    .line 85
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getCustomBadgeView()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 90
    .line 91
    .line 92
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->c:I

    .line 93
    .line 94
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 99
    .line 100
    .line 101
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 102
    .line 103
    if-nez v0, :cond_2

    .line 104
    .line 105
    move-object v0, v1

    .line 106
    :cond_2
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 107
    .line 108
    .line 109
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 110
    .line 111
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 112
    .line 113
    .line 114
    move-result-object v0

    .line 115
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 116
    .line 117
    .line 118
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 119
    .line 120
    if-nez v0, :cond_3

    .line 121
    .line 122
    goto :goto_1

    .line 123
    :cond_3
    move-object v1, v0

    .line 124
    :goto_1
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 125
    .line 126
    .line 127
    return-void

    .line 128
    :pswitch_2
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 129
    .line 130
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 131
    .line 132
    .line 133
    move-result-object v0

    .line 134
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 135
    .line 136
    .line 137
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getCustomBadgeView()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    .line 138
    .line 139
    .line 140
    move-result-object v0

    .line 141
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 142
    .line 143
    .line 144
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->c:I

    .line 145
    .line 146
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 147
    .line 148
    .line 149
    move-result-object v0

    .line 150
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 151
    .line 152
    .line 153
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 154
    .line 155
    if-nez v0, :cond_4

    .line 156
    .line 157
    goto :goto_2

    .line 158
    :cond_4
    move-object v1, v0

    .line 159
    :goto_2
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 160
    .line 161
    .line 162
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 163
    .line 164
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 165
    .line 166
    .line 167
    move-result-object v0

    .line 168
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 169
    .line 170
    .line 171
    return-void

    .line 172
    :pswitch_3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 173
    .line 174
    if-nez v0, :cond_5

    .line 175
    .line 176
    goto :goto_3

    .line 177
    :cond_5
    move-object v1, v0

    .line 178
    :goto_3
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 179
    .line 180
    .line 181
    return-void

    .line 182
    :pswitch_4
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getDragAndDropImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 183
    .line 184
    .line 185
    move-result-object v0

    .line 186
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 187
    .line 188
    .line 189
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 190
    .line 191
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 192
    .line 193
    .line 194
    move-result-object v0

    .line 195
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 196
    .line 197
    .line 198
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 199
    .line 200
    if-nez v0, :cond_6

    .line 201
    .line 202
    goto :goto_4

    .line 203
    :cond_6
    move-object v1, v0

    .line 204
    :goto_4
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 205
    .line 206
    .line 207
    return-void

    .line 208
    :pswitch_5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getDragAndDropImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 209
    .line 210
    .line 211
    move-result-object v0

    .line 212
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 213
    .line 214
    .line 215
    return-void

    .line 216
    :pswitch_6
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 217
    .line 218
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 219
    .line 220
    .line 221
    move-result-object v0

    .line 222
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 223
    .line 224
    .line 225
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 226
    .line 227
    if-nez v0, :cond_7

    .line 228
    .line 229
    goto :goto_5

    .line 230
    :cond_7
    move-object v1, v0

    .line 231
    :goto_5
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 232
    .line 233
    .line 234
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 235
    .line 236
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 237
    .line 238
    .line 239
    move-result-object v0

    .line 240
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 241
    .line 242
    .line 243
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getChevronImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 244
    .line 245
    .line 246
    move-result-object v0

    .line 247
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 248
    .line 249
    .line 250
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 251
    .line 252
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 253
    .line 254
    .line 255
    move-result-object v0

    .line 256
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 257
    .line 258
    .line 259
    return-void

    .line 260
    :pswitch_7
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 261
    .line 262
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 263
    .line 264
    .line 265
    move-result-object v0

    .line 266
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 267
    .line 268
    .line 269
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getChevronImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 270
    .line 271
    .line 272
    move-result-object v0

    .line 273
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 274
    .line 275
    .line 276
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 277
    .line 278
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 279
    .line 280
    .line 281
    move-result-object v0

    .line 282
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 283
    .line 284
    .line 285
    return-void

    .line 286
    :pswitch_8
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 287
    .line 288
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 289
    .line 290
    .line 291
    move-result-object v0

    .line 292
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 293
    .line 294
    .line 295
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 296
    .line 297
    if-nez v0, :cond_8

    .line 298
    .line 299
    move-object v0, v1

    .line 300
    :cond_8
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 301
    .line 302
    .line 303
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 304
    .line 305
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 306
    .line 307
    .line 308
    move-result-object v0

    .line 309
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 310
    .line 311
    .line 312
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 313
    .line 314
    if-nez v0, :cond_9

    .line 315
    .line 316
    goto :goto_6

    .line 317
    :cond_9
    move-object v1, v0

    .line 318
    :goto_6
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 319
    .line 320
    .line 321
    return-void

    .line 322
    :pswitch_9
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 323
    .line 324
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 325
    .line 326
    .line 327
    move-result-object v0

    .line 328
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 329
    .line 330
    .line 331
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 332
    .line 333
    if-nez v0, :cond_a

    .line 334
    .line 335
    move-object v0, v1

    .line 336
    :cond_a
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 337
    .line 338
    .line 339
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 340
    .line 341
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 342
    .line 343
    .line 344
    move-result-object v0

    .line 345
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 346
    .line 347
    .line 348
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 349
    .line 350
    if-nez v0, :cond_b

    .line 351
    .line 352
    goto :goto_7

    .line 353
    :cond_b
    move-object v1, v0

    .line 354
    :goto_7
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 355
    .line 356
    .line 357
    return-void

    .line 358
    :pswitch_a
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 359
    .line 360
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 361
    .line 362
    .line 363
    move-result-object v0

    .line 364
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 365
    .line 366
    .line 367
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->g:Lorg/xbet/uikit/components/tag/Tag;

    .line 368
    .line 369
    if-nez v0, :cond_c

    .line 370
    .line 371
    move-object v0, v1

    .line 372
    :cond_c
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 373
    .line 374
    .line 375
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 376
    .line 377
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 378
    .line 379
    .line 380
    move-result-object v0

    .line 381
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 382
    .line 383
    .line 384
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 385
    .line 386
    if-nez v0, :cond_d

    .line 387
    .line 388
    goto :goto_8

    .line 389
    :cond_d
    move-object v1, v0

    .line 390
    :goto_8
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 391
    .line 392
    .line 393
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->a:I

    .line 394
    .line 395
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 396
    .line 397
    .line 398
    move-result-object v0

    .line 399
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 400
    .line 401
    .line 402
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 403
    .line 404
    .line 405
    move-result-object v0

    .line 406
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 407
    .line 408
    .line 409
    return-void

    .line 410
    :pswitch_b
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 411
    .line 412
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 413
    .line 414
    .line 415
    move-result-object v0

    .line 416
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 417
    .line 418
    .line 419
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 420
    .line 421
    if-nez v0, :cond_e

    .line 422
    .line 423
    goto :goto_9

    .line 424
    :cond_e
    move-object v1, v0

    .line 425
    :goto_9
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 426
    .line 427
    .line 428
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->b:I

    .line 429
    .line 430
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->k(I)Landroid/widget/Space;

    .line 431
    .line 432
    .line 433
    move-result-object v0

    .line 434
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 435
    .line 436
    .line 437
    return-void

    .line 438
    :pswitch_c
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 439
    .line 440
    .line 441
    move-result-object v0

    .line 442
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 443
    .line 444
    .line 445
    return-void

    .line 446
    :pswitch_d
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 447
    .line 448
    if-nez v0, :cond_f

    .line 449
    .line 450
    goto :goto_a

    .line 451
    :cond_f
    move-object v1, v0

    .line 452
    :goto_a
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 453
    .line 454
    .line 455
    return-void

    .line 456
    nop

    .line 457
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final setAccordionClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setAccordionExpanded(Z)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->j:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    move-object v0, v1

    .line 7
    :cond_0
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x2

    .line 9
    invoke-static {v0, p1, v2, v3, v1}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded$default(Lorg/xbet/uikit/components/accordion/Accordion;ZZILjava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method

.method public final setActionIcon(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    return-void
.end method

.method public final setActionIcon(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    return-void
.end method

.method public final setActionIconClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setActionIconTint(Landroid/content/res/ColorStateList;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setActionIconTintByColorAttr(Ljava/lang/Integer;)V
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    if-eqz p1, :cond_0

    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 5
    .line 6
    .line 7
    move-result-object v1

    .line 8
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    const/4 v2, 0x2

    .line 13
    invoke-static {v1, p1, v0, v2, v0}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIconTint(Landroid/content/res/ColorStateList;)V

    .line 22
    .line 23
    .line 24
    return-void

    .line 25
    :cond_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageTintList(Landroid/content/res/ColorStateList;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public final setCounterNumber(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->h:Lorg/xbet/uikit/components/counter/DSCounter;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/counter/DSCounter;->l(Ljava/lang/Integer;)Lorg/xbet/uikit/components/counter/DSCounter;

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setCustomBadgeType(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getCustomBadgeView()Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;

    move-result-object v0

    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/custom_badge/DsCustomBadge;->setStyle(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    return-void
.end method

.method public final setCustomBadgeType(ZZ)V
    .locals 0

    if-eqz p1, :cond_0

    .line 1
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->POPULAR:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    goto :goto_0

    :cond_0
    if-eqz p2, :cond_1

    .line 2
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->NEW:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    goto :goto_0

    .line 3
    :cond_1
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;->NONE:Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 4
    :goto_0
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCustomBadgeType(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    return-void
.end method

.method public final setDragAndDrapTouchListener(Landroid/view/View$OnTouchListener;)V
    .locals 1
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "ClickableViewAccessibility"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getDragAndDropImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setFavoriteIcon(Z)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    sget p1, LlZ0/h;->ic_glyph_favourite_active:I

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    sget p1, LlZ0/h;->ic_glyph_favourite_inactive:I

    .line 7
    .line 8
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->getActionIconImageView()Landroidx/appcompat/widget/AppCompatImageView;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final setListCheckBoxClickListener(Landroid/view/View$OnClickListener;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setListCheckBoxEnabledWithAlpha(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    move-object v0, v1

    .line 7
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->setEnabled(Z)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 11
    .line 12
    if-nez v0, :cond_1

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_1
    move-object v1, v0

    .line 16
    :goto_0
    if-eqz p1, :cond_2

    .line 17
    .line 18
    const/high16 p1, 0x3f800000    # 1.0f

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_2
    const p1, 0x3e4ccccd

    .line 22
    .line 23
    .line 24
    :goto_1
    invoke-virtual {v1, p1}, Landroid/view/View;->setAlpha(F)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public final setListCheckboxChecked(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->setChecked(Z)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setListCheckboxCheckedChangeListener(Lc01/m;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->i:Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/list_checkbox/DSListCheckBox;->setCheckedChangeListener(Lc01/m;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setStyle(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setType(Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final setTagVisible(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->g:Lorg/xbet/uikit/components/tag/Tag;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    :cond_0
    if-eqz p1, :cond_1

    .line 7
    .line 8
    const/4 p1, 0x0

    .line 9
    goto :goto_0

    .line 10
    :cond_1
    const/16 p1, 0x8

    .line 11
    .line 12
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    return-void
.end method
