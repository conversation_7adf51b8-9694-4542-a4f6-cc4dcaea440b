.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/Throwable;

.field public final synthetic b:L<PERSON><PERSON>/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;->a:Ljava/lang/Throwable;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;->b:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;->a:Ljava/lang/Throwable;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;->b:Lkotlin/jvm/functions/Function0;

    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, Ljava/lang/String;

    invoke-static {v0, v1, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->z3(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
