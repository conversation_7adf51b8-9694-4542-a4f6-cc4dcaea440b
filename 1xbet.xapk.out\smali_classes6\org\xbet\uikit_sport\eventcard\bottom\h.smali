.class public final synthetic Lorg/xbet/uikit_sport/eventcard/bottom/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Landroid/view/View$OnClickListener;

.field public final synthetic b:LC31/h;

.field public final synthetic c:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;


# direct methods
.method public synthetic constructor <init>(Landroid/view/View$OnClickListener;LC31/h;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/h;->a:Landroid/view/View$OnClickListener;

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/h;->b:LC31/h;

    iput-object p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/h;->c:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/h;->a:Landroid/view/View$OnClickListener;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/h;->b:LC31/h;

    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/h;->c:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->s(Landroid/view/View$OnClickListener;LC31/h;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;Landroid/view/View;)V

    return-void
.end method
