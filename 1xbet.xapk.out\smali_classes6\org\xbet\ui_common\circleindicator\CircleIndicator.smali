.class public final Lorg/xbet/ui_common/circleindicator/CircleIndicator;
.super Landroid/widget/LinearLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/ui_common/circleindicator/CircleIndicator$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0085\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0008\u0004\n\u0002\u0008\u0004\n\u0002\u0008\u0004\n\u0002\u0008\u0004\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003*\u0005;?CGK\u0008\u0007\u0018\u0000 \u001c2\u00020\u0001:\u0001<B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0011\u0010\u000b\u001a\u0004\u0018\u00010\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0011\u0010\r\u001a\u0004\u0018\u00010\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0019\u0010\u0013\u001a\u00020\u00062\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u0011H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0017\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J!\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0003\u001a\u00020\u00022\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u0004H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0017J\u000f\u0010\u001c\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ)\u0010\"\u001a\u00020\n2\u0006\u0010\u001e\u001a\u00020\u00062\u0008\u0008\u0001\u0010\u001f\u001a\u00020\u00062\u0006\u0010!\u001a\u00020 H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u000f\u0010$\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008$\u0010\u001dJ\u000f\u0010&\u001a\u00020%H\u0014\u00a2\u0006\u0004\u0008&\u0010\'J\u0019\u0010)\u001a\u00020\n2\u0008\u0010(\u001a\u0004\u0018\u00010%H\u0014\u00a2\u0006\u0004\u0008)\u0010*J\u0015\u0010-\u001a\u00020\n2\u0006\u0010,\u001a\u00020+\u00a2\u0006\u0004\u0008-\u0010.J\u0015\u00101\u001a\u00020\n2\u0006\u00100\u001a\u00020/\u00a2\u0006\u0004\u00081\u00102J\u001d\u00107\u001a\u00020\n2\u0006\u00104\u001a\u0002032\u0006\u00106\u001a\u000205\u00a2\u0006\u0004\u00087\u00108J\r\u00109\u001a\u00020\n\u00a2\u0006\u0004\u00089\u0010\u001dJ\r\u0010:\u001a\u00020\n\u00a2\u0006\u0004\u0008:\u0010\u001dR\u0014\u0010>\u001a\u00020;8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010B\u001a\u00020?8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010F\u001a\u00020C8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010J\u001a\u00020G8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010N\u001a\u00020K8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0018\u00106\u001a\u0004\u0018\u0001058\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0018\u00104\u001a\u0004\u0018\u0001038\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u0018\u00100\u001a\u0004\u0018\u00010/8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0016\u0010V\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\"\u0010UR\u0016\u0010W\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010UR\u0016\u0010\u001a\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\r\u0010UR\u0016\u0010X\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00089\u0010UR\u0016\u0010[\u001a\u00020Y8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010ZR\u0016\u0010^\u001a\u00020\\8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008:\u0010]\u00a8\u0006_"
    }
    d2 = {
        "Lorg/xbet/ui_common/circleindicator/CircleIndicator;",
        "Landroid/widget/LinearLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attributeSet",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "m",
        "()Lkotlin/Unit;",
        "k",
        "removedPosition",
        "p",
        "(I)I",
        "Landroidx/recyclerview/widget/RecyclerView$LayoutManager;",
        "layoutManager",
        "q",
        "(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I",
        "position",
        "j",
        "(I)V",
        "r",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "count",
        "s",
        "o",
        "()V",
        "orientation",
        "backgroundDrawableId",
        "Landroid/animation/Animator;",
        "animator",
        "i",
        "(IILandroid/animation/Animator;)V",
        "onDetachedFromWindow",
        "Landroid/os/Parcelable;",
        "onSaveInstanceState",
        "()Landroid/os/Parcelable;",
        "state",
        "onRestoreInstanceState",
        "(Landroid/os/Parcelable;)V",
        "Landroidx/viewpager/widget/ViewPager;",
        "viewPager",
        "setViewPager",
        "(Landroidx/viewpager/widget/ViewPager;)V",
        "Landroidx/viewpager2/widget/ViewPager2;",
        "viewPager2",
        "setViewPager2",
        "(Landroidx/viewpager2/widget/ViewPager2;)V",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "recyclerView",
        "Landroidx/recyclerview/widget/x;",
        "pagerSnapHelper",
        "setRecyclerView",
        "(Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/x;)V",
        "l",
        "n",
        "org/xbet/ui_common/circleindicator/CircleIndicator$e",
        "a",
        "Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;",
        "mInternalPageChangeListener",
        "org/xbet/ui_common/circleindicator/CircleIndicator$d",
        "b",
        "Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;",
        "internalPageChangeListener",
        "org/xbet/ui_common/circleindicator/CircleIndicator$f",
        "c",
        "Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;",
        "scrollListener",
        "org/xbet/ui_common/circleindicator/CircleIndicator$b",
        "d",
        "Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;",
        "adapterRVDataObserver",
        "org/xbet/ui_common/circleindicator/CircleIndicator$c",
        "e",
        "Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;",
        "adapterVPDataObserver",
        "f",
        "Landroidx/recyclerview/widget/x;",
        "g",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "h",
        "Landroidx/viewpager2/widget/ViewPager2;",
        "I",
        "lastPosition",
        "currentPosition",
        "snapViewPosition",
        "Lorg/xbet/ui_common/circleindicator/b;",
        "Lorg/xbet/ui_common/circleindicator/b;",
        "settings",
        "Lorg/xbet/ui_common/circleindicator/a;",
        "Lorg/xbet/ui_common/circleindicator/a;",
        "animators",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final o:Lorg/xbet/ui_common/circleindicator/CircleIndicator$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final p:I


# instance fields
.field public final a:Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Landroidx/recyclerview/widget/x;

.field public g:Landroidx/recyclerview/widget/RecyclerView;

.field public h:Landroidx/viewpager2/widget/ViewPager2;

.field public i:I

.field public j:I

.field public k:I

.field public l:I

.field public m:Lorg/xbet/ui_common/circleindicator/b;

.field public n:Lorg/xbet/ui_common/circleindicator/a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->o:Lorg/xbet/ui_common/circleindicator/CircleIndicator$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->p:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p3, Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;

    invoke-direct {p3, p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;-><init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V

    iput-object p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;

    .line 6
    new-instance p3, Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;

    invoke-direct {p3, p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;-><init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V

    iput-object p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;

    .line 7
    new-instance p3, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;

    invoke-direct {p3, p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;-><init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V

    iput-object p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c:Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;

    .line 8
    new-instance p3, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;

    invoke-direct {p3, p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;-><init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V

    iput-object p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->d:Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;

    .line 9
    new-instance p3, Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;

    invoke-direct {p3, p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;-><init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V

    iput-object p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->e:Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;

    const/4 p3, -0x1

    .line 10
    iput p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 11
    iput p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 12
    iput p3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k:I

    .line 13
    invoke-virtual {p0, p1, p2}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->r(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final synthetic a(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic b(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)I
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->p(I)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic c(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/recyclerview/widget/RecyclerView;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xbet/ui_common/circleindicator/CircleIndicator;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->q(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic e(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->l:I

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic f(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/viewpager2/widget/ViewPager2;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h:Landroidx/viewpager2/widget/ViewPager2;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic h(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->s(I)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final i(IILandroid/animation/Animator;)V
    .locals 3

    .line 1
    invoke-virtual {p3}, Landroid/animation/Animator;->isRunning()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p3}, Landroid/animation/Animator;->end()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p3}, Landroid/animation/Animator;->cancel()V

    .line 11
    .line 12
    .line 13
    :cond_0
    new-instance v0, Landroid/view/View;

    .line 14
    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-direct {v0, v1}, Landroid/view/View;-><init>(Landroid/content/Context;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    invoke-static {v1, p2}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 27
    .line 28
    .line 29
    move-result-object p2

    .line 30
    invoke-virtual {v0, p2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 31
    .line 32
    .line 33
    iget-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 34
    .line 35
    const/4 v1, 0x0

    .line 36
    if-nez p2, :cond_1

    .line 37
    .line 38
    move-object p2, v1

    .line 39
    :cond_1
    invoke-virtual {p2}, Lorg/xbet/ui_common/circleindicator/b;->g()I

    .line 40
    .line 41
    .line 42
    move-result p2

    .line 43
    iget-object v2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 44
    .line 45
    if-nez v2, :cond_2

    .line 46
    .line 47
    move-object v2, v1

    .line 48
    :cond_2
    invoke-virtual {v2}, Lorg/xbet/ui_common/circleindicator/b;->b()I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    invoke-virtual {p0, v0, p2, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;II)V

    .line 53
    .line 54
    .line 55
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 56
    .line 57
    .line 58
    move-result-object p2

    .line 59
    check-cast p2, Landroid/widget/LinearLayout$LayoutParams;

    .line 60
    .line 61
    if-nez p1, :cond_5

    .line 62
    .line 63
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 64
    .line 65
    if-nez p1, :cond_3

    .line 66
    .line 67
    move-object p1, v1

    .line 68
    :cond_3
    invoke-virtual {p1}, Lorg/xbet/ui_common/circleindicator/b;->c()I

    .line 69
    .line 70
    .line 71
    move-result p1

    .line 72
    iput p1, p2, Landroid/widget/LinearLayout$LayoutParams;->leftMargin:I

    .line 73
    .line 74
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 75
    .line 76
    if-nez p1, :cond_4

    .line 77
    .line 78
    goto :goto_0

    .line 79
    :cond_4
    move-object v1, p1

    .line 80
    :goto_0
    invoke-virtual {v1}, Lorg/xbet/ui_common/circleindicator/b;->c()I

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    iput p1, p2, Landroid/widget/LinearLayout$LayoutParams;->rightMargin:I

    .line 85
    .line 86
    goto :goto_2

    .line 87
    :cond_5
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 88
    .line 89
    if-nez p1, :cond_6

    .line 90
    .line 91
    move-object p1, v1

    .line 92
    :cond_6
    invoke-virtual {p1}, Lorg/xbet/ui_common/circleindicator/b;->c()I

    .line 93
    .line 94
    .line 95
    move-result p1

    .line 96
    iput p1, p2, Landroid/widget/LinearLayout$LayoutParams;->topMargin:I

    .line 97
    .line 98
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 99
    .line 100
    if-nez p1, :cond_7

    .line 101
    .line 102
    goto :goto_1

    .line 103
    :cond_7
    move-object v1, p1

    .line 104
    :goto_1
    invoke-virtual {v1}, Lorg/xbet/ui_common/circleindicator/b;->c()I

    .line 105
    .line 106
    .line 107
    move-result p1

    .line 108
    iput p1, p2, Landroid/widget/LinearLayout$LayoutParams;->bottomMargin:I

    .line 109
    .line 110
    :goto_2
    invoke-virtual {v0, p2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 111
    .line 112
    .line 113
    invoke-virtual {p3, v0}, Landroid/animation/Animator;->setTarget(Ljava/lang/Object;)V

    .line 114
    .line 115
    .line 116
    invoke-virtual {p3}, Landroid/animation/Animator;->start()V

    .line 117
    .line 118
    .line 119
    return-void
.end method

.method public final j(I)V
    .locals 4

    .line 1
    iget v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    if-nez v0, :cond_1

    .line 10
    .line 11
    move-object v0, v1

    .line 12
    :cond_1
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->a()Landroid/animation/Animator;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {v0}, Landroid/animation/Animator;->isRunning()Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-eqz v0, :cond_4

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 23
    .line 24
    if-nez v0, :cond_2

    .line 25
    .line 26
    move-object v0, v1

    .line 27
    :cond_2
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->a()Landroid/animation/Animator;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {v0}, Landroid/animation/Animator;->end()V

    .line 32
    .line 33
    .line 34
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 35
    .line 36
    if-nez v0, :cond_3

    .line 37
    .line 38
    move-object v0, v1

    .line 39
    :cond_3
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->a()Landroid/animation/Animator;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 44
    .line 45
    .line 46
    :cond_4
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 47
    .line 48
    if-nez v0, :cond_5

    .line 49
    .line 50
    move-object v0, v1

    .line 51
    :cond_5
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->b()Landroid/animation/Animator;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {v0}, Landroid/animation/Animator;->isRunning()Z

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    if-eqz v0, :cond_8

    .line 60
    .line 61
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 62
    .line 63
    if-nez v0, :cond_6

    .line 64
    .line 65
    move-object v0, v1

    .line 66
    :cond_6
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->b()Landroid/animation/Animator;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-virtual {v0}, Landroid/animation/Animator;->end()V

    .line 71
    .line 72
    .line 73
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 74
    .line 75
    if-nez v0, :cond_7

    .line 76
    .line 77
    move-object v0, v1

    .line 78
    :cond_7
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->b()Landroid/animation/Animator;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    invoke-virtual {v0}, Landroid/animation/Animator;->cancel()V

    .line 83
    .line 84
    .line 85
    :cond_8
    iget v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 86
    .line 87
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    .line 88
    .line 89
    .line 90
    move-result-object v0

    .line 91
    iget v2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 92
    .line 93
    if-ltz v2, :cond_c

    .line 94
    .line 95
    if-eqz v0, :cond_c

    .line 96
    .line 97
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    iget-object v3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 102
    .line 103
    if-nez v3, :cond_9

    .line 104
    .line 105
    move-object v3, v1

    .line 106
    :cond_9
    invoke-virtual {v3}, Lorg/xbet/ui_common/circleindicator/b;->f()I

    .line 107
    .line 108
    .line 109
    move-result v3

    .line 110
    invoke-static {v2, v3}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 111
    .line 112
    .line 113
    move-result-object v2

    .line 114
    invoke-virtual {v0, v2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 115
    .line 116
    .line 117
    iget-object v2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 118
    .line 119
    if-nez v2, :cond_a

    .line 120
    .line 121
    move-object v2, v1

    .line 122
    :cond_a
    invoke-virtual {v2}, Lorg/xbet/ui_common/circleindicator/a;->a()Landroid/animation/Animator;

    .line 123
    .line 124
    .line 125
    move-result-object v2

    .line 126
    invoke-virtual {v2, v0}, Landroid/animation/Animator;->setTarget(Ljava/lang/Object;)V

    .line 127
    .line 128
    .line 129
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 130
    .line 131
    if-nez v0, :cond_b

    .line 132
    .line 133
    move-object v0, v1

    .line 134
    :cond_b
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/a;->a()Landroid/animation/Animator;

    .line 135
    .line 136
    .line 137
    move-result-object v0

    .line 138
    invoke-virtual {v0}, Landroid/animation/Animator;->start()V

    .line 139
    .line 140
    .line 141
    :cond_c
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->getChildAt(I)Landroid/view/View;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    if-eqz v0, :cond_10

    .line 146
    .line 147
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 148
    .line 149
    .line 150
    move-result-object v2

    .line 151
    iget-object v3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 152
    .line 153
    if-nez v3, :cond_d

    .line 154
    .line 155
    move-object v3, v1

    .line 156
    :cond_d
    invoke-virtual {v3}, Lorg/xbet/ui_common/circleindicator/b;->a()I

    .line 157
    .line 158
    .line 159
    move-result v3

    .line 160
    invoke-static {v2, v3}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 161
    .line 162
    .line 163
    move-result-object v2

    .line 164
    invoke-virtual {v0, v2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 165
    .line 166
    .line 167
    iget-object v2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 168
    .line 169
    if-nez v2, :cond_e

    .line 170
    .line 171
    move-object v2, v1

    .line 172
    :cond_e
    invoke-virtual {v2}, Lorg/xbet/ui_common/circleindicator/a;->b()Landroid/animation/Animator;

    .line 173
    .line 174
    .line 175
    move-result-object v2

    .line 176
    invoke-virtual {v2, v0}, Landroid/animation/Animator;->setTarget(Ljava/lang/Object;)V

    .line 177
    .line 178
    .line 179
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 180
    .line 181
    if-nez v0, :cond_f

    .line 182
    .line 183
    goto :goto_0

    .line 184
    :cond_f
    move-object v1, v0

    .line 185
    :goto_0
    invoke-virtual {v1}, Lorg/xbet/ui_common/circleindicator/a;->b()Landroid/animation/Animator;

    .line 186
    .line 187
    .line 188
    move-result-object v0

    .line 189
    invoke-virtual {v0}, Landroid/animation/Animator;->start()V

    .line 190
    .line 191
    .line 192
    :cond_10
    iput p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 193
    .line 194
    return-void
.end method

.method public final k()Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h:Landroidx/viewpager2/widget/ViewPager2;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->o(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 8
    .line 9
    .line 10
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object v0

    .line 13
    :cond_0
    const/4 v0, 0x0

    .line 14
    return-object v0
.end method

.method public final l()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->hasObservers()Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 18
    .line 19
    if-eqz v0, :cond_0

    .line 20
    .line 21
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    if-eqz v0, :cond_0

    .line 26
    .line 27
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->d:Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;

    .line 28
    .line 29
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 30
    .line 31
    .line 32
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m()Lkotlin/Unit;

    .line 33
    .line 34
    .line 35
    :cond_1
    return-void
.end method

.method public final m()Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c:Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->removeOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$s;)V

    .line 8
    .line 9
    .line 10
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object v0

    .line 13
    :cond_0
    const/4 v0, 0x0

    .line 14
    return-object v0
.end method

.method public final n()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h:Landroidx/viewpager2/widget/ViewPager2;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->hasObservers()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->e:Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;

    .line 18
    .line 19
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->unregisterAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 20
    .line 21
    .line 22
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k()Lkotlin/Unit;

    .line 23
    .line 24
    .line 25
    :cond_1
    return-void
.end method

.method public final o()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Landroid/view/ViewGroup;->removeAllViews()V

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k:I

    .line 5
    .line 6
    const/4 v1, 0x2

    .line 7
    if-lt v0, v1, :cond_5

    .line 8
    .line 9
    iget v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 10
    .line 11
    iput v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    :goto_0
    if-ge v1, v0, :cond_5

    .line 15
    .line 16
    iget v2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 17
    .line 18
    const/4 v3, 0x0

    .line 19
    if-ne v2, v1, :cond_2

    .line 20
    .line 21
    invoke-virtual {p0}, Landroid/widget/LinearLayout;->getOrientation()I

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    iget-object v4, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 26
    .line 27
    if-nez v4, :cond_0

    .line 28
    .line 29
    move-object v4, v3

    .line 30
    :cond_0
    invoke-virtual {v4}, Lorg/xbet/ui_common/circleindicator/b;->a()I

    .line 31
    .line 32
    .line 33
    move-result v4

    .line 34
    iget-object v5, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 35
    .line 36
    if-nez v5, :cond_1

    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_1
    move-object v3, v5

    .line 40
    :goto_1
    invoke-virtual {v3}, Lorg/xbet/ui_common/circleindicator/a;->d()Landroid/animation/Animator;

    .line 41
    .line 42
    .line 43
    move-result-object v3

    .line 44
    invoke-virtual {p0, v2, v4, v3}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i(IILandroid/animation/Animator;)V

    .line 45
    .line 46
    .line 47
    goto :goto_3

    .line 48
    :cond_2
    invoke-virtual {p0}, Landroid/widget/LinearLayout;->getOrientation()I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    iget-object v4, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 53
    .line 54
    if-nez v4, :cond_3

    .line 55
    .line 56
    move-object v4, v3

    .line 57
    :cond_3
    invoke-virtual {v4}, Lorg/xbet/ui_common/circleindicator/b;->f()I

    .line 58
    .line 59
    .line 60
    move-result v4

    .line 61
    iget-object v5, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 62
    .line 63
    if-nez v5, :cond_4

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_4
    move-object v3, v5

    .line 67
    :goto_2
    invoke-virtual {v3}, Lorg/xbet/ui_common/circleindicator/a;->c()Landroid/animation/Animator;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    invoke-virtual {p0, v2, v4, v3}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i(IILandroid/animation/Animator;)V

    .line 72
    .line 73
    .line 74
    :goto_3
    add-int/lit8 v1, v1, 0x1

    .line 75
    .line 76
    goto :goto_0

    .line 77
    :cond_5
    return-void
.end method

.method public onDetachedFromWindow()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c:Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->removeOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$s;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h:Landroidx/viewpager2/widget/ViewPager2;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;

    .line 15
    .line 16
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->o(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-super {p0}, Landroid/widget/LinearLayout;->onDetachedFromWindow()V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public onRestoreInstanceState(Landroid/os/Parcelable;)V
    .locals 2

    .line 1
    instance-of v0, p1, Landroid/os/Bundle;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Landroid/os/Bundle;

    .line 6
    .line 7
    const-string v0, "BUNDLE_KEY_SUPER"

    .line 8
    .line 9
    invoke-virtual {p1, v0}, Landroid/os/Bundle;->getParcelable(Ljava/lang/String;)Landroid/os/Parcelable;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-super {p0, v0}, Landroid/widget/LinearLayout;->onRestoreInstanceState(Landroid/os/Parcelable;)V

    .line 14
    .line 15
    .line 16
    const-string v0, "BUNDLE_KEY_INFO_POSITION"

    .line 17
    .line 18
    invoke-virtual {p1, v0}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;)I

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_0

    .line 23
    .line 24
    invoke-virtual {p1, v0}, Landroid/os/BaseBundle;->getInt(Ljava/lang/String;)I

    .line 25
    .line 26
    .line 27
    move-result p1

    .line 28
    iput p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->l:I

    .line 29
    .line 30
    :cond_0
    return-void
.end method

.method public onSaveInstanceState()Landroid/os/Parcelable;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroid/os/Bundle;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/os/Bundle;-><init>()V

    .line 4
    .line 5
    .line 6
    iget v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 7
    .line 8
    const/4 v2, -0x1

    .line 9
    if-eq v1, v2, :cond_0

    .line 10
    .line 11
    const-string v2, "BUNDLE_KEY_INFO_POSITION"

    .line 12
    .line 13
    invoke-virtual {v0, v2, v1}, Landroid/os/BaseBundle;->putInt(Ljava/lang/String;I)V

    .line 14
    .line 15
    .line 16
    :cond_0
    const-string v1, "BUNDLE_KEY_SUPER"

    .line 17
    .line 18
    invoke-super {p0}, Landroid/widget/LinearLayout;->onSaveInstanceState()Landroid/os/Parcelable;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {v0, v1, v2}, Landroid/os/Bundle;->putParcelable(Ljava/lang/String;Landroid/os/Parcelable;)V

    .line 23
    .line 24
    .line 25
    return-object v0
.end method

.method public final p(I)I
    .locals 2

    .line 1
    iget v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    if-eq v0, v1, :cond_1

    .line 5
    .line 6
    if-lez v0, :cond_1

    .line 7
    .line 8
    if-ge p1, v0, :cond_0

    .line 9
    .line 10
    add-int/lit8 v0, v0, -0x1

    .line 11
    .line 12
    :cond_0
    return v0

    .line 13
    :cond_1
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 14
    .line 15
    if-eqz p1, :cond_2

    .line 16
    .line 17
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    goto :goto_0

    .line 22
    :cond_2
    const/4 p1, 0x0

    .line 23
    :goto_0
    invoke-virtual {p0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->q(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    return p1
.end method

.method public final q(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I
    .locals 2

    .line 1
    const/4 v0, -0x1

    .line 2
    if-nez p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->f:Landroidx/recyclerview/widget/x;

    .line 6
    .line 7
    if-eqz v1, :cond_1

    .line 8
    .line 9
    invoke-virtual {v1, p1}, Landroidx/recyclerview/widget/x;->findSnapView(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)Landroid/view/View;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    goto :goto_0

    .line 14
    :cond_1
    const/4 v1, 0x0

    .line 15
    :goto_0
    if-nez v1, :cond_2

    .line 16
    .line 17
    return v0

    .line 18
    :cond_2
    invoke-virtual {p1, v1}, Landroidx/recyclerview/widget/RecyclerView$LayoutManager;->getPosition(Landroid/view/View;)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    return p1
.end method

.method public final r(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 11

    .line 1
    sget-object v0, Lpb/m;->CircleIndicator:[I

    .line 2
    .line 3
    invoke-virtual {p1, p2, v0}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[I)Landroid/content/res/TypedArray;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    const/16 v0, 0x8

    .line 8
    .line 9
    const/4 v1, -0x1

    .line 10
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 11
    .line 12
    .line 13
    move-result v4

    .line 14
    const/4 v0, 0x5

    .line 15
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 16
    .line 17
    .line 18
    move-result v5

    .line 19
    const/4 v0, 0x6

    .line 20
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    sget v0, Lpb/b;->scale_with_alpha:I

    .line 25
    .line 26
    const/4 v2, 0x0

    .line 27
    invoke-virtual {p2, v2, v0}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 28
    .line 29
    .line 30
    move-result v6

    .line 31
    const/4 v0, 0x1

    .line 32
    invoke-virtual {p2, v0, v2}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 33
    .line 34
    .line 35
    move-result v7

    .line 36
    const/4 v8, 0x2

    .line 37
    sget v9, Lpb/g;->white_radius:I

    .line 38
    .line 39
    invoke-virtual {p2, v8, v9}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 40
    .line 41
    .line 42
    move-result v8

    .line 43
    const/4 v9, 0x3

    .line 44
    invoke-virtual {p2, v9, v8}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 45
    .line 46
    .line 47
    move-result v9

    .line 48
    const/4 v10, 0x7

    .line 49
    invoke-virtual {p2, v10, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 50
    .line 51
    .line 52
    move-result v10

    .line 53
    if-ne v10, v0, :cond_0

    .line 54
    .line 55
    const/4 v2, 0x1

    .line 56
    :cond_0
    invoke-virtual {p0, v2}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 57
    .line 58
    .line 59
    const/4 v0, 0x4

    .line 60
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 61
    .line 62
    .line 63
    move-result v0

    .line 64
    if-ltz v0, :cond_1

    .line 65
    .line 66
    goto :goto_0

    .line 67
    :cond_1
    const/16 v0, 0x11

    .line 68
    .line 69
    :goto_0
    invoke-virtual {p0, v0}, Landroid/widget/LinearLayout;->setGravity(I)V

    .line 70
    .line 71
    .line 72
    sget-object v2, Lorg/xbet/ui_common/circleindicator/b;->h:Lorg/xbet/ui_common/circleindicator/b$a;

    .line 73
    .line 74
    invoke-virtual/range {v2 .. v9}, Lorg/xbet/ui_common/circleindicator/b$a;->a(IIIIIII)Lorg/xbet/ui_common/circleindicator/b;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    iput-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 79
    .line 80
    sget-object v1, Lorg/xbet/ui_common/circleindicator/a;->e:Lorg/xbet/ui_common/circleindicator/a$a;

    .line 81
    .line 82
    const/4 v2, 0x0

    .line 83
    if-nez v0, :cond_2

    .line 84
    .line 85
    move-object v0, v2

    .line 86
    :cond_2
    invoke-virtual {v0}, Lorg/xbet/ui_common/circleindicator/b;->d()I

    .line 87
    .line 88
    .line 89
    move-result v0

    .line 90
    iget-object v3, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->m:Lorg/xbet/ui_common/circleindicator/b;

    .line 91
    .line 92
    if-nez v3, :cond_3

    .line 93
    .line 94
    goto :goto_1

    .line 95
    :cond_3
    move-object v2, v3

    .line 96
    :goto_1
    invoke-virtual {v2}, Lorg/xbet/ui_common/circleindicator/b;->e()I

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    invoke-virtual {v1, p1, v0, v2}, Lorg/xbet/ui_common/circleindicator/a$a;->b(Landroid/content/Context;II)Lorg/xbet/ui_common/circleindicator/a;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->n:Lorg/xbet/ui_common/circleindicator/a;

    .line 105
    .line 106
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    .line 107
    .line 108
    .line 109
    return-void
.end method

.method public final s(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k:I

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->o()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setRecyclerView(Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/x;)V
    .locals 1
    .param p1    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroidx/recyclerview/widget/x;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->f:Landroidx/recyclerview/widget/x;

    .line 4
    .line 5
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    if-eqz p2, :cond_0

    .line 10
    .line 11
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->d:Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;

    .line 12
    .line 13
    invoke-virtual {p2, v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 14
    .line 15
    .line 16
    const/4 v0, -0x1

    .line 17
    iput v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 18
    .line 19
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 20
    .line 21
    .line 22
    move-result p2

    .line 23
    iput p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k:I

    .line 24
    .line 25
    invoke-virtual {p1}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    invoke-virtual {p0, p2}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->q(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    iput p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 34
    .line 35
    invoke-virtual {p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->o()V

    .line 36
    .line 37
    .line 38
    iget-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c:Lorg/xbet/ui_common/circleindicator/CircleIndicator$f;

    .line 39
    .line 40
    invoke-virtual {p1, p2}, Landroidx/recyclerview/widget/RecyclerView;->addOnScrollListener(Landroidx/recyclerview/widget/RecyclerView$s;)V

    .line 41
    .line 42
    .line 43
    :cond_0
    return-void
.end method

.method public final setViewPager(Landroidx/viewpager/widget/ViewPager;)V
    .locals 2
    .param p1    # Landroidx/viewpager/widget/ViewPager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Landroidx/viewpager/widget/ViewPager;->getAdapter()Landroidx/viewpager/widget/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    const/4 v1, -0x1

    .line 8
    iput v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 9
    .line 10
    invoke-virtual {v0}, Landroidx/viewpager/widget/a;->e()I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    iput v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k:I

    .line 15
    .line 16
    invoke-virtual {p1}, Landroidx/viewpager/widget/ViewPager;->getCurrentItem()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    iput v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 21
    .line 22
    invoke-virtual {p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->o()V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;

    .line 26
    .line 27
    invoke-virtual {p1, v0}, Landroidx/viewpager/widget/ViewPager;->I(Landroidx/viewpager/widget/ViewPager$i;)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;

    .line 31
    .line 32
    invoke-virtual {p1, v0}, Landroidx/viewpager/widget/ViewPager;->c(Landroidx/viewpager/widget/ViewPager$i;)V

    .line 33
    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;

    .line 36
    .line 37
    invoke-virtual {p1}, Landroidx/viewpager/widget/ViewPager;->getCurrentItem()I

    .line 38
    .line 39
    .line 40
    move-result p1

    .line 41
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$e;->onPageSelected(I)V

    .line 42
    .line 43
    .line 44
    :cond_0
    return-void
.end method

.method public final setViewPager2(Landroidx/viewpager2/widget/ViewPager2;)V
    .locals 2
    .param p1    # Landroidx/viewpager2/widget/ViewPager2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h:Landroidx/viewpager2/widget/ViewPager2;

    .line 2
    .line 3
    invoke-virtual {p1}, Landroidx/viewpager2/widget/ViewPager2;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->e:Lorg/xbet/ui_common/circleindicator/CircleIndicator$c;

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->registerAdapterDataObserver(Landroidx/recyclerview/widget/RecyclerView$i;)V

    .line 12
    .line 13
    .line 14
    const/4 v1, -0x1

    .line 15
    iput v1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->i:I

    .line 16
    .line 17
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    iput v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->k:I

    .line 22
    .line 23
    invoke-virtual {p1}, Landroidx/viewpager2/widget/ViewPager2;->getCurrentItem()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    iput v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->j:I

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->o()V

    .line 30
    .line 31
    .line 32
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;

    .line 33
    .line 34
    invoke-virtual {p1, v0}, Landroidx/viewpager2/widget/ViewPager2;->h(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 35
    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;

    .line 38
    .line 39
    invoke-virtual {p1}, Landroidx/viewpager2/widget/ViewPager2;->getCurrentItem()I

    .line 40
    .line 41
    .line 42
    move-result p1

    .line 43
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;->onPageSelected(I)V

    .line 44
    .line 45
    .line 46
    :cond_0
    return-void
.end method
