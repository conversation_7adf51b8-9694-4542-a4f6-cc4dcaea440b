.class public final Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u0000 &2\u00020\u0001:\u0001\u001bB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\u000b\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001f\u0010\u000f\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J7\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0015\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u0019\u00a2\u0006\u0004\u0008\u001b\u0010\u001cR\u0014\u0010\u001f\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001eR\u0014\u0010!\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010\u001eR\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$\u00a8\u0006\'"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "onAttachedToWindow",
        "()V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "",
        "amount",
        "a",
        "(Ljava/lang/String;)V",
        "Landroid/widget/ImageView;",
        "Landroid/widget/ImageView;",
        "imageBack",
        "b",
        "imageScore",
        "Landroid/widget/TextView;",
        "c",
        "Landroid/widget/TextView;",
        "jackpotText",
        "d",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->d:Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Landroid/widget/ImageView;

    invoke-direct {p2, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 6
    sget p3, Lpb/g;->tv_bet_back:I

    invoke-static {p1, p3}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p3

    invoke-virtual {p2, p3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 7
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 8
    new-instance p2, Landroid/widget/ImageView;

    invoke-direct {p2, p1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 9
    sget p3, Lpb/g;->tv_bet_score:I

    invoke-static {p1, p3}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p3

    invoke-virtual {p2, p3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 10
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 11
    new-instance p2, Landroid/widget/TextView;

    invoke-direct {p2, p1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    const/16 p3, 0x11

    .line 12
    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setGravity(I)V

    const/4 p3, 0x2

    const/16 v0, 0x10

    .line 13
    invoke-static {p2, p3, v0, p3, p3}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 14
    sget p3, Lpb/e;->white:I

    invoke-static {p1, p3}, LF0/b;->getColor(Landroid/content/Context;I)I

    move-result p1

    invoke-virtual {p2, p1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 15
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->c:Landroid/widget/TextView;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->c:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public onAttachedToWindow()V
    .locals 1

    .line 1
    invoke-super {p0}, Landroid/widget/FrameLayout;->onAttachedToWindow()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 5
    .line 6
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->c:Landroid/widget/TextView;

    .line 15
    .line 16
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 5

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    move-object p1, p0

    .line 5
    iget-object p2, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 6
    .line 7
    invoke-virtual {p2}, Landroid/view/View;->getWidth()I

    .line 8
    .line 9
    .line 10
    move-result p2

    .line 11
    int-to-float p2, p2

    .line 12
    const p3, 0x3f051eb8

    .line 13
    .line 14
    .line 15
    mul-float p2, p2, p3

    .line 16
    .line 17
    iget-object p3, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 18
    .line 19
    invoke-virtual {p3}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 20
    .line 21
    .line 22
    move-result-object p3

    .line 23
    invoke-virtual {p3}, Landroid/graphics/drawable/Drawable;->getIntrinsicHeight()I

    .line 24
    .line 25
    .line 26
    move-result p3

    .line 27
    int-to-float p3, p3

    .line 28
    iget-object p4, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 29
    .line 30
    invoke-virtual {p4}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 31
    .line 32
    .line 33
    move-result-object p4

    .line 34
    invoke-virtual {p4}, Landroid/graphics/drawable/Drawable;->getIntrinsicWidth()I

    .line 35
    .line 36
    .line 37
    move-result p4

    .line 38
    int-to-float p4, p4

    .line 39
    div-float/2addr p3, p4

    .line 40
    mul-float p3, p3, p2

    .line 41
    .line 42
    iget-object p4, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 43
    .line 44
    invoke-virtual {p4}, Landroid/view/View;->getWidth()I

    .line 45
    .line 46
    .line 47
    move-result p4

    .line 48
    int-to-float p4, p4

    .line 49
    const p5, 0x3e75c28f

    .line 50
    .line 51
    .line 52
    mul-float p4, p4, p5

    .line 53
    .line 54
    add-float p5, p4, p2

    .line 55
    .line 56
    iget-object v0, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 57
    .line 58
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    int-to-float v0, v0

    .line 63
    const v1, 0x3f11eb85

    .line 64
    .line 65
    .line 66
    mul-float v0, v0, v1

    .line 67
    .line 68
    add-float v1, v0, p3

    .line 69
    .line 70
    const v2, 0x3f68f5c3

    .line 71
    .line 72
    .line 73
    mul-float v2, v2, p2

    .line 74
    .line 75
    const/high16 v3, 0x3f000000    # 0.5f

    .line 76
    .line 77
    mul-float v3, v3, p3

    .line 78
    .line 79
    sub-float/2addr p2, v2

    .line 80
    const/4 v4, 0x2

    .line 81
    int-to-float v4, v4

    .line 82
    div-float/2addr p2, v4

    .line 83
    add-float/2addr p2, p4

    .line 84
    add-float/2addr v2, p2

    .line 85
    sub-float/2addr p3, v3

    .line 86
    div-float/2addr p3, v4

    .line 87
    add-float/2addr p3, v0

    .line 88
    add-float/2addr v3, p3

    .line 89
    iget-object v4, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 90
    .line 91
    float-to-int p4, p4

    .line 92
    float-to-int v0, v0

    .line 93
    float-to-int p5, p5

    .line 94
    float-to-int v1, v1

    .line 95
    invoke-virtual {v4, p4, v0, p5, v1}, Landroid/view/View;->layout(IIII)V

    .line 96
    .line 97
    .line 98
    iget-object p4, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->c:Landroid/widget/TextView;

    .line 99
    .line 100
    float-to-int p2, p2

    .line 101
    float-to-int p3, p3

    .line 102
    float-to-int p5, v2

    .line 103
    float-to-int v0, v3

    .line 104
    invoke-virtual {p4, p2, p3, p5, v0}, Landroid/view/View;->layout(IIII)V

    .line 105
    .line 106
    .line 107
    return-void
.end method

.method public onMeasure(II)V
    .locals 6

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 5
    .line 6
    invoke-virtual {p2}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 7
    .line 8
    .line 9
    move-result-object p2

    .line 10
    invoke-virtual {p2}, Landroid/graphics/drawable/Drawable;->getIntrinsicHeight()I

    .line 11
    .line 12
    .line 13
    move-result p2

    .line 14
    int-to-float p2, p2

    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 16
    .line 17
    invoke-virtual {v0}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Landroid/graphics/drawable/Drawable;->getIntrinsicWidth()I

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    int-to-float v0, v0

    .line 26
    div-float/2addr p2, v0

    .line 27
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    int-to-float v0, v0

    .line 32
    mul-float p2, p2, v0

    .line 33
    .line 34
    float-to-int p2, p2

    .line 35
    const/high16 v0, 0x40000000    # 2.0f

    .line 36
    .line 37
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 38
    .line 39
    .line 40
    move-result p2

    .line 41
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    int-to-float v1, v1

    .line 46
    const v2, 0x3f051eb8

    .line 47
    .line 48
    .line 49
    mul-float v1, v1, v2

    .line 50
    .line 51
    float-to-int v1, v1

    .line 52
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 53
    .line 54
    invoke-virtual {v2}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    invoke-virtual {v2}, Landroid/graphics/drawable/Drawable;->getIntrinsicHeight()I

    .line 59
    .line 60
    .line 61
    move-result v2

    .line 62
    int-to-float v2, v2

    .line 63
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 64
    .line 65
    invoke-virtual {v3}, Landroid/view/View;->getBackground()Landroid/graphics/drawable/Drawable;

    .line 66
    .line 67
    .line 68
    move-result-object v3

    .line 69
    invoke-virtual {v3}, Landroid/graphics/drawable/Drawable;->getIntrinsicWidth()I

    .line 70
    .line 71
    .line 72
    move-result v3

    .line 73
    int-to-float v3, v3

    .line 74
    div-float/2addr v2, v3

    .line 75
    int-to-float v3, v1

    .line 76
    mul-float v2, v2, v3

    .line 77
    .line 78
    float-to-int v2, v2

    .line 79
    invoke-static {v1, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 80
    .line 81
    .line 82
    move-result v1

    .line 83
    invoke-static {v2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 84
    .line 85
    .line 86
    move-result v4

    .line 87
    const v5, 0x3f68f5c3

    .line 88
    .line 89
    .line 90
    mul-float v3, v3, v5

    .line 91
    .line 92
    float-to-int v3, v3

    .line 93
    int-to-float v2, v2

    .line 94
    const/high16 v5, 0x3f000000    # 0.5f

    .line 95
    .line 96
    mul-float v2, v2, v5

    .line 97
    .line 98
    float-to-int v2, v2

    .line 99
    invoke-static {v3, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 100
    .line 101
    .line 102
    move-result v3

    .line 103
    invoke-static {v2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 104
    .line 105
    .line 106
    move-result v0

    .line 107
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->a:Landroid/widget/ImageView;

    .line 108
    .line 109
    invoke-virtual {v2, p1, p2}, Landroid/view/View;->measure(II)V

    .line 110
    .line 111
    .line 112
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->b:Landroid/widget/ImageView;

    .line 113
    .line 114
    invoke-virtual {v2, v1, v4}, Landroid/view/View;->measure(II)V

    .line 115
    .line 116
    .line 117
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/custom/TvJackpotView;->c:Landroid/widget/TextView;

    .line 118
    .line 119
    invoke-virtual {v1, v3, v0}, Landroid/view/View;->measure(II)V

    .line 120
    .line 121
    .line 122
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 123
    .line 124
    .line 125
    return-void
.end method
