.class public final LN81/e$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN81/q;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN81/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Lk8/d;

.field public final b:LN81/e$a;


# direct methods
.method public constructor <init>(Lk8/d;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LN81/e$a;->b:LN81/e$a;

    .line 4
    iput-object p1, p0, LN81/e$a;->a:Lk8/d;

    return-void
.end method

.method public synthetic constructor <init>(Lk8/d;LN81/f;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, LN81/e$a;-><init>(Lk8/d;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskNotificationWorker;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LN81/e$a;->d(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskNotificationWorker;)Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskNotificationWorker;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskFinishingNotificationWorker;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LN81/e$a;->c(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskFinishingNotificationWorker;)Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskFinishingNotificationWorker;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskFinishingNotificationWorker;)Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskFinishingNotificationWorker;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LN81/e$a;->a:Lk8/d;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/workers/a;->a(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskFinishingNotificationWorker;Lk8/d;)V

    .line 4
    .line 5
    .line 6
    return-object p1
.end method

.method public final d(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskNotificationWorker;)Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskNotificationWorker;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LN81/e$a;->a:Lk8/d;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/workers/b;->a(Lorg/xplatform/aggregator/daily_tasks/impl/workers/DailyTaskNotificationWorker;Lk8/d;)V

    .line 4
    .line 5
    .line 6
    return-object p1
.end method
