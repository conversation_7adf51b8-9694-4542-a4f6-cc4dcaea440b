.class final Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.promo.fragment.PromoMakeBetDsFragment$onObserveData$2"
    f = "PromoMakeBetDsFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
        "action",
        "",
        "<anonymous>",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->invoke(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_7

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;

    .line 14
    .line 15
    sget-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;

    .line 16
    .line 17
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 24
    .line 25
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->E2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V

    .line 26
    .line 27
    .line 28
    goto :goto_1

    .line 29
    :cond_0
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;

    .line 30
    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 34
    .line 35
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;

    .line 36
    .line 37
    invoke-static {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;)V

    .line 38
    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;

    .line 42
    .line 43
    if-eqz v0, :cond_2

    .line 44
    .line 45
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 46
    .line 47
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;

    .line 48
    .line 49
    invoke-static {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->F2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;)V

    .line 50
    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_2
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;

    .line 54
    .line 55
    if-eqz v0, :cond_3

    .line 56
    .line 57
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 58
    .line 59
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;

    .line 60
    .line 61
    invoke-static {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->H2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;)V

    .line 62
    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_3
    instance-of p1, p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$a;

    .line 66
    .line 67
    if-eqz p1, :cond_6

    .line 68
    .line 69
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 70
    .line 71
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 72
    .line 73
    .line 74
    move-result-object p1

    .line 75
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 76
    .line 77
    if-eqz v0, :cond_4

    .line 78
    .line 79
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 80
    .line 81
    goto :goto_0

    .line 82
    :cond_4
    const/4 p1, 0x0

    .line 83
    :goto_0
    if-eqz p1, :cond_5

    .line 84
    .line 85
    const/4 v0, 0x1

    .line 86
    invoke-virtual {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 87
    .line 88
    .line 89
    :cond_5
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 90
    .line 91
    const/4 v0, 0x0

    .line 92
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->D2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Z)V

    .line 93
    .line 94
    .line 95
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 96
    .line 97
    return-object p1

    .line 98
    :cond_6
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 99
    .line 100
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 101
    .line 102
    .line 103
    throw p1

    .line 104
    :cond_7
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 105
    .line 106
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 107
    .line 108
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 109
    .line 110
    .line 111
    throw p1
.end method
