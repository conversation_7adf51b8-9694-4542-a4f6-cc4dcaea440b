.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0002\u0008\n\u0008\u0087\u0008\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\u0008H\u00d6\u0001\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0010\u0010\u000c\u001a\u00020\u000bH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u001a\u0010\u0010\u001a\u00020\u00042\u0008\u0010\u000f\u001a\u0004\u0018\u00010\u000eH\u00d6\u0003\u00a2\u0006\u0004\u0008\u0010\u0010\u0011R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0012\u0010\u0013\u001a\u0004\u0008\u0014\u0010\u0015R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0016\u001a\u0004\u0008\u0012\u0010\u0017\u00a8\u0006\u0018"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
        "LvX0/e;",
        "betInput",
        "",
        "betEnable",
        "<init>",
        "(LvX0/e;Z)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "LvX0/e;",
        "b",
        "()LvX0/e;",
        "Z",
        "()Z",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final c:I


# instance fields
.field public final a:LvX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget v0, LvX0/e;->b:I

    sput v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->c:I

    return-void
.end method

.method public constructor <init>(LvX0/e;Z)V
    .locals 0
    .param p1    # LvX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a:LvX0/e;

    .line 5
    .line 6
    iput-boolean p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b:Z

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b:Z

    .line 2
    .line 3
    return v0
.end method

.method public final b()LvX0/e;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a:LvX0/e;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a:LvX0/e;

    iget-object v3, p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a:LvX0/e;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-boolean v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b:Z

    iget-boolean p1, p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b:Z

    if-eq v1, p1, :cond_3

    return v2

    :cond_3
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a:LvX0/e;

    invoke-virtual {v0}, LvX0/e;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a:LvX0/e;

    iget-boolean v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b:Z

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "ChangeBetInputState(betInput="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", betEnable="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
