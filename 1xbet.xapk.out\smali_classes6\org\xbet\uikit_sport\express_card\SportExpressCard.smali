.class public final Lorg/xbet/uikit_sport/express_card/SportExpressCard;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/express_card/SportExpressCard$a;,
        Lorg/xbet/uikit_sport/express_card/SportExpressCard$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009e\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0010\u000e\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u0007\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010!\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0007\u0018\u0000 m2\u00020\u0001:\u0001AB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u000f\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0010\u001a\u00020\u000fH\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001f\u0010\u0016\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J7\u0010\u001e\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u001d\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0015\u0010\"\u001a\u00020\u00112\u0006\u0010!\u001a\u00020 \u00a2\u0006\u0004\u0008\"\u0010#J\u0015\u0010%\u001a\u00020\u00112\u0006\u0010$\u001a\u00020 \u00a2\u0006\u0004\u0008%\u0010#J\u0015\u0010\'\u001a\u00020\u00112\u0006\u0010&\u001a\u00020 \u00a2\u0006\u0004\u0008\'\u0010#J\u001b\u0010*\u001a\u00020\u00112\u000c\u0010)\u001a\u0008\u0012\u0004\u0012\u00020\u00110(\u00a2\u0006\u0004\u0008*\u0010+J\u001b\u0010,\u001a\u00020\u00112\u000c\u0010)\u001a\u0008\u0012\u0004\u0012\u00020\u00110(\u00a2\u0006\u0004\u0008,\u0010+J\u001b\u00100\u001a\u00020\u00112\u000c\u0010/\u001a\u0008\u0012\u0004\u0012\u00020.0-\u00a2\u0006\u0004\u00080\u00101J\u0015\u00104\u001a\u00020\u00112\u0006\u00103\u001a\u000202\u00a2\u0006\u0004\u00084\u00105J\u0017\u00108\u001a\u0002072\u0006\u00106\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u00088\u00109J\u001f\u0010<\u001a\u00020\u00112\u0006\u0010:\u001a\u0002072\u0006\u0010;\u001a\u00020.H\u0002\u00a2\u0006\u0004\u0008<\u0010=J\u000f\u0010>\u001a\u000207H\u0002\u00a2\u0006\u0004\u0008>\u0010?R\u0014\u0010C\u001a\u00020@8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010G\u001a\u00020D8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010J\u001a\u00020\u00018\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0016\u0010N\u001a\u00020K8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0016\u0010Q\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0016\u0010R\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010PR\u0014\u0010T\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010PR\u0014\u0010U\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010PR\u0014\u0010V\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010PR\u0014\u0010W\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010PR\u0014\u0010Z\u001a\u00020X8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u0010YR\u0014\u0010^\u001a\u00020[8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u001a\u0010b\u001a\u0008\u0012\u0004\u0012\u0002070_8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u001c\u0010/\u001a\u0008\u0012\u0004\u0012\u00020.0-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008c\u0010aR\u0016\u00103\u001a\u0002028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u0016\u0010i\u001a\u00020f8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u001c\u0010l\u001a\u0008\u0012\u0004\u0012\u00020\u00110(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008j\u0010k\u00a8\u0006n"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/express_card/SportExpressCard;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "getHeightByStyle",
        "()I",
        "Lu31/b;",
        "getMarketStyle",
        "()Lu31/b;",
        "Landroid/graphics/Canvas;",
        "canvas",
        "",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "",
        "title",
        "setHeaderTitle",
        "(Ljava/lang/String;)V",
        "coef",
        "setCoefficient",
        "description",
        "setMarketDescription",
        "Lkotlin/Function0;",
        "listener",
        "f",
        "(Lkotlin/jvm/functions/Function0;)V",
        "h",
        "",
        "LG31/b;",
        "items",
        "setSportEventList",
        "(Ljava/util/List;)V",
        "Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;",
        "style",
        "setStyle",
        "(Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;)V",
        "index",
        "LG31/a;",
        "k",
        "(I)LG31/a;",
        "view",
        "item",
        "j",
        "(LG31/a;LG31/b;)V",
        "i",
        "()LG31/a;",
        "Landroid/widget/TextView;",
        "a",
        "Landroid/widget/TextView;",
        "headerTextView",
        "Landroidx/compose/ui/platform/ComposeView;",
        "b",
        "Landroidx/compose/ui/platform/ComposeView;",
        "market",
        "c",
        "Landroid/widget/FrameLayout;",
        "sportEventLayout",
        "",
        "d",
        "F",
        "cornerRadius",
        "e",
        "I",
        "headerHeight",
        "eventHeight",
        "g",
        "padding4",
        "padding6",
        "padding8",
        "padding12",
        "Landroid/graphics/Paint;",
        "Landroid/graphics/Paint;",
        "backgroundPaint",
        "Landroid/graphics/RectF;",
        "l",
        "Landroid/graphics/RectF;",
        "roundedRectangleBackground",
        "",
        "m",
        "Ljava/util/List;",
        "itemViews",
        "n",
        "o",
        "Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;",
        "Lu31/a$b;",
        "p",
        "Lu31/a$b;",
        "marketModel",
        "q",
        "Lkotlin/jvm/functions/Function0;",
        "onClick",
        "r",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final r:Lorg/xbet/uikit_sport/express_card/SportExpressCard$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final s:I


# instance fields
.field public final a:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroidx/compose/ui/platform/ComposeView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/widget/FrameLayout;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:F

.field public e:I

.field public f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LG31/a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public n:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LG31/b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public p:Lu31/a$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public q:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/express_card/SportExpressCard$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->r:Lorg/xbet/uikit_sport/express_card/SportExpressCard$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->s:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 10
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v0, Landroidx/compose/ui/platform/ComposeView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Landroidx/compose/ui/platform/ComposeView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->b:Landroidx/compose/ui/platform/ComposeView;

    .line 6
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_16:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    int-to-float p1, p1

    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d:F

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_30:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_58:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->f:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_6:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->h:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_8:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {p3, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->j:I

    .line 13
    new-instance p3, Landroid/graphics/Paint;

    const/4 v2, 0x1

    invoke-direct {p3, v2}, Landroid/graphics/Paint;-><init>(I)V

    .line 14
    sget v3, LlZ0/d;->uikitBackgroundContent:I

    const/4 v4, 0x0

    const/4 v5, 0x2

    invoke-static {v1, v3, v4, v5, v4}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v3

    invoke-virtual {p3, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 15
    iput-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->k:Landroid/graphics/Paint;

    .line 16
    new-instance p3, Landroid/graphics/RectF;

    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    move-result v3

    int-to-float v3, v3

    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    move-result v4

    int-to-float v4, v4

    const/4 v5, 0x0

    invoke-direct {p3, v5, v5, v3, v4}, Landroid/graphics/RectF;-><init>(FFFF)V

    iput-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->l:Landroid/graphics/RectF;

    .line 17
    new-instance p3, Ljava/util/ArrayList;

    invoke-direct {p3}, Ljava/util/ArrayList;-><init>()V

    iput-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 18
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->n:Ljava/util/List;

    .line 19
    sget-object p3, Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;->LARGE:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    iput-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 20
    new-instance v3, Lu31/a$b;

    .line 21
    sget-object v6, Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;->DEFAULT:Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;

    const/4 v8, 0x0

    const/4 v9, 0x0

    .line 22
    const-string v4, ""

    const-string v5, ""

    const/4 v7, 0x0

    invoke-direct/range {v3 .. v9}, Lu31/a$b;-><init>(Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;ZZZ)V

    iput-object v3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->p:Lu31/a$b;

    .line 23
    new-instance p3, LG31/c;

    invoke-direct {p3}, LG31/c;-><init>()V

    iput-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->q:Lkotlin/jvm/functions/Function0;

    const/4 p3, 0x0

    .line 24
    invoke-virtual {p0, p2, p3, p2, p2}, Landroid/view/View;->setPadding(IIII)V

    const/4 v3, 0x3

    .line 25
    invoke-virtual {p0, v3}, Landroid/view/View;->setLayoutDirection(I)V

    .line 26
    new-instance v4, Landroid/widget/TextView;

    invoke-direct {v4, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 27
    invoke-virtual {v4, p3, p2, p3, p1}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 28
    invoke-virtual {v4, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 29
    sget-object p1, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v4, p1}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 30
    invoke-virtual {v4, v3}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 p1, 0x5

    .line 31
    invoke-virtual {v4, p1}, Landroid/view/View;->setTextDirection(I)V

    .line 32
    sget p1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {v4, p1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 33
    iput-object v4, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 34
    new-instance p1, Landroid/widget/FrameLayout;

    invoke-direct {p1, v1}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 35
    invoke-virtual {p0, v4}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 36
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 37
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 38
    invoke-virtual {p0, p3}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic b()Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->l()Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method

.method public static final synthetic c(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)Lu31/a$b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->p:Lu31/a$b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)Lu31/b;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->getMarketStyle()Lu31/b;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic e(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)Lkotlin/jvm/functions/Function0;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->q:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final g(Lkotlin/jvm/functions/Function0;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final getHeightByStyle()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/express_card/SportExpressCard$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_3

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    if-eq v0, v1, :cond_2

    .line 16
    .line 17
    const/4 v1, 0x3

    .line 18
    if-eq v0, v1, :cond_1

    .line 19
    .line 20
    const/4 v1, 0x4

    .line 21
    if-ne v0, v1, :cond_0

    .line 22
    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    sget v1, LlZ0/g;->size_152:I

    .line 28
    .line 29
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    return v0

    .line 34
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 35
    .line 36
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 37
    .line 38
    .line 39
    throw v0

    .line 40
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    sget v1, LlZ0/g;->size_148:I

    .line 45
    .line 46
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 47
    .line 48
    .line 49
    move-result v0

    .line 50
    return v0

    .line 51
    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    sget v1, LlZ0/g;->size_132:I

    .line 56
    .line 57
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    return v0

    .line 62
    :cond_3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    sget v1, LlZ0/g;->size_138:I

    .line 67
    .line 68
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 69
    .line 70
    .line 71
    move-result v0

    .line 72
    return v0
.end method

.method private final getMarketStyle()Lu31/b;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/express_card/SportExpressCard$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_3

    .line 13
    .line 14
    const/4 v2, 0x2

    .line 15
    if-eq v0, v2, :cond_2

    .line 16
    .line 17
    const/4 v3, 0x3

    .line 18
    if-eq v0, v3, :cond_1

    .line 19
    .line 20
    const/4 v2, 0x4

    .line 21
    if-ne v0, v2, :cond_0

    .line 22
    .line 23
    new-instance v0, Lu31/b$a;

    .line 24
    .line 25
    invoke-direct {v0, v1}, Lu31/b$a;-><init>(I)V

    .line 26
    .line 27
    .line 28
    return-object v0

    .line 29
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 30
    .line 31
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 32
    .line 33
    .line 34
    throw v0

    .line 35
    :cond_1
    new-instance v0, Lu31/b$c;

    .line 36
    .line 37
    const/4 v3, 0x0

    .line 38
    invoke-direct {v0, v1, v3, v2, v3}, Lu31/b$c;-><init>(ILu31/b$c$a;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 39
    .line 40
    .line 41
    return-object v0

    .line 42
    :cond_2
    new-instance v0, Lu31/b$d;

    .line 43
    .line 44
    invoke-direct {v0, v1}, Lu31/b$d;-><init>(I)V

    .line 45
    .line 46
    .line 47
    return-object v0

    .line 48
    :cond_3
    new-instance v0, Lu31/b$b;

    .line 49
    .line 50
    invoke-direct {v0, v1}, Lu31/b$b;-><init>(I)V

    .line 51
    .line 52
    .line 53
    return-object v0
.end method

.method public static final l()Lkotlin/Unit;
    .locals 1

    .line 1
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public final f(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, LG31/d;

    .line 2
    .line 3
    invoke-direct {v0, p1}, LG31/d;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final h(Lkotlin/jvm/functions/Function0;)V
    .locals 0
    .param p1    # Lkotlin/jvm/functions/Function0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->q:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-void
.end method

.method public final i()LG31/a;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/express_card/SportExpressCard$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-eq v0, v1, :cond_3

    .line 13
    .line 14
    const/4 v1, 0x2

    .line 15
    if-eq v0, v1, :cond_2

    .line 16
    .line 17
    const/4 v1, 0x3

    .line 18
    if-eq v0, v1, :cond_1

    .line 19
    .line 20
    const/4 v1, 0x4

    .line 21
    if-ne v0, v1, :cond_0

    .line 22
    .line 23
    new-instance v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;

    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    sget-object v2, Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;->RECTANGLE:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 30
    .line 31
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V

    .line 32
    .line 33
    .line 34
    return-object v0

    .line 35
    :cond_0
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 36
    .line 37
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 38
    .line 39
    .line 40
    throw v0

    .line 41
    :cond_1
    new-instance v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;

    .line 42
    .line 43
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    sget-object v2, Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;->CIRCLE:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 48
    .line 49
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V

    .line 50
    .line 51
    .line 52
    return-object v0

    .line 53
    :cond_2
    new-instance v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;

    .line 54
    .line 55
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    sget-object v2, Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;->SMALL:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 60
    .line 61
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V

    .line 62
    .line 63
    .line 64
    return-object v0

    .line 65
    :cond_3
    new-instance v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;

    .line 66
    .line 67
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    sget-object v2, Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;->LARGE:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 72
    .line 73
    invoke-direct {v0, v1, v2}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V

    .line 74
    .line 75
    .line 76
    return-object v0
.end method

.method public final j(LG31/a;LG31/b;)V
    .locals 2

    .line 1
    move-object v0, p1

    .line 2
    check-cast v0, Landroid/view/View;

    .line 3
    .line 4
    const/4 v1, 0x0

    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p2}, LG31/b;->a()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-interface {p1, v0}, LG31/a;->setCoef(Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p2}, LG31/b;->b()Lorg/xbet/uikit_sport/express_card/a;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    instance-of v1, v0, Lorg/xbet/uikit_sport/express_card/a$a;

    .line 20
    .line 21
    if-eqz v1, :cond_0

    .line 22
    .line 23
    invoke-virtual {p2}, LG31/b;->b()Lorg/xbet/uikit_sport/express_card/a;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    check-cast p2, Lorg/xbet/uikit_sport/express_card/a$a;

    .line 28
    .line 29
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/express_card/a$a;->a()I

    .line 30
    .line 31
    .line 32
    move-result p2

    .line 33
    invoke-interface {p1, p2}, LG31/a;->setSportIcon(I)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_0
    instance-of v0, v0, Lorg/xbet/uikit_sport/express_card/a$b;

    .line 38
    .line 39
    if-eqz v0, :cond_1

    .line 40
    .line 41
    invoke-virtual {p2}, LG31/b;->b()Lorg/xbet/uikit_sport/express_card/a;

    .line 42
    .line 43
    .line 44
    move-result-object p2

    .line 45
    check-cast p2, Lorg/xbet/uikit_sport/express_card/a$b;

    .line 46
    .line 47
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/express_card/a$b;->a()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    invoke-interface {p1, p2}, LG31/a;->a(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    return-void

    .line 55
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 56
    .line 57
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 58
    .line 59
    .line 60
    throw p1
.end method

.method public final k(I)LG31/a;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    check-cast p1, LG31/a;

    .line 8
    .line 9
    if-nez p1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i()LG31/a;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 16
    .line 17
    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 21
    .line 22
    move-object v1, p1

    .line 23
    check-cast v1, Landroid/view/View;

    .line 24
    .line 25
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 26
    .line 27
    .line 28
    :cond_0
    return-object p1
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->l:Landroid/graphics/RectF;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d:F

    .line 4
    .line 5
    iget-object v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->k:Landroid/graphics/Paint;

    .line 6
    .line 7
    invoke-virtual {p1, v0, v1, v1, v2}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 8
    .line 9
    .line 10
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onDraw(Landroid/graphics/Canvas;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    const/4 p2, 0x0

    .line 6
    const/4 p3, 0x1

    .line 7
    if-ne p1, p3, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    goto :goto_0

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getPaddingTop()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    iget-object v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 17
    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    iget v3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g:I

    .line 23
    .line 24
    add-int/2addr v2, v3

    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 26
    .line 27
    .line 28
    move-result v3

    .line 29
    iget v4, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->j:I

    .line 30
    .line 31
    sub-int/2addr v3, v4

    .line 32
    iget-object v4, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 33
    .line 34
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 35
    .line 36
    .line 37
    move-result v4

    .line 38
    add-int/2addr v4, v0

    .line 39
    invoke-virtual {v1, v2, v0, v3, v4}, Landroid/view/View;->layout(IIII)V

    .line 40
    .line 41
    .line 42
    iget-object v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 43
    .line 44
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    add-int/2addr v0, v1

    .line 49
    iget-object v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 50
    .line 51
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 52
    .line 53
    .line 54
    move-result v2

    .line 55
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 56
    .line 57
    .line 58
    move-result v3

    .line 59
    sub-int/2addr p4, v3

    .line 60
    iget-object v3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 61
    .line 62
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 63
    .line 64
    .line 65
    move-result v3

    .line 66
    add-int/2addr v3, v0

    .line 67
    invoke-virtual {v1, v2, v0, p4, v3}, Landroid/view/View;->layout(IIII)V

    .line 68
    .line 69
    .line 70
    iget-object p4, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 71
    .line 72
    new-instance v1, Ljava/util/ArrayList;

    .line 73
    .line 74
    const/16 v2, 0xa

    .line 75
    .line 76
    invoke-static {p4, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 77
    .line 78
    .line 79
    move-result v2

    .line 80
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    .line 81
    .line 82
    .line 83
    invoke-interface {p4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 84
    .line 85
    .line 86
    move-result-object p4

    .line 87
    :goto_1
    invoke-interface {p4}, Ljava/util/Iterator;->hasNext()Z

    .line 88
    .line 89
    .line 90
    move-result v2

    .line 91
    if-eqz v2, :cond_1

    .line 92
    .line 93
    invoke-interface {p4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    check-cast v2, LG31/a;

    .line 98
    .line 99
    check-cast v2, Landroid/view/View;

    .line 100
    .line 101
    invoke-interface {v1, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    goto :goto_1

    .line 105
    :cond_1
    new-instance p4, Ljava/util/ArrayList;

    .line 106
    .line 107
    invoke-direct {p4}, Ljava/util/ArrayList;-><init>()V

    .line 108
    .line 109
    .line 110
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 111
    .line 112
    .line 113
    move-result-object v1

    .line 114
    :cond_2
    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 115
    .line 116
    .line 117
    move-result v2

    .line 118
    if-eqz v2, :cond_3

    .line 119
    .line 120
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object v2

    .line 124
    move-object v3, v2

    .line 125
    check-cast v3, Landroid/view/View;

    .line 126
    .line 127
    invoke-virtual {v3}, Landroid/view/View;->getVisibility()I

    .line 128
    .line 129
    .line 130
    move-result v3

    .line 131
    if-nez v3, :cond_2

    .line 132
    .line 133
    invoke-interface {p4, v2}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 134
    .line 135
    .line 136
    goto :goto_2

    .line 137
    :cond_3
    if-eqz p1, :cond_4

    .line 138
    .line 139
    invoke-static {p4}, Lkotlin/collections/CollectionsKt;->d1(Ljava/lang/Iterable;)Ljava/util/List;

    .line 140
    .line 141
    .line 142
    move-result-object p4

    .line 143
    :cond_4
    invoke-interface {p4}, Ljava/util/Collection;->isEmpty()Z

    .line 144
    .line 145
    .line 146
    move-result p1

    .line 147
    if-nez p1, :cond_5

    .line 148
    .line 149
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 150
    .line 151
    .line 152
    move-result p1

    .line 153
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 154
    .line 155
    .line 156
    move-result v1

    .line 157
    sub-int/2addr p1, v1

    .line 158
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 159
    .line 160
    .line 161
    move-result v1

    .line 162
    sub-int/2addr p1, v1

    .line 163
    iget v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g:I

    .line 164
    .line 165
    invoke-interface {p4}, Ljava/util/List;->size()I

    .line 166
    .line 167
    .line 168
    move-result v2

    .line 169
    sub-int/2addr v2, p3

    .line 170
    mul-int v1, v1, v2

    .line 171
    .line 172
    sub-int/2addr p1, v1

    .line 173
    invoke-interface {p4}, Ljava/util/List;->size()I

    .line 174
    .line 175
    .line 176
    move-result p3

    .line 177
    div-int/2addr p1, p3

    .line 178
    iget-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 179
    .line 180
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 181
    .line 182
    .line 183
    move-result p3

    .line 184
    invoke-interface {p4}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 185
    .line 186
    .line 187
    move-result-object p4

    .line 188
    const/4 v1, 0x0

    .line 189
    :goto_3
    invoke-interface {p4}, Ljava/util/Iterator;->hasNext()Z

    .line 190
    .line 191
    .line 192
    move-result v2

    .line 193
    if-eqz v2, :cond_5

    .line 194
    .line 195
    invoke-interface {p4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object v2

    .line 199
    check-cast v2, Landroid/view/View;

    .line 200
    .line 201
    add-int v3, v1, p1

    .line 202
    .line 203
    invoke-virtual {v2, v1, p2, v3, p3}, Landroid/view/View;->layout(IIII)V

    .line 204
    .line 205
    .line 206
    iget v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g:I

    .line 207
    .line 208
    add-int/2addr v1, v3

    .line 209
    goto :goto_3

    .line 210
    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 211
    .line 212
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 213
    .line 214
    .line 215
    move-result p1

    .line 216
    iget-object p3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 217
    .line 218
    sget-object p4, Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;->WATERMARK_LARGE:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 219
    .line 220
    if-ne p3, p4, :cond_6

    .line 221
    .line 222
    iget p2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i:I

    .line 223
    .line 224
    :cond_6
    add-int/2addr p1, p2

    .line 225
    add-int/2addr v0, p1

    .line 226
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->b:Landroidx/compose/ui/platform/ComposeView;

    .line 227
    .line 228
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 229
    .line 230
    .line 231
    move-result p2

    .line 232
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 233
    .line 234
    .line 235
    move-result p3

    .line 236
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 237
    .line 238
    .line 239
    move-result p4

    .line 240
    sub-int/2addr p3, p4

    .line 241
    invoke-virtual {p0}, Landroid/view/View;->getPaddingBottom()I

    .line 242
    .line 243
    .line 244
    move-result p4

    .line 245
    sub-int/2addr p5, p4

    .line 246
    invoke-virtual {p1, p2, v0, p3, p5}, Landroid/view/View;->layout(IIII)V

    .line 247
    .line 248
    .line 249
    return-void
.end method

.method public onMeasure(II)V
    .locals 6

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->getHeightByStyle()I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 10
    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    sub-int v1, p1, v1

    .line 16
    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    sub-int/2addr v1, v2

    .line 22
    iget v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i:I

    .line 23
    .line 24
    sub-int/2addr v1, v2

    .line 25
    const/high16 v2, 0x40000000    # 2.0f

    .line 26
    .line 27
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    iget v3, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e:I

    .line 32
    .line 33
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 34
    .line 35
    .line 36
    move-result v3

    .line 37
    invoke-virtual {v0, v1, v3}, Landroid/view/View;->measure(II)V

    .line 38
    .line 39
    .line 40
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->b:Landroidx/compose/ui/platform/ComposeView;

    .line 41
    .line 42
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    sub-int v1, p1, v1

    .line 47
    .line 48
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 49
    .line 50
    .line 51
    move-result v3

    .line 52
    sub-int/2addr v1, v3

    .line 53
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 54
    .line 55
    .line 56
    move-result v1

    .line 57
    const/4 v3, 0x0

    .line 58
    invoke-static {v3, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 59
    .line 60
    .line 61
    move-result v4

    .line 62
    invoke-virtual {v0, v1, v4}, Landroid/view/View;->measure(II)V

    .line 63
    .line 64
    .line 65
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->c:Landroid/widget/FrameLayout;

    .line 66
    .line 67
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 68
    .line 69
    .line 70
    move-result v1

    .line 71
    sub-int v1, p1, v1

    .line 72
    .line 73
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 74
    .line 75
    .line 76
    move-result v4

    .line 77
    sub-int/2addr v1, v4

    .line 78
    invoke-static {v1, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 79
    .line 80
    .line 81
    move-result v1

    .line 82
    iget v4, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->f:I

    .line 83
    .line 84
    invoke-static {v4, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 85
    .line 86
    .line 87
    move-result v4

    .line 88
    invoke-virtual {v0, v1, v4}, Landroid/view/View;->measure(II)V

    .line 89
    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 92
    .line 93
    new-instance v1, Ljava/util/ArrayList;

    .line 94
    .line 95
    const/16 v4, 0xa

    .line 96
    .line 97
    invoke-static {v0, v4}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 98
    .line 99
    .line 100
    move-result v4

    .line 101
    invoke-direct {v1, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 102
    .line 103
    .line 104
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 109
    .line 110
    .line 111
    move-result v4

    .line 112
    if-eqz v4, :cond_0

    .line 113
    .line 114
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object v4

    .line 118
    check-cast v4, LG31/a;

    .line 119
    .line 120
    check-cast v4, Landroid/view/View;

    .line 121
    .line 122
    invoke-interface {v1, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    goto :goto_0

    .line 126
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    .line 127
    .line 128
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 129
    .line 130
    .line 131
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 132
    .line 133
    .line 134
    move-result-object v1

    .line 135
    :cond_1
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 136
    .line 137
    .line 138
    move-result v4

    .line 139
    if-eqz v4, :cond_2

    .line 140
    .line 141
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 142
    .line 143
    .line 144
    move-result-object v4

    .line 145
    move-object v5, v4

    .line 146
    check-cast v5, Landroid/view/View;

    .line 147
    .line 148
    invoke-virtual {v5}, Landroid/view/View;->getVisibility()I

    .line 149
    .line 150
    .line 151
    move-result v5

    .line 152
    if-nez v5, :cond_1

    .line 153
    .line 154
    invoke-interface {v0, v4}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 155
    .line 156
    .line 157
    goto :goto_1

    .line 158
    :cond_2
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 159
    .line 160
    .line 161
    move-result v1

    .line 162
    if-nez v1, :cond_3

    .line 163
    .line 164
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 165
    .line 166
    .line 167
    move-result v1

    .line 168
    sub-int v1, p1, v1

    .line 169
    .line 170
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 171
    .line 172
    .line 173
    move-result v4

    .line 174
    sub-int/2addr v1, v4

    .line 175
    iget v4, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g:I

    .line 176
    .line 177
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 178
    .line 179
    .line 180
    move-result v5

    .line 181
    add-int/lit8 v5, v5, -0x1

    .line 182
    .line 183
    mul-int v4, v4, v5

    .line 184
    .line 185
    sub-int/2addr v1, v4

    .line 186
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 187
    .line 188
    .line 189
    move-result v4

    .line 190
    div-int/2addr v1, v4

    .line 191
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 192
    .line 193
    .line 194
    move-result v1

    .line 195
    invoke-static {v3, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 196
    .line 197
    .line 198
    move-result v2

    .line 199
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 200
    .line 201
    .line 202
    move-result-object v0

    .line 203
    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 204
    .line 205
    .line 206
    move-result v3

    .line 207
    if-eqz v3, :cond_3

    .line 208
    .line 209
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 210
    .line 211
    .line 212
    move-result-object v3

    .line 213
    check-cast v3, Landroid/view/View;

    .line 214
    .line 215
    invoke-virtual {v3, v1, v2}, Landroid/view/View;->measure(II)V

    .line 216
    .line 217
    .line 218
    goto :goto_2

    .line 219
    :cond_3
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->l:Landroid/graphics/RectF;

    .line 220
    .line 221
    int-to-float v1, p1

    .line 222
    int-to-float v2, p2

    .line 223
    const/4 v3, 0x0

    .line 224
    invoke-virtual {v0, v3, v3, v1, v2}, Landroid/graphics/RectF;->set(FFFF)V

    .line 225
    .line 226
    .line 227
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 228
    .line 229
    .line 230
    return-void
.end method

.method public final setCoefficient(Ljava/lang/String;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->p:Lu31/a$b;

    .line 2
    .line 3
    const/16 v7, 0x3d

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/4 v1, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    const/4 v5, 0x0

    .line 10
    const/4 v6, 0x0

    .line 11
    move-object v2, p1

    .line 12
    invoke-static/range {v0 .. v8}, Lu31/a$b;->b(Lu31/a$b;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;ZZZILjava/lang/Object;)Lu31/a$b;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->p:Lu31/a$b;

    .line 17
    .line 18
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->b:Landroidx/compose/ui/platform/ComposeView;

    .line 19
    .line 20
    new-instance v0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$c;

    .line 21
    .line 22
    invoke-direct {v0, p0}, Lorg/xbet/uikit_sport/express_card/SportExpressCard$c;-><init>(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)V

    .line 23
    .line 24
    .line 25
    const v1, 0x5d36e1bf

    .line 26
    .line 27
    .line 28
    const/4 v2, 0x1

    .line 29
    invoke-static {v1, v2, v0}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {p1, v0}, LF11/j;->h(Landroidx/compose/ui/platform/ComposeView;Lkotlin/jvm/functions/Function2;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final setHeaderTitle(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setMarketDescription(Ljava/lang/String;)V
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->p:Lu31/a$b;

    .line 2
    .line 3
    const/16 v7, 0x3e

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    const/4 v5, 0x0

    .line 10
    const/4 v6, 0x0

    .line 11
    move-object v1, p1

    .line 12
    invoke-static/range {v0 .. v8}, Lu31/a$b;->b(Lu31/a$b;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit_sport/compose/sport_market/model/CoefficientState;ZZZILjava/lang/Object;)Lu31/a$b;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->p:Lu31/a$b;

    .line 17
    .line 18
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->b:Landroidx/compose/ui/platform/ComposeView;

    .line 19
    .line 20
    new-instance v0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;

    .line 21
    .line 22
    invoke-direct {v0, p0}, Lorg/xbet/uikit_sport/express_card/SportExpressCard$d;-><init>(Lorg/xbet/uikit_sport/express_card/SportExpressCard;)V

    .line 23
    .line 24
    .line 25
    const v1, -0x5d36e296

    .line 26
    .line 27
    .line 28
    const/4 v2, 0x1

    .line 29
    invoke-static {v1, v2, v0}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {p1, v0}, LF11/j;->h(Landroidx/compose/ui/platform/ComposeView;Lkotlin/jvm/functions/Function2;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final setSportEventList(Ljava/util/List;)V
    .locals 5
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LG31/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->n:Ljava/util/List;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    const/4 v0, 0x6

    .line 11
    invoke-static {p1, v0}, Lkotlin/collections/CollectionsKt;->p1(Ljava/lang/Iterable;I)Ljava/util/List;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const/4 v2, 0x0

    .line 20
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 21
    .line 22
    .line 23
    move-result v3

    .line 24
    if-eqz v3, :cond_2

    .line 25
    .line 26
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    add-int/lit8 v4, v2, 0x1

    .line 31
    .line 32
    if-gez v2, :cond_1

    .line 33
    .line 34
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 35
    .line 36
    .line 37
    :cond_1
    check-cast v3, LG31/b;

    .line 38
    .line 39
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->k(I)LG31/a;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-virtual {p0, v2, v3}, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->j(LG31/a;LG31/b;)V

    .line 44
    .line 45
    .line 46
    move v2, v4

    .line 47
    goto :goto_0

    .line 48
    :cond_2
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    iget-object v1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 53
    .line 54
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    :goto_1
    if-ge v0, v1, :cond_3

    .line 59
    .line 60
    iget-object v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->m:Ljava/util/List;

    .line 61
    .line 62
    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v2

    .line 66
    check-cast v2, Landroid/view/View;

    .line 67
    .line 68
    const/16 v3, 0x8

    .line 69
    .line 70
    invoke-virtual {v2, v3}, Landroid/view/View;->setVisibility(I)V

    .line 71
    .line 72
    .line 73
    add-int/lit8 v0, v0, 0x1

    .line 74
    .line 75
    goto :goto_1

    .line 76
    :cond_3
    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->n:Ljava/util/List;

    .line 77
    .line 78
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 79
    .line 80
    .line 81
    return-void
.end method

.method public final setStyle(Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;)V
    .locals 3
    .param p1    # Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->o:Lorg/xbet/uikit_sport/express_card/ExpressCardStyle;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/uikit_sport/express_card/SportExpressCard$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    aget p1, v0, p1

    .line 10
    .line 11
    const/4 v0, 0x1

    .line 12
    const/4 v1, 0x0

    .line 13
    if-eq p1, v0, :cond_3

    .line 14
    .line 15
    const/4 v0, 0x2

    .line 16
    if-eq p1, v0, :cond_2

    .line 17
    .line 18
    const/4 v0, 0x3

    .line 19
    if-eq p1, v0, :cond_1

    .line 20
    .line 21
    const/4 v0, 0x4

    .line 22
    if-ne p1, v0, :cond_0

    .line 23
    .line 24
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    sget v0, LlZ0/g;->size_38:I

    .line 29
    .line 30
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e:I

    .line 35
    .line 36
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 37
    .line 38
    iget v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->j:I

    .line 39
    .line 40
    iget v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i:I

    .line 41
    .line 42
    invoke-virtual {p1, v1, v0, v1, v2}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    sget v0, LlZ0/g;->radius_20:I

    .line 50
    .line 51
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 52
    .line 53
    .line 54
    move-result p1

    .line 55
    int-to-float p1, p1

    .line 56
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d:F

    .line 57
    .line 58
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    sget v0, LlZ0/g;->size_58:I

    .line 63
    .line 64
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->f:I

    .line 69
    .line 70
    goto/16 :goto_0

    .line 71
    .line 72
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 73
    .line 74
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 75
    .line 76
    .line 77
    throw p1

    .line 78
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    sget v0, LlZ0/g;->size_36:I

    .line 83
    .line 84
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 85
    .line 86
    .line 87
    move-result p1

    .line 88
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e:I

    .line 89
    .line 90
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 91
    .line 92
    iget v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->j:I

    .line 93
    .line 94
    iget v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->h:I

    .line 95
    .line 96
    invoke-virtual {p1, v1, v0, v1, v2}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    sget v0, LlZ0/g;->radius_20:I

    .line 104
    .line 105
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 106
    .line 107
    .line 108
    move-result p1

    .line 109
    int-to-float p1, p1

    .line 110
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d:F

    .line 111
    .line 112
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    sget v0, LlZ0/g;->size_60:I

    .line 117
    .line 118
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 119
    .line 120
    .line 121
    move-result p1

    .line 122
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->f:I

    .line 123
    .line 124
    goto :goto_0

    .line 125
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 126
    .line 127
    sget v0, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 128
    .line 129
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 130
    .line 131
    .line 132
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    sget v0, LlZ0/g;->size_36:I

    .line 137
    .line 138
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 139
    .line 140
    .line 141
    move-result p1

    .line 142
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e:I

    .line 143
    .line 144
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 145
    .line 146
    iget v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->j:I

    .line 147
    .line 148
    iget v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i:I

    .line 149
    .line 150
    invoke-virtual {p1, v1, v0, v1, v2}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 151
    .line 152
    .line 153
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    sget v0, LlZ0/g;->radius_20:I

    .line 158
    .line 159
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 160
    .line 161
    .line 162
    move-result p1

    .line 163
    int-to-float p1, p1

    .line 164
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d:F

    .line 165
    .line 166
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    sget v0, LlZ0/g;->space_46:I

    .line 171
    .line 172
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 173
    .line 174
    .line 175
    move-result p1

    .line 176
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->f:I

    .line 177
    .line 178
    goto :goto_0

    .line 179
    :cond_3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 180
    .line 181
    .line 182
    move-result-object p1

    .line 183
    sget v0, LlZ0/g;->size_30:I

    .line 184
    .line 185
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 186
    .line 187
    .line 188
    move-result p1

    .line 189
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->e:I

    .line 190
    .line 191
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->a:Landroid/widget/TextView;

    .line 192
    .line 193
    iget v0, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->i:I

    .line 194
    .line 195
    iget v2, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->g:I

    .line 196
    .line 197
    invoke-virtual {p1, v1, v0, v1, v2}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 198
    .line 199
    .line 200
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 201
    .line 202
    .line 203
    move-result-object p1

    .line 204
    sget v0, LlZ0/g;->radius_16:I

    .line 205
    .line 206
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 207
    .line 208
    .line 209
    move-result p1

    .line 210
    int-to-float p1, p1

    .line 211
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->d:F

    .line 212
    .line 213
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 214
    .line 215
    .line 216
    move-result-object p1

    .line 217
    sget v0, LlZ0/g;->size_58:I

    .line 218
    .line 219
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 220
    .line 221
    .line 222
    move-result p1

    .line 223
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/SportExpressCard;->f:I

    .line 224
    .line 225
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 226
    .line 227
    .line 228
    return-void
.end method
