.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lck/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->m0:Lck/a;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->l0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LAX0/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->n0:LAX0/b;

    .line 2
    .line 3
    return-void
.end method

.method public static e(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
