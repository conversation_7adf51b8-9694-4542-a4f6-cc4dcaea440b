.class public final Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0010\u0006\n\u0002\u0008\u001b\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0018\u00002\u00020\u0001:\u0001SBA\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u000f\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0013\u0010\u001a\u001a\u0008\u0012\u0004\u0012\u00020\u00190\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\r\u0010 \u001a\u00020\u0012\u00a2\u0006\u0004\u0008 \u0010\u0014J\u0019\u0010\"\u001a\u0004\u0018\u00010!2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u0018\u0010\'\u001a\u00020&2\u0006\u0010%\u001a\u00020$H\u0082@\u00a2\u0006\u0004\u0008\'\u0010(J\u001f\u0010+\u001a\u00020\u00122\u0006\u0010)\u001a\u00020&2\u0006\u0010*\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008+\u0010,J\u001f\u0010.\u001a\u00020&2\u0006\u0010-\u001a\u00020&2\u0006\u0010*\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u0008.\u0010/J\u001f\u00102\u001a\u00020\u001c2\u0006\u00101\u001a\u0002002\u0006\u0010*\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u00082\u00103J\u0017\u00104\u001a\u0002002\u0006\u0010-\u001a\u00020\u001cH\u0002\u00a2\u0006\u0004\u00084\u00105R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0016\u0010F\u001a\u00020\u001c8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0018\u0010I\u001a\u0004\u0018\u00010&8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0016\u0010K\u001a\u00020\u00028\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008J\u00107R\u0016\u0010O\u001a\u00020L8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u001a\u0010R\u001a\u0008\u0012\u0004\u0012\u00020\u00190\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010Q\u00a8\u0006T"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "",
        "fromAggregator",
        "Lxb1/a;",
        "getTvBetInfoUseCase",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "LSX0/a;",
        "lottieConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lfk/l;",
        "getLastBalanceUseCase",
        "Lm8/a;",
        "coroutineDispatchers",
        "<init>",
        "(ZLxb1/a;Lgk/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lfk/l;Lm8/a;)V",
        "",
        "N3",
        "()V",
        "Lorg/xbet/uikit/components/lottie/a;",
        "I3",
        "()Lorg/xbet/uikit/components/lottie/a;",
        "Lkotlinx/coroutines/flow/V;",
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;",
        "M3",
        "()Lkotlinx/coroutines/flow/V;",
        "",
        "date",
        "K3",
        "(Ljava/lang/String;)V",
        "Q3",
        "Lvb1/b;",
        "J3",
        "(Ljava/lang/String;)Lvb1/b;",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "Lvb1/a;",
        "H3",
        "(Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "tvBetJackpot",
        "currencySymbol",
        "P3",
        "(Lvb1/a;Ljava/lang/String;)V",
        "item",
        "G3",
        "(Lvb1/a;Ljava/lang/String;)Lvb1/a;",
        "",
        "sum",
        "F3",
        "(DLjava/lang/String;)Ljava/lang/String;",
        "O3",
        "(Ljava/lang/String;)D",
        "v1",
        "Z",
        "x1",
        "Lxb1/a;",
        "y1",
        "Lgk/b;",
        "F1",
        "LSX0/a;",
        "H1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "I1",
        "Lfk/l;",
        "P1",
        "Lm8/a;",
        "S1",
        "Ljava/lang/String;",
        "lastCategoryId",
        "V1",
        "Lvb1/a;",
        "savedInfo",
        "b2",
        "dataLoaded",
        "Lorg/xbet/balance/model/BalanceScreenType;",
        "v2",
        "Lorg/xbet/balance/model/BalanceScreenType;",
        "balanceScreenType",
        "x2",
        "Lkotlinx/coroutines/flow/V;",
        "stateFlow",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final F1:LSX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lfk/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public S1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public V1:Lvb1/a;

.field public b2:Z

.field public final v1:Z

.field public v2:Lorg/xbet/balance/model/BalanceScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lxb1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(ZLxb1/a;Lgk/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lfk/l;Lm8/a;)V
    .locals 0
    .param p2    # Lxb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LSX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lfk/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v1:Z

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x1:Lxb1/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->y1:Lgk/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->F1:LSX0/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->H1:Lorg/xbet/ui_common/utils/internet/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->I1:Lfk/l;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->P1:Lm8/a;

    .line 17
    .line 18
    const-string p2, ""

    .line 19
    .line 20
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->S1:Ljava/lang/String;

    .line 21
    .line 22
    if-eqz p1, :cond_0

    .line 23
    .line 24
    sget-object p1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    sget-object p1, Lorg/xbet/balance/model/BalanceScreenType;->MULTI:Lorg/xbet/balance/model/BalanceScreenType;

    .line 28
    .line 29
    :goto_0
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v2:Lorg/xbet/balance/model/BalanceScreenType;

    .line 30
    .line 31
    new-instance p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$c;

    .line 32
    .line 33
    const/4 p2, 0x1

    .line 34
    invoke-direct {p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$c;-><init>(Z)V

    .line 35
    .line 36
    .line 37
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 42
    .line 43
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->N3()V

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lorg/xbet/balance/model/BalanceScreenType;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v2:Lorg/xbet/balance/model/BalanceScreenType;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->b2:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lvb1/a;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->P3(Lvb1/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->S1:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method

.method private final I3()Lorg/xbet/uikit/components/lottie/a;
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->F1:LSX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    sget v2, Lpb/k;->data_retrieval_error:I

    .line 6
    .line 7
    const/16 v7, 0x1c

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const-wide/16 v5, 0x0

    .line 13
    .line 14
    invoke-static/range {v0 .. v8}, LSX0/a$a;->a(LSX0/a;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;IILkotlin/jvm/functions/Function0;JILjava/lang/Object;)Lorg/xbet/uikit/components/lottie/a;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    return-object v0
.end method

.method public static final L3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->b2:Z

    .line 3
    .line 4
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 5
    .line 6
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;

    .line 7
    .line 8
    const/4 v1, 0x1

    .line 9
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->I3()Lorg/xbet/uikit/components/lottie/a;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-direct {v0, v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;-><init>(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 14
    .line 15
    .line 16
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method private final N3()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->H1:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->P1:Lm8/a;

    .line 22
    .line 23
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->c0(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public static final R3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 2

    .line 1
    const/4 p1, 0x0

    .line 2
    iput-boolean p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->b2:Z

    .line 3
    .line 4
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 5
    .line 6
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;

    .line 7
    .line 8
    const/4 v1, 0x1

    .line 9
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->I3()Lorg/xbet/uikit/components/lottie/a;

    .line 10
    .line 11
    .line 12
    move-result-object p0

    .line 13
    invoke-direct {v0, v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;-><init>(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 14
    .line 15
    .line 16
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->L3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->R3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic r3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lorg/xbet/balance/model/BalanceScreenType;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v2:Lorg/xbet/balance/model/BalanceScreenType;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;DLjava/lang/String;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->F3(DLjava/lang/String;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->b2:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->v1:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lfk/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->I1:Lfk/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->H3(Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lorg/xbet/uikit/components/lottie/a;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->I3()Lorg/xbet/uikit/components/lottie/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;)Lvb1/b;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->J3(Ljava/lang/String;)Lvb1/b;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lvb1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->V1:Lvb1/a;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final F3(DLjava/lang/String;)Ljava/lang/String;
    .locals 6

    .line 1
    sget-object v0, Ll8/j;->a:Ll8/j;

    .line 2
    .line 3
    const/4 v4, 0x2

    .line 4
    const/4 v5, 0x0

    .line 5
    const/4 v3, 0x0

    .line 6
    move-wide v1, p1

    .line 7
    invoke-static/range {v0 .. v5}, Ll8/j;->g(Ll8/j;DLcom/xbet/onexcore/utils/ValueType;ILjava/lang/Object;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    new-instance p2, Ljava/lang/StringBuilder;

    .line 12
    .line 13
    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 17
    .line 18
    .line 19
    const-string p1, " "

    .line 20
    .line 21
    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 22
    .line 23
    .line 24
    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    .line 26
    .line 27
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    return-object p1
.end method

.method public final G3(Lvb1/a;Ljava/lang/String;)Lvb1/a;
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual/range {p1 .. p1}, Lvb1/a;->e()Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v5, Ljava/util/ArrayList;

    .line 8
    .line 9
    const/16 v2, 0xa

    .line 10
    .line 11
    invoke-static {v1, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 12
    .line 13
    .line 14
    move-result v3

    .line 15
    invoke-direct {v5, v3}, Ljava/util/ArrayList;-><init>(I)V

    .line 16
    .line 17
    .line 18
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 23
    .line 24
    .line 25
    move-result v3

    .line 26
    if-eqz v3, :cond_1

    .line 27
    .line 28
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    move-object v6, v3

    .line 33
    check-cast v6, Lvb1/b;

    .line 34
    .line 35
    invoke-virtual {v6}, Lvb1/b;->e()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v3

    .line 39
    new-instance v9, Ljava/util/ArrayList;

    .line 40
    .line 41
    invoke-static {v3, v2}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 42
    .line 43
    .line 44
    move-result v4

    .line 45
    invoke-direct {v9, v4}, Ljava/util/ArrayList;-><init>(I)V

    .line 46
    .line 47
    .line 48
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    :goto_1
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 53
    .line 54
    .line 55
    move-result v4

    .line 56
    if-eqz v4, :cond_0

    .line 57
    .line 58
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    move-object v10, v4

    .line 63
    check-cast v10, Lvb1/c;

    .line 64
    .line 65
    invoke-virtual {v10}, Lvb1/c;->e()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object v4

    .line 69
    invoke-virtual {v0, v4}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->O3(Ljava/lang/String;)D

    .line 70
    .line 71
    .line 72
    move-result-wide v7

    .line 73
    move-object/from16 v4, p2

    .line 74
    .line 75
    invoke-virtual {v0, v7, v8, v4}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->F3(DLjava/lang/String;)Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v11

    .line 79
    const/4 v14, 0x6

    .line 80
    const/4 v15, 0x0

    .line 81
    const/4 v12, 0x0

    .line 82
    const/4 v13, 0x0

    .line 83
    invoke-static/range {v10 .. v15}, Lvb1/c;->b(Lvb1/c;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Lvb1/c;

    .line 84
    .line 85
    .line 86
    move-result-object v7

    .line 87
    invoke-interface {v9, v7}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    goto :goto_1

    .line 91
    :cond_0
    move-object/from16 v4, p2

    .line 92
    .line 93
    const/4 v10, 0x3

    .line 94
    const/4 v11, 0x0

    .line 95
    const/4 v7, 0x0

    .line 96
    const/4 v8, 0x0

    .line 97
    invoke-static/range {v6 .. v11}, Lvb1/b;->b(Lvb1/b;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;ILjava/lang/Object;)Lvb1/b;

    .line 98
    .line 99
    .line 100
    move-result-object v3

    .line 101
    invoke-interface {v5, v3}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    goto :goto_0

    .line 105
    :cond_1
    const/4 v7, 0x5

    .line 106
    const/4 v8, 0x0

    .line 107
    const-wide/16 v3, 0x0

    .line 108
    .line 109
    const/4 v6, 0x0

    .line 110
    move-object/from16 v2, p1

    .line 111
    .line 112
    invoke-static/range {v2 .. v8}, Lvb1/a;->b(Lvb1/a;DLjava/util/List;Ljava/util/List;ILjava/lang/Object;)Lvb1/a;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    return-object v1
.end method

.method public final H3(Lorg/xbet/balance/model/BalanceModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/balance/model/BalanceModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lvb1/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x2

    .line 34
    const/4 v4, 0x1

    .line 35
    if-eqz v2, :cond_3

    .line 36
    .line 37
    if-eq v2, v4, :cond_2

    .line 38
    .line 39
    if-ne v2, v3, :cond_1

    .line 40
    .line 41
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->L$0:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 44
    .line 45
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    goto :goto_3

    .line 49
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 50
    .line 51
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 52
    .line 53
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    throw p1

    .line 57
    :cond_2
    iget-object p1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->L$0:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 60
    .line 61
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 62
    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_3
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->y1:Lgk/b;

    .line 69
    .line 70
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getCurrencyId()J

    .line 71
    .line 72
    .line 73
    move-result-wide v5

    .line 74
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->L$0:Ljava/lang/Object;

    .line 75
    .line 76
    iput v4, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->label:I

    .line 77
    .line 78
    invoke-interface {p2, v5, v6, v0}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p2

    .line 82
    if-ne p2, v1, :cond_4

    .line 83
    .line 84
    goto :goto_2

    .line 85
    :cond_4
    :goto_1
    check-cast p2, Lbk/a;

    .line 86
    .line 87
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x1:Lxb1/a;

    .line 88
    .line 89
    invoke-virtual {p2}, Lbk/a;->c()Ljava/lang/String;

    .line 90
    .line 91
    .line 92
    move-result-object p2

    .line 93
    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->L$0:Ljava/lang/Object;

    .line 94
    .line 95
    iput v3, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getJackpotData$1;->label:I

    .line 96
    .line 97
    invoke-virtual {v2, p2, v0}, Lxb1/a;->a(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    move-result-object p2

    .line 101
    if-ne p2, v1, :cond_5

    .line 102
    .line 103
    :goto_2
    return-object v1

    .line 104
    :cond_5
    :goto_3
    check-cast p2, Lvb1/a;

    .line 105
    .line 106
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getCurrencySymbol()Ljava/lang/String;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    invoke-virtual {p0, p2, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->G3(Lvb1/a;Ljava/lang/String;)Lvb1/a;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->V1:Lvb1/a;

    .line 115
    .line 116
    return-object p1
.end method

.method public final J3(Ljava/lang/String;)Lvb1/b;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->V1:Lvb1/a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_2

    .line 5
    .line 6
    invoke-virtual {v0}, Lvb1/a;->e()Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    if-eqz v0, :cond_2

    .line 11
    .line 12
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    if-eqz v2, :cond_1

    .line 21
    .line 22
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    move-object v3, v2

    .line 27
    check-cast v3, Lvb1/b;

    .line 28
    .line 29
    invoke-virtual {v3}, Lvb1/b;->c()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v3

    .line 33
    invoke-static {v3, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 34
    .line 35
    .line 36
    move-result v3

    .line 37
    if-eqz v3, :cond_0

    .line 38
    .line 39
    move-object v1, v2

    .line 40
    :cond_1
    check-cast v1, Lvb1/b;

    .line 41
    .line 42
    :cond_2
    return-object v1
.end method

.method public final K3(Ljava/lang/String;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/b;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/b;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, v2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$getTableInfoByDate$2;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final M3()Lkotlinx/coroutines/flow/V;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final O3(Ljava/lang/String;)D
    .locals 2

    .line 1
    invoke-static {p1}, Lkotlin/text/u;->u(Ljava/lang/String;)Ljava/lang/Double;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Double;->doubleValue()D

    .line 8
    .line 9
    .line 10
    move-result-wide v0

    .line 11
    return-wide v0

    .line 12
    :cond_0
    const-wide/16 v0, 0x0

    .line 13
    .line 14
    return-wide v0
.end method

.method public final P3(Lvb1/a;Ljava/lang/String;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->S1:Ljava/lang/String;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, 0x0

    .line 8
    if-nez v0, :cond_1

    .line 9
    .line 10
    invoke-virtual {p1}, Lvb1/a;->d()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    check-cast v0, Lkotlin/Pair;

    .line 19
    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    invoke-virtual {v0}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Ljava/lang/String;

    .line 27
    .line 28
    if-eqz v0, :cond_0

    .line 29
    .line 30
    goto :goto_1

    .line 31
    :cond_0
    new-instance p1, Ljava/util/NoSuchElementException;

    .line 32
    .line 33
    invoke-direct {p1}, Ljava/util/NoSuchElementException;-><init>()V

    .line 34
    .line 35
    .line 36
    throw p1

    .line 37
    :cond_1
    invoke-virtual {p1}, Lvb1/a;->d()Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    if-eqz v2, :cond_3

    .line 50
    .line 51
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    move-object v3, v2

    .line 56
    check-cast v3, Lkotlin/Pair;

    .line 57
    .line 58
    invoke-virtual {v3}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v3

    .line 62
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->S1:Ljava/lang/String;

    .line 63
    .line 64
    invoke-static {v3, v4}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 65
    .line 66
    .line 67
    move-result v3

    .line 68
    if-eqz v3, :cond_2

    .line 69
    .line 70
    goto :goto_0

    .line 71
    :cond_3
    move-object v2, v1

    .line 72
    :goto_0
    check-cast v2, Lkotlin/Pair;

    .line 73
    .line 74
    if-eqz v2, :cond_7

    .line 75
    .line 76
    invoke-virtual {v2}, Lkotlin/Pair;->getFirst()Ljava/lang/Object;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    check-cast v0, Ljava/lang/String;

    .line 81
    .line 82
    if-eqz v0, :cond_7

    .line 83
    .line 84
    :goto_1
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->J3(Ljava/lang/String;)Lvb1/b;

    .line 85
    .line 86
    .line 87
    move-result-object v0

    .line 88
    if-eqz v0, :cond_4

    .line 89
    .line 90
    invoke-virtual {v0}, Lvb1/b;->d()Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    goto :goto_2

    .line 95
    :cond_4
    move-object v2, v1

    .line 96
    :goto_2
    if-eqz v0, :cond_5

    .line 97
    .line 98
    invoke-virtual {v0}, Lvb1/b;->c()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object v3

    .line 102
    goto :goto_3

    .line 103
    :cond_5
    move-object v3, v1

    .line 104
    :goto_3
    new-instance v4, Ljava/lang/StringBuilder;

    .line 105
    .line 106
    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    .line 107
    .line 108
    .line 109
    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 110
    .line 111
    .line 112
    const-string v2, " - "

    .line 113
    .line 114
    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 115
    .line 116
    .line 117
    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 118
    .line 119
    .line 120
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v2

    .line 124
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x2:Lkotlinx/coroutines/flow/V;

    .line 125
    .line 126
    new-instance v4, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;

    .line 127
    .line 128
    if-eqz v0, :cond_6

    .line 129
    .line 130
    invoke-virtual {v0}, Lvb1/b;->e()Ljava/util/List;

    .line 131
    .line 132
    .line 133
    move-result-object v1

    .line 134
    :cond_6
    invoke-virtual {p1}, Lvb1/a;->c()D

    .line 135
    .line 136
    .line 137
    move-result-wide v5

    .line 138
    invoke-virtual {p0, v5, v6, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->F3(DLjava/lang/String;)Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object p2

    .line 142
    invoke-virtual {p1}, Lvb1/a;->d()Ljava/util/List;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    invoke-direct {v4, v2, v1, p2, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;-><init>(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V

    .line 147
    .line 148
    .line 149
    invoke-interface {v3, v4}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 150
    .line 151
    .line 152
    return-void

    .line 153
    :cond_7
    new-instance p1, Ljava/util/NoSuchElementException;

    .line 154
    .line 155
    invoke-direct {p1}, Ljava/util/NoSuchElementException;-><init>()V

    .line 156
    .line 157
    .line 158
    throw p1
.end method

.method public final Q3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->P1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/a;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/a;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$update$2;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method
