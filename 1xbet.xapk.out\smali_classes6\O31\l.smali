.class public final synthetic LO31/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LO31/n;


# direct methods
.method public synthetic constructor <init>(LO31/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO31/l;->a:LO31/n;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LO31/l;->a:LO31/n;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, LO31/n;->t(LO31/n;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
