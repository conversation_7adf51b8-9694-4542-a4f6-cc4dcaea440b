.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0010\u000e\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u0000 E2\u00020\u0001:\u0001FB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eR\"\u0010\u0016\u001a\u00020\u000f8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0012\u0010\u0013\"\u0004\u0008\u0014\u0010\u0015R\u001b\u0010\u001c\u001a\u00020\u00178BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u001b\u0010\"\u001a\u00020\u001d8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!R\u001b\u0010\'\u001a\u00020#8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008$\u0010\u0019\u001a\u0004\u0008%\u0010&R+\u00100\u001a\u00020(2\u0006\u0010)\u001a\u00020(8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008*\u0010+\u001a\u0004\u0008,\u0010-\"\u0004\u0008.\u0010/R+\u00108\u001a\u0002012\u0006\u0010)\u001a\u0002018B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00082\u00103\u001a\u0004\u00084\u00105\"\u0004\u00086\u00107R+\u0010<\u001a\u00020(2\u0006\u0010)\u001a\u00020(8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u00089\u0010+\u001a\u0004\u0008:\u0010-\"\u0004\u0008;\u0010/R+\u0010D\u001a\u00020=2\u0006\u0010)\u001a\u00020=8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\"\u0004\u0008B\u0010C\u00a8\u0006G"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;",
        "state",
        "P2",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;)V",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "O2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;",
        "j0",
        "Lkotlin/j;",
        "N2",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;",
        "viewModel",
        "LS91/b0;",
        "k0",
        "LRc/c;",
        "M2",
        "()LS91/b0;",
        "viewBinding",
        "Lfb1/e;",
        "l0",
        "I2",
        "()Lfb1/e;",
        "adapter",
        "",
        "<set-?>",
        "m0",
        "LeX0/f;",
        "J2",
        "()J",
        "R2",
        "(J)V",
        "tournamentId",
        "",
        "n0",
        "LeX0/k;",
        "K2",
        "()Ljava/lang/String;",
        "S2",
        "(Ljava/lang/String;)V",
        "tournamentTitle",
        "o0",
        "getStageTournamentID",
        "Q2",
        "stageTournamentID",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
        "b1",
        "LeX0/j;",
        "L2",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
        "T2",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;)V",
        "type",
        "k1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public final b1:LeX0/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public i0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 9

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTournamentPrizeItemBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "tournamentId"

    .line 20
    .line 21
    const-string v5, "getTournamentId()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "tournamentTitle"

    .line 33
    .line 34
    const-string v6, "getTournamentTitle()Ljava/lang/String;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "stageTournamentID"

    .line 46
    .line 47
    const-string v7, "getStageTournamentID()J"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    new-instance v6, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 57
    .line 58
    const-string v7, "type"

    .line 59
    .line 60
    const-string v8, "getType()Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;"

    .line 61
    .line 62
    invoke-direct {v6, v1, v7, v8, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 63
    .line 64
    .line 65
    invoke-static {v6}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    const/4 v6, 0x5

    .line 70
    new-array v6, v6, [Lkotlin/reflect/m;

    .line 71
    .line 72
    aput-object v0, v6, v4

    .line 73
    .line 74
    const/4 v0, 0x1

    .line 75
    aput-object v2, v6, v0

    .line 76
    .line 77
    const/4 v0, 0x2

    .line 78
    aput-object v3, v6, v0

    .line 79
    .line 80
    const/4 v0, 0x3

    .line 81
    aput-object v5, v6, v0

    .line 82
    .line 83
    const/4 v0, 0x4

    .line 84
    aput-object v1, v6, v0

    .line 85
    .line 86
    sput-object v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 87
    .line 88
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;

    .line 89
    .line 90
    const/4 v1, 0x0

    .line 91
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 92
    .line 93
    .line 94
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->k1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;

    .line 95
    .line 96
    return-void
.end method

.method public constructor <init>()V
    .locals 12

    .line 1
    sget v0, Lu91/c;->fragment_tournament_prize_item:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$viewModel$2;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$viewModel$2;-><init>(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/a;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/a;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$special$$inlined$viewModels$default$1;

    .line 19
    .line 20
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$special$$inlined$viewModels$default$1;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    const-class v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$special$$inlined$viewModels$default$2;

    .line 34
    .line 35
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$special$$inlined$viewModels$default$3;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v1}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$viewBinding$2;

    .line 51
    .line 52
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->k0:LRc/c;

    .line 57
    .line 58
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/b;

    .line 59
    .line 60
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)V

    .line 61
    .line 62
    .line 63
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->l0:Lkotlin/j;

    .line 68
    .line 69
    new-instance v6, LeX0/f;

    .line 70
    .line 71
    const/4 v10, 0x2

    .line 72
    const/4 v11, 0x0

    .line 73
    const-string v7, "PRIZE_TOURNAMENT_ITEM"

    .line 74
    .line 75
    const-wide/16 v8, 0x0

    .line 76
    .line 77
    invoke-direct/range {v6 .. v11}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 78
    .line 79
    .line 80
    iput-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->m0:LeX0/f;

    .line 81
    .line 82
    new-instance v0, LeX0/k;

    .line 83
    .line 84
    const-string v1, "PRIZE_TOURNAMENT_TITLE"

    .line 85
    .line 86
    const/4 v2, 0x2

    .line 87
    invoke-direct {v0, v1, v5, v2, v5}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 88
    .line 89
    .line 90
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->n0:LeX0/k;

    .line 91
    .line 92
    new-instance v6, LeX0/f;

    .line 93
    .line 94
    const-string v7, "PRIZE_STAGE_TOURNAMENT_ID"

    .line 95
    .line 96
    invoke-direct/range {v6 .. v11}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 97
    .line 98
    .line 99
    iput-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->o0:LeX0/f;

    .line 100
    .line 101
    new-instance v0, LeX0/j;

    .line 102
    .line 103
    const-string v1, "PAGE_TYPE"

    .line 104
    .line 105
    invoke-direct {v0, v1}, LeX0/j;-><init>(Ljava/lang/String;)V

    .line 106
    .line 107
    .line 108
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->b1:LeX0/j;

    .line 109
    .line 110
    return-void
.end method

.method public static synthetic A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->U2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic B2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->P2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;J)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->Q2(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->R2(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->S2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->T2(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final G2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)Lfb1/e;
    .locals 2

    .line 1
    new-instance v0, Lfb1/e;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/c;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/c;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, v1}, Lfb1/e;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method

.method public static final H2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Lkb1/z$c;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->N2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, p1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->J3(Lkb1/z$c;Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method private final J2()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->m0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method private final K2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method private final R2(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->m0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final S2(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final U2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->O2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)Lfb1/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->G2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)Lfb1/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Lkb1/z$c;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->H2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Lkb1/z$c;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final I2()Lfb1/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lfb1/e;

    .line 8
    .line 9
    return-object v0
.end method

.method public final L2()Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->b1:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/j;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/io/Serializable;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 13
    .line 14
    return-object v0
.end method

.method public final M2()LS91/b0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->k0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/b0;

    .line 13
    .line 14
    return-object v0
.end method

.method public final N2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final O2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final P2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;)V
    .locals 5

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;

    .line 2
    .line 3
    if-eqz v0, :cond_3

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->M2()LS91/b0;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iget-object v0, v0, LS91/b0;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 10
    .line 11
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;

    .line 12
    .line 13
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    sget-object v2, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 22
    .line 23
    if-eq v1, v2, :cond_0

    .line 24
    .line 25
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    sget v2, Lpb/f;->space_80:I

    .line 30
    .line 31
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    goto :goto_0

    .line 36
    :cond_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    sget v2, Lpb/f;->space_34:I

    .line 41
    .line 42
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    :goto_0
    invoke-virtual {v0}, Landroid/view/View;->getPaddingStart()I

    .line 47
    .line 48
    .line 49
    move-result v2

    .line 50
    invoke-virtual {v0}, Landroid/view/View;->getPaddingTop()I

    .line 51
    .line 52
    .line 53
    move-result v3

    .line 54
    invoke-virtual {v0}, Landroid/view/View;->getPaddingEnd()I

    .line 55
    .line 56
    .line 57
    move-result v4

    .line 58
    invoke-virtual {v0, v2, v3, v4, v1}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->I2()Lfb1/e;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->L2()Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    sget-object v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$b;->a:[I

    .line 70
    .line 71
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 72
    .line 73
    .line 74
    move-result v1

    .line 75
    aget v1, v2, v1

    .line 76
    .line 77
    const/4 v2, 0x1

    .line 78
    if-eq v1, v2, :cond_2

    .line 79
    .line 80
    const/4 v2, 0x2

    .line 81
    if-ne v1, v2, :cond_1

    .line 82
    .line 83
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c()Ljava/util/List;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    goto :goto_1

    .line 88
    :cond_1
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 89
    .line 90
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 91
    .line 92
    .line 93
    throw p1

    .line 94
    :cond_2
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a()Ljava/util/List;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    :goto_1
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 99
    .line 100
    .line 101
    :cond_3
    return-void
.end method

.method public final Q2(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->o0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final T2(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->b1:LeX0/j;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x4

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/j;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/io/Serializable;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->M2()LS91/b0;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iget-object p1, p1, LS91/b0;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->I2()Lfb1/e;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public u2()V
    .locals 6

    .line 1
    sget-object v0, LVa1/u;->a:LVa1/u;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->J2()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->K2()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v4

    .line 11
    sget-object v3, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 14
    .line 15
    .line 16
    move-result-object v5

    .line 17
    invoke-virtual {v5}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    invoke-virtual/range {v0 .. v5}, LVa1/u;->e(JLorg/xplatform/aggregator/api/navigation/TournamentsPage;Ljava/lang/String;Landroid/app/Application;)LVa1/r;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-interface {v0, p0}, LVa1/r;->h(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->N2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->G3()Lkotlinx/coroutines/flow/f0;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$onObserveData$1;

    .line 10
    .line 11
    const/4 v0, 0x0

    .line 12
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 16
    .line 17
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 26
    .line 27
    const/4 v6, 0x0

    .line 28
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 29
    .line 30
    .line 31
    const/4 v10, 0x3

    .line 32
    const/4 v11, 0x0

    .line 33
    const/4 v7, 0x0

    .line 34
    const/4 v8, 0x0

    .line 35
    move-object v6, v0

    .line 36
    move-object v9, v1

    .line 37
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 38
    .line 39
    .line 40
    return-void
.end method
