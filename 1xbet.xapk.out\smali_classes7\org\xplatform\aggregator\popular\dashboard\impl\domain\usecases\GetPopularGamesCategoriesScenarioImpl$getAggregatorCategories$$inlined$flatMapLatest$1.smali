.class public final Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.dashboard.impl.domain.usecases.GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1"
    f = "GetPopularGamesCategoriesScenarioImpl.kt"
    l = {
        0xbd
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->l(I)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "LTb1/a;",
        ">;>;",
        "Ljava/util/List<",
        "+",
        "LTb1/a;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u00020\u0004\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0001*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u0006\u0010\u0003\u001a\u00028\u0001H\n\u00a8\u0006\u0005"
    }
    d2 = {
        "R",
        "T",
        "Lkotlinx/coroutines/flow/f;",
        "it",
        "",
        "kotlinx/coroutines/flow/FlowKt__MergeKt$flatMapLatest$1",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V
    .locals 0

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    const/4 p2, 0x3

    invoke-direct {p0, p2, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "+",
            "LTb1/a;",
            ">;>;",
            "Ljava/util/List<",
            "+",
            "LTb1/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    invoke-direct {v0, p3, v1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Lkotlinx/coroutines/flow/f;

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 32
    .line 33
    check-cast v1, Ljava/util/List;

    .line 34
    .line 35
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 36
    .line 37
    invoke-static {v3}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->d(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lkotlinx/coroutines/flow/e;

    .line 38
    .line 39
    .line 40
    move-result-object v3

    .line 41
    iget-object v4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 42
    .line 43
    invoke-static {v4}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->i(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lcom/xbet/onexuser/domain/user/c;

    .line 44
    .line 45
    .line 46
    move-result-object v4

    .line 47
    invoke-virtual {v4}, Lcom/xbet/onexuser/domain/user/c;->d()Lkotlinx/coroutines/flow/e;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    new-instance v5, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$2$1;

    .line 52
    .line 53
    const/4 v6, 0x0

    .line 54
    invoke-direct {v5, v1, v6}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$2$1;-><init>(Ljava/util/List;Lkotlin/coroutines/e;)V

    .line 55
    .line 56
    .line 57
    invoke-static {v3, v4, v5}, Lkotlinx/coroutines/flow/g;->o(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    iput v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;->label:I

    .line 62
    .line 63
    invoke-static {p1, v1, p0}, Lkotlinx/coroutines/flow/g;->H(Lkotlinx/coroutines/flow/f;Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    if-ne p1, v0, :cond_2

    .line 68
    .line 69
    return-object v0

    .line 70
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 71
    .line 72
    return-object p1
.end method
