.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0001\u0018\u0000 F2\u00020\u0001:\u0001GB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\t\u0010\u0003J\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0003J\u0019\u0010\u0017\u001a\u00020\u00062\u0008\u0010\u0016\u001a\u0004\u0018\u00010\u0015H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u0019\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u0003J\u000f\u0010\u001a\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u0003J\u000f\u0010\u001b\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u0003J\u000f\u0010\u001c\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u001c\u0010\u0003R\"\u0010$\u001a\u00020\u001d8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!\"\u0004\u0008\"\u0010#R\"\u0010,\u001a\u00020%8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008&\u0010\'\u001a\u0004\u0008(\u0010)\"\u0004\u0008*\u0010+R\"\u00104\u001a\u00020-8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101\"\u0004\u00082\u00103R\u001b\u0010:\u001a\u0002058BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00086\u00107\u001a\u0004\u00088\u00109R\u001b\u0010@\u001a\u00020;8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008<\u0010=\u001a\u0004\u0008>\u0010?R\u001b\u0010E\u001a\u00020A8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008B\u0010=\u001a\u0004\u0008C\u0010D\u00a8\u0006H"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;",
        "action",
        "",
        "T2",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;)V",
        "Q2",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;",
        "S2",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;)V",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;",
        "R2",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;)V",
        "",
        "isEnabled",
        "P2",
        "(Z)V",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "s2",
        "x2",
        "onDestroyView",
        "v2",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "N2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LzX0/k;",
        "j0",
        "LzX0/k;",
        "J2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "LAX0/b;",
        "k0",
        "LAX0/b;",
        "K2",
        "()LAX0/b;",
        "setSuccessBetAlertManager",
        "(LAX0/b;)V",
        "successBetAlertManager",
        "LOU0/e;",
        "l0",
        "LRc/c;",
        "I2",
        "()LOU0/e;",
        "binding",
        "Landroid/text/TextWatcher;",
        "m0",
        "Lkotlin/j;",
        "L2",
        "()Landroid/text/TextWatcher;",
        "textWatcher",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;",
        "n0",
        "M2",
        "()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;",
        "viewModel",
        "o0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic b1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final k1:I

.field public static final o0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public i0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public j0:LzX0/k;

.field public k0:LAX0/b;

.field public final l0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/toto_bet/impl/databinding/FragmentPromoBetDsTotoBetBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->b1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->o0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$a;

    .line 31
    .line 32
    const/16 v0, 0x8

    .line 33
    .line 34
    sput v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->k1:I

    .line 35
    .line 36
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LNU0/b;->fragment_promo_bet_ds_toto_bet:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$binding$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->l0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/b;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->m0:Lkotlin/j;

    .line 24
    .line 25
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/c;

    .line 26
    .line 27
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/c;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V

    .line 28
    .line 29
    .line 30
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$1;

    .line 31
    .line 32
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 33
    .line 34
    .line 35
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 36
    .line 37
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$2;

    .line 38
    .line 39
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 40
    .line 41
    .line 42
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 43
    .line 44
    .line 45
    move-result-object v1

    .line 46
    const-class v2, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 47
    .line 48
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$3;

    .line 53
    .line 54
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 55
    .line 56
    .line 57
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$4;

    .line 58
    .line 59
    const/4 v5, 0x0

    .line 60
    invoke-direct {v4, v5, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 61
    .line 62
    .line 63
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->n0:Lkotlin/j;

    .line 68
    .line 69
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$b;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->U2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$b;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic B2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)LOU0/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic C2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->M2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic D2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->P2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->Q2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->R2(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->S2(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->T2(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final L2()Landroid/text/TextWatcher;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->m0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/text/TextWatcher;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final O2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->M2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->F3()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method private final P2(Z)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/e;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/buttons/DSButton;->setEnabled(Z)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v0, v0, LOU0/e;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 15
    .line 16
    if-eqz p1, :cond_0

    .line 17
    .line 18
    const/high16 p1, 0x3f800000    # 1.0f

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const p1, 0x3ec28f5c

    .line 22
    .line 23
    .line 24
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setAlpha(F)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public static final U2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$b;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final V2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->N2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->V2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->O2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final I2()LOU0/e;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->l0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LOU0/e;

    .line 13
    .line 14
    return-object v0
.end method

.method public final J2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->j0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final K2()LAX0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->k0:LAX0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final M2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->n0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final N2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Q2()V
    .locals 14

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->J2()LzX0/k;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    new-instance v3, Ly01/g;

    .line 24
    .line 25
    sget-object v4, Ly01/i$a;->a:Ly01/i$a;

    .line 26
    .line 27
    sget v0, Lpb/k;->toto_promocode_not_found:I

    .line 28
    .line 29
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v5

    .line 33
    const/16 v10, 0x3c

    .line 34
    .line 35
    const/4 v11, 0x0

    .line 36
    const/4 v6, 0x0

    .line 37
    const/4 v7, 0x0

    .line 38
    const/4 v8, 0x0

    .line 39
    const/4 v9, 0x0

    .line 40
    invoke-direct/range {v3 .. v11}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 41
    .line 42
    .line 43
    const/16 v12, 0x1fc

    .line 44
    .line 45
    const/4 v13, 0x0

    .line 46
    const/4 v5, 0x0

    .line 47
    const/4 v7, 0x0

    .line 48
    const/4 v8, 0x0

    .line 49
    const/4 v10, 0x0

    .line 50
    move-object v4, p0

    .line 51
    invoke-static/range {v2 .. v13}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 52
    .line 53
    .line 54
    return-void
.end method

.method public final R2(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;)V
    .locals 14

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->J2()LzX0/k;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-virtual {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;->a()Ly01/g;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget-object v6, p1, LOU0/e;->b:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 32
    .line 33
    const/16 v12, 0x1f4

    .line 34
    .line 35
    const/4 v13, 0x0

    .line 36
    const/4 v5, 0x0

    .line 37
    const/4 v7, 0x0

    .line 38
    const/4 v8, 0x0

    .line 39
    const/4 v9, 0x0

    .line 40
    const/4 v10, 0x0

    .line 41
    const/4 v11, 0x0

    .line 42
    move-object v4, p0

    .line 43
    invoke-static/range {v2 .. v13}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 44
    .line 45
    .line 46
    return-void
.end method

.method public final S2(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;)V
    .locals 15

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v2

    .line 14
    :goto_0
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-virtual {v0}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 24
    .line 25
    if-eqz v1, :cond_2

    .line 26
    .line 27
    move-object v2, v0

    .line 28
    check-cast v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 29
    .line 30
    :cond_2
    if-eqz v2, :cond_3

    .line 31
    .line 32
    const/4 v0, 0x0

    .line 33
    invoke-virtual {v2, v0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 34
    .line 35
    .line 36
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->J2()LzX0/k;

    .line 37
    .line 38
    .line 39
    move-result-object v3

    .line 40
    new-instance v4, Ly01/g;

    .line 41
    .line 42
    sget-object v5, Ly01/i$c;->a:Ly01/i$c;

    .line 43
    .line 44
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;->a()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v6

    .line 48
    const/16 v11, 0x3c

    .line 49
    .line 50
    const/4 v12, 0x0

    .line 51
    const/4 v7, 0x0

    .line 52
    const/4 v8, 0x0

    .line 53
    const/4 v9, 0x0

    .line 54
    const/4 v10, 0x0

    .line 55
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 56
    .line 57
    .line 58
    const/16 v13, 0x1fc

    .line 59
    .line 60
    const/4 v14, 0x0

    .line 61
    const/4 v6, 0x0

    .line 62
    const/4 v8, 0x0

    .line 63
    const/4 v9, 0x0

    .line 64
    const/4 v11, 0x0

    .line 65
    move-object v5, p0

    .line 66
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 67
    .line 68
    .line 69
    return-void
.end method

.method public final T2(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;)V
    .locals 19

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-eqz v2, :cond_0

    .line 11
    .line 12
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    move-object v1, v3

    .line 16
    :goto_0
    if-eqz v1, :cond_1

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-virtual {v1, v2}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 20
    .line 21
    .line 22
    :cond_1
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->K2()LAX0/b;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    new-instance v4, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;

    .line 27
    .line 28
    sget v2, Lpb/k;->bet_processed_successfully:I

    .line 29
    .line 30
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;->b()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    sget v2, Lpb/k;->history:I

    .line 39
    .line 40
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v7

    .line 44
    sget v2, Lpb/k;->continue_action:I

    .line 45
    .line 46
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    sget v2, Lpb/k;->bet_sum:I

    .line 51
    .line 52
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v10

    .line 56
    const/16 v13, 0xd0

    .line 57
    .line 58
    const/4 v14, 0x0

    .line 59
    const/4 v9, 0x0

    .line 60
    const/4 v11, 0x0

    .line 61
    const/4 v12, 0x0

    .line 62
    invoke-direct/range {v4 .. v14}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 63
    .line 64
    .line 65
    new-instance v5, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;

    .line 66
    .line 67
    sget v2, Lpb/k;->promo:I

    .line 68
    .line 69
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;->d()Ljava/lang/String;

    .line 74
    .line 75
    .line 76
    move-result-object v6

    .line 77
    new-instance v7, Ljava/lang/StringBuilder;

    .line 78
    .line 79
    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    .line 80
    .line 81
    .line 82
    invoke-virtual {v7, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 83
    .line 84
    .line 85
    const-string v2, ": "

    .line 86
    .line 87
    invoke-virtual {v7, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 88
    .line 89
    .line 90
    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object v6

    .line 97
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;->c()Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v7

    .line 101
    invoke-virtual/range {p1 .. p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;->a()J

    .line 102
    .line 103
    .line 104
    move-result-wide v8

    .line 105
    invoke-static {v8, v9}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 106
    .line 107
    .line 108
    move-result-object v13

    .line 109
    const/16 v17, 0x43c

    .line 110
    .line 111
    const/16 v18, 0x0

    .line 112
    .line 113
    const/4 v8, 0x0

    .line 114
    const/4 v9, 0x0

    .line 115
    const/4 v10, 0x0

    .line 116
    const-string v12, "REQUEST_SUCCESS_BET_KEY"

    .line 117
    .line 118
    const-string v14, "TOTO"

    .line 119
    .line 120
    const/4 v15, 0x0

    .line 121
    const/16 v16, 0x0

    .line 122
    .line 123
    invoke-direct/range {v5 .. v18}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;ZLjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 124
    .line 125
    .line 126
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 127
    .line 128
    .line 129
    move-result-object v2

    .line 130
    invoke-virtual {v2}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 131
    .line 132
    .line 133
    move-result-object v2

    .line 134
    invoke-virtual {v1, v4, v5, v2}, LAX0/b;->d(Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;Landroidx/fragment/app/FragmentManager;)V

    .line 135
    .line 136
    .line 137
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 142
    .line 143
    if-eqz v2, :cond_2

    .line 144
    .line 145
    move-object v3, v1

    .line 146
    check-cast v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 147
    .line 148
    :cond_2
    if-eqz v3, :cond_3

    .line 149
    .line 150
    invoke-virtual {v3}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 151
    .line 152
    .line 153
    :cond_3
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/e;->f:Lorg/xbet/uikit/components/dsTextField/DSTextField;

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->L2()Landroid/text/TextWatcher;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v0, v1}, Lorg/xbet/uikit/components/dsTextField/DSTextField;->I(Landroid/text/TextWatcher;)V

    .line 12
    .line 13
    .line 14
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public s2()V
    .locals 0

    .line 1
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iget-object p1, p1, LOU0/e;->f:Lorg/xbet/uikit/components/dsTextField/DSTextField;

    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->L2()Landroid/text/TextWatcher;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/dsTextField/DSTextField;->e(Landroid/text/TextWatcher;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->I2()LOU0/e;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    iget-object p1, p1, LOU0/e;->c:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 22
    .line 23
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/a;

    .line 24
    .line 25
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/a;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V

    .line 26
    .line 27
    .line 28
    const/4 v1, 0x1

    .line 29
    const/4 v2, 0x0

    .line 30
    invoke-static {p1, v2, v0, v1, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LTU0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LTU0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LTU0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v2, v0}, LTU0/d;->a(LwX0/c;)LTU0/c;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0, p0}, LTU0/c;->a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    new-instance v2, Ljava/lang/StringBuilder;

    .line 70
    .line 71
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 72
    .line 73
    .line 74
    const-string v3, "Cannot create dependency "

    .line 75
    .line 76
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->M2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->D3()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->M2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->B3()Lkotlinx/coroutines/flow/e;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->M2()Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->C3()Lkotlinx/coroutines/flow/e;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$3;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v4

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v5, 0x3

    .line 108
    const/4 v6, 0x0

    .line 109
    const/4 v2, 0x0

    .line 110
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 111
    .line 112
    .line 113
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
