.class public final Lorg/xplatform/aggregator/impl/tvbet/presentation/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/<PERSON>an;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lxb1/a;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgk/b;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lxb1/a;",
            ">;",
            "LBc/a<",
            "Lgk/b;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->g:LBc/a;

    .line 17
    .line 18
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/tvbet/presentation/c;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "Lxb1/a;",
            ">;",
            "LBc/a<",
            "Lgk/b;",
            ">;",
            "LBc/a<",
            "LSX0/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lfk/l;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/c;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;

    .line 2
    .line 3
    move-object v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    move-object v7, p6

    .line 10
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static c(ZLxb1/a;Lgk/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lfk/l;Lm8/a;)Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;
    .locals 8

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 2
    .line 3
    move v1, p0

    .line 4
    move-object v2, p1

    .line 5
    move-object v3, p2

    .line 6
    move-object v4, p3

    .line 7
    move-object v5, p4

    .line 8
    move-object v6, p5

    .line 9
    move-object v7, p6

    .line 10
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;-><init>(ZLxb1/a;Lgk/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lfk/l;Lm8/a;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/lang/Boolean;

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->b:LBc/a;

    .line 14
    .line 15
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    move-object v2, v0

    .line 20
    check-cast v2, Lxb1/a;

    .line 21
    .line 22
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->c:LBc/a;

    .line 23
    .line 24
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    move-object v3, v0

    .line 29
    check-cast v3, Lgk/b;

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->d:LBc/a;

    .line 32
    .line 33
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    move-object v4, v0

    .line 38
    check-cast v4, LSX0/a;

    .line 39
    .line 40
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->e:LBc/a;

    .line 41
    .line 42
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    move-object v5, v0

    .line 47
    check-cast v5, Lorg/xbet/ui_common/utils/internet/a;

    .line 48
    .line 49
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->f:LBc/a;

    .line 50
    .line 51
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    move-object v6, v0

    .line 56
    check-cast v6, Lfk/l;

    .line 57
    .line 58
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->g:LBc/a;

    .line 59
    .line 60
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    move-object v7, v0

    .line 65
    check-cast v7, Lm8/a;

    .line 66
    .line 67
    invoke-static/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->c(ZLxb1/a;Lgk/b;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;Lfk/l;Lm8/a;)Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 68
    .line 69
    .line 70
    move-result-object v0

    .line 71
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/c;->b()Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
