.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_prizes_alt_design.TournamentPrizesAltDesignViewModel$onParticipateClick$2"
    f = "TournamentPrizesAltDesignViewModel.kt"
    l = {
        0xbe
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->K3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

.field final synthetic $screenName:Ljava/lang/String;

.field final synthetic $tournamentId:J

.field final synthetic $tournamentTitle:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;",
            "J",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    move-object/from16 v2, p1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw v1

    .line 28
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 32
    .line 33
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)Lw81/g;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    iget-wide v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    .line 38
    .line 39
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 40
    .line 41
    iput v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->label:I

    .line 42
    .line 43
    invoke-interface {v2, v4, v5, v6, v0}, Lw81/g;->a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    if-ne v2, v1, :cond_2

    .line 48
    .line 49
    return-object v1

    .line 50
    :cond_2
    :goto_0
    check-cast v2, Lh81/b;

    .line 51
    .line 52
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 53
    .line 54
    invoke-static {v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;Lh81/b;)Z

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    if-eqz v1, :cond_3

    .line 59
    .line 60
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 61
    .line 62
    iget-wide v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    .line 63
    .line 64
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    .line 65
    .line 66
    invoke-static {v1, v3, v4, v2, v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLh81/b;Ljava/lang/String;)V

    .line 67
    .line 68
    .line 69
    :cond_3
    instance-of v1, v2, Lh81/b$g;

    .line 70
    .line 71
    if-eqz v1, :cond_4

    .line 72
    .line 73
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 74
    .line 75
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LwX0/C;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    invoke-virtual {v1}, LwX0/D;->a()LwX0/c;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    if-eqz v1, :cond_9

    .line 84
    .line 85
    iget-object v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 86
    .line 87
    iget-wide v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    .line 88
    .line 89
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$kind:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 90
    .line 91
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    .line 92
    .line 93
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$screenName:Ljava/lang/String;

    .line 94
    .line 95
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/l;

    .line 96
    .line 97
    invoke-direct/range {v2 .. v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/l;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    invoke-virtual {v1, v2}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 101
    .line 102
    .line 103
    goto/16 :goto_1

    .line 104
    .line 105
    :cond_4
    instance-of v1, v2, Lh81/b$c;

    .line 106
    .line 107
    if-eqz v1, :cond_5

    .line 108
    .line 109
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 110
    .line 111
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LP91/b;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    new-instance v2, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 116
    .line 117
    new-instance v7, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsScreen;

    .line 118
    .line 119
    const-wide/16 v3, 0x0

    .line 120
    .line 121
    invoke-direct {v7, v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsScreen;-><init>(J)V

    .line 122
    .line 123
    .line 124
    const/16 v14, 0xf7

    .line 125
    .line 126
    const/4 v15, 0x0

    .line 127
    const/4 v3, 0x0

    .line 128
    const/4 v4, 0x0

    .line 129
    const-wide/16 v5, 0x0

    .line 130
    .line 131
    const/4 v8, 0x0

    .line 132
    const-wide/16 v9, 0x0

    .line 133
    .line 134
    const-wide/16 v11, 0x0

    .line 135
    .line 136
    const/4 v13, 0x0

    .line 137
    invoke-direct/range {v2 .. v15}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 138
    .line 139
    .line 140
    new-instance v3, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 141
    .line 142
    new-instance v4, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 143
    .line 144
    iget-wide v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    .line 145
    .line 146
    sget-object v7, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->GAMES:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 147
    .line 148
    iget-object v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentTitle:Ljava/lang/String;

    .line 149
    .line 150
    const/16 v10, 0x8

    .line 151
    .line 152
    const/4 v11, 0x0

    .line 153
    const/4 v9, 0x0

    .line 154
    invoke-direct/range {v4 .. v11}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;-><init>(JLorg/xplatform/aggregator/api/navigation/TournamentsPage;Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 155
    .line 156
    .line 157
    const/16 v15, 0xf7

    .line 158
    .line 159
    const/16 v16, 0x0

    .line 160
    .line 161
    move-object v8, v4

    .line 162
    const/4 v4, 0x0

    .line 163
    const/4 v5, 0x0

    .line 164
    const-wide/16 v6, 0x0

    .line 165
    .line 166
    const/4 v9, 0x0

    .line 167
    const-wide/16 v10, 0x0

    .line 168
    .line 169
    const-wide/16 v12, 0x0

    .line 170
    .line 171
    const/4 v14, 0x0

    .line 172
    invoke-direct/range {v3 .. v16}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 173
    .line 174
    .line 175
    invoke-virtual {v1, v2, v3}, LP91/b;->b(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 176
    .line 177
    .line 178
    goto/16 :goto_1

    .line 179
    .line 180
    :cond_5
    instance-of v1, v2, Lh81/b$a;

    .line 181
    .line 182
    const/4 v3, 0x0

    .line 183
    if-eqz v1, :cond_6

    .line 184
    .line 185
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 186
    .line 187
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 188
    .line 189
    .line 190
    move-result-object v1

    .line 191
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$b$a;

    .line 192
    .line 193
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 194
    .line 195
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 196
    .line 197
    .line 198
    move-result-object v4

    .line 199
    sget v5, Lpb/k;->app_win_congratulations:I

    .line 200
    .line 201
    new-array v6, v3, [Ljava/lang/Object;

    .line 202
    .line 203
    invoke-interface {v4, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 204
    .line 205
    .line 206
    move-result-object v4

    .line 207
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 208
    .line 209
    invoke-static {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 210
    .line 211
    .line 212
    move-result-object v5

    .line 213
    sget v6, Lpb/k;->tournamnet_enrolled_success:I

    .line 214
    .line 215
    new-array v7, v3, [Ljava/lang/Object;

    .line 216
    .line 217
    invoke-interface {v5, v6, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 218
    .line 219
    .line 220
    move-result-object v5

    .line 221
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 222
    .line 223
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 224
    .line 225
    .line 226
    move-result-object v6

    .line 227
    sget v7, Lpb/k;->ok_new:I

    .line 228
    .line 229
    new-array v3, v3, [Ljava/lang/Object;

    .line 230
    .line 231
    invoke-interface {v6, v7, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 232
    .line 233
    .line 234
    move-result-object v3

    .line 235
    sget-object v6, Lorg/xbet/uikit/components/dialog/AlertType;->SUCCESS:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 236
    .line 237
    invoke-direct {v2, v4, v5, v3, v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 238
    .line 239
    .line 240
    invoke-virtual {v1, v2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 241
    .line 242
    .line 243
    iget-object v7, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 244
    .line 245
    iget-wide v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->$tournamentId:J

    .line 246
    .line 247
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)J

    .line 248
    .line 249
    .line 250
    move-result-wide v10

    .line 251
    const/4 v12, 0x0

    .line 252
    invoke-virtual/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->L3(JJZ)V

    .line 253
    .line 254
    .line 255
    goto/16 :goto_1

    .line 256
    .line 257
    :cond_6
    instance-of v1, v2, Lh81/b$b;

    .line 258
    .line 259
    if-eqz v1, :cond_8

    .line 260
    .line 261
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 262
    .line 263
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 264
    .line 265
    .line 266
    move-result-object v1

    .line 267
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 268
    .line 269
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 270
    .line 271
    .line 272
    move-result-object v4

    .line 273
    sget v5, Lpb/k;->tournamenet_dialor_title:I

    .line 274
    .line 275
    new-array v6, v3, [Ljava/lang/Object;

    .line 276
    .line 277
    invoke-interface {v4, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 278
    .line 279
    .line 280
    move-result-object v4

    .line 281
    check-cast v2, Lh81/b$b;

    .line 282
    .line 283
    invoke-virtual {v2}, Lh81/b$b;->b()Ljava/lang/String;

    .line 284
    .line 285
    .line 286
    move-result-object v2

    .line 287
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 288
    .line 289
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 290
    .line 291
    .line 292
    move-result v6

    .line 293
    if-nez v6, :cond_7

    .line 294
    .line 295
    invoke-static {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 296
    .line 297
    .line 298
    move-result-object v2

    .line 299
    sget v5, Lpb/k;->unknown_service_error:I

    .line 300
    .line 301
    new-array v6, v3, [Ljava/lang/Object;

    .line 302
    .line 303
    invoke-interface {v2, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 304
    .line 305
    .line 306
    move-result-object v2

    .line 307
    :cond_7
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 308
    .line 309
    invoke-static {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 310
    .line 311
    .line 312
    move-result-object v5

    .line 313
    sget v6, Lpb/k;->ok_new:I

    .line 314
    .line 315
    new-array v3, v3, [Ljava/lang/Object;

    .line 316
    .line 317
    invoke-interface {v5, v6, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 318
    .line 319
    .line 320
    move-result-object v3

    .line 321
    sget-object v5, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 322
    .line 323
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$b$a;

    .line 324
    .line 325
    invoke-direct {v6, v4, v2, v3, v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 326
    .line 327
    .line 328
    invoke-virtual {v1, v6}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 329
    .line 330
    .line 331
    goto :goto_1

    .line 332
    :cond_8
    instance-of v1, v2, Lh81/b$f;

    .line 333
    .line 334
    if-eqz v1, :cond_9

    .line 335
    .line 336
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 337
    .line 338
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 339
    .line 340
    .line 341
    move-result-object v1

    .line 342
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$b$a;

    .line 343
    .line 344
    iget-object v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 345
    .line 346
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 347
    .line 348
    .line 349
    move-result-object v4

    .line 350
    sget v5, Lpb/k;->tournamenet_dialor_title:I

    .line 351
    .line 352
    new-array v6, v3, [Ljava/lang/Object;

    .line 353
    .line 354
    invoke-interface {v4, v5, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 355
    .line 356
    .line 357
    move-result-object v4

    .line 358
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 359
    .line 360
    invoke-static {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 361
    .line 362
    .line 363
    move-result-object v5

    .line 364
    sget v6, Lpb/k;->unknown_service_error:I

    .line 365
    .line 366
    new-array v7, v3, [Ljava/lang/Object;

    .line 367
    .line 368
    invoke-interface {v5, v6, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 369
    .line 370
    .line 371
    move-result-object v5

    .line 372
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$onParticipateClick$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    .line 373
    .line 374
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)LHX0/e;

    .line 375
    .line 376
    .line 377
    move-result-object v6

    .line 378
    sget v7, Lpb/k;->ok_new:I

    .line 379
    .line 380
    new-array v3, v3, [Ljava/lang/Object;

    .line 381
    .line 382
    invoke-interface {v6, v7, v3}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 383
    .line 384
    .line 385
    move-result-object v3

    .line 386
    sget-object v6, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 387
    .line 388
    invoke-direct {v2, v4, v5, v3, v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 389
    .line 390
    .line 391
    invoke-virtual {v1, v2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 392
    .line 393
    .line 394
    :cond_9
    :goto_1
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 395
    .line 396
    return-object v1
.end method
