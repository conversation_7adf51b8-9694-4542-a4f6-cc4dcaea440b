.class public final synthetic Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:LUX0/k;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;LUX0/k;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;->a:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;->b:Ljava/lang/String;

    iput-object p3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;->c:LUX0/k;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;->a:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/V;->c:LUX0/k;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, v2, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularSimpleBannersContainerDelegateKt;->b(Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/lang/String;LUX0/k;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
