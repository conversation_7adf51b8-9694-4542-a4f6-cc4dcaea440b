.class public final synthetic LK31/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LK31/d;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LK31/d;->a:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    invoke-static {v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->e(Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Lorg/xbet/uikit/components/counter/a;

    move-result-object v0

    return-object v0
.end method
