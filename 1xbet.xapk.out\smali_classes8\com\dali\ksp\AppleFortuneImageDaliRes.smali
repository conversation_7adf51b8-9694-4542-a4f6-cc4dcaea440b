طيب.class public final Lcom/dali/ksp/AppleFortuneImageDaliRes;
.super Lorg/xbet/core/presentation/dali/res/AppleFortuneImageDali;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u00c6\u0002\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003R\u001c\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0005\u0010\u0006\u001a\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lcom/dali/ksp/AppleFortuneImageDaliRes;",
        "Lorg/xbet/core/presentation/dali/res/AppleFortuneImageDali;",
        "<init>",
        "()V",
        "Lcom/dali/android/processor/b;",
        "background",
        "Lcom/dali/android/processor/b;",
        "getBackground",
        "()Lcom/dali/android/processor/b;",
        "core_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lcom/dali/ksp/AppleFortuneImageDaliRes;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private static final background:Lcom/dali/android/processor/b;


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 1
    new-instance v0, Lcom/dali/ksp/AppleFortuneImageDaliRes;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/dali/ksp/AppleFortuneImageDaliRes;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/dali/ksp/AppleFortuneImageDaliRes;->INSTANCE:Lcom/dali/ksp/AppleFortuneImageDaliRes;

    .line 7
    .line 8
    new-instance v0, Lcom/dali/android/processor/b;

    .line 9
    .line 10
    sget v1, LIv/c;->game_apple_fortune_placeholder:I

    .line 11
    .line 12
    const-string v2, "background.webp"

    .line 13
    .line 14
    const-string v3, "AppleFortuneImageDali.background"

    .line 15
    .line 16
    invoke-direct {v0, v3, v1, v2}, Lcom/dali/android/processor/b;-><init>(Ljava/lang/String;ILjava/lang/String;)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lcom/dali/ksp/AppleFortuneImageDaliRes;->background:Lcom/dali/android/processor/b;

    .line 20
    .line 21
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/core/presentation/dali/res/AppleFortuneImageDali;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public getBackground()Lcom/dali/android/processor/b;
    .locals 1

    .line 1
    sget-object v0, Lcom/dali/ksp/AppleFortuneImageDaliRes;->background:Lcom/dali/android/processor/b;

    .line 2
    .line 3
    return-object v0
.end method
