.class public final synthetic LO31/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LO31/b;


# direct methods
.method public synthetic constructor <init>(LO31/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO31/a;->a:LO31/b;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LO31/a;->a:LO31/b;

    check-cast p1, LP31/g;

    invoke-static {v0, p1}, LO31/b;->o(LO31/b;LP31/g;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
