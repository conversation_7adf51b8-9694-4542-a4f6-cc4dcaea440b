.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00f2\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008+\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u0000 _2\u00020\u0001:\u0001aB\u0091\u0001\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u00a2\u0006\u0004\u0008$\u0010%J\u0013\u0010(\u001a\u0008\u0012\u0004\u0012\u00020\'0&\u00a2\u0006\u0004\u0008(\u0010)J\u0013\u0010+\u001a\u0008\u0012\u0004\u0012\u00020*0&\u00a2\u0006\u0004\u0008+\u0010)J\r\u0010-\u001a\u00020,\u00a2\u0006\u0004\u0008-\u0010.Ji\u0010@\u001a\u00020,2\u0006\u00100\u001a\u00020/2\u0006\u00102\u001a\u0002012\u0006\u00103\u001a\u0002012\u0008\u00104\u001a\u0004\u0018\u0001012\u0006\u00106\u001a\u0002052\u0006\u00108\u001a\u0002072\u0006\u00109\u001a\u0002012\u000c\u0010<\u001a\u0008\u0012\u0004\u0012\u00020;0:2\u0008\u0008\u0002\u0010>\u001a\u00020=2\u0008\u0008\u0002\u0010?\u001a\u00020=\u00a2\u0006\u0004\u0008@\u0010AJ9\u0010J\u001a\u00020,2\u0006\u0010C\u001a\u00020B2\u0006\u0010D\u001a\u0002052\u0006\u0010F\u001a\u00020E2\u0012\u0010I\u001a\u000e\u0012\u0004\u0012\u00020H\u0012\u0004\u0012\u00020,0G\u00a2\u0006\u0004\u0008J\u0010KJ9\u0010N\u001a\u00020,2\u0006\u0010C\u001a\u00020B2\u0006\u0010M\u001a\u00020L2\u0006\u0010D\u001a\u0002052\u0012\u0010I\u001a\u000e\u0012\u0004\u0012\u00020H\u0012\u0004\u0012\u00020,0G\u00a2\u0006\u0004\u0008N\u0010OJ\u0010\u0010P\u001a\u00020,H\u0086@\u00a2\u0006\u0004\u0008P\u0010QJ(\u0010S\u001a\u00020,2\u0006\u0010C\u001a\u00020B2\u0006\u0010R\u001a\u00020=2\u0006\u0010D\u001a\u000205H\u0086@\u00a2\u0006\u0004\u0008S\u0010TJ;\u0010X\u001a\u00020,2\u0006\u0010V\u001a\u00020U2\u0006\u0010W\u001a\u0002052\u0006\u0010F\u001a\u00020E2\u0012\u0010I\u001a\u000e\u0012\u0004\u0012\u00020H\u0012\u0004\u0012\u00020,0GH\u0002\u00a2\u0006\u0004\u0008X\u0010YJ_\u0010\\\u001a\u00020,2\u0006\u00100\u001a\u00020/2\u0006\u0010Z\u001a\u0002012\u0006\u0010[\u001a\u0002012\u0008\u00104\u001a\u0004\u0018\u0001012\u0006\u00106\u001a\u0002052\u0006\u00108\u001a\u0002072\u0006\u00109\u001a\u0002012\u000c\u0010<\u001a\u0008\u0012\u0004\u0012\u00020;0:2\u0006\u0010?\u001a\u00020=H\u0002\u00a2\u0006\u0004\u0008\\\u0010]J\'\u0010_\u001a\u00020,2\u0006\u0010C\u001a\u00020B2\u0006\u0010^\u001a\u00020/2\u0006\u0010D\u001a\u000205H\u0002\u00a2\u0006\u0004\u0008_\u0010`R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008k\u0010lR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008m\u0010nR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008o\u0010pR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008q\u0010rR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008s\u0010tR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008u\u0010vR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008w\u0010xR\u0014\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0014\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008{\u0010|R\u0014\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010}R\u0014\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010~R\u0015\u0010\u0080\u0001\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010\u007fR\u001d\u0010\u0083\u0001\u001a\t\u0012\u0004\u0012\u00020\'0\u0081\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008-\u0010\u0082\u0001R\u001b\u0010\u0086\u0001\u001a\u0005\u0018\u00010\u0084\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008X\u0010\u0085\u0001R\u001d\u0010\u0087\u0001\u001a\t\u0012\u0004\u0012\u00020*0\u0081\u00018\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\\\u0010\u0082\u0001R\u001b\u0010\u0088\u0001\u001a\u0005\u0018\u00010\u0084\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008J\u0010\u0085\u0001R\u001b\u0010\u0089\u0001\u001a\u0005\u0018\u00010\u0084\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008@\u0010\u0085\u0001\u00a8\u0006\u008a\u0001"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "",
        "LQW0/c;",
        "coroutinesLib",
        "LwX0/C;",
        "routerHolder",
        "LwX0/a;",
        "appScreensProvider",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;",
        "addFavoritePopularUseCase",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;",
        "removeFavoritePopularUseCase",
        "LJT/a;",
        "addAggregatorLastActionUseCase",
        "Lv81/d;",
        "checkBalanceForAggregatorWarningUseCase",
        "Lv81/u;",
        "updateBalanceForAggregatorWarningUseCase",
        "Le81/b;",
        "getAggregatorOpenGameBalanceResultModelScenario",
        "Lak/a;",
        "balanceFeature",
        "Lc81/c;",
        "aggregatorScreenProvider",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "aggregatorScreenFactory",
        "LVg0/a;",
        "promotionsNewsScreenFactory",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Le81/d;",
        "getGameToOpenScenario",
        "Lm8/a;",
        "dispatchers",
        "<init>",
        "(LQW0/c;LwX0/C;LwX0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;LJT/a;Lv81/d;Lv81/u;Le81/b;Lak/a;Lc81/c;Lorg/xplatform/aggregator/api/navigation/a;LVg0/a;Lorg/xbet/remoteconfig/domain/usecases/i;Le81/d;Lm8/a;)V",
        "Lkotlinx/coroutines/flow/Z;",
        "Lq81/d;",
        "o",
        "()Lkotlinx/coroutines/flow/Z;",
        "Lq81/c;",
        "p",
        "",
        "r",
        "()V",
        "",
        "partitionId",
        "",
        "providerId",
        "providerName",
        "imgBanner",
        "",
        "subCategoryId",
        "Lorg/xplatform/aggregator/api/model/BrandType;",
        "brandType",
        "description",
        "",
        "Lorg/xplatform/aggregator/api/model/PartitionBrandModel;",
        "partitions",
        "",
        "hasAggregatorBrands",
        "hasAggregatorBrandsFullInfo",
        "v",
        "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/model/BrandType;Ljava/lang/String;Ljava/util/List;ZZ)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "subcategoryId",
        "Lkotlinx/coroutines/N;",
        "coroutineScope",
        "Lkotlin/Function1;",
        "",
        "errorHandler",
        "u",
        "(Lorg/xplatform/aggregator/api/model/Game;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "q",
        "(Lorg/xplatform/aggregator/api/model/Game;Lorg/xbet/balance/model/BalanceModel;ILkotlin/jvm/functions/Function1;)V",
        "x",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "isFavorite",
        "y",
        "(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xplatform/banners/api/domain/models/BannerModel;",
        "banner",
        "position",
        "s",
        "(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V",
        "brandId",
        "brandName",
        "t",
        "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/model/BrandType;Ljava/lang/String;Ljava/util/List;Z)V",
        "balanceId",
        "w",
        "(Lorg/xplatform/aggregator/api/model/Game;JI)V",
        "a",
        "LwX0/C;",
        "b",
        "LwX0/a;",
        "c",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "d",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;",
        "e",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;",
        "f",
        "LJT/a;",
        "g",
        "Lv81/d;",
        "h",
        "Lv81/u;",
        "i",
        "Le81/b;",
        "j",
        "Lak/a;",
        "k",
        "Lc81/c;",
        "l",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "m",
        "LVg0/a;",
        "n",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "Le81/d;",
        "Lm8/a;",
        "Lkotlinx/coroutines/N;",
        "delegateScope",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "bannersViewAction",
        "Lkotlinx/coroutines/x0;",
        "Lkotlinx/coroutines/x0;",
        "openBannerJob",
        "openGameViewAction",
        "launchGameJob",
        "onGameClickJob",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final w:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LwX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:LJT/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lv81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Lv81/u;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Le81/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lak/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lc81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lorg/xplatform/aggregator/api/navigation/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:LVg0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Le81/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlinx/coroutines/N;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lq81/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public s:Lkotlinx/coroutines/x0;

.field public final t:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lq81/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public u:Lkotlinx/coroutines/x0;

.field public v:Lkotlinx/coroutines/x0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->w:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$a;

    return-void
.end method

.method public constructor <init>(LQW0/c;LwX0/C;LwX0/a;Lorg/xbet/ui_common/utils/internet/a;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;LJT/a;Lv81/d;Lv81/u;Le81/b;Lak/a;Lc81/c;Lorg/xplatform/aggregator/api/navigation/a;LVg0/a;Lorg/xbet/remoteconfig/domain/usecases/i;Le81/d;Lm8/a;)V
    .locals 0
    .param p1    # LQW0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwX0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LJT/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lv81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lv81/u;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Le81/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Lak/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lc81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xplatform/aggregator/api/navigation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LVg0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Le81/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->a:LwX0/C;

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->b:LwX0/a;

    .line 7
    .line 8
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->c:Lorg/xbet/ui_common/utils/internet/a;

    .line 9
    .line 10
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->d:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;

    .line 11
    .line 12
    iput-object p6, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->e:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;

    .line 13
    .line 14
    iput-object p7, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->f:LJT/a;

    .line 15
    .line 16
    iput-object p8, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->g:Lv81/d;

    .line 17
    .line 18
    iput-object p9, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->h:Lv81/u;

    .line 19
    .line 20
    iput-object p10, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->i:Le81/b;

    .line 21
    .line 22
    iput-object p11, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->j:Lak/a;

    .line 23
    .line 24
    iput-object p12, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->k:Lc81/c;

    .line 25
    .line 26
    iput-object p13, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->l:Lorg/xplatform/aggregator/api/navigation/a;

    .line 27
    .line 28
    iput-object p14, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->m:LVg0/a;

    .line 29
    .line 30
    iput-object p15, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->n:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 31
    .line 32
    move-object/from16 p2, p16

    .line 33
    .line 34
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->o:Le81/d;

    .line 35
    .line 36
    move-object/from16 p2, p17

    .line 37
    .line 38
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->p:Lm8/a;

    .line 39
    .line 40
    invoke-interface {p1}, LQW0/c;->a()Lm8/a;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    const/4 p2, 0x0

    .line 49
    const/4 p3, 0x1

    .line 50
    invoke-static {p2, p3, p2}, Lkotlinx/coroutines/Q0;->b(Lkotlinx/coroutines/x0;ILjava/lang/Object;)Lkotlinx/coroutines/z;

    .line 51
    .line 52
    .line 53
    move-result-object p2

    .line 54
    invoke-virtual {p1, p2}, Lkotlin/coroutines/a;->plus(Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-static {p1}, Lkotlinx/coroutines/O;->a(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->q:Lkotlinx/coroutines/N;

    .line 63
    .line 64
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 65
    .line 66
    sget-object p2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 67
    .line 68
    invoke-direct {p1, p3, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 69
    .line 70
    .line 71
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->r:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 72
    .line 73
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 74
    .line 75
    invoke-direct {p1, p3, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 76
    .line 77
    .line 78
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->t:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 79
    .line 80
    return-void
.end method

.method public static final synthetic a(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)LJT/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->f:LJT/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lc81/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->k:Lc81/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lak/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->j:Lak/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->r:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lv81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->g:Lv81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/internet/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->c:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Le81/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->i:Le81/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Le81/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->o:Le81/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->t:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)LVg0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->m:LVg0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic k(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->a:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic l(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lv81/u;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->h:Lv81/u;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic m(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->s(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic n(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/api/model/Game;JI)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->w(Lorg/xplatform/aggregator/api/model/Game;JI)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final o()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lq81/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->r:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Lkotlinx/coroutines/flow/Z;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/Z<",
            "Lq81/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->t:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final q(Lorg/xplatform/aggregator/api/model/Game;Lorg/xbet/balance/model/BalanceModel;ILkotlin/jvm/functions/Function1;)V
    .locals 11
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "Lorg/xbet/balance/model/BalanceModel;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->u:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->q:Lkotlinx/coroutines/N;

    .line 11
    .line 12
    new-instance v4, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$onBalanceChosen$1;

    .line 13
    .line 14
    const/4 v9, 0x0

    .line 15
    move-object v7, p0

    .line 16
    move-object v6, p1

    .line 17
    move-object v5, p2

    .line 18
    move v8, p3

    .line 19
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$onBalanceChosen$1;-><init>(Lorg/xbet/balance/model/BalanceModel;Lorg/xplatform/aggregator/api/model/Game;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    move-object p1, v7

    .line 23
    const/16 v9, 0xe

    .line 24
    .line 25
    const/4 v10, 0x0

    .line 26
    const/4 v5, 0x0

    .line 27
    const/4 v6, 0x0

    .line 28
    const/4 v7, 0x0

    .line 29
    move-object v8, v4

    .line 30
    move-object v4, p4

    .line 31
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 32
    .line 33
    .line 34
    move-result-object p2

    .line 35
    iput-object p2, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->u:Lkotlinx/coroutines/x0;

    .line 36
    .line 37
    return-void
.end method

.method public final r()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->q:Lkotlinx/coroutines/N;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/O;->e(Lkotlinx/coroutines/N;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final s(Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/banners/api/domain/models/BannerModel;",
            "I",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->s:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    new-instance v4, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openBanner$1;

    .line 11
    .line 12
    invoke-direct {v4, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openBanner$1;-><init>(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    iget-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->p:Lm8/a;

    .line 16
    .line 17
    invoke-interface {p4}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 18
    .line 19
    .line 20
    move-result-object v6

    .line 21
    new-instance v8, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openBanner$2;

    .line 22
    .line 23
    invoke-direct {v8, p0, p1, p2, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openBanner$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/banners/api/domain/models/BannerModel;ILkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v9, 0xa

    .line 27
    .line 28
    const/4 v10, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    move-object v3, p3

    .line 32
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->s:Lkotlinx/coroutines/x0;

    .line 37
    .line 38
    return-void
.end method

.method public final t(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/model/BrandType;Ljava/lang/String;Ljava/util/List;Z)V
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I",
            "Lorg/xplatform/aggregator/api/model/BrandType;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/PartitionBrandModel;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->a:LwX0/C;

    .line 4
    .line 5
    invoke-virtual {v1}, LwX0/D;->a()LwX0/c;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->l:Lorg/xplatform/aggregator/api/navigation/a;

    .line 12
    .line 13
    new-instance v3, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Providers;

    .line 14
    .line 15
    new-instance v4, Lorg/xplatform/aggregator/api/navigation/AggregatorBrandItemModel;

    .line 16
    .line 17
    invoke-static/range {p3 .. p3}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 18
    .line 19
    .line 20
    move-result-wide v7

    .line 21
    const/4 v11, 0x1

    .line 22
    const/16 v17, 0x0

    .line 23
    .line 24
    move-wide/from16 v5, p1

    .line 25
    .line 26
    move-object/from16 v9, p4

    .line 27
    .line 28
    move-object/from16 v10, p5

    .line 29
    .line 30
    move/from16 v12, p6

    .line 31
    .line 32
    move-object/from16 v14, p7

    .line 33
    .line 34
    move-object/from16 v16, p8

    .line 35
    .line 36
    move-object/from16 v15, p9

    .line 37
    .line 38
    move/from16 v13, p10

    .line 39
    .line 40
    invoke-direct/range {v4 .. v17}, Lorg/xplatform/aggregator/api/navigation/AggregatorBrandItemModel;-><init>(JJLjava/lang/String;Ljava/lang/String;ZIZLorg/xplatform/aggregator/api/model/BrandType;Ljava/util/List;Ljava/lang/String;Z)V

    .line 41
    .line 42
    .line 43
    invoke-direct {v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Providers;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorBrandItemModel;)V

    .line 44
    .line 45
    .line 46
    const/4 v4, 0x0

    .line 47
    invoke-interface {v2, v4, v3}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 48
    .line 49
    .line 50
    move-result-object v2

    .line 51
    invoke-virtual {v1, v2}, LwX0/c;->m(Lq4/q;)V

    .line 52
    .line 53
    .line 54
    :cond_0
    return-void
.end method

.method public final u(Lorg/xplatform/aggregator/api/model/Game;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
    .locals 11
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlinx/coroutines/N;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Throwable;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->v:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->u:Lkotlinx/coroutines/x0;

    .line 11
    .line 12
    if-eqz v0, :cond_1

    .line 13
    .line 14
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    :cond_1
    new-instance v8, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;

    .line 18
    .line 19
    invoke-direct {v8, p0, p1, p2, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v9, 0xe

    .line 23
    .line 24
    const/4 v10, 0x0

    .line 25
    const/4 v5, 0x0

    .line 26
    const/4 v6, 0x0

    .line 27
    const/4 v7, 0x0

    .line 28
    move-object v3, p3

    .line 29
    move-object v4, p4

    .line 30
    invoke-static/range {v3 .. v10}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->v:Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    return-void
.end method

.method public final v(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/model/BrandType;Ljava/lang/String;Ljava/util/List;ZZ)V
    .locals 1
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xplatform/aggregator/api/model/BrandType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I",
            "Lorg/xplatform/aggregator/api/model/BrandType;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/PartitionBrandModel;",
            ">;ZZ)V"
        }
    .end annotation

    .line 1
    if-eqz p10, :cond_0

    .line 2
    .line 3
    move-object p10, p9

    .line 4
    move-object p9, p8

    .line 5
    move-object p8, p7

    .line 6
    move p7, p6

    .line 7
    move-object p6, p5

    .line 8
    move-object p5, p4

    .line 9
    move-object p4, p3

    .line 10
    move-wide p2, p1

    .line 11
    move-object p1, p0

    .line 12
    invoke-virtual/range {p1 .. p11}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->t(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/model/BrandType;Ljava/lang/String;Ljava/util/List;Z)V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    move-object p5, p4

    .line 17
    move p7, p6

    .line 18
    move-object p4, p3

    .line 19
    move-wide p2, p1

    .line 20
    move-object p1, p0

    .line 21
    iget-object p6, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->a:LwX0/C;

    .line 22
    .line 23
    invoke-virtual {p6}, LwX0/D;->a()LwX0/c;

    .line 24
    .line 25
    .line 26
    move-result-object p10

    .line 27
    if-eqz p10, :cond_1

    .line 28
    .line 29
    move-object p6, p4

    .line 30
    move-wide p3, p2

    .line 31
    iget-object p2, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->l:Lorg/xplatform/aggregator/api/navigation/a;

    .line 32
    .line 33
    invoke-static {p6}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 34
    .line 35
    .line 36
    move-result-wide p8

    .line 37
    move v0, p7

    .line 38
    move-object p7, p5

    .line 39
    move-wide p5, p8

    .line 40
    move p9, v0

    .line 41
    const/4 p8, 0x1

    .line 42
    invoke-interface/range {p2 .. p9}, Lorg/xplatform/aggregator/api/navigation/a;->i(JJLjava/lang/String;ZI)Lq4/q;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    invoke-virtual {p10, p2}, LwX0/c;->m(Lq4/q;)V

    .line 47
    .line 48
    .line 49
    :cond_1
    return-void
.end method

.method public final w(Lorg/xplatform/aggregator/api/model/Game;JI)V
    .locals 13

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->a:LwX0/C;

    .line 2
    .line 3
    invoke-virtual {v0}, LwX0/D;->a()LwX0/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->b:LwX0/a;

    .line 10
    .line 11
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getProviderId()J

    .line 16
    .line 17
    .line 18
    move-result-wide v4

    .line 19
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNeedTransfer()Z

    .line 20
    .line 21
    .line 22
    move-result v6

    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getProductId()J

    .line 24
    .line 25
    .line 26
    move-result-wide v7

    .line 27
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getNoLoyalty()Z

    .line 28
    .line 29
    .line 30
    move-result v9

    .line 31
    move-wide v10, p2

    .line 32
    move/from16 v12, p4

    .line 33
    .line 34
    invoke-interface/range {v1 .. v12}, LwX0/a;->v(JJZJZJI)Lq4/q;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-virtual {v0, p1}, LwX0/c;->m(Lq4/q;)V

    .line 39
    .line 40
    .line 41
    :cond_0
    return-void
.end method

.method public final x(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_1

    .line 42
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 43
    .line 44
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 45
    .line 46
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 47
    .line 48
    .line 49
    throw p1

    .line 50
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 51
    .line 52
    .line 53
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->j:Lak/a;

    .line 54
    .line 55
    invoke-interface {p1}, Lak/a;->F()Lek/d;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 60
    .line 61
    sget-object v4, Lorg/xbet/balance/model/BalanceRefreshType;->NOW:Lorg/xbet/balance/model/BalanceRefreshType;

    .line 62
    .line 63
    iput v3, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$updateBalance$1;->label:I

    .line 64
    .line 65
    invoke-interface {p1, v2, v4, v0}, Lek/d;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v1, :cond_3

    .line 70
    .line 71
    return-object v1

    .line 72
    :cond_3
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 73
    .line 74
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->j:Lak/a;

    .line 75
    .line 76
    invoke-interface {v0}, Lak/a;->m()Lek/f;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 81
    .line 82
    invoke-interface {v0, v1, p1}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 83
    .line 84
    .line 85
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 86
    .line 87
    return-object p1
.end method

.method public final y(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "ZI",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->n:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    invoke-interface {v0}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lek0/o;->o()Lek0/a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lek0/a;->c()Z

    .line 12
    .line 13
    .line 14
    move-result v4

    .line 15
    if-eqz p2, :cond_1

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->e:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;

    .line 18
    .line 19
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 20
    .line 21
    .line 22
    move-result-wide v2

    .line 23
    move v5, p3

    .line 24
    move-object v6, p4

    .line 25
    invoke-virtual/range {v1 .. v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/h;->a(JZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object p2

    .line 33
    if-ne p1, p2, :cond_0

    .line 34
    .line 35
    return-object p1

    .line 36
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 37
    .line 38
    return-object p1

    .line 39
    :cond_1
    move v5, p3

    .line 40
    move-object v6, p4

    .line 41
    iget-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->d:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;

    .line 42
    .line 43
    invoke-virtual {p2, p1, v4, v5, v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/a;->a(Lorg/xplatform/aggregator/api/model/Game;ZILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    move-result-object p2

    .line 51
    if-ne p1, p2, :cond_2

    .line 52
    .line 53
    return-object p1

    .line 54
    :cond_2
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 55
    .line 56
    return-object p1
.end method
