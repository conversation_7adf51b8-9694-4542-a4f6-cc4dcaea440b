.class public final Ln11/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0003\u001a/\u0010\u0007\u001a\u00020\u00052\u0006\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u000c\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u0004H\u0007\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a\u0019\u0010\t\u001a\u00020\u00052\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u0002H\u0003\u00a2\u0006\u0004\u0008\t\u0010\n\u001a\u000f\u0010\u000c\u001a\u00020\u000bH\u0003\u00a2\u0006\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Landroidx/compose/ui/graphics/S1;",
        "shape",
        "Landroidx/compose/ui/l;",
        "modifier",
        "Lkotlin/Function0;",
        "",
        "content",
        "d",
        "(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;Landroidx/compose/runtime/j;II)V",
        "f",
        "(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V",
        "",
        "i",
        "(Landroidx/compose/runtime/j;I)Z",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Ln11/d;->e(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroidx/compose/runtime/r1;ZLjava/util/List;Landroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Ln11/d;->g(Landroidx/compose/runtime/r1;ZLjava/util/List;Landroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Ln11/d;->h(Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;Landroidx/compose/runtime/j;II)V
    .locals 9
    .param p0    # Landroidx/compose/ui/graphics/S1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function2;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/graphics/S1;",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function2<",
            "-",
            "Landroidx/compose/runtime/j;",
            "-",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .annotation runtime Lkotlin/e;
    .end annotation

    .line 1
    const v0, 0x7aa5471f

    .line 2
    .line 3
    .line 4
    invoke-interface {p3, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 5
    .line 6
    .line 7
    move-result-object p3

    .line 8
    and-int/lit8 v1, p5, 0x1

    .line 9
    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    or-int/lit8 v1, p4, 0x6

    .line 13
    .line 14
    goto :goto_1

    .line 15
    :cond_0
    and-int/lit8 v1, p4, 0x6

    .line 16
    .line 17
    if-nez v1, :cond_2

    .line 18
    .line 19
    invoke-interface {p3, p0}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    if-eqz v1, :cond_1

    .line 24
    .line 25
    const/4 v1, 0x4

    .line 26
    goto :goto_0

    .line 27
    :cond_1
    const/4 v1, 0x2

    .line 28
    :goto_0
    or-int/2addr v1, p4

    .line 29
    goto :goto_1

    .line 30
    :cond_2
    move v1, p4

    .line 31
    :goto_1
    and-int/lit8 v2, p5, 0x2

    .line 32
    .line 33
    if-eqz v2, :cond_3

    .line 34
    .line 35
    or-int/lit8 v1, v1, 0x30

    .line 36
    .line 37
    goto :goto_3

    .line 38
    :cond_3
    and-int/lit8 v3, p4, 0x30

    .line 39
    .line 40
    if-nez v3, :cond_5

    .line 41
    .line 42
    invoke-interface {p3, p1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    if-eqz v3, :cond_4

    .line 47
    .line 48
    const/16 v3, 0x20

    .line 49
    .line 50
    goto :goto_2

    .line 51
    :cond_4
    const/16 v3, 0x10

    .line 52
    .line 53
    :goto_2
    or-int/2addr v1, v3

    .line 54
    :cond_5
    :goto_3
    and-int/lit8 v3, p5, 0x4

    .line 55
    .line 56
    if-eqz v3, :cond_6

    .line 57
    .line 58
    or-int/lit16 v1, v1, 0x180

    .line 59
    .line 60
    goto :goto_5

    .line 61
    :cond_6
    and-int/lit16 v3, p4, 0x180

    .line 62
    .line 63
    if-nez v3, :cond_8

    .line 64
    .line 65
    invoke-interface {p3, p2}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 66
    .line 67
    .line 68
    move-result v3

    .line 69
    if-eqz v3, :cond_7

    .line 70
    .line 71
    const/16 v3, 0x100

    .line 72
    .line 73
    goto :goto_4

    .line 74
    :cond_7
    const/16 v3, 0x80

    .line 75
    .line 76
    :goto_4
    or-int/2addr v1, v3

    .line 77
    :cond_8
    :goto_5
    and-int/lit16 v3, v1, 0x93

    .line 78
    .line 79
    const/16 v4, 0x92

    .line 80
    .line 81
    if-ne v3, v4, :cond_b

    .line 82
    .line 83
    invoke-interface {p3}, Landroidx/compose/runtime/j;->c()Z

    .line 84
    .line 85
    .line 86
    move-result v3

    .line 87
    if-nez v3, :cond_9

    .line 88
    .line 89
    goto :goto_7

    .line 90
    :cond_9
    invoke-interface {p3}, Landroidx/compose/runtime/j;->n()V

    .line 91
    .line 92
    .line 93
    :cond_a
    :goto_6
    move-object v3, p1

    .line 94
    goto/16 :goto_9

    .line 95
    .line 96
    :cond_b
    :goto_7
    if-eqz v2, :cond_c

    .line 97
    .line 98
    sget-object p1, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 99
    .line 100
    :cond_c
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 101
    .line 102
    .line 103
    move-result v2

    .line 104
    if-eqz v2, :cond_d

    .line 105
    .line 106
    const/4 v2, -0x1

    .line 107
    const-string v3, "org.xbet.uikit.compose.components.shimmer.ShimmerComponent (ShimmerComponent.kt:52)"

    .line 108
    .line 109
    invoke-static {v0, v1, v2, v3}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 110
    .line 111
    .line 112
    :cond_d
    sget-object v0, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 113
    .line 114
    sget-object v2, Landroidx/compose/ui/e;->a:Landroidx/compose/ui/e$a;

    .line 115
    .line 116
    invoke-virtual {v2}, Landroidx/compose/ui/e$a;->o()Landroidx/compose/ui/e;

    .line 117
    .line 118
    .line 119
    move-result-object v2

    .line 120
    const/4 v3, 0x0

    .line 121
    invoke-static {v2, v3}, Landroidx/compose/foundation/layout/BoxKt;->g(Landroidx/compose/ui/e;Z)Landroidx/compose/ui/layout/J;

    .line 122
    .line 123
    .line 124
    move-result-object v2

    .line 125
    invoke-static {p3, v3}, Landroidx/compose/runtime/g;->a(Landroidx/compose/runtime/j;I)I

    .line 126
    .line 127
    .line 128
    move-result v4

    .line 129
    invoke-interface {p3}, Landroidx/compose/runtime/j;->g()Landroidx/compose/runtime/v;

    .line 130
    .line 131
    .line 132
    move-result-object v5

    .line 133
    invoke-static {p3, v0}, Landroidx/compose/ui/ComposedModifierKt;->e(Landroidx/compose/runtime/j;Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 134
    .line 135
    .line 136
    move-result-object v0

    .line 137
    sget-object v6, Landroidx/compose/ui/node/ComposeUiNode;->z0:Landroidx/compose/ui/node/ComposeUiNode$Companion;

    .line 138
    .line 139
    invoke-virtual {v6}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->a()Lkotlin/jvm/functions/Function0;

    .line 140
    .line 141
    .line 142
    move-result-object v7

    .line 143
    invoke-interface {p3}, Landroidx/compose/runtime/j;->D()Landroidx/compose/runtime/e;

    .line 144
    .line 145
    .line 146
    move-result-object v8

    .line 147
    invoke-static {v8}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 148
    .line 149
    .line 150
    move-result v8

    .line 151
    if-nez v8, :cond_e

    .line 152
    .line 153
    invoke-static {}, Landroidx/compose/runtime/g;->c()V

    .line 154
    .line 155
    .line 156
    :cond_e
    invoke-interface {p3}, Landroidx/compose/runtime/j;->l()V

    .line 157
    .line 158
    .line 159
    invoke-interface {p3}, Landroidx/compose/runtime/j;->B()Z

    .line 160
    .line 161
    .line 162
    move-result v8

    .line 163
    if-eqz v8, :cond_f

    .line 164
    .line 165
    invoke-interface {p3, v7}, Landroidx/compose/runtime/j;->V(Lkotlin/jvm/functions/Function0;)V

    .line 166
    .line 167
    .line 168
    goto :goto_8

    .line 169
    :cond_f
    invoke-interface {p3}, Landroidx/compose/runtime/j;->h()V

    .line 170
    .line 171
    .line 172
    :goto_8
    invoke-static {p3}, Landroidx/compose/runtime/Updater;->a(Landroidx/compose/runtime/j;)Landroidx/compose/runtime/j;

    .line 173
    .line 174
    .line 175
    move-result-object v7

    .line 176
    invoke-virtual {v6}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->c()Lkotlin/jvm/functions/Function2;

    .line 177
    .line 178
    .line 179
    move-result-object v8

    .line 180
    invoke-static {v7, v2, v8}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 181
    .line 182
    .line 183
    invoke-virtual {v6}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->e()Lkotlin/jvm/functions/Function2;

    .line 184
    .line 185
    .line 186
    move-result-object v2

    .line 187
    invoke-static {v7, v5, v2}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 188
    .line 189
    .line 190
    invoke-virtual {v6}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->b()Lkotlin/jvm/functions/Function2;

    .line 191
    .line 192
    .line 193
    move-result-object v2

    .line 194
    invoke-interface {v7}, Landroidx/compose/runtime/j;->B()Z

    .line 195
    .line 196
    .line 197
    move-result v5

    .line 198
    if-nez v5, :cond_10

    .line 199
    .line 200
    invoke-interface {v7}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 201
    .line 202
    .line 203
    move-result-object v5

    .line 204
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 205
    .line 206
    .line 207
    move-result-object v8

    .line 208
    invoke-static {v5, v8}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 209
    .line 210
    .line 211
    move-result v5

    .line 212
    if-nez v5, :cond_11

    .line 213
    .line 214
    :cond_10
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 215
    .line 216
    .line 217
    move-result-object v5

    .line 218
    invoke-interface {v7, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 219
    .line 220
    .line 221
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 222
    .line 223
    .line 224
    move-result-object v4

    .line 225
    invoke-interface {v7, v4, v2}, Landroidx/compose/runtime/j;->d(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 226
    .line 227
    .line 228
    :cond_11
    invoke-virtual {v6}, Landroidx/compose/ui/node/ComposeUiNode$Companion;->d()Lkotlin/jvm/functions/Function2;

    .line 229
    .line 230
    .line 231
    move-result-object v2

    .line 232
    invoke-static {v7, v0, v2}, Landroidx/compose/runtime/Updater;->c(Landroidx/compose/runtime/j;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V

    .line 233
    .line 234
    .line 235
    sget-object v0, Landroidx/compose/foundation/layout/BoxScopeInstance;->a:Landroidx/compose/foundation/layout/BoxScopeInstance;

    .line 236
    .line 237
    shr-int/lit8 v1, v1, 0x6

    .line 238
    .line 239
    and-int/lit8 v1, v1, 0xe

    .line 240
    .line 241
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 242
    .line 243
    .line 244
    move-result-object v1

    .line 245
    invoke-interface {p2, p3, v1}, Lkotlin/jvm/functions/Function2;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 246
    .line 247
    .line 248
    invoke-static {p1, p0}, Landroidx/compose/ui/draw/d;->a(Landroidx/compose/ui/l;Landroidx/compose/ui/graphics/S1;)Landroidx/compose/ui/l;

    .line 249
    .line 250
    .line 251
    move-result-object v1

    .line 252
    invoke-interface {v0, v1}, Landroidx/compose/foundation/layout/h;->b(Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 253
    .line 254
    .line 255
    move-result-object v0

    .line 256
    invoke-static {v0, p3, v3, v3}, Ln11/d;->f(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 257
    .line 258
    .line 259
    invoke-interface {p3}, Landroidx/compose/runtime/j;->j()V

    .line 260
    .line 261
    .line 262
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 263
    .line 264
    .line 265
    move-result v0

    .line 266
    if-eqz v0, :cond_a

    .line 267
    .line 268
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 269
    .line 270
    .line 271
    goto/16 :goto_6

    .line 272
    .line 273
    :goto_9
    invoke-interface {p3}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 274
    .line 275
    .line 276
    move-result-object p1

    .line 277
    if-eqz p1, :cond_12

    .line 278
    .line 279
    new-instance v1, Ln11/a;

    .line 280
    .line 281
    move-object v2, p0

    .line 282
    move-object v4, p2

    .line 283
    move v5, p4

    .line 284
    move v6, p5

    .line 285
    invoke-direct/range {v1 .. v6}, Ln11/a;-><init>(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;II)V

    .line 286
    .line 287
    .line 288
    invoke-interface {p1, v1}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 289
    .line 290
    .line 291
    :cond_12
    return-void
.end method

.method public static final e(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v5, p4

    .line 11
    move-object v3, p5

    .line 12
    invoke-static/range {v0 .. v5}, Ln11/d;->d(Landroidx/compose/ui/graphics/S1;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function2;Landroidx/compose/runtime/j;II)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final f(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V
    .locals 22

    .line 1
    move/from16 v0, p2

    .line 2
    .line 3
    move/from16 v1, p3

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x3

    .line 7
    const/4 v4, 0x6

    .line 8
    const v5, 0x7d668fbb

    .line 9
    .line 10
    .line 11
    move-object/from16 v6, p1

    .line 12
    .line 13
    invoke-interface {v6, v5}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 14
    .line 15
    .line 16
    move-result-object v11

    .line 17
    const/4 v6, 0x1

    .line 18
    and-int/lit8 v7, v1, 0x1

    .line 19
    .line 20
    const/4 v8, 0x2

    .line 21
    if-eqz v7, :cond_0

    .line 22
    .line 23
    or-int/lit8 v9, v0, 0x6

    .line 24
    .line 25
    move v14, v9

    .line 26
    move-object/from16 v9, p0

    .line 27
    .line 28
    goto :goto_1

    .line 29
    :cond_0
    and-int/lit8 v9, v0, 0x6

    .line 30
    .line 31
    if-nez v9, :cond_2

    .line 32
    .line 33
    move-object/from16 v9, p0

    .line 34
    .line 35
    invoke-interface {v11, v9}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 36
    .line 37
    .line 38
    move-result v10

    .line 39
    if-eqz v10, :cond_1

    .line 40
    .line 41
    const/4 v10, 0x4

    .line 42
    goto :goto_0

    .line 43
    :cond_1
    const/4 v10, 0x2

    .line 44
    :goto_0
    or-int/2addr v10, v0

    .line 45
    move v14, v10

    .line 46
    goto :goto_1

    .line 47
    :cond_2
    move-object/from16 v9, p0

    .line 48
    .line 49
    move v14, v0

    .line 50
    :goto_1
    and-int/lit8 v10, v14, 0x3

    .line 51
    .line 52
    if-ne v10, v8, :cond_4

    .line 53
    .line 54
    invoke-interface {v11}, Landroidx/compose/runtime/j;->c()Z

    .line 55
    .line 56
    .line 57
    move-result v10

    .line 58
    if-nez v10, :cond_3

    .line 59
    .line 60
    goto :goto_2

    .line 61
    :cond_3
    invoke-interface {v11}, Landroidx/compose/runtime/j;->n()V

    .line 62
    .line 63
    .line 64
    move-object v15, v9

    .line 65
    goto/16 :goto_4

    .line 66
    .line 67
    :cond_4
    :goto_2
    if-eqz v7, :cond_5

    .line 68
    .line 69
    sget-object v7, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 70
    .line 71
    move-object v15, v7

    .line 72
    goto :goto_3

    .line 73
    :cond_5
    move-object v15, v9

    .line 74
    :goto_3
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 75
    .line 76
    .line 77
    move-result v7

    .line 78
    if-eqz v7, :cond_6

    .line 79
    .line 80
    const/4 v7, -0x1

    .line 81
    const-string v9, "org.xbet.uikit.compose.components.shimmer.ShimmerEffect (ShimmerComponent.kt:66)"

    .line 82
    .line 83
    invoke-static {v5, v14, v7, v9}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 84
    .line 85
    .line 86
    :cond_6
    invoke-static {v11, v2}, Ln11/d;->i(Landroidx/compose/runtime/j;I)Z

    .line 87
    .line 88
    .line 89
    move-result v5

    .line 90
    sget-object v7, LB11/e;->a:LB11/e;

    .line 91
    .line 92
    invoke-virtual {v7, v11, v4}, LB11/e;->g(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/StaticColors;

    .line 93
    .line 94
    .line 95
    move-result-object v9

    .line 96
    invoke-virtual {v9}, Lorg/xbet/uikit/compose/color/StaticColors;->getTransparent-0d7_KjU()J

    .line 97
    .line 98
    .line 99
    move-result-wide v9

    .line 100
    invoke-static {v9, v10}, Landroidx/compose/ui/graphics/v0;->g(J)Landroidx/compose/ui/graphics/v0;

    .line 101
    .line 102
    .line 103
    move-result-object v9

    .line 104
    invoke-virtual {v7, v11, v4}, LB11/e;->b(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/ThemeColors;

    .line 105
    .line 106
    .line 107
    move-result-object v10

    .line 108
    invoke-virtual {v10}, Lorg/xbet/uikit/compose/color/ThemeColors;->getSecondary10-0d7_KjU()J

    .line 109
    .line 110
    .line 111
    move-result-wide v12

    .line 112
    invoke-static {v12, v13}, Landroidx/compose/ui/graphics/v0;->g(J)Landroidx/compose/ui/graphics/v0;

    .line 113
    .line 114
    .line 115
    move-result-object v10

    .line 116
    invoke-virtual {v7, v11, v4}, LB11/e;->g(Landroidx/compose/runtime/j;I)Lorg/xbet/uikit/compose/color/StaticColors;

    .line 117
    .line 118
    .line 119
    move-result-object v7

    .line 120
    invoke-virtual {v7}, Lorg/xbet/uikit/compose/color/StaticColors;->getTransparent-0d7_KjU()J

    .line 121
    .line 122
    .line 123
    move-result-wide v12

    .line 124
    invoke-static {v12, v13}, Landroidx/compose/ui/graphics/v0;->g(J)Landroidx/compose/ui/graphics/v0;

    .line 125
    .line 126
    .line 127
    move-result-object v7

    .line 128
    new-array v3, v3, [Landroidx/compose/ui/graphics/v0;

    .line 129
    .line 130
    aput-object v9, v3, v2

    .line 131
    .line 132
    aput-object v10, v3, v6

    .line 133
    .line 134
    aput-object v7, v3, v8

    .line 135
    .line 136
    invoke-static {v3}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 137
    .line 138
    .line 139
    move-result-object v3

    .line 140
    const-string v6, ""

    .line 141
    .line 142
    invoke-static {v6, v11, v4, v2}, Landroidx/compose/animation/core/InfiniteTransitionKt;->c(Ljava/lang/String;Landroidx/compose/runtime/j;II)Landroidx/compose/animation/core/InfiniteTransition;

    .line 143
    .line 144
    .line 145
    move-result-object v6

    .line 146
    invoke-static {}, Landroidx/compose/animation/core/D;->e()Landroidx/compose/animation/core/B;

    .line 147
    .line 148
    .line 149
    move-result-object v4

    .line 150
    const/4 v7, 0x0

    .line 151
    const/16 v9, 0x9c4

    .line 152
    .line 153
    invoke-static {v9, v2, v4, v8, v7}, Landroidx/compose/animation/core/h;->n(IILandroidx/compose/animation/core/B;ILjava/lang/Object;)Landroidx/compose/animation/core/f0;

    .line 154
    .line 155
    .line 156
    move-result-object v16

    .line 157
    sget-object v17, Landroidx/compose/animation/core/RepeatMode;->Restart:Landroidx/compose/animation/core/RepeatMode;

    .line 158
    .line 159
    const/16 v20, 0x4

    .line 160
    .line 161
    const/16 v21, 0x0

    .line 162
    .line 163
    const-wide/16 v18, 0x0

    .line 164
    .line 165
    invoke-static/range {v16 .. v21}, Landroidx/compose/animation/core/h;->e(Landroidx/compose/animation/core/A;Landroidx/compose/animation/core/RepeatMode;JILjava/lang/Object;)Landroidx/compose/animation/core/L;

    .line 166
    .line 167
    .line 168
    move-result-object v9

    .line 169
    sget v2, Landroidx/compose/animation/core/InfiniteTransition;->f:I

    .line 170
    .line 171
    or-int/lit16 v2, v2, 0x6180

    .line 172
    .line 173
    sget v4, Landroidx/compose/animation/core/L;->d:I

    .line 174
    .line 175
    shl-int/lit8 v4, v4, 0x9

    .line 176
    .line 177
    or-int v12, v2, v4

    .line 178
    .line 179
    const/4 v13, 0x0

    .line 180
    const/high16 v7, -0x40800000    # -1.0f

    .line 181
    .line 182
    const/high16 v8, 0x3f800000    # 1.0f

    .line 183
    .line 184
    const-string v10, ""

    .line 185
    .line 186
    invoke-static/range {v6 .. v13}, Landroidx/compose/animation/core/InfiniteTransitionKt;->a(Landroidx/compose/animation/core/InfiniteTransition;FFLandroidx/compose/animation/core/L;Ljava/lang/String;Landroidx/compose/runtime/j;II)Landroidx/compose/runtime/r1;

    .line 187
    .line 188
    .line 189
    move-result-object v2

    .line 190
    const v4, -0x6815fd56

    .line 191
    .line 192
    .line 193
    invoke-interface {v11, v4}, Landroidx/compose/runtime/j;->t(I)V

    .line 194
    .line 195
    .line 196
    invoke-interface {v11, v2}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 197
    .line 198
    .line 199
    move-result v4

    .line 200
    invoke-interface {v11, v5}, Landroidx/compose/runtime/j;->v(Z)Z

    .line 201
    .line 202
    .line 203
    move-result v6

    .line 204
    or-int/2addr v4, v6

    .line 205
    invoke-interface {v11, v3}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 206
    .line 207
    .line 208
    move-result v6

    .line 209
    or-int/2addr v4, v6

    .line 210
    invoke-interface {v11}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 211
    .line 212
    .line 213
    move-result-object v6

    .line 214
    if-nez v4, :cond_7

    .line 215
    .line 216
    sget-object v4, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 217
    .line 218
    invoke-virtual {v4}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 219
    .line 220
    .line 221
    move-result-object v4

    .line 222
    if-ne v6, v4, :cond_8

    .line 223
    .line 224
    :cond_7
    new-instance v6, Ln11/b;

    .line 225
    .line 226
    invoke-direct {v6, v2, v5, v3}, Ln11/b;-><init>(Landroidx/compose/runtime/r1;ZLjava/util/List;)V

    .line 227
    .line 228
    .line 229
    invoke-interface {v11, v6}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 230
    .line 231
    .line 232
    :cond_8
    check-cast v6, Lkotlin/jvm/functions/Function1;

    .line 233
    .line 234
    invoke-interface {v11}, Landroidx/compose/runtime/j;->q()V

    .line 235
    .line 236
    .line 237
    and-int/lit8 v2, v14, 0xe

    .line 238
    .line 239
    invoke-static {v15, v6, v11, v2}, Landroidx/compose/foundation/CanvasKt;->b(Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;I)V

    .line 240
    .line 241
    .line 242
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 243
    .line 244
    .line 245
    move-result v2

    .line 246
    if-eqz v2, :cond_9

    .line 247
    .line 248
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 249
    .line 250
    .line 251
    :cond_9
    :goto_4
    invoke-interface {v11}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 252
    .line 253
    .line 254
    move-result-object v2

    .line 255
    if-eqz v2, :cond_a

    .line 256
    .line 257
    new-instance v3, Ln11/c;

    .line 258
    .line 259
    invoke-direct {v3, v15, v0, v1}, Ln11/c;-><init>(Landroidx/compose/ui/l;II)V

    .line 260
    .line 261
    .line 262
    invoke-interface {v2, v3}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 263
    .line 264
    .line 265
    :cond_a
    return-void
.end method

.method public static final g(Landroidx/compose/runtime/r1;ZLjava/util/List;Landroidx/compose/ui/graphics/drawscope/f;)Lkotlin/Unit;
    .locals 26

    .line 1
    invoke-interface/range {p3 .. p3}, Landroidx/compose/ui/graphics/drawscope/f;->b()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    const/16 v2, 0x20

    .line 6
    .line 7
    shr-long/2addr v0, v2

    .line 8
    long-to-int v1, v0

    .line 9
    invoke-static {v1}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    invoke-interface/range {p3 .. p3}, Landroidx/compose/ui/graphics/drawscope/f;->b()J

    .line 14
    .line 15
    .line 16
    move-result-wide v3

    .line 17
    const-wide v5, 0xffffffffL

    .line 18
    .line 19
    .line 20
    .line 21
    .line 22
    and-long/2addr v3, v5

    .line 23
    long-to-int v1, v3

    .line 24
    invoke-static {v1}, Ljava/lang/Float;->intBitsToFloat(I)F

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    invoke-interface/range {p0 .. p0}, Landroidx/compose/runtime/r1;->getValue()Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    check-cast v3, Ljava/lang/Number;

    .line 33
    .line 34
    invoke-virtual {v3}, Ljava/lang/Number;->floatValue()F

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    mul-float v3, v3, v0

    .line 39
    .line 40
    const/high16 v4, 0x3fc00000    # 1.5f

    .line 41
    .line 42
    mul-float v3, v3, v4

    .line 43
    .line 44
    if-eqz p1, :cond_0

    .line 45
    .line 46
    sub-float v3, v0, v3

    .line 47
    .line 48
    :cond_0
    mul-float v4, v4, v0

    .line 49
    .line 50
    if-eqz p1, :cond_1

    .line 51
    .line 52
    sub-float v4, v3, v4

    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_1
    add-float/2addr v4, v3

    .line 56
    :goto_0
    sget-object v7, Landroidx/compose/ui/graphics/k0;->b:Landroidx/compose/ui/graphics/k0$a;

    .line 57
    .line 58
    invoke-static {v3}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 59
    .line 60
    .line 61
    move-result v3

    .line 62
    int-to-long v8, v3

    .line 63
    const/4 v3, 0x0

    .line 64
    invoke-static {v3}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 65
    .line 66
    .line 67
    move-result v10

    .line 68
    int-to-long v10, v10

    .line 69
    shl-long/2addr v8, v2

    .line 70
    and-long/2addr v10, v5

    .line 71
    or-long/2addr v8, v10

    .line 72
    invoke-static {v8, v9}, Lb0/f;->e(J)J

    .line 73
    .line 74
    .line 75
    move-result-wide v9

    .line 76
    invoke-static {v4}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 77
    .line 78
    .line 79
    move-result v4

    .line 80
    int-to-long v11, v4

    .line 81
    invoke-static {v3}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 82
    .line 83
    .line 84
    move-result v3

    .line 85
    int-to-long v3, v3

    .line 86
    shl-long/2addr v11, v2

    .line 87
    and-long/2addr v3, v5

    .line 88
    or-long/2addr v3, v11

    .line 89
    invoke-static {v3, v4}, Lb0/f;->e(J)J

    .line 90
    .line 91
    .line 92
    move-result-wide v11

    .line 93
    sget-object v3, Landroidx/compose/ui/graphics/X1;->a:Landroidx/compose/ui/graphics/X1$a;

    .line 94
    .line 95
    invoke-virtual {v3}, Landroidx/compose/ui/graphics/X1$a;->a()I

    .line 96
    .line 97
    .line 98
    move-result v13

    .line 99
    move-object/from16 v8, p2

    .line 100
    .line 101
    invoke-virtual/range {v7 .. v13}, Landroidx/compose/ui/graphics/k0$a;->e(Ljava/util/List;JJI)Landroidx/compose/ui/graphics/k0;

    .line 102
    .line 103
    .line 104
    move-result-object v15

    .line 105
    invoke-static {v0}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 106
    .line 107
    .line 108
    move-result v0

    .line 109
    int-to-long v3, v0

    .line 110
    invoke-static {v1}, Ljava/lang/Float;->floatToRawIntBits(F)I

    .line 111
    .line 112
    .line 113
    move-result v0

    .line 114
    int-to-long v0, v0

    .line 115
    shl-long v2, v3, v2

    .line 116
    .line 117
    and-long/2addr v0, v5

    .line 118
    or-long/2addr v0, v2

    .line 119
    invoke-static {v0, v1}, Lb0/l;->d(J)J

    .line 120
    .line 121
    .line 122
    move-result-wide v18

    .line 123
    const/16 v24, 0x7a

    .line 124
    .line 125
    const/16 v25, 0x0

    .line 126
    .line 127
    const-wide/16 v16, 0x0

    .line 128
    .line 129
    const/16 v20, 0x0

    .line 130
    .line 131
    const/16 v21, 0x0

    .line 132
    .line 133
    const/16 v22, 0x0

    .line 134
    .line 135
    const/16 v23, 0x0

    .line 136
    .line 137
    move-object/from16 v14, p3

    .line 138
    .line 139
    invoke-static/range {v14 .. v25}, Landroidx/compose/ui/graphics/drawscope/DrawScope$-CC;->o(Landroidx/compose/ui/graphics/drawscope/f;Landroidx/compose/ui/graphics/k0;JJFLandroidx/compose/ui/graphics/drawscope/g;Landroidx/compose/ui/graphics/w0;IILjava/lang/Object;)V

    .line 140
    .line 141
    .line 142
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 143
    .line 144
    return-object v0
.end method

.method public static final h(Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    or-int/lit8 p1, p1, 0x1

    .line 2
    .line 3
    invoke-static {p1}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    invoke-static {p0, p3, p1, p2}, Ln11/d;->f(Landroidx/compose/ui/l;Landroidx/compose/runtime/j;II)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final i(Landroidx/compose/runtime/j;I)Z
    .locals 3

    .line 1
    const v0, 0x225a9a08

    .line 2
    .line 3
    .line 4
    invoke-interface {p0, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 5
    .line 6
    .line 7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    if-eqz v1, :cond_0

    .line 12
    .line 13
    const/4 v1, -0x1

    .line 14
    const-string v2, "org.xbet.uikit.compose.components.shimmer.getRtl (ShimmerComponent.kt:112)"

    .line 15
    .line 16
    invoke-static {v0, p1, v1, v2}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 17
    .line 18
    .line 19
    :cond_0
    invoke-static {}, Landroidx/compose/ui/platform/AndroidCompositionLocals_androidKt;->f()Landroidx/compose/runtime/x0;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-interface {p0, p1}, Landroidx/compose/runtime/j;->G(Landroidx/compose/runtime/s;)Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    check-cast p1, Landroid/content/res/Configuration;

    .line 28
    .line 29
    invoke-virtual {p1}, Landroid/content/res/Configuration;->getLayoutDirection()I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    const/4 v0, 0x1

    .line 34
    if-ne p1, v0, :cond_1

    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_1
    const/4 v0, 0x0

    .line 38
    :goto_0
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 39
    .line 40
    .line 41
    move-result p1

    .line 42
    if-eqz p1, :cond_2

    .line 43
    .line 44
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 45
    .line 46
    .line 47
    :cond_2
    invoke-interface {p0}, Landroidx/compose/runtime/j;->q()V

    .line 48
    .line 49
    .line 50
    return v0
.end method
