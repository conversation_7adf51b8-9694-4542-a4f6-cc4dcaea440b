.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/x;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/x$a;
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0001\u001a\u00020\u0000*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0001\u0010\u0002\u001a\u0013\u0010\u0004\u001a\u00020\u0000*\u00020\u0003H\u0000\u00a2\u0006\u0004\u0008\u0004\u0010\u0005\u00a8\u0006\u0006"
    }
    d2 = {
        "",
        "b",
        "(I)I",
        "Lorg/xplatform/aggregator/api/model/PartitionType;",
        "a",
        "(Lorg/xplatform/aggregator/api/model/PartitionType;)I",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Lorg/xplatform/aggregator/api/model/PartitionType;)I
    .locals 1
    .param p0    # Lorg/xplatform/aggregator/api/model/PartitionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/x$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p0}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    aget p0, v0, p0

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-ne p0, v0, :cond_0

    .line 11
    .line 12
    const/16 p0, 0x1fff

    .line 13
    .line 14
    return p0

    .line 15
    :cond_0
    const/16 p0, 0x1ffe

    .line 16
    .line 17
    return p0
.end method

.method public static final b(I)I
    .locals 1

    .line 1
    sget v0, Lpb/k;->exclusive_slots:I

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    const/16 p0, 0x4aa

    .line 6
    .line 7
    return p0

    .line 8
    :cond_0
    sget v0, Lpb/k;->one_x_live_text:I

    .line 9
    .line 10
    if-ne p0, v0, :cond_1

    .line 11
    .line 12
    const/16 p0, 0x50c

    .line 13
    .line 14
    return p0

    .line 15
    :cond_1
    sget v0, Lpb/k;->slots_popular:I

    .line 16
    .line 17
    if-ne p0, v0, :cond_2

    .line 18
    .line 19
    const/16 p0, 0x4a8

    .line 20
    .line 21
    return p0

    .line 22
    :cond_2
    sget v0, Lpb/k;->live_casino_popular:I

    .line 23
    .line 24
    if-ne p0, v0, :cond_3

    .line 25
    .line 26
    const/16 p0, 0x50d

    .line 27
    .line 28
    return p0

    .line 29
    :cond_3
    const/4 p0, 0x0

    .line 30
    return p0
.end method
