.class public final synthetic LNZ0/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/chips/DsChipGroup;

.field public final synthetic b:Lorg/xbet/uikit/components/chips/DsChip;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/chips/DsChipGroup;Lorg/xbet/uikit/components/chips/DsChip;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/j;->a:Lorg/xbet/uikit/components/chips/DsChipGroup;

    iput-object p2, p0, LNZ0/j;->b:Lorg/xbet/uikit/components/chips/DsChip;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LNZ0/j;->a:Lorg/xbet/uikit/components/chips/DsChipGroup;

    iget-object v1, p0, LNZ0/j;->b:Lorg/xbet/uikit/components/chips/DsChip;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/chips/DsChipGroup;->c(Lorg/xbet/uikit/components/chips/DsChipGroup;Lorg/xbet/uikit/components/chips/DsChip;Landroid/view/View;)V

    return-void
.end method
