.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/z;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LCb1/a;


# direct methods
.method public synthetic constructor <init>(LCb1/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/z;->a:LCb1/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/z;->a:LCb1/a;

    check-cast p1, LB4/a;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularTournamentBannerDelegateKt;->b(LCb1/a;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
