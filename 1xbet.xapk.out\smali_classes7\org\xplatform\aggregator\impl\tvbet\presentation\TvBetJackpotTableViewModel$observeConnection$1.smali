.class final Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tvbet.presentation.TvBetJackpotTableViewModel$observeConnection$1"
    f = "TvBetJackpotTableViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->N3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "connectionState",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field synthetic Z$0:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;Lkotlin/coroutines/e;)V

    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    iput-boolean p1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->Z$0:Z

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-boolean p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->Z$0:Z

    .line 12
    .line 13
    if-eqz p1, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 16
    .line 17
    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->t3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-nez v0, :cond_0

    .line 22
    .line 23
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 24
    .line 25
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->Q3()V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 29
    .line 30
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->A3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;

    .line 35
    .line 36
    const/4 v1, 0x0

    .line 37
    const/4 v2, 0x0

    .line 38
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;-><init>(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 39
    .line 40
    .line 41
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_0
    if-nez p1, :cond_1

    .line 46
    .line 47
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 48
    .line 49
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->t3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Z

    .line 50
    .line 51
    .line 52
    move-result p1

    .line 53
    if-nez p1, :cond_1

    .line 54
    .line 55
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 56
    .line 57
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->A3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;

    .line 62
    .line 63
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$observeConnection$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 64
    .line 65
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->x3(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;)Lorg/xbet/uikit/components/lottie/a;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    const/4 v2, 0x1

    .line 70
    invoke-direct {v0, v2, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;-><init>(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 71
    .line 72
    .line 73
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 74
    .line 75
    .line 76
    :cond_1
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 77
    .line 78
    return-object p1

    .line 79
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 80
    .line 81
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 82
    .line 83
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    throw p1
.end method
