.class public final Lcom/google/android/gms/common/api/ComplianceOptions$Builder;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/gms/common/api/ComplianceOptions;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Builder"
.end annotation


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    iput v0, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->a:I

    iput v0, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->b:I

    const/4 v0, 0x0

    iput v0, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->c:I

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->d:Z

    return-void
.end method


# virtual methods
.method public a()Lcom/google/android/gms/common/api/ComplianceOptions;
    .locals 5
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
    .end annotation

    .line 1
    new-instance v0, Lcom/google/android/gms/common/api/ComplianceOptions;

    .line 2
    .line 3
    iget v1, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->a:I

    .line 4
    .line 5
    iget v2, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->b:I

    .line 6
    .line 7
    iget v3, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->c:I

    .line 8
    .line 9
    iget-boolean v4, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->d:Z

    .line 10
    .line 11
    invoke-direct {v0, v1, v2, v3, v4}, Lcom/google/android/gms/common/api/ComplianceOptions;-><init>(IIIZ)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public b(I)Lcom/google/android/gms/common/api/ComplianceOptions$Builder;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
    .end annotation

    .line 1
    iput p1, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->a:I

    return-object p0
.end method

.method public c(I)Lcom/google/android/gms/common/api/ComplianceOptions$Builder;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
    .end annotation

    .line 1
    iput p1, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->b:I

    return-object p0
.end method

.method public d(Z)Lcom/google/android/gms/common/api/ComplianceOptions$Builder;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
    .end annotation

    .line 1
    iput-boolean p1, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->d:Z

    return-object p0
.end method

.method public e(I)Lcom/google/android/gms/common/api/ComplianceOptions$Builder;
    .locals 0
    .annotation build Landroidx/annotation/NonNull;
    .end annotation

    .annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
    .end annotation

    .line 1
    iput p1, p0, Lcom/google/android/gms/common/api/ComplianceOptions$Builder;->c:I

    return-object p0
.end method
