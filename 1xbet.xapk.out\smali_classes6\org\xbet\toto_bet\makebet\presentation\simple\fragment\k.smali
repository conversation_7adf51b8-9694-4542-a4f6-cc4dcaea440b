.class public final synthetic Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/k;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/k;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    invoke-static {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->F2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Landroid/view/View;)V

    return-void
.end method
