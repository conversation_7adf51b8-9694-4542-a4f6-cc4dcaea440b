.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0014\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u0000 92\u00020\u0001:\u0001\u001eB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ-\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000c\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J/\u0010\u0017\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u0017\u0010\u001b\u001a\u00020\u00102\u0006\u0010\u001a\u001a\u00020\u0019H\u0014\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001e\u001a\u00020\u00062\u0006\u0010\u001d\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fR\u0016\u0010!\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010 R\u0016\u0010#\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\"\u0010 R\u0016\u0010%\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008$\u0010 R\u0016\u0010(\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0016\u0010+\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008)\u0010*R\u0016\u0010-\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008,\u0010*R\u0014\u00101\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0014\u00102\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u00100R\u0014\u00104\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00083\u00100R\u0014\u00108\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107\u00a8\u0006:"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "start",
        "end",
        "current",
        "",
        "text",
        "",
        "setUpData",
        "(JJJLjava/lang/String;)V",
        "w",
        "h",
        "oldw",
        "oldh",
        "onSizeChanged",
        "(IIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "attr",
        "a",
        "(I)I",
        "J",
        "startValue",
        "b",
        "endValue",
        "c",
        "currentValue",
        "d",
        "Ljava/lang/String;",
        "iconText",
        "e",
        "I",
        "backgroundColor",
        "f",
        "primaryColor",
        "Landroid/graphics/Paint;",
        "g",
        "Landroid/graphics/Paint;",
        "defaultPaint",
        "progressPaint",
        "i",
        "textPaint",
        "Landroid/graphics/RectF;",
        "j",
        "Landroid/graphics/RectF;",
        "rectF",
        "k",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k:Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public a:J

.field public b:J

.field public c:J

.field public d:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:I

.field public f:I

.field public final g:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->k:Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView$a;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 4
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    const-string p2, ""

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->d:Ljava/lang/String;

    .line 6
    sget p2, Lpb/c;->background:I

    invoke-virtual {p0, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->a(I)I

    move-result p2

    iput p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->e:I

    .line 7
    sget p2, Lpb/c;->primaryColor:I

    invoke-virtual {p0, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->a(I)I

    move-result p2

    iput p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->f:I

    .line 8
    new-instance p2, Landroid/graphics/Paint;

    invoke-direct {p2}, Landroid/graphics/Paint;-><init>()V

    .line 9
    iget p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->e:I

    invoke-static {p1, p3}, LF0/b;->getColor(Landroid/content/Context;I)I

    move-result p3

    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setColor(I)V

    .line 10
    sget-object p3, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    const/4 v0, 0x1

    .line 11
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 12
    sget-object v1, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    const/high16 v2, 0x40800000    # 4.0f

    invoke-virtual {v1, p1, v2}, Lorg/xbet/ui_common/utils/g;->k(Landroid/content/Context;F)I

    move-result v3

    int-to-float v3, v3

    invoke-virtual {p2, v3}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 13
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->g:Landroid/graphics/Paint;

    .line 14
    new-instance p2, Landroid/graphics/Paint;

    invoke-direct {p2}, Landroid/graphics/Paint;-><init>()V

    .line 15
    iget v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->f:I

    invoke-static {p1, v3}, LF0/b;->getColor(Landroid/content/Context;I)I

    move-result v3

    invoke-virtual {p2, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 16
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 17
    invoke-virtual {v1, p1, v2}, Lorg/xbet/ui_common/utils/g;->k(Landroid/content/Context;F)I

    move-result p3

    int-to-float p3, p3

    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 18
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 19
    sget-object p3, Landroid/graphics/Paint$Cap;->ROUND:Landroid/graphics/Paint$Cap;

    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setStrokeCap(Landroid/graphics/Paint$Cap;)V

    .line 20
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->h:Landroid/graphics/Paint;

    .line 21
    new-instance p2, Landroid/graphics/Paint;

    invoke-direct {p2}, Landroid/graphics/Paint;-><init>()V

    .line 22
    iget p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->f:I

    invoke-static {p1, p3}, LF0/b;->getColor(Landroid/content/Context;I)I

    move-result p3

    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setColor(I)V

    const/high16 p3, 0x41600000    # 14.0f

    .line 23
    invoke-virtual {v1, p1, p3}, Lorg/xbet/ui_common/utils/g;->T(Landroid/content/Context;F)I

    move-result p1

    int-to-float p1, p1

    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 24
    sget-object p1, Landroid/graphics/Paint$Align;->CENTER:Landroid/graphics/Paint$Align;

    invoke-virtual {p2, p1}, Landroid/graphics/Paint;->setTextAlign(Landroid/graphics/Paint$Align;)V

    .line 25
    invoke-virtual {p2, v0}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 26
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->i:Landroid/graphics/Paint;

    .line 27
    new-instance p1, Landroid/graphics/RectF;

    invoke-direct {p1}, Landroid/graphics/RectF;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->j:Landroid/graphics/RectF;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final a(I)I
    .locals 3

    .line 1
    sget-object v0, Lub/b;->a:Lub/b;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    const/4 v2, 0x1

    .line 8
    invoke-virtual {v0, v1, p1, v2}, Lub/b;->e(Landroid/content/Context;IZ)I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    return p1
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 8
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/view/View;->onDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->b:J

    .line 5
    .line 6
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->a:J

    .line 7
    .line 8
    sub-long/2addr v0, v2

    .line 9
    long-to-float v0, v0

    .line 10
    iget-wide v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->c:J

    .line 11
    .line 12
    sub-long/2addr v4, v2

    .line 13
    long-to-float v1, v4

    .line 14
    div-float/2addr v1, v0

    .line 15
    const/high16 v0, 0x43b40000    # 360.0f

    .line 16
    .line 17
    mul-float v5, v1, v0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->j:Landroid/graphics/RectF;

    .line 20
    .line 21
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->g:Landroid/graphics/Paint;

    .line 22
    .line 23
    invoke-virtual {p1, v0, v1}, Landroid/graphics/Canvas;->drawOval(Landroid/graphics/RectF;Landroid/graphics/Paint;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Landroid/view/View;->getHeight()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    int-to-float v0, v0

    .line 31
    const/high16 v1, 0x40000000    # 2.0f

    .line 32
    .line 33
    div-float/2addr v0, v1

    .line 34
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->i:Landroid/graphics/Paint;

    .line 35
    .line 36
    invoke-virtual {v2}, Landroid/graphics/Paint;->descent()F

    .line 37
    .line 38
    .line 39
    move-result v2

    .line 40
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->i:Landroid/graphics/Paint;

    .line 41
    .line 42
    invoke-virtual {v3}, Landroid/graphics/Paint;->ascent()F

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    add-float/2addr v2, v3

    .line 47
    div-float/2addr v2, v1

    .line 48
    sub-float/2addr v0, v2

    .line 49
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 50
    .line 51
    .line 52
    move-result v2

    .line 53
    int-to-float v2, v2

    .line 54
    div-float v1, v2, v1

    .line 55
    .line 56
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->j:Landroid/graphics/RectF;

    .line 57
    .line 58
    const/4 v6, 0x0

    .line 59
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->h:Landroid/graphics/Paint;

    .line 60
    .line 61
    const/high16 v4, -0x3d4c0000    # -90.0f

    .line 62
    .line 63
    move-object v2, p1

    .line 64
    invoke-virtual/range {v2 .. v7}, Landroid/graphics/Canvas;->drawArc(Landroid/graphics/RectF;FFZLandroid/graphics/Paint;)V

    .line 65
    .line 66
    .line 67
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->d:Ljava/lang/String;

    .line 68
    .line 69
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->i:Landroid/graphics/Paint;

    .line 70
    .line 71
    invoke-virtual {v2, p1, v1, v0, v3}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 72
    .line 73
    .line 74
    return-void
.end method

.method public onSizeChanged(IIII)V
    .locals 0

    .line 1
    invoke-super {p0, p1, p2, p3, p4}, Landroid/view/View;->onSizeChanged(IIII)V

    .line 2
    .line 3
    .line 4
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->g:Landroid/graphics/Paint;

    .line 5
    .line 6
    invoke-virtual {p3}, Landroid/graphics/Paint;->getStrokeWidth()F

    .line 7
    .line 8
    .line 9
    move-result p3

    .line 10
    const/high16 p4, 0x40000000    # 2.0f

    .line 11
    .line 12
    div-float/2addr p3, p4

    .line 13
    iget-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->j:Landroid/graphics/RectF;

    .line 14
    .line 15
    int-to-float p1, p1

    .line 16
    sub-float/2addr p1, p3

    .line 17
    int-to-float p2, p2

    .line 18
    sub-float/2addr p2, p3

    .line 19
    invoke-virtual {p4, p3, p3, p1, p2}, Landroid/graphics/RectF;->set(FFFF)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final setUpData(JJJLjava/lang/String;)V
    .locals 0
    .param p7    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->a:J

    .line 2
    .line 3
    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->b:J

    .line 4
    .line 5
    iput-wide p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->c:J

    .line 6
    .line 7
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/views/TournamentStageProgressIconView;->d:Ljava/lang/String;

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 10
    .line 11
    .line 12
    return-void
.end method
