.class public final Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0002\u0008\u0004\u001a;\u0010\t\u001a\u00020\u00072\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u00032\u0012\u0010\u0008\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005H\u0007\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LHd/c;",
        "Ls31/a;",
        "dsGameEventAdapterListUiModel",
        "Landroidx/compose/ui/l;",
        "modifier",
        "Lkotlin/Function1;",
        "",
        "",
        "onPlayerClick",
        "c",
        "(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p6}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;->e(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LHd/c;Lkotlin/jvm/functions/Function1;Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;->d(LHd/c;Lkotlin/jvm/functions/Function1;Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V
    .locals 18
    .param p0    # LHd/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LHd/c<",
            "+",
            "Ls31/a;",
            ">;",
            "Landroidx/compose/ui/l;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/String;",
            "Lkotlin/Unit;",
            ">;",
            "Landroidx/compose/runtime/j;",
            "II)V"
        }
    .end annotation

    .annotation runtime LlZ0/a;
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v3, p2

    .line 4
    .line 5
    move/from16 v4, p4

    .line 6
    .line 7
    const v0, 0x7507861f

    .line 8
    .line 9
    .line 10
    move-object/from16 v2, p3

    .line 11
    .line 12
    invoke-interface {v2, v0}, Landroidx/compose/runtime/j;->C(I)Landroidx/compose/runtime/j;

    .line 13
    .line 14
    .line 15
    move-result-object v15

    .line 16
    and-int/lit8 v2, p5, 0x1

    .line 17
    .line 18
    const/4 v5, 0x4

    .line 19
    if-eqz v2, :cond_0

    .line 20
    .line 21
    or-int/lit8 v2, v4, 0x6

    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_0
    and-int/lit8 v2, v4, 0x6

    .line 25
    .line 26
    if-nez v2, :cond_2

    .line 27
    .line 28
    invoke-interface {v15, v1}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 29
    .line 30
    .line 31
    move-result v2

    .line 32
    if-eqz v2, :cond_1

    .line 33
    .line 34
    const/4 v2, 0x4

    .line 35
    goto :goto_0

    .line 36
    :cond_1
    const/4 v2, 0x2

    .line 37
    :goto_0
    or-int/2addr v2, v4

    .line 38
    goto :goto_1

    .line 39
    :cond_2
    move v2, v4

    .line 40
    :goto_1
    and-int/lit8 v6, p5, 0x2

    .line 41
    .line 42
    if-eqz v6, :cond_4

    .line 43
    .line 44
    or-int/lit8 v2, v2, 0x30

    .line 45
    .line 46
    :cond_3
    move-object/from16 v7, p1

    .line 47
    .line 48
    goto :goto_3

    .line 49
    :cond_4
    and-int/lit8 v7, v4, 0x30

    .line 50
    .line 51
    if-nez v7, :cond_3

    .line 52
    .line 53
    move-object/from16 v7, p1

    .line 54
    .line 55
    invoke-interface {v15, v7}, Landroidx/compose/runtime/j;->s(Ljava/lang/Object;)Z

    .line 56
    .line 57
    .line 58
    move-result v8

    .line 59
    if-eqz v8, :cond_5

    .line 60
    .line 61
    const/16 v8, 0x20

    .line 62
    .line 63
    goto :goto_2

    .line 64
    :cond_5
    const/16 v8, 0x10

    .line 65
    .line 66
    :goto_2
    or-int/2addr v2, v8

    .line 67
    :goto_3
    and-int/lit8 v8, p5, 0x4

    .line 68
    .line 69
    const/16 v9, 0x100

    .line 70
    .line 71
    if-eqz v8, :cond_6

    .line 72
    .line 73
    or-int/lit16 v2, v2, 0x180

    .line 74
    .line 75
    goto :goto_5

    .line 76
    :cond_6
    and-int/lit16 v8, v4, 0x180

    .line 77
    .line 78
    if-nez v8, :cond_8

    .line 79
    .line 80
    invoke-interface {v15, v3}, Landroidx/compose/runtime/j;->R(Ljava/lang/Object;)Z

    .line 81
    .line 82
    .line 83
    move-result v8

    .line 84
    if-eqz v8, :cond_7

    .line 85
    .line 86
    const/16 v8, 0x100

    .line 87
    .line 88
    goto :goto_4

    .line 89
    :cond_7
    const/16 v8, 0x80

    .line 90
    .line 91
    :goto_4
    or-int/2addr v2, v8

    .line 92
    :cond_8
    :goto_5
    and-int/lit16 v8, v2, 0x93

    .line 93
    .line 94
    const/16 v10, 0x92

    .line 95
    .line 96
    if-ne v8, v10, :cond_a

    .line 97
    .line 98
    invoke-interface {v15}, Landroidx/compose/runtime/j;->c()Z

    .line 99
    .line 100
    .line 101
    move-result v8

    .line 102
    if-nez v8, :cond_9

    .line 103
    .line 104
    goto :goto_6

    .line 105
    :cond_9
    invoke-interface {v15}, Landroidx/compose/runtime/j;->n()V

    .line 106
    .line 107
    .line 108
    move-object v2, v7

    .line 109
    goto :goto_9

    .line 110
    :cond_a
    :goto_6
    if-eqz v6, :cond_b

    .line 111
    .line 112
    sget-object v6, Landroidx/compose/ui/l;->v0:Landroidx/compose/ui/l$a;

    .line 113
    .line 114
    goto :goto_7

    .line 115
    :cond_b
    move-object v6, v7

    .line 116
    :goto_7
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 117
    .line 118
    .line 119
    move-result v7

    .line 120
    if-eqz v7, :cond_c

    .line 121
    .line 122
    const/4 v7, -0x1

    .line 123
    const-string v8, "org.xbet.uikit_sport.compose.sport_game_events.DsSportGameEvents (DsSportGameEvents.kt:26)"

    .line 124
    .line 125
    invoke-static {v0, v2, v7, v8}, Landroidx/compose/runtime/l;->U(IIILjava/lang/String;)V

    .line 126
    .line 127
    .line 128
    :cond_c
    const v0, -0x615d173a

    .line 129
    .line 130
    .line 131
    invoke-interface {v15, v0}, Landroidx/compose/runtime/j;->t(I)V

    .line 132
    .line 133
    .line 134
    and-int/lit8 v0, v2, 0xe

    .line 135
    .line 136
    const/4 v7, 0x0

    .line 137
    const/4 v8, 0x1

    .line 138
    if-ne v0, v5, :cond_d

    .line 139
    .line 140
    const/4 v0, 0x1

    .line 141
    goto :goto_8

    .line 142
    :cond_d
    const/4 v0, 0x0

    .line 143
    :goto_8
    and-int/lit16 v5, v2, 0x380

    .line 144
    .line 145
    if-ne v5, v9, :cond_e

    .line 146
    .line 147
    const/4 v7, 0x1

    .line 148
    :cond_e
    or-int/2addr v0, v7

    .line 149
    invoke-interface {v15}, Landroidx/compose/runtime/j;->P()Ljava/lang/Object;

    .line 150
    .line 151
    .line 152
    move-result-object v5

    .line 153
    if-nez v0, :cond_f

    .line 154
    .line 155
    sget-object v0, Landroidx/compose/runtime/j;->a:Landroidx/compose/runtime/j$a;

    .line 156
    .line 157
    invoke-virtual {v0}, Landroidx/compose/runtime/j$a;->a()Ljava/lang/Object;

    .line 158
    .line 159
    .line 160
    move-result-object v0

    .line 161
    if-ne v5, v0, :cond_10

    .line 162
    .line 163
    :cond_f
    new-instance v5, Lorg/xbet/uikit_sport/compose/sport_game_events/a;

    .line 164
    .line 165
    invoke-direct {v5, v1, v3}, Lorg/xbet/uikit_sport/compose/sport_game_events/a;-><init>(LHd/c;Lkotlin/jvm/functions/Function1;)V

    .line 166
    .line 167
    .line 168
    invoke-interface {v15, v5}, Landroidx/compose/runtime/j;->I(Ljava/lang/Object;)V

    .line 169
    .line 170
    .line 171
    :cond_10
    move-object v14, v5

    .line 172
    check-cast v14, Lkotlin/jvm/functions/Function1;

    .line 173
    .line 174
    invoke-interface {v15}, Landroidx/compose/runtime/j;->q()V

    .line 175
    .line 176
    .line 177
    shr-int/lit8 v0, v2, 0x3

    .line 178
    .line 179
    and-int/lit8 v16, v0, 0xe

    .line 180
    .line 181
    const/16 v17, 0x1fe

    .line 182
    .line 183
    move-object v5, v6

    .line 184
    const/4 v6, 0x0

    .line 185
    const/4 v7, 0x0

    .line 186
    const/4 v8, 0x0

    .line 187
    const/4 v9, 0x0

    .line 188
    const/4 v10, 0x0

    .line 189
    const/4 v11, 0x0

    .line 190
    const/4 v12, 0x0

    .line 191
    const/4 v13, 0x0

    .line 192
    invoke-static/range {v5 .. v17}, Landroidx/compose/foundation/lazy/LazyDslKt;->b(Landroidx/compose/ui/l;Landroidx/compose/foundation/lazy/LazyListState;Landroidx/compose/foundation/layout/Y;ZLandroidx/compose/foundation/layout/Arrangement$m;Landroidx/compose/ui/e$b;Landroidx/compose/foundation/gestures/q;ZLandroidx/compose/foundation/O;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 193
    .line 194
    .line 195
    invoke-static {}, Landroidx/compose/runtime/l;->M()Z

    .line 196
    .line 197
    .line 198
    move-result v0

    .line 199
    if-eqz v0, :cond_11

    .line 200
    .line 201
    invoke-static {}, Landroidx/compose/runtime/l;->T()V

    .line 202
    .line 203
    .line 204
    :cond_11
    move-object v2, v5

    .line 205
    :goto_9
    invoke-interface {v15}, Landroidx/compose/runtime/j;->E()Landroidx/compose/runtime/M0;

    .line 206
    .line 207
    .line 208
    move-result-object v6

    .line 209
    if-eqz v6, :cond_12

    .line 210
    .line 211
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/b;

    .line 212
    .line 213
    move/from16 v5, p5

    .line 214
    .line 215
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/b;-><init>(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;II)V

    .line 216
    .line 217
    .line 218
    invoke-interface {v6, v0}, Landroidx/compose/runtime/M0;->a(Lkotlin/jvm/functions/Function2;)V

    .line 219
    .line 220
    .line 221
    :cond_12
    return-void
.end method

.method public static final d(LHd/c;Lkotlin/jvm/functions/Function1;Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;
    .locals 3

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$1;->INSTANCE:Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$1;

    .line 2
    .line 3
    invoke-interface {p0}, Ljava/util/List;->size()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    new-instance v2, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$3;

    .line 8
    .line 9
    invoke-direct {v2, v0, p0}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$3;-><init>(Lkotlin/jvm/functions/Function1;Ljava/util/List;)V

    .line 10
    .line 11
    .line 12
    new-instance v0, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;

    .line 13
    .line 14
    invoke-direct {v0, p0, p1}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt$DsSportGameEvents$lambda$2$lambda$1$$inlined$items$default$4;-><init>(Ljava/util/List;Lkotlin/jvm/functions/Function1;)V

    .line 15
    .line 16
    .line 17
    const p0, -0x25b7f321

    .line 18
    .line 19
    .line 20
    const/4 p1, 0x1

    .line 21
    invoke-static {p0, p1, v0}, Landroidx/compose/runtime/internal/b;->b(IZLjava/lang/Object;)Landroidx/compose/runtime/internal/a;

    .line 22
    .line 23
    .line 24
    move-result-object p0

    .line 25
    const/4 p1, 0x0

    .line 26
    invoke-interface {p2, v1, p1, v2, p0}, Landroidx/compose/foundation/lazy/t;->a(ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;LOc/o;)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 30
    .line 31
    return-object p0
.end method

.method public static final e(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;
    .locals 6

    .line 1
    or-int/lit8 p3, p3, 0x1

    .line 2
    .line 3
    invoke-static {p3}, Landroidx/compose/runtime/A0;->a(I)I

    .line 4
    .line 5
    .line 6
    move-result v4

    .line 7
    move-object v0, p0

    .line 8
    move-object v1, p1

    .line 9
    move-object v2, p2

    .line 10
    move v5, p4

    .line 11
    move-object v3, p5

    .line 12
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;->c(LHd/c;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function1;Landroidx/compose/runtime/j;II)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method
