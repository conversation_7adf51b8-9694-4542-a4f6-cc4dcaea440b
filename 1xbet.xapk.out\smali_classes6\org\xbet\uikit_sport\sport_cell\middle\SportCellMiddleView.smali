.class public final Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
.super Landroid/widget/LinearLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;,
        Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0010\r\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 =2\u00020\u0001:\u0001\u001eB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0015\u0010\u0011\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0011\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0011\u0010\u0014J\u0015\u0010\u0015\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\u0004\u0008\u0015\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J\u000f\u0010\u0017\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u000f\u0010\u0019\u001a\u0004\u0018\u00010\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u0018J\r\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001b\u0010\u001cR\u0016\u0010 \u001a\u00020\u001d8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR$\u0010%\u001a\u00020\u00062\u0006\u0010!\u001a\u00020\u00068\u0002@BX\u0082\u000e\u00a2\u0006\u000c\n\u0004\u0008\"\u0010#\"\u0004\u0008$\u0010\u0014R$\u0010(\u001a\u00020\u00062\u0006\u0010!\u001a\u00020\u00068\u0002@BX\u0082\u000e\u00a2\u0006\u000c\n\u0004\u0008&\u0010#\"\u0004\u0008\'\u0010\u0014R\u0014\u0010*\u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010\u001fR\u0014\u0010,\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010#R\u0014\u0010.\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010#R\u0014\u00100\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u0010#R\u001b\u00104\u001a\u00020\u001a8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00081\u00102\u001a\u0004\u00083\u0010\u001cR\u001b\u00107\u001a\u00020\u001a8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00085\u00102\u001a\u0004\u00086\u0010\u001cR \u0010<\u001a\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020\u000c088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;\u00a8\u0006>"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;",
        "Landroid/widget/LinearLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;",
        "type",
        "",
        "setStyle",
        "(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)V",
        "",
        "text",
        "setTitleText",
        "(Ljava/lang/String;)V",
        "resId",
        "(I)V",
        "setSubtitleText",
        "",
        "getTitleText",
        "()Ljava/lang/CharSequence;",
        "getSubtitleText",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "getTitle",
        "()Landroidx/appcompat/widget/AppCompatTextView;",
        "",
        "a",
        "Z",
        "subtitleAvailable",
        "value",
        "b",
        "I",
        "setTitleTextStyle",
        "titleTextStyle",
        "c",
        "setTitleMaxLines",
        "titleMaxLines",
        "d",
        "isRtl",
        "e",
        "rtlTextViewGravity",
        "f",
        "space2",
        "g",
        "space6",
        "h",
        "Lkotlin/j;",
        "getTitleTextView",
        "titleTextView",
        "i",
        "getSubtitleTextView",
        "subtitleTextView",
        "Lkotlin/Function1;",
        "Landroid/content/res/TypedArray;",
        "j",
        "Lkotlin/jvm/functions/Function1;",
        "applyAttrs",
        "k",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final l:I


# instance fields
.field public a:Z

.field public b:I

.field public c:I

.field public final d:Z

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/content/res/TypedArray;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->k:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->l:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    sget v0, LlZ0/n;->TextStyle_Text_Regular_TextPrimary:I

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->b:I

    const/4 v0, -0x1

    .line 7
    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->c:I

    .line 8
    invoke-static {}, LQ0/a;->c()LQ0/a;

    move-result-object v0

    invoke-virtual {v0}, LQ0/a;->h()Z

    move-result v0

    iput-boolean v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->d:Z

    if-eqz v0, :cond_0

    const v0, 0x800005

    goto :goto_0

    :cond_0
    const v0, 0x800003

    .line 9
    :goto_0
    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->e:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_2:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->f:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_6:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->g:I

    .line 12
    new-instance v0, LL31/a;

    invoke-direct {v0, p1, p0}, LL31/a;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)V

    .line 13
    sget-object v1, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 14
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->h:Lkotlin/j;

    .line 15
    new-instance v0, LL31/b;

    invoke-direct {v0, p1, p0}, LL31/b;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)V

    .line 16
    invoke-static {v1, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    .line 17
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->i:Lkotlin/j;

    .line 18
    new-instance v0, LL31/c;

    invoke-direct {v0, p0, p1}, LL31/c;-><init>(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Landroid/content/Context;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->j:Lkotlin/jvm/functions/Function1;

    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 21
    sget-object v1, Lm31/g;->SportCellMiddleView:[I

    const/4 v2, 0x0

    .line 22
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p1

    invoke-interface {v0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCellMiddleStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->e(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->d(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->f(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;Landroid/content/Context;Landroid/content/res/TypedArray;)Lkotlin/Unit;
    .locals 2

    .line 1
    sget v0, Lm31/g;->SportCellMiddleView_sportCellMiddleTitleTextStyle:I

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->b:I

    .line 4
    .line 5
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getResourceId(II)I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleTextStyle(I)V

    .line 10
    .line 11
    .line 12
    sget v0, Lm31/g;->SportCellMiddleView_sportCellMiddleTitleMaxLines:I

    .line 13
    .line 14
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->c:I

    .line 15
    .line 16
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getInteger(II)I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleMaxLines(I)V

    .line 21
    .line 22
    .line 23
    sget v0, Lm31/g;->SportCellMiddleView_sportCellMiddleTitleText:I

    .line 24
    .line 25
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    if-eqz v0, :cond_0

    .line 34
    .line 35
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    if-nez v0, :cond_1

    .line 40
    .line 41
    :cond_0
    sget v0, Lm31/g;->SportCellMiddleView_sportCellMiddleTitleText:I

    .line 42
    .line 43
    invoke-virtual {p2, v0}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    if-nez v0, :cond_1

    .line 48
    .line 49
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    :cond_1
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleText(Ljava/lang/String;)V

    .line 62
    .line 63
    .line 64
    sget v0, Lm31/g;->SportCellMiddleView_sportCellMiddleSubtitleAvailable:I

    .line 65
    .line 66
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->a:Z

    .line 67
    .line 68
    invoke-virtual {p2, v0, v1}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 69
    .line 70
    .line 71
    move-result v0

    .line 72
    iput-boolean v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->a:Z

    .line 73
    .line 74
    sget v0, Lm31/g;->SportCellMiddleView_sportCellMiddleSubtitleText:I

    .line 75
    .line 76
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    if-eqz p1, :cond_2

    .line 85
    .line 86
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    if-nez p1, :cond_3

    .line 91
    .line 92
    :cond_2
    sget p1, Lm31/g;->SportCellMiddleView_sportCellMiddleSubtitleText:I

    .line 93
    .line 94
    invoke-virtual {p2, p1}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    if-nez p1, :cond_3

    .line 99
    .line 100
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    invoke-virtual {p1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    :cond_3
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setSubtitleText(Ljava/lang/String;)V

    .line 113
    .line 114
    .line 115
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 116
    .line 117
    return-object p0
.end method

.method public static final e(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;
    .locals 2

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 7
    .line 8
    const/4 v1, -0x2

    .line 9
    invoke-direct {p0, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 13
    .line 14
    .line 15
    iget p0, p1, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->e:I

    .line 16
    .line 17
    or-int/lit8 p0, p0, 0x10

    .line 18
    .line 19
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setGravity(I)V

    .line 20
    .line 21
    .line 22
    sget p0, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    .line 23
    .line 24
    invoke-static {v0, p0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 25
    .line 26
    .line 27
    const/4 p0, 0x0

    .line 28
    iget p1, p1, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->f:I

    .line 29
    .line 30
    invoke-virtual {v0, p0, p1, p0, p0}, Landroid/view/View;->setPadding(IIII)V

    .line 31
    .line 32
    .line 33
    const/4 p0, 0x1

    .line 34
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 35
    .line 36
    .line 37
    const/16 p0, 0x8

    .line 38
    .line 39
    invoke-virtual {v0, p0}, Landroid/view/View;->setVisibility(I)V

    .line 40
    .line 41
    .line 42
    return-object v0
.end method

.method public static final f(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;)Landroidx/appcompat/widget/AppCompatTextView;
    .locals 2

    .line 1
    new-instance v0, Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 7
    .line 8
    .line 9
    move-result p0

    .line 10
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 11
    .line 12
    .line 13
    new-instance p0, Landroid/widget/LinearLayout$LayoutParams;

    .line 14
    .line 15
    const/4 v1, -0x2

    .line 16
    invoke-direct {p0, v1, v1}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 20
    .line 21
    .line 22
    iget p0, p1, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->e:I

    .line 23
    .line 24
    or-int/lit8 p0, p0, 0x10

    .line 25
    .line 26
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setGravity(I)V

    .line 27
    .line 28
    .line 29
    sget-object p0, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 30
    .line 31
    invoke-virtual {v0, p0}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 32
    .line 33
    .line 34
    return-object v0
.end method

.method private final getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->i:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatTextView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->h:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/appcompat/widget/AppCompatTextView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final setTitleMaxLines(I)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->c:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method private final setTitleTextStyle(I)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->b:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0, p1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final getSubtitleText()Ljava/lang/CharSequence;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final getTitle()Landroidx/appcompat/widget/AppCompatTextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final getTitleText()Ljava/lang/CharSequence;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final setStyle(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)V
    .locals 4
    .param p1    # Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->k:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;->a(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    sget-object v2, Lm31/g;->SportCellMiddleView:[I

    .line 12
    .line 13
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->j:Lkotlin/jvm/functions/Function1;

    .line 14
    .line 15
    invoke-virtual {v1, v0, v2}, Landroid/content/Context;->obtainStyledAttributes(I[I)Landroid/content/res/TypedArray;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {v3, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 23
    .line 24
    .line 25
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$b;->a:[I

    .line 26
    .line 27
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    aget p1, v0, p1

    .line 32
    .line 33
    const/4 v0, -0x2

    .line 34
    const-string v1, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 35
    .line 36
    const/4 v2, 0x0

    .line 37
    packed-switch p1, :pswitch_data_0

    .line 38
    .line 39
    .line 40
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 41
    .line 42
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 43
    .line 44
    .line 45
    throw p1

    .line 46
    :pswitch_0
    iget p1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->g:I

    .line 47
    .line 48
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 53
    .line 54
    .line 55
    move-result v3

    .line 56
    invoke-virtual {p0, v2, p1, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    sget v2, LlZ0/g;->size_64:I

    .line 64
    .line 65
    invoke-virtual {p1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 66
    .line 67
    .line 68
    move-result p1

    .line 69
    invoke-virtual {p0, p1}, Landroid/view/View;->setMinimumHeight(I)V

    .line 70
    .line 71
    .line 72
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    if-eqz p1, :cond_0

    .line 77
    .line 78
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 79
    .line 80
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 81
    .line 82
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 83
    .line 84
    .line 85
    return-void

    .line 86
    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    .line 87
    .line 88
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    throw p1

    .line 92
    :pswitch_1
    iget p1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->g:I

    .line 93
    .line 94
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 95
    .line 96
    .line 97
    move-result v2

    .line 98
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 99
    .line 100
    .line 101
    move-result v3

    .line 102
    invoke-virtual {p0, v2, p1, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 103
    .line 104
    .line 105
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    sget v2, LlZ0/g;->size_48:I

    .line 110
    .line 111
    invoke-virtual {p1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 112
    .line 113
    .line 114
    move-result p1

    .line 115
    invoke-virtual {p0, p1}, Landroid/view/View;->setMinimumHeight(I)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    if-eqz p1, :cond_1

    .line 123
    .line 124
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 125
    .line 126
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 127
    .line 128
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 129
    .line 130
    .line 131
    return-void

    .line 132
    :cond_1
    new-instance p1, Ljava/lang/NullPointerException;

    .line 133
    .line 134
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 135
    .line 136
    .line 137
    throw p1

    .line 138
    :pswitch_2
    iget p1, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->g:I

    .line 139
    .line 140
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 141
    .line 142
    .line 143
    move-result v2

    .line 144
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 145
    .line 146
    .line 147
    move-result v3

    .line 148
    invoke-virtual {p0, v2, p1, v3, p1}, Landroid/view/View;->setPadding(IIII)V

    .line 149
    .line 150
    .line 151
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 152
    .line 153
    .line 154
    move-result-object p1

    .line 155
    sget v2, LlZ0/g;->size_40:I

    .line 156
    .line 157
    invoke-virtual {p1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 158
    .line 159
    .line 160
    move-result p1

    .line 161
    invoke-virtual {p0, p1}, Landroid/view/View;->setMinimumHeight(I)V

    .line 162
    .line 163
    .line 164
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 165
    .line 166
    .line 167
    move-result-object p1

    .line 168
    if-eqz p1, :cond_2

    .line 169
    .line 170
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 171
    .line 172
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 173
    .line 174
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 175
    .line 176
    .line 177
    return-void

    .line 178
    :cond_2
    new-instance p1, Ljava/lang/NullPointerException;

    .line 179
    .line 180
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 181
    .line 182
    .line 183
    throw p1

    .line 184
    :pswitch_3
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 185
    .line 186
    .line 187
    move-result p1

    .line 188
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 189
    .line 190
    .line 191
    move-result v0

    .line 192
    invoke-virtual {p0, p1, v2, v0, v2}, Landroid/view/View;->setPadding(IIII)V

    .line 193
    .line 194
    .line 195
    invoke-virtual {p0, v2}, Landroid/view/View;->setMinimumHeight(I)V

    .line 196
    .line 197
    .line 198
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    if-eqz p1, :cond_3

    .line 203
    .line 204
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 205
    .line 206
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    sget v1, LlZ0/g;->size_66:I

    .line 211
    .line 212
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 213
    .line 214
    .line 215
    move-result v0

    .line 216
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 217
    .line 218
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 219
    .line 220
    .line 221
    return-void

    .line 222
    :cond_3
    new-instance p1, Ljava/lang/NullPointerException;

    .line 223
    .line 224
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 225
    .line 226
    .line 227
    throw p1

    .line 228
    :pswitch_4
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 229
    .line 230
    .line 231
    move-result p1

    .line 232
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 233
    .line 234
    .line 235
    move-result v0

    .line 236
    invoke-virtual {p0, p1, v2, v0, v2}, Landroid/view/View;->setPadding(IIII)V

    .line 237
    .line 238
    .line 239
    invoke-virtual {p0, v2}, Landroid/view/View;->setMinimumHeight(I)V

    .line 240
    .line 241
    .line 242
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 243
    .line 244
    .line 245
    move-result-object p1

    .line 246
    if-eqz p1, :cond_4

    .line 247
    .line 248
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 249
    .line 250
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 251
    .line 252
    .line 253
    move-result-object v0

    .line 254
    sget v1, LlZ0/g;->size_64:I

    .line 255
    .line 256
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 257
    .line 258
    .line 259
    move-result v0

    .line 260
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 261
    .line 262
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 263
    .line 264
    .line 265
    return-void

    .line 266
    :cond_4
    new-instance p1, Ljava/lang/NullPointerException;

    .line 267
    .line 268
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 269
    .line 270
    .line 271
    throw p1

    .line 272
    :pswitch_5
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 273
    .line 274
    .line 275
    move-result p1

    .line 276
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 277
    .line 278
    .line 279
    move-result v0

    .line 280
    invoke-virtual {p0, p1, v2, v0, v2}, Landroid/view/View;->setPadding(IIII)V

    .line 281
    .line 282
    .line 283
    invoke-virtual {p0, v2}, Landroid/view/View;->setMinimumHeight(I)V

    .line 284
    .line 285
    .line 286
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 287
    .line 288
    .line 289
    move-result-object p1

    .line 290
    if-eqz p1, :cond_5

    .line 291
    .line 292
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 293
    .line 294
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 295
    .line 296
    .line 297
    move-result-object v0

    .line 298
    sget v1, LlZ0/g;->size_48:I

    .line 299
    .line 300
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 301
    .line 302
    .line 303
    move-result v0

    .line 304
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 305
    .line 306
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 307
    .line 308
    .line 309
    return-void

    .line 310
    :cond_5
    new-instance p1, Ljava/lang/NullPointerException;

    .line 311
    .line 312
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 313
    .line 314
    .line 315
    throw p1

    .line 316
    :pswitch_6
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 317
    .line 318
    .line 319
    move-result p1

    .line 320
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 321
    .line 322
    .line 323
    move-result v0

    .line 324
    invoke-virtual {p0, p1, v2, v0, v2}, Landroid/view/View;->setPadding(IIII)V

    .line 325
    .line 326
    .line 327
    invoke-virtual {p0, v2}, Landroid/view/View;->setMinimumHeight(I)V

    .line 328
    .line 329
    .line 330
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 331
    .line 332
    .line 333
    move-result-object p1

    .line 334
    if-eqz p1, :cond_6

    .line 335
    .line 336
    check-cast p1, Landroid/widget/FrameLayout$LayoutParams;

    .line 337
    .line 338
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 339
    .line 340
    .line 341
    move-result-object v0

    .line 342
    sget v1, LlZ0/g;->size_40:I

    .line 343
    .line 344
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 345
    .line 346
    .line 347
    move-result v0

    .line 348
    iput v0, p1, Landroid/widget/FrameLayout$LayoutParams;->height:I

    .line 349
    .line 350
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 351
    .line 352
    .line 353
    return-void

    .line 354
    :cond_6
    new-instance p1, Ljava/lang/NullPointerException;

    .line 355
    .line 356
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 357
    .line 358
    .line 359
    throw p1

    .line 360
    nop

    .line 361
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public final setSubtitleText(I)V
    .locals 1

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(I)V

    return-void
.end method

.method public final setSubtitleText(Ljava/lang/String;)V
    .locals 2
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 2
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->a:Z

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    invoke-static {p1}, Lkotlin/text/StringsKt;->B0(Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    .line 3
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getSubtitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    const/16 v1, 0x8

    .line 4
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public final setTitleText(I)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(I)V

    return-void
.end method

.method public final setTitleText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->getTitleTextView()Landroidx/appcompat/widget/AppCompatTextView;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
