.class public abstract Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u000c\n\u0002\u0010\u000b\n\u0002\u0008\u0013\n\u0002\u0018\u0002\n\u0002\u0008\u0013\u0008\'\u0018\u0000 ?2\u00020\u0001:\u0001\u001aB\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0017\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u001d\u0010\u0011\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0014\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\r\u0010\u0016\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\'\u0010\u001a\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0019H$\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u000f\u0010\u001c\u001a\u00020\u000cH\u0004\u00a2\u0006\u0004\u0008\u001c\u0010\u001dR\u001a\u0010 \u001a\u00020\u00068\u0004X\u0084\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001e\u001a\u0004\u0008\u001f\u0010\u0017R\u001a\u0010\"\u001a\u00020\u00068\u0004X\u0084D\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001e\u001a\u0004\u0008!\u0010\u0017R\u001a\u0010%\u001a\u00020\u00068\u0004X\u0084D\u00a2\u0006\u000c\n\u0004\u0008#\u0010\u001e\u001a\u0004\u0008$\u0010\u0017R\u001a\u0010(\u001a\u00020\u00068\u0004X\u0084\u0004\u00a2\u0006\u000c\n\u0004\u0008&\u0010\u001e\u001a\u0004\u0008\'\u0010\u0017R\"\u0010,\u001a\u00020\u00068\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u0008)\u0010\u001e\u001a\u0004\u0008*\u0010\u0017\"\u0004\u0008+\u0010\u0015R$\u00104\u001a\u0004\u0018\u00010-8\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u0008.\u0010/\u001a\u0004\u00080\u00101\"\u0004\u00082\u00103R$\u00108\u001a\u0004\u0018\u00010-8\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u00085\u0010/\u001a\u0004\u00086\u00101\"\u0004\u00087\u00103R\"\u0010<\u001a\u00020\u00068\u0004@\u0004X\u0084\u000e\u00a2\u0006\u0012\n\u0004\u00089\u0010\u001e\u001a\u0004\u0008:\u0010\u0017\"\u0004\u0008;\u0010\u0015R\u0016\u0010>\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008=\u0010\u001e\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "total",
        "win",
        "setTotalAndWinCounts",
        "(II)V",
        "color",
        "setWinIndicatorColor",
        "(I)V",
        "getWinCount",
        "()I",
        "index",
        "",
        "a",
        "(Landroid/graphics/Canvas;IZ)V",
        "b",
        "()V",
        "I",
        "getGapSize",
        "gapSize",
        "getSpaceCount",
        "spaceCount",
        "c",
        "getMaxTotalCount",
        "maxTotalCount",
        "d",
        "getIndicatorHeight",
        "indicatorHeight",
        "e",
        "getIndicatorWidth",
        "setIndicatorWidth",
        "indicatorWidth",
        "Landroid/graphics/drawable/Drawable;",
        "f",
        "Landroid/graphics/drawable/Drawable;",
        "getRegularIndicator",
        "()Landroid/graphics/drawable/Drawable;",
        "setRegularIndicator",
        "(Landroid/graphics/drawable/Drawable;)V",
        "regularIndicator",
        "g",
        "getWinIndicator",
        "setWinIndicator",
        "winIndicator",
        "h",
        "getTotalCount",
        "setTotalCount",
        "totalCount",
        "i",
        "winCount",
        "j",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final j:Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final k:I


# instance fields
.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public e:I

.field public f:Landroid/graphics/drawable/Drawable;

.field public g:Landroid/graphics/drawable/Drawable;

.field public h:I

.field public i:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->j:Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->k:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_2:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->a:I

    const/4 p1, 0x3

    .line 6
    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->b:I

    const/4 p1, 0x4

    .line 7
    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->c:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_10:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->d:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_10:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->e:I

    .line 10
    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->h:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public abstract a(Landroid/graphics/Canvas;IZ)V
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
.end method

.method public final b()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->f:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    iget v2, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->e:I

    .line 7
    .line 8
    iget v3, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->d:I

    .line 9
    .line 10
    invoke-virtual {v0, v1, v1, v2, v3}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 11
    .line 12
    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->g:Landroid/graphics/drawable/Drawable;

    .line 14
    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    iget v2, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->e:I

    .line 18
    .line 19
    iget v3, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->d:I

    .line 20
    .line 21
    invoke-virtual {v0, v1, v1, v2, v3}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 22
    .line 23
    .line 24
    :cond_1
    return-void
.end method

.method public final getGapSize()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->a:I

    .line 2
    .line 3
    return v0
.end method

.method public final getIndicatorHeight()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final getIndicatorWidth()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->e:I

    .line 2
    .line 3
    return v0
.end method

.method public final getMaxTotalCount()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final getRegularIndicator()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->f:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getSpaceCount()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->b:I

    .line 2
    .line 3
    return v0
.end method

.method public final getTotalCount()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->h:I

    .line 2
    .line 3
    return v0
.end method

.method public final getWinCount()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->i:I

    .line 2
    .line 3
    return v0
.end method

.method public final getWinIndicator()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->g:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    return-object v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->h:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x0

    .line 5
    :goto_0
    if-ge v2, v0, :cond_1

    .line 6
    .line 7
    iget v3, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->i:I

    .line 8
    .line 9
    if-le v3, v2, :cond_0

    .line 10
    .line 11
    const/4 v3, 0x1

    .line 12
    goto :goto_1

    .line 13
    :cond_0
    const/4 v3, 0x0

    .line 14
    :goto_1
    invoke-virtual {p0, p1, v2, v3}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->a(Landroid/graphics/Canvas;IZ)V

    .line 15
    .line 16
    .line 17
    add-int/lit8 v2, v2, 0x1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    return-void
.end method

.method public final setIndicatorWidth(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->e:I

    .line 2
    .line 3
    return-void
.end method

.method public final setRegularIndicator(Landroid/graphics/drawable/Drawable;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->f:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    return-void
.end method

.method public final setTotalAndWinCounts(II)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->h:I

    .line 2
    .line 3
    iput p2, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->i:I

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setTotalCount(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->h:I

    .line 2
    .line 3
    return-void
.end method

.method public final setWinIndicator(Landroid/graphics/drawable/Drawable;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->g:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    return-void
.end method

.method public final setWinIndicatorColor(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->g:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    invoke-static {v0, p1}, LJ0/a;->n(Landroid/graphics/drawable/Drawable;I)V

    .line 7
    .line 8
    .line 9
    return-void
.end method
