.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournament_stages_alt_design.TournamentStagesAltDesignViewModel$requestInitialData$1"
    f = "TournamentStagesAltDesignViewModel.kt"
    l = {
        0xd4
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->J3(JZLjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Li81/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Li81/a;",
        "tournamentFullInfo",
        "",
        "<anonymous>",
        "(Li81/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $typeStage:Ljava/lang/String;

.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->$typeStage:Ljava/lang/String;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->$typeStage:Ljava/lang/String;

    invoke-direct {v0, v1, v2, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(Li81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Li81/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->invoke(Li81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_1

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Li81/a;

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 32
    .line 33
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 38
    .line 39
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)LHX0/e;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 44
    .line 45
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lorg/xbet/onexlocalization/f;

    .line 46
    .line 47
    .line 48
    move-result-object v4

    .line 49
    invoke-virtual {v4}, Lorg/xbet/onexlocalization/f;->a()Ljava/util/Locale;

    .line 50
    .line 51
    .line 52
    move-result-object v4

    .line 53
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->$typeStage:Ljava/lang/String;

    .line 54
    .line 55
    const/4 v6, 0x0

    .line 56
    invoke-static {p1, v6, v3, v4, v5}, Ljb1/q;->g(Li81/a;ZLHX0/e;Ljava/util/Locale;Ljava/lang/String;)Ljava/util/List;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    new-instance v4, Ljava/util/ArrayList;

    .line 61
    .line 62
    const/16 v5, 0xa

    .line 63
    .line 64
    invoke-static {v3, v5}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 65
    .line 66
    .line 67
    move-result v5

    .line 68
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(I)V

    .line 69
    .line 70
    .line 71
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 72
    .line 73
    .line 74
    move-result-object v3

    .line 75
    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 76
    .line 77
    .line 78
    move-result v5

    .line 79
    if-eqz v5, :cond_2

    .line 80
    .line 81
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    move-result-object v5

    .line 85
    check-cast v5, Lc31/a;

    .line 86
    .line 87
    new-instance v6, Lkb1/B;

    .line 88
    .line 89
    invoke-direct {v6, v5}, Lkb1/B;-><init>(Lc31/a;)V

    .line 90
    .line 91
    .line 92
    invoke-interface {v4, v6}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 93
    .line 94
    .line 95
    goto :goto_0

    .line 96
    :cond_2
    invoke-virtual {p1}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 97
    .line 98
    .line 99
    move-result-object v3

    .line 100
    invoke-virtual {p1}, Li81/a;->k()Lh81/a;

    .line 101
    .line 102
    .line 103
    move-result-object v5

    .line 104
    invoke-virtual {p1}, Li81/a;->t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;

    .line 109
    .line 110
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)LHX0/e;

    .line 111
    .line 112
    .line 113
    move-result-object v6

    .line 114
    sget-object v7, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 115
    .line 116
    invoke-static {v5, p1, v7, v6}, LWa1/c;->b(Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;LHX0/e;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    .line 121
    .line 122
    invoke-direct {v5, v4, v3, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;-><init>(Ljava/util/List;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V

    .line 123
    .line 124
    .line 125
    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;->label:I

    .line 126
    .line 127
    invoke-interface {v1, v5, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    if-ne p1, v0, :cond_3

    .line 132
    .line 133
    return-object v0

    .line 134
    :cond_3
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 135
    .line 136
    return-object p1
.end method
