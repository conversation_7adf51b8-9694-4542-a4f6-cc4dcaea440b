.class public final Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;
.super Landroidx/viewpager2/widget/ViewPager2$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0017\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "org/xbet/ui_common/circleindicator/CircleIndicator$d",
        "Landroidx/viewpager2/widget/ViewPager2$i;",
        "",
        "position",
        "",
        "onPageSelected",
        "(I)V",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic b:Lorg/xbet/ui_common/circleindicator/CircleIndicator;


# direct methods
.method public constructor <init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/viewpager2/widget/ViewPager2$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onPageSelected(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$d;->b:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->a(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
