.class final Lcom/google/android/material/color/utilities/QuantizerWu$Box;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/material/color/utilities/QuantizerWu;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Box"
.end annotation


# instance fields
.field b0:I

.field b1:I

.field g0:I

.field g1:I

.field r0:I

.field r1:I

.field vol:I


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 2
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->r0:I

    .line 3
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->r1:I

    .line 4
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->g0:I

    .line 5
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->g1:I

    .line 6
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->b0:I

    .line 7
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->b1:I

    .line 8
    iput v0, p0, Lcom/google/android/material/color/utilities/QuantizerWu$Box;->vol:I

    return-void
.end method

.method public synthetic constructor <init>(Lcom/google/android/material/color/utilities/QuantizerWu$1;)V
    .locals 0

    .line 9
    invoke-direct {p0}, Lcom/google/android/material/color/utilities/QuantizerWu$Box;-><init>()V

    return-void
.end method
