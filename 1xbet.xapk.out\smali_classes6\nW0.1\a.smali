.class public interface abstract LnW0/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008f\u0018\u00002\u00020\u0001J4\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00080\u00072\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0006\u001a\u00020\u0002H\u00a7@\u00a2\u0006\u0004\u0008\t\u0010\nJ>\u0010\u000e\u001a\u0008\u0012\u0004\u0012\u00020\r0\u00072\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u000b\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0002H\u00a7@\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ>\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u00140\u00072\u0008\u0008\u0001\u0010\u0010\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0003\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0013\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016JH\u0010\u001c\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u00072\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0017\u001a\u00020\u00042\u0008\u0008\u0001\u0010\u0018\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u0019\u001a\u00020\u00022\u0008\u0008\u0001\u0010\u001a\u001a\u00020\u0002H\u00a7@\u00a2\u0006\u0004\u0008\u001c\u0010\u001d\u00a8\u0006\u001e"
    }
    d2 = {
        "LnW0/a;",
        "",
        "",
        "whence",
        "",
        "lng",
        "groupId",
        "Le8/b;",
        "LqW0/l;",
        "d",
        "(ILjava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;",
        "curISO",
        "totoType",
        "LqW0/j;",
        "c",
        "(ILjava/lang/String;Ljava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;",
        "token",
        "refId",
        "LqW0/b;",
        "totoJackpotBetRequest",
        "LqW0/c;",
        "b",
        "(Ljava/lang/String;IILqW0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "currencyIso",
        "jackpotType",
        "pageNum",
        "pageSize",
        "LqW0/e;",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;IIILkotlin/coroutines/e;)Ljava/lang/Object;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;Ljava/lang/String;IIILkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "curISO"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # I
        .annotation runtime Lbd1/t;
            value = "jackpotType"
        .end annotation
    .end param
    .param p4    # I
        .annotation runtime Lbd1/t;
            value = "pageNum"
        .end annotation
    .end param
    .param p5    # I
        .annotation runtime Lbd1/t;
            value = "pageSize"
        .end annotation
    .end param
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/toto/JackpotMobile/v1/tirags"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "III",
            "Lkotlin/coroutines/e<",
            "-",
            "Le8/b<",
            "LqW0/e;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract b(Ljava/lang/String;IILqW0/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation runtime Lbd1/i;
            value = "X-Auth"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # I
        .annotation runtime Lbd1/t;
            value = "ref"
        .end annotation
    .end param
    .param p3    # I
        .annotation runtime Lbd1/t;
            value = "whence"
        .end annotation
    .end param
    .param p4    # LqW0/b;
        .annotation runtime Lbd1/a;
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation runtime Lbd1/o;
        value = "/toto/JackpotMobile/v1/makeBet"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II",
            "LqW0/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Le8/b<",
            "LqW0/c;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract c(ILjava/lang/String;Ljava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # I
        .annotation runtime Lbd1/t;
            value = "whence"
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "curISO"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # I
        .annotation runtime Lbd1/t;
            value = "jackpotType"
        .end annotation
    .end param
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/toto/JackpotMobile/v1/activeTirag"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Le8/b<",
            "LqW0/j;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract d(ILjava/lang/String;ILkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # I
        .annotation runtime Lbd1/t;
            value = "whence"
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation runtime Lbd1/t;
            value = "lng"
        .end annotation

        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # I
        .annotation runtime Lbd1/t;
            value = "gr"
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation runtime Lbd1/f;
        value = "/toto/JackpotMobile/v1/types"
    .end annotation

    .annotation runtime Lbd1/k;
        value = {
            "Accept: application/vnd.xenvelop+json"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Le8/b<",
            "LqW0/l;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method
