.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
        "aggregatorPopularClassicCommonClickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/d;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/d;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt;->h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt;->g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)LA4/c;
    .locals 3
    .param p0    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/g;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/g;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/h;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/h;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt$popularClassicAggregatorBannerDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt$popularClassicAggregatorBannerDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt$popularClassicAggregatorBannerDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorBannerDelegateKt$popularClassicAggregatorBannerDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/d;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/d;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/d;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/i;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/i;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;)V

    .line 6
    .line 7
    .line 8
    const/4 p0, 0x1

    .line 9
    const/4 p1, 0x0

    .line 10
    invoke-static {v0, p1, v1, p0, p1}, LN11/f;->n(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 11
    .line 12
    .line 13
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/j;

    .line 14
    .line 15
    invoke-direct {p0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/j;-><init>(LB4/a;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 19
    .line 20
    .line 21
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 22
    .line 23
    return-object p0
.end method

.method public static final h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    invoke-interface {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;->l(Ljava/lang/String;Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LIb1/d;

    .line 6
    .line 7
    iget-object p1, p1, LIb1/d;->c:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    check-cast v1, LQb1/b;

    .line 22
    .line 23
    invoke-virtual {v1}, LQb1/b;->d()I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getText(I)Ljava/lang/CharSequence;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    check-cast p1, LIb1/d;

    .line 39
    .line 40
    iget-object p1, p1, LIb1/d;->b:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 41
    .line 42
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    check-cast p0, LQb1/b;

    .line 47
    .line 48
    invoke-virtual {p0}, LQb1/b;->W()I

    .line 49
    .line 50
    .line 51
    move-result p0

    .line 52
    invoke-virtual {p1, p0}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 53
    .line 54
    .line 55
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 56
    .line 57
    return-object p0
.end method
