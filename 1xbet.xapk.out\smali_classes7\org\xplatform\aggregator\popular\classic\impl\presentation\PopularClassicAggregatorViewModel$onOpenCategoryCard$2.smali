.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.PopularClassicAggregatorViewModel$onOpenCategoryCard$2"
    f = "PopularClassicAggregatorViewModel.kt"
    l = {
        0xe7
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->i(Ljava/lang/String;LHZ0/a;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $screenName:Ljava/lang/String;

.field final synthetic $selectedCategoryCard:LHZ0/a;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;LHZ0/a;Ljava/lang/String;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;",
            "LHZ0/a;",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->$selectedCategoryCard:LHZ0/a;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->$screenName:Ljava/lang/String;

    .line 6
    .line 7
    const/4 p1, 0x2

    .line 8
    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->$selectedCategoryCard:LHZ0/a;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->$screenName:Ljava/lang/String;

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;LHZ0/a;Ljava/lang/String;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 28
    .line 29
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lv81/p;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    iput v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->label:I

    .line 34
    .line 35
    invoke-interface {p1, p0}, Lv81/p;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    if-ne p1, v0, :cond_2

    .line 40
    .line 41
    return-object v0

    .line 42
    :cond_2
    :goto_0
    check-cast p1, Ljava/lang/Iterable;

    .line 43
    .line 44
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->$selectedCategoryCard:LHZ0/a;

    .line 45
    .line 46
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    :cond_3
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 51
    .line 52
    .line 53
    move-result v1

    .line 54
    if-eqz v1, :cond_4

    .line 55
    .line 56
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    move-object v2, v1

    .line 61
    check-cast v2, Lg81/b;

    .line 62
    .line 63
    invoke-virtual {v2}, Lg81/b;->g()J

    .line 64
    .line 65
    .line 66
    move-result-wide v2

    .line 67
    long-to-int v3, v2

    .line 68
    invoke-virtual {v0}, LHZ0/a;->e()I

    .line 69
    .line 70
    .line 71
    move-result v2

    .line 72
    if-ne v3, v2, :cond_3

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :cond_4
    const/4 v1, 0x0

    .line 76
    :goto_1
    check-cast v1, Lg81/b;

    .line 77
    .line 78
    if-eqz v1, :cond_5

    .line 79
    .line 80
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 81
    .line 82
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;->$screenName:Ljava/lang/String;

    .line 83
    .line 84
    invoke-static {p1, v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->g4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lg81/b;)V

    .line 85
    .line 86
    .line 87
    :cond_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 88
    .line 89
    return-object p1
.end method
