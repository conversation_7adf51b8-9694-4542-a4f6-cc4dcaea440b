.class public final LN81/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LE81/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J!\u0010\t\u001a\u00020\u00082\u0006\u0010\u0005\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0016\u00a2\u0006\u0004\u0008\t\u0010\n\u00a8\u0006\u000b"
    }
    d2 = {
        "LN81/w;",
        "LE81/a;",
        "<init>",
        "()V",
        "Landroidx/fragment/app/FragmentManager;",
        "fragmentManager",
        "LA81/a;",
        "callback",
        "",
        "a",
        "(Landroidx/fragment/app/FragmentManager;LA81/a;)V",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public a(Landroidx/fragment/app/FragmentManager;LA81/a;)V
    .locals 1
    .param p1    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog;->x1:Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog$a;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskOnboardingBottomSheetDialog$a;->a(Landroidx/fragment/app/FragmentManager;LA81/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
