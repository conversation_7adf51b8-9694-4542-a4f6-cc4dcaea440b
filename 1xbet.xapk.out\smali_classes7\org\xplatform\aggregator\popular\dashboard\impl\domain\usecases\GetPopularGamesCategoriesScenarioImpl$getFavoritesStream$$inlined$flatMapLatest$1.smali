.class public final Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.dashboard.impl.domain.usecases.GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1"
    f = "GetPopularGamesCategoriesScenarioImpl.kt"
    l = {
        0xbf,
        0xbd
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->n()Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;>;",
        "Ln9/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u00020\u0004\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0001*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u0006\u0010\u0003\u001a\u00028\u0001H\n\u00a8\u0006\u0005"
    }
    d2 = {
        "R",
        "T",
        "Lkotlinx/coroutines/flow/f;",
        "it",
        "",
        "kotlinx/coroutines/flow/FlowKt__MergeKt$flatMapLatest$1",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field private synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V
    .locals 0

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    const/4 p2, 0x3

    invoke-direct {p0, p2, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "+",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;",
            "Ln9/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    invoke-direct {v0, p3, v1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_2

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto/16 :goto_3

    .line 19
    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 29
    .line 30
    check-cast v1, Ln9/b;

    .line 31
    .line 32
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v3, Lkotlinx/coroutines/flow/f;

    .line 35
    .line 36
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 44
    .line 45
    check-cast p1, Lkotlinx/coroutines/flow/f;

    .line 46
    .line 47
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 48
    .line 49
    check-cast v1, Ln9/b;

    .line 50
    .line 51
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 52
    .line 53
    .line 54
    move-result v4

    .line 55
    invoke-virtual {v1}, Ln9/b;->d()Z

    .line 56
    .line 57
    .line 58
    move-result v5

    .line 59
    if-eq v4, v5, :cond_4

    .line 60
    .line 61
    iget-object v4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 62
    .line 63
    invoke-static {v4}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->c(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lv81/f;

    .line 64
    .line 65
    .line 66
    move-result-object v4

    .line 67
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 68
    .line 69
    iput-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 70
    .line 71
    iput v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 72
    .line 73
    invoke-interface {v4, p0}, Lv81/f;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    if-ne v3, v0, :cond_3

    .line 78
    .line 79
    goto :goto_2

    .line 80
    :cond_3
    move-object v3, p1

    .line 81
    :goto_0
    move-object p1, v3

    .line 82
    :cond_4
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 83
    .line 84
    .line 85
    move-result v1

    .line 86
    const/4 v3, 0x0

    .line 87
    if-eqz v1, :cond_5

    .line 88
    .line 89
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 90
    .line 91
    invoke-static {v1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->f(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lv81/m;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    iget-object v4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 96
    .line 97
    invoke-static {v4}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->g(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 98
    .line 99
    .line 100
    move-result-object v4

    .line 101
    invoke-interface {v4}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 102
    .line 103
    .line 104
    move-result-object v4

    .line 105
    invoke-virtual {v4}, Lek0/o;->o()Lek0/a;

    .line 106
    .line 107
    .line 108
    move-result-object v4

    .line 109
    invoke-virtual {v4}, Lek0/a;->c()Z

    .line 110
    .line 111
    .line 112
    move-result v4

    .line 113
    iget-object v5, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 114
    .line 115
    invoke-static {v5}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->h(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Li8/j;

    .line 116
    .line 117
    .line 118
    move-result-object v5

    .line 119
    invoke-interface {v5}, Li8/j;->invoke()Ljava/lang/String;

    .line 120
    .line 121
    .line 122
    move-result-object v5

    .line 123
    invoke-interface {v1, v4, v5}, Lv81/m;->a(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    new-instance v4, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$lambda$7$$inlined$map$1;

    .line 128
    .line 129
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$lambda$7$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 130
    .line 131
    .line 132
    goto :goto_1

    .line 133
    :cond_5
    new-instance v1, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$1$2;

    .line 134
    .line 135
    invoke-direct {v1, v3}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$1$2;-><init>(Lkotlin/coroutines/e;)V

    .line 136
    .line 137
    .line 138
    invoke-static {v1}, Lkotlinx/coroutines/flow/g;->V(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 139
    .line 140
    .line 141
    move-result-object v4

    .line 142
    :goto_1
    iput-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 143
    .line 144
    iput-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 145
    .line 146
    iput v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 147
    .line 148
    invoke-static {p1, v4, p0}, Lkotlinx/coroutines/flow/g;->H(Lkotlinx/coroutines/flow/f;Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 149
    .line 150
    .line 151
    move-result-object p1

    .line 152
    if-ne p1, v0, :cond_6

    .line 153
    .line 154
    :goto_2
    return-object v0

    .line 155
    :cond_6
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 156
    .line 157
    return-object p1
.end method
