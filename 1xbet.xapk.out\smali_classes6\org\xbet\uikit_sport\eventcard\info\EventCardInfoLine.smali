.class public final Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/info/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\r\n\u0002\u0008\u0002\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0007\u0018\u0000 ,2\u00020\u00012\u00020\u0002:\u0001)B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\r\u001a\u00020\u000c2\u0008\u0010\u000b\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0004\u0008\r\u0010\u0010J\u0017\u0010\u0011\u001a\u00020\u000c2\u0008\u0008\u0001\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0011\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u000c2\u0008\u0010\u000b\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0004\u0008\u0011\u0010\u0010J\u0015\u0010\u0014\u001a\u00020\u000c2\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0015\u0010\u0018\u001a\u00020\u000c2\u0006\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0015\u0010\u001b\u001a\u00020\u000c2\u0006\u0010\u001a\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u001b\u0010\u0015J\u0015\u0010\u001e\u001a\u00020\u000c2\u0006\u0010\u001d\u001a\u00020\u001c\u00a2\u0006\u0004\u0008\u001e\u0010\u001fJ\u0015\u0010!\u001a\u00020\u000c2\u0006\u0010 \u001a\u00020\u0016\u00a2\u0006\u0004\u0008!\u0010\u0019J\r\u0010\"\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\"\u0010#J\r\u0010$\u001a\u00020\u000c\u00a2\u0006\u0004\u0008$\u0010#J\u000f\u0010&\u001a\u00020%H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010(\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008(\u0010#R\u0014\u0010+\u001a\u00020%8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008)\u0010*\u00a8\u0006-"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/info/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attr",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "text",
        "",
        "setInfoText",
        "(I)V",
        "",
        "(Ljava/lang/CharSequence;)V",
        "setAltInfoText",
        "",
        "milliSeconds",
        "setTime",
        "(J)V",
        "",
        "isVisible",
        "setTimerVisibility",
        "(Z)V",
        "intervalMs",
        "setUpdateInterval",
        "Lorg/xbet/uikit/components/timer/Timer$TimeDirection;",
        "timeDirection",
        "setTimeDirection",
        "(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V",
        "hideAfterFinished",
        "setHideAfterFinished",
        "u",
        "()V",
        "v",
        "LC31/p;",
        "w",
        "()LC31/p;",
        "t",
        "a",
        "LC31/p;",
        "binding",
        "b",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final c:I


# instance fields
.field public final a:LC31/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->b:Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->c:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0, p0}, LC31/p;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/p;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 7
    sget-object v1, Lm31/g;->EventCardInfo:[I

    const/4 v2, 0x0

    .line 8
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 9
    sget p3, Lm31/g;->EventCardInfo_infoText:I

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p3

    invoke-virtual {p0, p3}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->setInfoText(Ljava/lang/CharSequence;)V

    .line 10
    sget p3, Lm31/g;->EventCardInfo_altInfoText:I

    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p3

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->setAltInfoText(Ljava/lang/CharSequence;)V

    .line 11
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    .line 12
    iget-object p1, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    new-instance p2, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$2;

    invoke-direct {p2, p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine$2;-><init>(Ljava/lang/Object;)V

    invoke-virtual {p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setOnTimerFinished(Lkotlin/jvm/functions/Function0;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardInfoLineStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static final synthetic s(Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->t()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final setAltInfoText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->setAltInfoText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setAltInfoText(Ljava/lang/CharSequence;)V
    .locals 2

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    iget-object v0, v0, LC31/p;->b:Landroid/widget/TextView;

    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 4
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    if-eqz p1, :cond_2

    const/16 v1, 0x8

    .line 5
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->w()LC31/p;

    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->t()V

    return-void
.end method

.method public final setHideAfterFinished(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setHideAfterFinished(Z)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setInfoText(I)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->setInfoText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInfoText(Ljava/lang/CharSequence;)V
    .locals 2

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    iget-object v0, v0, LC31/p;->c:Landroid/widget/TextView;

    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    .line 4
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    if-eqz p1, :cond_2

    const/16 v1, 0x8

    .line 5
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->w()LC31/p;

    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->t()V

    return-void
.end method

.method public final setTime(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setTime(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimeDirection(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/timer/Timer$TimeDirection;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/timer/Timer;->setTimeDirection(Lorg/xbet/uikit/components/timer/Timer$TimeDirection;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setTimerVisibility(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/16 p1, 0x8

    .line 10
    .line 11
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->w()LC31/p;

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->t()V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final setUpdateInterval(J)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit/components/timer/Timer;->setUpdateTimeIntervalMs(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final t()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    invoke-virtual {v0}, LC31/p;->getRoot()Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, v0, LC31/p;->c:Landroid/widget/TextView;

    .line 8
    .line 9
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    const/4 v3, 0x0

    .line 14
    if-nez v2, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    iget-object v2, v0, LC31/p;->b:Landroid/widget/TextView;

    .line 18
    .line 19
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    if-nez v2, :cond_1

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_1
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 27
    .line 28
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    if-nez v0, :cond_2

    .line 33
    .line 34
    :goto_0
    const/4 v0, 0x1

    .line 35
    goto :goto_1

    .line 36
    :cond_2
    const/4 v0, 0x0

    .line 37
    :goto_1
    if-eqz v0, :cond_3

    .line 38
    .line 39
    goto :goto_2

    .line 40
    :cond_3
    const/16 v3, 0x8

    .line 41
    .line 42
    :goto_2
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final u()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/timer/Timer;->t()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->w()LC31/p;

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final v()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v0, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 4
    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/timer/Timer;->u()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->w()LC31/p;

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final w()LC31/p;
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/info/EventCardInfoLine;->a:LC31/p;

    .line 2
    .line 3
    iget-object v1, v0, LC31/p;->c:Landroid/widget/TextView;

    .line 4
    .line 5
    iget-object v2, v0, LC31/p;->d:Lorg/xbet/uikit/components/timer/Timer;

    .line 6
    .line 7
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    const/4 v3, 0x1

    .line 12
    const/4 v4, 0x2

    .line 13
    if-nez v2, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    iget-object v2, v0, LC31/p;->b:Landroid/widget/TextView;

    .line 17
    .line 18
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 19
    .line 20
    .line 21
    move-result v2

    .line 22
    if-nez v2, :cond_1

    .line 23
    .line 24
    :goto_0
    const/4 v2, 0x1

    .line 25
    goto :goto_1

    .line 26
    :cond_1
    const/4 v2, 0x2

    .line 27
    :goto_1
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 28
    .line 29
    .line 30
    iget-object v1, v0, LC31/p;->b:Landroid/widget/TextView;

    .line 31
    .line 32
    iget-object v2, v0, LC31/p;->c:Landroid/widget/TextView;

    .line 33
    .line 34
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 35
    .line 36
    .line 37
    move-result v2

    .line 38
    if-nez v2, :cond_2

    .line 39
    .line 40
    goto :goto_2

    .line 41
    :cond_2
    const/4 v3, 0x2

    .line 42
    :goto_2
    invoke-virtual {v1, v3}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 43
    .line 44
    .line 45
    return-object v0
.end method
