.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_list.AggregatorTournamentsViewModel$checkForParticipation$1"
    f = "AggregatorTournamentsViewModel.kt"
    l = {
        0xec,
        0xff,
        0x108,
        0x112
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->Q4(Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $blocked:Z

.field final synthetic $model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

.field final synthetic $screenName:Ljava/lang/String;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Ljava/lang/String;ZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;",
            "Ljava/lang/String;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$screenName:Ljava/lang/String;

    iput-boolean p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$blocked:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p5}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method

.method public static synthetic a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    invoke-virtual {p0, v0, v1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->h5(JZLjava/lang/String;)V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$screenName:Ljava/lang/String;

    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$blocked:Z

    move-object v5, p2

    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;Ljava/lang/String;ZLkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x4

    .line 8
    const/4 v3, 0x3

    .line 9
    const/4 v4, 0x2

    .line 10
    const/4 v5, 0x1

    .line 11
    if-eqz v1, :cond_3

    .line 12
    .line 13
    if-eq v1, v5, :cond_2

    .line 14
    .line 15
    if-eq v1, v4, :cond_1

    .line 16
    .line 17
    if-eq v1, v3, :cond_1

    .line 18
    .line 19
    if-ne v1, v2, :cond_0

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 23
    .line 24
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 25
    .line 26
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    throw p1

    .line 30
    :cond_1
    :goto_0
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 31
    .line 32
    .line 33
    goto/16 :goto_3

    .line 34
    .line 35
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 43
    .line 44
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->G4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lw81/g;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 49
    .line 50
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 51
    .line 52
    .line 53
    move-result-wide v6

    .line 54
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 55
    .line 56
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->i()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    iput v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->label:I

    .line 61
    .line 62
    invoke-interface {p1, v6, v7, v1, p0}, Lw81/g;->a(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    if-ne p1, v0, :cond_4

    .line 67
    .line 68
    goto/16 :goto_2

    .line 69
    .line 70
    :cond_4
    :goto_1
    check-cast p1, Lh81/b;

    .line 71
    .line 72
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 73
    .line 74
    invoke-static {v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->O4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lh81/b;)Z

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    if-eqz v1, :cond_5

    .line 79
    .line 80
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 81
    .line 82
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 83
    .line 84
    invoke-virtual {v5}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 85
    .line 86
    .line 87
    move-result-wide v5

    .line 88
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$screenName:Ljava/lang/String;

    .line 89
    .line 90
    invoke-static {v1, v5, v6, p1, v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->K4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;JLh81/b;Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    :cond_5
    instance-of v1, p1, Lh81/b$g;

    .line 94
    .line 95
    if-eqz v1, :cond_6

    .line 96
    .line 97
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 98
    .line 99
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->C4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LwX0/C;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    invoke-virtual {p1}, LwX0/D;->a()LwX0/c;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    if-eqz p1, :cond_c

    .line 108
    .line 109
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 110
    .line 111
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 112
    .line 113
    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$blocked:Z

    .line 114
    .line 115
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$screenName:Ljava/lang/String;

    .line 116
    .line 117
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;

    .line 118
    .line 119
    invoke-direct {v4, v0, v1, v2, v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)V

    .line 120
    .line 121
    .line 122
    invoke-virtual {p1, v4}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 123
    .line 124
    .line 125
    goto/16 :goto_3

    .line 126
    .line 127
    :cond_6
    instance-of v1, p1, Lh81/b$c;

    .line 128
    .line 129
    if-eqz v1, :cond_7

    .line 130
    .line 131
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 132
    .line 133
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->q4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LP91/b;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    new-instance v0, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 138
    .line 139
    new-instance v1, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;

    .line 140
    .line 141
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 142
    .line 143
    invoke-virtual {v2}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->h()J

    .line 144
    .line 145
    .line 146
    move-result-wide v2

    .line 147
    sget-object v4, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 148
    .line 149
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 150
    .line 151
    invoke-virtual {v5}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;->c()Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;

    .line 152
    .line 153
    .line 154
    move-result-object v5

    .line 155
    invoke-virtual {v5}, Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel$b;->g()Ljava/lang/String;

    .line 156
    .line 157
    .line 158
    move-result-object v5

    .line 159
    const/16 v7, 0x8

    .line 160
    .line 161
    const/4 v8, 0x0

    .line 162
    const/4 v6, 0x0

    .line 163
    invoke-direct/range {v1 .. v8}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentsFullInfoScreen;-><init>(JLorg/xplatform/aggregator/api/navigation/TournamentsPage;Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 164
    .line 165
    .line 166
    const/16 v12, 0xf7

    .line 167
    .line 168
    const/4 v13, 0x0

    .line 169
    move-object v5, v1

    .line 170
    const/4 v1, 0x0

    .line 171
    const/4 v2, 0x0

    .line 172
    const-wide/16 v3, 0x0

    .line 173
    .line 174
    const/4 v6, 0x0

    .line 175
    const-wide/16 v7, 0x0

    .line 176
    .line 177
    const-wide/16 v9, 0x0

    .line 178
    .line 179
    const/4 v11, 0x0

    .line 180
    invoke-direct/range {v0 .. v13}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 181
    .line 182
    .line 183
    invoke-virtual {p1, v0}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 184
    .line 185
    .line 186
    goto/16 :goto_3

    .line 187
    .line 188
    :cond_7
    instance-of v1, p1, Lh81/b$a;

    .line 189
    .line 190
    const/4 v5, 0x0

    .line 191
    if-eqz v1, :cond_8

    .line 192
    .line 193
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 194
    .line 195
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->$model:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    .line 196
    .line 197
    invoke-static {p1, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->N4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;)V

    .line 198
    .line 199
    .line 200
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 201
    .line 202
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->x4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lkotlinx/coroutines/flow/U;

    .line 203
    .line 204
    .line 205
    move-result-object p1

    .line 206
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;

    .line 207
    .line 208
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 209
    .line 210
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 211
    .line 212
    .line 213
    move-result-object v2

    .line 214
    sget v3, Lpb/k;->app_win_congratulations:I

    .line 215
    .line 216
    new-array v6, v5, [Ljava/lang/Object;

    .line 217
    .line 218
    invoke-interface {v2, v3, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 219
    .line 220
    .line 221
    move-result-object v2

    .line 222
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 223
    .line 224
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 225
    .line 226
    .line 227
    move-result-object v3

    .line 228
    sget v6, Lpb/k;->tournamnet_enrolled_success:I

    .line 229
    .line 230
    new-array v7, v5, [Ljava/lang/Object;

    .line 231
    .line 232
    invoke-interface {v3, v6, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 233
    .line 234
    .line 235
    move-result-object v3

    .line 236
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 237
    .line 238
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 239
    .line 240
    .line 241
    move-result-object v6

    .line 242
    sget v7, Lpb/k;->ok_new:I

    .line 243
    .line 244
    new-array v5, v5, [Ljava/lang/Object;

    .line 245
    .line 246
    invoke-interface {v6, v7, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 247
    .line 248
    .line 249
    move-result-object v5

    .line 250
    sget-object v6, Lorg/xbet/uikit/components/dialog/AlertType;->SUCCESS:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 251
    .line 252
    invoke-direct {v1, v2, v3, v5, v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 253
    .line 254
    .line 255
    iput v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->label:I

    .line 256
    .line 257
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 258
    .line 259
    .line 260
    move-result-object p1

    .line 261
    if-ne p1, v0, :cond_c

    .line 262
    .line 263
    goto/16 :goto_2

    .line 264
    .line 265
    :cond_8
    instance-of v1, p1, Lh81/b$b;

    .line 266
    .line 267
    if-eqz v1, :cond_a

    .line 268
    .line 269
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 270
    .line 271
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->x4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lkotlinx/coroutines/flow/U;

    .line 272
    .line 273
    .line 274
    move-result-object v1

    .line 275
    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 276
    .line 277
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 278
    .line 279
    .line 280
    move-result-object v2

    .line 281
    sget v4, Lpb/k;->tournamenet_dialor_title:I

    .line 282
    .line 283
    new-array v6, v5, [Ljava/lang/Object;

    .line 284
    .line 285
    invoke-interface {v2, v4, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 286
    .line 287
    .line 288
    move-result-object v2

    .line 289
    check-cast p1, Lh81/b$b;

    .line 290
    .line 291
    invoke-virtual {p1}, Lh81/b$b;->b()Ljava/lang/String;

    .line 292
    .line 293
    .line 294
    move-result-object p1

    .line 295
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 296
    .line 297
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 298
    .line 299
    .line 300
    move-result v6

    .line 301
    if-nez v6, :cond_9

    .line 302
    .line 303
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 304
    .line 305
    .line 306
    move-result-object p1

    .line 307
    sget v4, Lpb/k;->unknown_service_error:I

    .line 308
    .line 309
    new-array v6, v5, [Ljava/lang/Object;

    .line 310
    .line 311
    invoke-interface {p1, v4, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 312
    .line 313
    .line 314
    move-result-object p1

    .line 315
    :cond_9
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 316
    .line 317
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 318
    .line 319
    .line 320
    move-result-object v4

    .line 321
    sget v6, Lpb/k;->ok_new:I

    .line 322
    .line 323
    new-array v5, v5, [Ljava/lang/Object;

    .line 324
    .line 325
    invoke-interface {v4, v6, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 326
    .line 327
    .line 328
    move-result-object v4

    .line 329
    sget-object v5, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 330
    .line 331
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;

    .line 332
    .line 333
    invoke-direct {v6, v2, p1, v4, v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 334
    .line 335
    .line 336
    iput v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->label:I

    .line 337
    .line 338
    invoke-interface {v1, v6, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 339
    .line 340
    .line 341
    move-result-object p1

    .line 342
    if-ne p1, v0, :cond_c

    .line 343
    .line 344
    goto :goto_2

    .line 345
    :cond_a
    instance-of v1, p1, Lh81/b$f;

    .line 346
    .line 347
    if-eqz v1, :cond_b

    .line 348
    .line 349
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 350
    .line 351
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->x4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)Lkotlinx/coroutines/flow/U;

    .line 352
    .line 353
    .line 354
    move-result-object p1

    .line 355
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;

    .line 356
    .line 357
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 358
    .line 359
    invoke-static {v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 360
    .line 361
    .line 362
    move-result-object v3

    .line 363
    sget v4, Lpb/k;->tournamenet_dialor_title:I

    .line 364
    .line 365
    new-array v6, v5, [Ljava/lang/Object;

    .line 366
    .line 367
    invoke-interface {v3, v4, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 368
    .line 369
    .line 370
    move-result-object v3

    .line 371
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 372
    .line 373
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 374
    .line 375
    .line 376
    move-result-object v4

    .line 377
    sget v6, Lpb/k;->unknown_service_error:I

    .line 378
    .line 379
    new-array v7, v5, [Ljava/lang/Object;

    .line 380
    .line 381
    invoke-interface {v4, v6, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 382
    .line 383
    .line 384
    move-result-object v4

    .line 385
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 386
    .line 387
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->B4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)LHX0/e;

    .line 388
    .line 389
    .line 390
    move-result-object v6

    .line 391
    sget v7, Lpb/k;->ok_new:I

    .line 392
    .line 393
    new-array v5, v5, [Ljava/lang/Object;

    .line 394
    .line 395
    invoke-interface {v6, v7, v5}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 396
    .line 397
    .line 398
    move-result-object v5

    .line 399
    sget-object v6, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 400
    .line 401
    invoke-direct {v1, v3, v4, v5, v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 402
    .line 403
    .line 404
    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->label:I

    .line 405
    .line 406
    invoke-interface {p1, v1, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 407
    .line 408
    .line 409
    move-result-object p1

    .line 410
    if-ne p1, v0, :cond_c

    .line 411
    .line 412
    :goto_2
    return-object v0

    .line 413
    :cond_b
    instance-of v0, p1, Lh81/b$e;

    .line 414
    .line 415
    if-nez v0, :cond_d

    .line 416
    .line 417
    instance-of p1, p1, Lh81/b$d;

    .line 418
    .line 419
    if-eqz p1, :cond_c

    .line 420
    .line 421
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 422
    .line 423
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->p4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;)V

    .line 424
    .line 425
    .line 426
    :cond_c
    :goto_3
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 427
    .line 428
    return-object p1

    .line 429
    :cond_d
    new-instance p1, Lcom/xbet/onexuser/domain/exceptions/NotValidRefreshTokenException;

    .line 430
    .line 431
    invoke-direct {p1}, Lcom/xbet/onexuser/domain/exceptions/NotValidRefreshTokenException;-><init>()V

    .line 432
    .line 433
    .line 434
    throw p1
.end method
