.class public final synthetic Lo21/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo21/b;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lo21/b;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;

    invoke-static {v0}, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;->c(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;)Lorg/xbet/uikit/utils/z;

    move-result-object v0

    return-object v0
.end method
