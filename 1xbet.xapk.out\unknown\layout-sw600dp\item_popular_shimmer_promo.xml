<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/space_8"
    android:orientation="vertical"
    android:theme="?attr/uikitTheme">

    <org.xbet.uikit.components.shimmer.ShimmerView
        android:id="@+id/title"
        style="@style/Widget.Shimmer.Primary"
        android:layout_width="@dimen/shimmer_size_128"
        android:layout_height="@dimen/shimmer_size_20"
        android:layout_marginStart="@dimen/extra_large_horizontal_margin_dynamic"
        android:radius="@dimen/corner_radius_10" />

    <org.xbet.uikit.components.shimmer.ShimmerView
        android:id="@+id/tournamentBanner"
        style="@style/Widget.Shimmer.Primary"
        android:layout_width="match_parent"
        android:layout_height="@dimen/shimmer_size_272"
        android:layout_marginHorizontal="@dimen/medium_horizontal_margin_dynamic"
        android:layout_marginTop="@dimen/space_8"
        android:radius="@dimen/corner_radius_24" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/gameFirst"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/shimmer_size_190"
            android:layout_marginStart="@dimen/medium_horizontal_margin_dynamic"
            android:layout_marginTop="@dimen/space_8"
            android:layout_weight="0.5"
            android:radius="@dimen/corner_radius_24" />

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/gameSecond"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/shimmer_size_190"
            android:layout_marginStart="@dimen/space_8"
            android:layout_marginTop="@dimen/space_8"
            android:layout_marginEnd="@dimen/medium_horizontal_margin_dynamic"
            android:layout_weight="0.5"
            android:radius="@dimen/corner_radius_24" />
    </LinearLayout>
</LinearLayout>