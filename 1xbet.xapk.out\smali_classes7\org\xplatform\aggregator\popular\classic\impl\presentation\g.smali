.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;->b:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;->c:I

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;->b:Lorg/xplatform/aggregator/api/model/Game;

    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/g;->c:I

    invoke-static {v0, v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->A2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
