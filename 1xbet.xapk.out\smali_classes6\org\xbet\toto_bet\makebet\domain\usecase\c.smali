.class public final Lorg/xbet/toto_bet/makebet/domain/usecase/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0001\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\u0008H\u0086\u0002\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u000bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/domain/usecase/c;",
        "",
        "LVU0/a;",
        "totoBetMakeBetRepository",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/j;",
        "getVariantsAmountUseCase",
        "<init>",
        "(LVU0/a;Lorg/xbet/toto_bet/makebet/domain/usecase/j;)V",
        "Ljava/math/BigDecimal;",
        "a",
        "()Ljava/math/BigDecimal;",
        "LVU0/a;",
        "b",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/j;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LVU0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/toto_bet/makebet/domain/usecase/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(LVU0/a;Lorg/xbet/toto_bet/makebet/domain/usecase/j;)V
    .locals 0
    .param p1    # LVU0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/toto_bet/makebet/domain/usecase/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->a:LVU0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->b:Lorg/xbet/toto_bet/makebet/domain/usecase/j;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Ljava/math/BigDecimal;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Ljava/math/BigDecimal;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->a:LVU0/a;

    .line 4
    .line 5
    invoke-interface {v1}, LVU0/a;->a()LZV0/a;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, LZV0/a;->b()D

    .line 10
    .line 11
    .line 12
    move-result-wide v1

    .line 13
    invoke-static {v1, v2}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-direct {v0, v1}, Ljava/math/BigDecimal;-><init>(Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/c;->b:Lorg/xbet/toto_bet/makebet/domain/usecase/j;

    .line 21
    .line 22
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/domain/usecase/j;->a()J

    .line 23
    .line 24
    .line 25
    move-result-wide v1

    .line 26
    invoke-static {v1, v2}, Ljava/math/BigDecimal;->valueOf(J)Ljava/math/BigDecimal;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    invoke-virtual {v0, v1}, Ljava/math/BigDecimal;->multiply(Ljava/math/BigDecimal;)Ljava/math/BigDecimal;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    return-object v0
.end method
