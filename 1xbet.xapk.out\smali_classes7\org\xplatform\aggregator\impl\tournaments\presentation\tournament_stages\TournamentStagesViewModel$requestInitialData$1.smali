.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournament_stages.TournamentStagesViewModel$requestInitialData$1"
    f = "TournamentStagesViewModel.kt"
    l = {
        0xc0
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->I3(JZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Li81/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Li81/a;",
        "tournamentFullInfo",
        "",
        "<anonymous>",
        "(Li81/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(Li81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Li81/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->invoke(Li81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 17
    .line 18
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 19
    .line 20
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 25
    .line 26
    .line 27
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    .line 28
    .line 29
    check-cast p1, Li81/a;

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    .line 32
    .line 33
    invoke-static {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$c$a;

    .line 38
    .line 39
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    .line 40
    .line 41
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;)LHX0/e;

    .line 42
    .line 43
    .line 44
    move-result-object v4

    .line 45
    invoke-static {p1, v4}, Ljb1/B;->g(Li81/a;LHX0/e;)Ljava/util/List;

    .line 46
    .line 47
    .line 48
    move-result-object v4

    .line 49
    invoke-virtual {p1}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 50
    .line 51
    .line 52
    move-result-object v5

    .line 53
    invoke-virtual {p1}, Li81/a;->k()Lh81/a;

    .line 54
    .line 55
    .line 56
    move-result-object v6

    .line 57
    invoke-virtual {p1}, Li81/a;->t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iget-object v7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    .line 62
    .line 63
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;)LHX0/e;

    .line 64
    .line 65
    .line 66
    move-result-object v7

    .line 67
    sget-object v8, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 68
    .line 69
    invoke-static {v6, p1, v8, v7}, LWa1/c;->b(Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;LHX0/e;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    invoke-direct {v3, v4, v5, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$c$a;-><init>(Ljava/util/List;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;)V

    .line 74
    .line 75
    .line 76
    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$1;->label:I

    .line 77
    .line 78
    invoke-interface {v1, v3, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 79
    .line 80
    .line 81
    move-result-object p1

    .line 82
    if-ne p1, v0, :cond_2

    .line 83
    .line 84
    return-object v0

    .line 85
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 86
    .line 87
    return-object p1
.end method
