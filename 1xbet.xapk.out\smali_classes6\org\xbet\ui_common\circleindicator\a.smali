.class public final Lorg/xbet/ui_common/circleindicator/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/ui_common/circleindicator/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000e\u0008\u0007\u0018\u0000 \u000f2\u00020\u0001:\u0001\tB)\u0008\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\t\u0010\n\u001a\u0004\u0008\u000b\u0010\u000cR\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000b\u0010\n\u001a\u0004\u0008\t\u0010\u000cR\u0017\u0010\u0005\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\r\u0010\n\u001a\u0004\u0008\u000e\u0010\u000cR\u0017\u0010\u0006\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u000e\u0010\n\u001a\u0004\u0008\r\u0010\u000c\u00a8\u0006\u0010"
    }
    d2 = {
        "Lorg/xbet/ui_common/circleindicator/a;",
        "",
        "Landroid/animation/Animator;",
        "animatorOut",
        "animatorIn",
        "immediateAnimatorOut",
        "immediateAnimatorIn",
        "<init>",
        "(Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;)V",
        "a",
        "Landroid/animation/Animator;",
        "b",
        "()Landroid/animation/Animator;",
        "c",
        "d",
        "e",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final e:Lorg/xbet/ui_common/circleindicator/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final f:I


# instance fields
.field public final a:Landroid/animation/Animator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroid/animation/Animator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/animation/Animator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/animation/Animator;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/ui_common/circleindicator/a$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/ui_common/circleindicator/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/ui_common/circleindicator/a;->e:Lorg/xbet/ui_common/circleindicator/a$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/ui_common/circleindicator/a;->f:I

    return-void
.end method

.method public constructor <init>(Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/a;->a:Landroid/animation/Animator;

    .line 4
    iput-object p2, p0, Lorg/xbet/ui_common/circleindicator/a;->b:Landroid/animation/Animator;

    .line 5
    iput-object p3, p0, Lorg/xbet/ui_common/circleindicator/a;->c:Landroid/animation/Animator;

    .line 6
    iput-object p4, p0, Lorg/xbet/ui_common/circleindicator/a;->d:Landroid/animation/Animator;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xbet/ui_common/circleindicator/a;-><init>(Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;Landroid/animation/Animator;)V

    return-void
.end method


# virtual methods
.method public final a()Landroid/animation/Animator;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/a;->b:Landroid/animation/Animator;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Landroid/animation/Animator;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/a;->a:Landroid/animation/Animator;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c()Landroid/animation/Animator;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/a;->d:Landroid/animation/Animator;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Landroid/animation/Animator;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/ui_common/circleindicator/a;->c:Landroid/animation/Animator;

    .line 2
    .line 3
    return-object v0
.end method
