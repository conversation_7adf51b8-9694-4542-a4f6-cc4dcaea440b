.class public final synthetic LM01/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;

.field public final synthetic b:LM01/c;

.field public final synthetic c:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonsContainer;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function0;LM01/c;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonsContainer;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LM01/g;->a:Lkotlin/jvm/functions/Function0;

    iput-object p2, p0, LM01/g;->b:LM01/c;

    iput-object p3, p0, LM01/g;->c:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonsContainer;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, LM01/g;->a:Lkotlin/jvm/functions/Function0;

    iget-object v1, p0, LM01/g;->b:LM01/c;

    iget-object v2, p0, LM01/g;->c:Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonsContainer;

    invoke-static {v0, v1, v2}, Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonsContainer;->c(Lkotlin/jvm/functions/Function0;LM01/c;Lorg/xbet/uikit/components/toolbar/base/components/NavigationBarButtonsContainer;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
