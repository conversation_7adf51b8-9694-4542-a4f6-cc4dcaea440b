.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;LSX0/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;->j0:LSX0/c;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
