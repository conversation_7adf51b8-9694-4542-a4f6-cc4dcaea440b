.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

.field public final synthetic c:Z

.field public final synthetic d:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->c:Z

    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->d:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->b:Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->c:Z

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/r;->d:Ljava/lang/String;

    invoke-static {v0, v1, v2, v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$checkForParticipation$1;->a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentCardModel;ZLjava/lang/String;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
