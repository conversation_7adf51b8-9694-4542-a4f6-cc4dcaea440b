.class public final synthetic LNZ0/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/chips/ChipGroup;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/chips/ChipGroup;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/d;->a:Lorg/xbet/uikit/components/chips/ChipGroup;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LNZ0/d;->a:Lorg/xbet/uikit/components/chips/ChipGroup;

    check-cast p1, Lorg/xbet/uikit/components/chips/Chip;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, <PERSON>ja<PERSON>/lang/Boolean;->booleanValue()Z

    move-result p2

    invoke-static {v0, p1, p2}, Lorg/xbet/uikit/components/chips/ChipGroup;->c(Lorg/xbet/uikit/components/chips/ChipGroup;Lorg/xbet/uikit/components/chips/Chip;Z)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
