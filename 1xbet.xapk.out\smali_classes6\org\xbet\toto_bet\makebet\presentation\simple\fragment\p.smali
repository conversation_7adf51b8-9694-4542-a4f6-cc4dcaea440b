.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/p;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;LTZ0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lck/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->n0:Lck/a;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->l0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static d(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;LAX0/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->m0:LAX0/b;

    .line 2
    .line 3
    return-void
.end method

.method public static e(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
