.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnLayoutChangeListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->X3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0008\n\u0002\u0010\u0002\n\u0002\u0008\u0003*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001JW\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u0004H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "org/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b",
        "Landroid/view/View$OnLayoutChangeListener;",
        "Landroid/view/View;",
        "view",
        "",
        "left",
        "top",
        "right",
        "bottom",
        "oldLeft",
        "oldTop",
        "oldRight",
        "oldBottom",
        "",
        "onLayoutChange",
        "(Landroid/view/View;IIIIIIII)V",
        "core-ktx_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onLayoutChange(Landroid/view/View;IIIIIIII)V
    .locals 0

    .line 1
    invoke-virtual {p1, p0}, Landroid/view/View;->removeOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 2
    .line 3
    .line 4
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 5
    .line 6
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)LS91/L;

    .line 7
    .line 8
    .line 9
    move-result-object p2

    .line 10
    iget-object p2, p2, LS91/L;->e:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 11
    .line 12
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 13
    .line 14
    invoke-static {p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)LS91/L;

    .line 15
    .line 16
    .line 17
    move-result-object p3

    .line 18
    iget-object p3, p3, LS91/L;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 19
    .line 20
    invoke-static {p3}, Lorg/xbet/ui_common/utils/ViewExtensionsKt;->m(Landroid/view/View;)Z

    .line 21
    .line 22
    .line 23
    move-result p3

    .line 24
    invoke-virtual {p1, p2, p3}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 25
    .line 26
    .line 27
    return-void
.end method
