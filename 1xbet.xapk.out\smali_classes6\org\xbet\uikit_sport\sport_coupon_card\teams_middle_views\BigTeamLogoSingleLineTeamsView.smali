.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LY31/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u000c\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ7\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u001a\u001a\u00020\r2\u0006\u0010\u0019\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001d\u0010\u001eR\u0014\u0010!\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 R\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$R\u0014\u0010\'\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010$R\u0016\u0010\u001c\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008(\u0010 R\u0016\u0010*\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008)\u0010 \u00a8\u0006+"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;",
        "Landroid/widget/FrameLayout;",
        "LY31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "LX31/b;",
        "teamsUiModel",
        "setTeamsUiModel",
        "(LX31/b;)V",
        "scoreOccupiedWidth",
        "setScoreViewOccupiedWidth",
        "(I)V",
        "a",
        "I",
        "scoreHorizontalMargin",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;",
        "b",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;",
        "firstTeamView",
        "c",
        "secondTeamView",
        "d",
        "e",
        "secondTeamLeftPosition",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:I

.field public final b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:I

.field public e:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 12
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_4:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->a:I

    .line 6
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 7
    new-instance v6, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    const/4 v10, 0x6

    const/4 v11, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    move-object v7, v1

    invoke-direct/range {v6 .. v11}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    const/4 p1, 0x1

    .line 8
    invoke-virtual {v6, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->setReversedOrder(Z)V

    .line 9
    iput-object v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-lez p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-lez p1, :cond_0

    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 14
    .line 15
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    iget-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 20
    .line 21
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 22
    .line 23
    .line 24
    move-result p3

    .line 25
    const/4 p4, 0x0

    .line 26
    invoke-virtual {p1, p4, p4, p2, p3}, Landroid/view/View;->layout(IIII)V

    .line 27
    .line 28
    .line 29
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 30
    .line 31
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->e:I

    .line 32
    .line 33
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 34
    .line 35
    .line 36
    move-result p3

    .line 37
    add-int/2addr p3, p2

    .line 38
    iget-object p5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 39
    .line 40
    invoke-virtual {p5}, Landroid/view/View;->getMeasuredHeight()I

    .line 41
    .line 42
    .line 43
    move-result p5

    .line 44
    invoke-virtual {p1, p2, p4, p3, p5}, Landroid/view/View;->layout(IIII)V

    .line 45
    .line 46
    .line 47
    :cond_0
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->d:I

    .line 6
    .line 7
    sub-int/2addr v0, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->a:I

    .line 9
    .line 10
    mul-int/lit8 v1, v1, 0x2

    .line 11
    .line 12
    sub-int/2addr v0, v1

    .line 13
    div-int/lit8 v0, v0, 0x2

    .line 14
    .line 15
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 16
    .line 17
    const/high16 v2, 0x40000000    # 2.0f

    .line 18
    .line 19
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    invoke-virtual {v1, v3, p2}, Landroid/view/View;->measure(II)V

    .line 24
    .line 25
    .line 26
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 27
    .line 28
    invoke-static {v0, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    invoke-virtual {v1, v0, p2}, Landroid/view/View;->measure(II)V

    .line 33
    .line 34
    .line 35
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 36
    .line 37
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 38
    .line 39
    .line 40
    move-result p2

    .line 41
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 42
    .line 43
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    invoke-static {p2, v0}, Ljava/lang/Math;->max(II)I

    .line 48
    .line 49
    .line 50
    move-result p2

    .line 51
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 52
    .line 53
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->d:I

    .line 58
    .line 59
    add-int/2addr v0, v1

    .line 60
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->a:I

    .line 61
    .line 62
    mul-int/lit8 v1, v1, 0x2

    .line 63
    .line 64
    add-int/2addr v0, v1

    .line 65
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->e:I

    .line 66
    .line 67
    invoke-static {p2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 68
    .line 69
    .line 70
    move-result p2

    .line 71
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 72
    .line 73
    .line 74
    return-void
.end method

.method public final setScoreViewOccupiedWidth(I)V
    .locals 0

    .line 1
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->d:I

    .line 2
    .line 3
    return-void
.end method

.method public setTeamsUiModel(LX31/b;)V
    .locals 3
    .param p1    # LX31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/b$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LX31/b$c;

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    :goto_0
    if-nez p1, :cond_1

    .line 10
    .line 11
    goto :goto_1

    .line 12
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 13
    .line 14
    invoke-virtual {p1}, LX31/b$c;->b()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-virtual {p1}, LX31/b$c;->a()LX31/e;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->setTeam(Ljava/lang/String;LX31/e;)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 26
    .line 27
    invoke-virtual {p1}, LX31/b$c;->d()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {p1}, LX31/b$c;->c()LX31/e;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;->setTeam(Ljava/lang/String;LX31/e;)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 39
    .line 40
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    if-nez p1, :cond_2

    .line 45
    .line 46
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 47
    .line 48
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 49
    .line 50
    .line 51
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 52
    .line 53
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    if-nez p1, :cond_3

    .line 58
    .line 59
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/BigTeamLogoSingleLineTeamsView;->c:Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/SingleTeamNameWithTeamLogosView;

    .line 60
    .line 61
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 62
    .line 63
    .line 64
    :cond_3
    :goto_1
    return-void
.end method
