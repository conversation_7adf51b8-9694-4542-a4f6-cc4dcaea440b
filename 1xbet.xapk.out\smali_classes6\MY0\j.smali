.class public final LMY0/j;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0003\"\u0014\u0010\u0003\u001a\u00020\u00008\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0001\u0010\u0002\"\u0018\u0010\u0007\u001a\u00020\u0005*\u00020\u00048@X\u0080\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0001\u0010\u0006\u00a8\u0006\u0008"
    }
    d2 = {
        "Landroid/graphics/Paint$FontMetrics;",
        "a",
        "Landroid/graphics/Paint$FontMetrics;",
        "fm",
        "Landroid/graphics/Paint;",
        "",
        "(Landroid/graphics/Paint;)F",
        "lineHeight",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final a:Landroid/graphics/Paint$FontMetrics;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Landroid/graphics/Paint$FontMetrics;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/graphics/Paint$FontMetrics;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LMY0/j;->a:Landroid/graphics/Paint$FontMetrics;

    .line 7
    .line 8
    return-void
.end method

.method public static final a(Landroid/graphics/Paint;)F
    .locals 2
    .param p0    # Landroid/graphics/Paint;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    sget-object v0, LMY0/j;->a:Landroid/graphics/Paint$FontMetrics;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroid/graphics/Paint;->getFontMetrics(Landroid/graphics/Paint$FontMetrics;)F

    .line 4
    .line 5
    .line 6
    iget p0, v0, Landroid/graphics/Paint$FontMetrics;->bottom:F

    .line 7
    .line 8
    iget v1, v0, Landroid/graphics/Paint$FontMetrics;->top:F

    .line 9
    .line 10
    sub-float/2addr p0, v1

    .line 11
    iget v0, v0, Landroid/graphics/Paint$FontMetrics;->leading:F

    .line 12
    .line 13
    add-float/2addr p0, v0

    .line 14
    return p0
.end method
