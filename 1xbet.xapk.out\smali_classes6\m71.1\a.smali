.class public final Lm71/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm71/a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\t\u0018\u0000 \u00162\u00020\u0001:\u0001\nB\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0013\u0010\n\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001b\u0010\u000f\u001a\u00020\u000e2\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0008\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0013\u0010\u0011\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u0008\u00a2\u0006\u0004\u0008\u0011\u0010\u000bR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\n\u0010\u0012R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0013R\u001a\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\t0\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0014\u00a8\u0006\u0017"
    }
    d2 = {
        "Lm71/a;",
        "",
        "LRf0/f;",
        "prefs",
        "Lcom/google/gson/Gson;",
        "gson",
        "<init>",
        "(LRf0/f;Lcom/google/gson/Gson;)V",
        "",
        "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
        "a",
        "()Ljava/util/List;",
        "",
        "typeIds",
        "",
        "c",
        "(Ljava/util/List;)V",
        "b",
        "LRf0/f;",
        "Lcom/google/gson/Gson;",
        "Ljava/util/List;",
        "widgetSections",
        "d",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lm71/a$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:LRf0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/google/gson/Gson;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lm71/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lm71/a$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lm71/a;->d:Lm71/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(LRf0/f;Lcom/google/gson/Gson;)V
    .locals 1
    .param p1    # LRf0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/google/gson/Gson;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lm71/a;->a:LRf0/f;

    .line 5
    .line 6
    iput-object p2, p0, Lm71/a;->b:Lcom/google/gson/Gson;

    .line 7
    .line 8
    const/4 p1, 0x6

    .line 9
    new-array p1, p1, [Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 10
    .line 11
    sget-object p2, Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;->LIVE:Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 12
    .line 13
    const/4 v0, 0x0

    .line 14
    aput-object p2, p1, v0

    .line 15
    .line 16
    sget-object p2, Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;->RESULTS:Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 17
    .line 18
    const/4 v0, 0x1

    .line 19
    aput-object p2, p1, v0

    .line 20
    .line 21
    sget-object p2, Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;->CYBER:Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 22
    .line 23
    const/4 v0, 0x2

    .line 24
    aput-object p2, p1, v0

    .line 25
    .line 26
    sget-object p2, Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;->SEARCH:Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 27
    .line 28
    const/4 v0, 0x3

    .line 29
    aput-object p2, p1, v0

    .line 30
    .line 31
    sget-object p2, Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;->AGGREGATOR:Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 32
    .line 33
    const/4 v0, 0x4

    .line 34
    aput-object p2, p1, v0

    .line 35
    .line 36
    sget-object p2, Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;->XGAMES:Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;

    .line 37
    .line 38
    const/4 v0, 0x5

    .line 39
    aput-object p2, p1, v0

    .line 40
    .line 41
    invoke-static {p1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 42
    .line 43
    .line 44
    move-result-object p1

    .line 45
    iput-object p1, p0, Lm71/a;->c:Ljava/util/List;

    .line 46
    .line 47
    return-void
.end method


# virtual methods
.method public final a()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/widget/impl/domain/models/WidgetSectionsType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lm71/a;->c:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    :try_start_0
    iget-object v0, p0, Lm71/a;->a:LRf0/f;

    .line 2
    .line 3
    const-string v1, "saved_section_quick_available"

    .line 4
    .line 5
    const-string v2, ""

    .line 6
    .line 7
    invoke-virtual {v0, v1, v2}, LRf0/f;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v1, p0, Lm71/a;->b:Lcom/google/gson/Gson;

    .line 12
    .line 13
    new-instance v2, Lm71/a$b;

    .line 14
    .line 15
    invoke-direct {v2}, Lm71/a$b;-><init>()V

    .line 16
    .line 17
    .line 18
    invoke-virtual {v2}, Lcom/google/gson/reflect/TypeToken;->e()Ljava/lang/reflect/Type;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    invoke-virtual {v1, v0, v2}, Lcom/google/gson/Gson;->o(Ljava/lang/String;Ljava/lang/reflect/Type;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    check-cast v0, Ljava/util/List;

    .line 27
    .line 28
    if-nez v0, :cond_0

    .line 29
    .line 30
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 34
    :cond_0
    return-object v0

    .line 35
    :catch_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    return-object v0
.end method

.method public final c(Ljava/util/List;)V
    .locals 2
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lm71/a;->b:Lcom/google/gson/Gson;

    .line 2
    .line 3
    new-instance v1, Lm71/a$c;

    .line 4
    .line 5
    invoke-direct {v1}, Lm71/a$c;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v1}, Lcom/google/gson/reflect/TypeToken;->e()Ljava/lang/reflect/Type;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, p1, v1}, Lcom/google/gson/Gson;->y(Ljava/lang/Object;Ljava/lang/reflect/Type;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    iget-object v0, p0, Lm71/a;->a:LRf0/f;

    .line 17
    .line 18
    const-string v1, "saved_section_quick_available"

    .line 19
    .line 20
    invoke-virtual {v0, v1, p1}, LRf0/f;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method
