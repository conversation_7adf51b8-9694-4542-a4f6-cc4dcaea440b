.class public final Lcom/google/android/gms/common/internal/ServiceSpecificExtraArgs;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build Lcom/google/android/gms/common/annotation/KeepForSdk;
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/google/android/gms/common/internal/ServiceSpecificExtraArgs$CastExtraArgs;,
        Lcom/google/android/gms/common/internal/ServiceSpecificExtraArgs$GamesExtraArgs;,
        Lcom/google/android/gms/common/internal/ServiceSpecificExtraArgs$PlusExtraArgs;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
