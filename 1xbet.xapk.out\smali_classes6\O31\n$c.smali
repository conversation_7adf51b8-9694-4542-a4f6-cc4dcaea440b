.class public final LO31/n$c;
.super Landroidx/recyclerview/widget/i$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LO31/n;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroidx/recyclerview/widget/i$f<",
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0004\u0008\u0082\u0003\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u001f\u0010\u0008\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\n\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\n\u0010\t\u00a8\u0006\u000b"
    }
    d2 = {
        "LO31/n$c;",
        "Landroidx/recyclerview/widget/i$f;",
        "Lorg/xbet/uikit_sport/sport_collection/b;",
        "<init>",
        "()V",
        "oldItem",
        "newItem",
        "",
        "e",
        "(Lorg/xbet/uikit_sport/sport_collection/b;Lorg/xbet/uikit_sport/sport_collection/b;)Z",
        "d",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Landroidx/recyclerview/widget/i$f;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LO31/n$c;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/uikit_sport/sport_collection/b;

    .line 2
    .line 3
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/b;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LO31/n$c;->d(Lorg/xbet/uikit_sport/sport_collection/b;Lorg/xbet/uikit_sport/sport_collection/b;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public bridge synthetic b(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/uikit_sport/sport_collection/b;

    .line 2
    .line 3
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/b;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LO31/n$c;->e(Lorg/xbet/uikit_sport/sport_collection/b;Lorg/xbet/uikit_sport/sport_collection/b;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public d(Lorg/xbet/uikit_sport/sport_collection/b;Lorg/xbet/uikit_sport/sport_collection/b;)Z
    .locals 0
    .param p1    # Lorg/xbet/uikit_sport/sport_collection/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_sport/sport_collection/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p1, p2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method

.method public e(Lorg/xbet/uikit_sport/sport_collection/b;Lorg/xbet/uikit_sport/sport_collection/b;)Z
    .locals 5
    .param p1    # Lorg/xbet/uikit_sport/sport_collection/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_sport/sport_collection/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_collection/b;->a()Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/sport_collection/b;->a()Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x1

    .line 11
    if-ne v0, v1, :cond_1

    .line 12
    .line 13
    instance-of v0, p1, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 14
    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    instance-of v0, p2, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 18
    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    check-cast p1, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_collection/b$c;->b()LP31/j;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p1}, LP31/j;->b()J

    .line 28
    .line 29
    .line 30
    move-result-wide v0

    .line 31
    check-cast p2, Lorg/xbet/uikit_sport/sport_collection/b$c;

    .line 32
    .line 33
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/sport_collection/b$c;->b()LP31/j;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-virtual {p1}, LP31/j;->b()J

    .line 38
    .line 39
    .line 40
    move-result-wide p1

    .line 41
    cmp-long v4, v0, p1

    .line 42
    .line 43
    if-nez v4, :cond_0

    .line 44
    .line 45
    return v3

    .line 46
    :cond_0
    return v2

    .line 47
    :cond_1
    invoke-virtual {p1}, Lorg/xbet/uikit_sport/sport_collection/b;->a()Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    invoke-virtual {p2}, Lorg/xbet/uikit_sport/sport_collection/b;->a()Lorg/xbet/uikit_sport/sport_collection/ButtonClickType;

    .line 52
    .line 53
    .line 54
    move-result-object p2

    .line 55
    if-ne p1, p2, :cond_2

    .line 56
    .line 57
    return v3

    .line 58
    :cond_2
    return v2
.end method
