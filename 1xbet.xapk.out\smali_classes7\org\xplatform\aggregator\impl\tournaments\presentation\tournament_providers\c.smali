.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;->b:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;->b:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
