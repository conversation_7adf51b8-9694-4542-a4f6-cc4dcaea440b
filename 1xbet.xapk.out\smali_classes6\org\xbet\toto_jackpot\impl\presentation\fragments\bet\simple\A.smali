.class public final synthetic Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/A;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/A;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/A;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModel;

    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, Ljava/lang/String;

    invoke-static {v0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModel;->r3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/bet/simple/TotoJackpotSimpleBetViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
