.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LCb1/a;",
        "aggregatorPopularItemsClickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "d",
        "(LCb1/a;Ljava/lang/String;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/e;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt;->e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LNb1/b;LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt;->g(LNb1/b;LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt;->f(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final d(LCb1/a;Ljava/lang/String;)LA4/c;
    .locals 3
    .param p0    # LCb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LCb1/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/m;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/m;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/n;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/n;-><init>(LCb1/a;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt$popularClassicSelectionContainerDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt$popularClassicSelectionContainerDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt$popularClassicSelectionContainerDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicSelectionContainerDelegateKt$popularClassicSelectionContainerDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/e;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/e;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/e;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final f(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 16

    .line 1
    move-object/from16 v0, p2

    .line 2
    .line 3
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    check-cast v1, LIb1/e;

    .line 8
    .line 9
    iget-object v1, v1, LIb1/e;->c:Landroidx/recyclerview/widget/RecyclerView;

    .line 10
    .line 11
    invoke-virtual {v0}, LB4/a;->g()Landroid/content/Context;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    sget v3, Lpb/f;->space_8:I

    .line 20
    .line 21
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 22
    .line 23
    .line 24
    move-result v5

    .line 25
    invoke-virtual {v0}, LB4/a;->g()Landroid/content/Context;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    sget v3, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 34
    .line 35
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 36
    .line 37
    .line 38
    move-result v6

    .line 39
    invoke-virtual {v0}, LB4/a;->g()Landroid/content/Context;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    sget v3, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 48
    .line 49
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 50
    .line 51
    .line 52
    move-result v8

    .line 53
    new-instance v4, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 54
    .line 55
    const/16 v14, 0x1d4

    .line 56
    .line 57
    const/4 v15, 0x0

    .line 58
    const/4 v7, 0x0

    .line 59
    const/4 v9, 0x0

    .line 60
    const/4 v10, 0x1

    .line 61
    const/4 v11, 0x0

    .line 62
    const/4 v12, 0x0

    .line 63
    const/4 v13, 0x0

    .line 64
    invoke-direct/range {v4 .. v15}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 65
    .line 66
    .line 67
    invoke-virtual {v1, v4}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 68
    .line 69
    .line 70
    new-instance v1, LNb1/b;

    .line 71
    .line 72
    move-object/from16 v2, p0

    .line 73
    .line 74
    move-object/from16 v3, p1

    .line 75
    .line 76
    invoke-direct {v1, v2, v3}, LNb1/b;-><init>(LCb1/a;Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    invoke-virtual {v0}, LB4/a;->e()LL2/a;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    check-cast v2, LIb1/e;

    .line 84
    .line 85
    iget-object v2, v2, LIb1/e;->c:Landroidx/recyclerview/widget/RecyclerView;

    .line 86
    .line 87
    invoke-virtual {v2, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 88
    .line 89
    .line 90
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/o;

    .line 91
    .line 92
    invoke-direct {v2, v1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/o;-><init>(LNb1/b;LB4/a;)V

    .line 93
    .line 94
    .line 95
    invoke-virtual {v0, v2}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 96
    .line 97
    .line 98
    sget-object v0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 99
    .line 100
    return-object v0
.end method

.method public static final g(LNb1/b;LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lt81/d;

    .line 6
    .line 7
    invoke-virtual {p1}, Lt81/d;->d()Ljava/util/List;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method
