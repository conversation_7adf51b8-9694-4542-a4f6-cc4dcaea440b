.class public final synthetic Lo01/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/pagecontrol/PageControl;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/pagecontrol/PageControl;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo01/a;->a:Lorg/xbet/uikit/components/pagecontrol/PageControl;

    iput p2, p0, Lo01/a;->b:I

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lo01/a;->a:Lorg/xbet/uikit/components/pagecontrol/PageControl;

    iget v1, p0, Lo01/a;->b:I

    invoke-static {v0, v1}, Lorg/xbet/uikit/components/pagecontrol/PageControl;->f(Lorg/xbet/uikit/components/pagecontrol/PageControl;I)V

    return-void
.end method
