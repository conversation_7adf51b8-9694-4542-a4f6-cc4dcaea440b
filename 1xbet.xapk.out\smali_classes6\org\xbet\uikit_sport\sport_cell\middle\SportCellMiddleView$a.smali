.class public final Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0003\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;",
        "",
        "<init>",
        "()V",
        "Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;",
        "type",
        "",
        "b",
        "(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)I",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;-><init>()V

    return-void
.end method

.method public static final synthetic a(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)I
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a;->b(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method


# virtual methods
.method public final b(Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleType;)I
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView$a$a;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    packed-switch p1, :pswitch_data_0

    .line 10
    .line 11
    .line 12
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 13
    .line 14
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 15
    .line 16
    .line 17
    throw p1

    .line 18
    :pswitch_0
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Dynamic_L:I

    .line 19
    .line 20
    return p1

    .line 21
    :pswitch_1
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Dynamic_M:I

    .line 22
    .line 23
    return p1

    .line 24
    :pswitch_2
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Dynamic_S:I

    .line 25
    .line 26
    return p1

    .line 27
    :pswitch_3
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Static_XL:I

    .line 28
    .line 29
    return p1

    .line 30
    :pswitch_4
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Static_L:I

    .line 31
    .line 32
    return p1

    .line 33
    :pswitch_5
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Static_M:I

    .line 34
    .line 35
    return p1

    .line 36
    :pswitch_6
    sget p1, Lm31/f;->Widget_SportCell_SportCellMiddle_Static_S:I

    .line 37
    .line 38
    return p1

    .line 39
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
