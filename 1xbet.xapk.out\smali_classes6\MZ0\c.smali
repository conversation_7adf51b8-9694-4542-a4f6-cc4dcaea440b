.class public final synthetic LMZ0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/widget/CompoundButton$OnCheckedChangeListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/cells/right/CellRightRadioButton;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/cells/right/CellRightRadioButton;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LMZ0/c;->a:Lorg/xbet/uikit/components/cells/right/CellRightRadioButton;

    return-void
.end method


# virtual methods
.method public final onCheckedChanged(Landroid/widget/CompoundButton;Z)V
    .locals 1

    .line 1
    iget-object v0, p0, LMZ0/c;->a:Lorg/xbet/uikit/components/cells/right/CellRightRadioButton;

    invoke-static {v0, p1, p2}, Lorg/xbet/uikit/components/cells/right/CellRightRadioButton;->a(Lorg/xbet/uikit/components/cells/right/CellRightRadioButton;Landroid/widget/CompoundButton;Z)V

    return-void
.end method
