.class public final synthetic LN31/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN31/b;->a:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LN31/b;->a:Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;

    invoke-static {v0}, Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;->b(Lorg/xbet/uikit_sport/sport_collection/SportCollectionItem;)Landroid/view/View;

    move-result-object v0

    return-object v0
.end method
