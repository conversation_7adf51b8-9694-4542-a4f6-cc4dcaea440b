.class public final synthetic Lo01/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/pagecontrol/PageControl;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/pagecontrol/PageControl;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo01/b;->a:Lorg/xbet/uikit/components/pagecontrol/PageControl;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, Lo01/b;->a:Lorg/xbet/uikit/components/pagecontrol/PageControl;

    invoke-static {v0}, Lorg/xbet/uikit/components/pagecontrol/PageControl;->e(Lorg/xbet/uikit/components/pagecontrol/PageControl;)V

    return-void
.end method
