.class public final Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;
.super Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0008\u0008\u0086\u0008\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\u0008\u001a\u00020\u0004H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0010\u0010\u000b\u001a\u00020\nH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001a\u0010\u0010\u001a\u00020\u000f2\u0008\u0010\u000e\u001a\u0004\u0018\u00010\rH\u00d6\u0003\u00a2\u0006\u0004\u0008\u0010\u0010\u0011R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0012\u0010\u0013\u001a\u0004\u0008\u0014\u0010\u0015R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0014\u0010\u0016\u001a\u0004\u0008\u0012\u0010\t\u00a8\u0006\u0017"
    }
    d2 = {
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;",
        "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;",
        "Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
        "jackpotHistory",
        "",
        "currencySymbol",
        "<init>",
        "(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;Ljava/lang/String;)V",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
        "b",
        "()Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;",
        "Ljava/lang/String;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;Ljava/lang/String;)V
    .locals 1
    .param p1    # Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 3
    .line 4
    .line 5
    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    .line 6
    .line 7
    iput-object p2, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->b:Ljava/lang/String;

    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->b:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;

    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    iget-object v3, p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->b:Ljava/lang/String;

    iget-object p1, p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->b:Ljava/lang/String;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    return v2

    :cond_3
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    invoke-virtual {v0}, Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->b:Ljava/lang/String;

    invoke-virtual {v1}, Ljava/lang/String;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 4
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->a:Lorg/xbet/toto_jackpot/impl/domain/model/TotoJackpotHistoryItemModel;

    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$f$c;->b:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "UpdateTirag(jackpotHistory="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", currencySymbol="

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
