.class public final LN81/e$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN81/q$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN81/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LN81/f;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LN81/e$b;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lk8/d;)LN81/q;
    .locals 2

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    new-instance v0, LN81/e$a;

    .line 5
    .line 6
    const/4 v1, 0x0

    .line 7
    invoke-direct {v0, p1, v1}, LN81/e$a;-><init>(Lk8/d;LN81/f;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
