.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/g;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/g;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;->B2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignFragment;Landroid/view/View;)V

    return-void
.end method
