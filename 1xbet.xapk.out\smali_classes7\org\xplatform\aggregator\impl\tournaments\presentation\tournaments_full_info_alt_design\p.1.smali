.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/p;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/p;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/p;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, Ljava/lang/String;

    invoke-static {v0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
