<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <org.xbet.uikit.components.toolbar.base.DSNavigationBarBasic
        android:id="@+id/navigationBarAggregator"
        style="@style/Widgets.BasicNavigationBar.Title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:showShadow="false"
        app:title="@string/providers" />

    <FrameLayout
        android:id="@+id/authButtonsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/uikitBackgroundContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigationBarAggregator">

        <org.xbet.uikit.components.authorizationbuttons.AuthorizationButtons
            android:id="@+id/authButtonsGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_12"
            android:layout_marginVertical="@dimen/space_8"
            android:visibility="gone"
            app:dsAuthorizationButtonLabel="@string/button_login"
            app:dsRegistrationButtonLabel="@string/registration" />
    </FrameLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/authButtonsLayout">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/static_transparent"
            app:elevation="@dimen/elevation_0">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsingToolbarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/static_transparent"
                app:layout_scrollFlags="scroll|exitUntilCollapsed|snap">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <org.xbet.uikit.components.accountselection.AccountSelection
                        android:id="@+id/accountSelection"
                        style="@style/Widget.AccountSelection.Primary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/uikitBackgroundContent"
                        android:paddingBottom="@dimen/space_8"
                        app:topUpButtonText="@string/top_up" />

                    <org.xbet.uikit.components.bannercollection.BannerCollection
                        android:id="@+id/bannerCollection"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:paddingVertical="@dimen/space_8" />

                </LinearLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <org.xbet.uikit_aggregator.aggregatorprovidercardcollection.AggregatorProviderCardCollection
            android:id="@+id/vAggregatorProviderCardCollection"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingBottom="@dimen/space_96"
            android:paddingTop="@dimen/space_8"
            android:paddingHorizontal="@dimen/medium_horizontal_margin_dynamic"
            android:scrollbarStyle="outsideOverlay"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        <org.xbet.uikit.components.lottie_empty.DsLottieEmptyContainer
            android:id="@+id/lottieEmptyView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_0"
            android:visibility="gone"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <View
        android:id="@+id/closeKeyboardArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:elevation="@dimen/elevation_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigationBarAggregator" />

</androidx.constraintlayout.widget.ConstraintLayout>