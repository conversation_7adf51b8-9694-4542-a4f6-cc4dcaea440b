.class public final synthetic Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;

.field public final synthetic b:LUX0/k;

.field public final synthetic c:LVb1/a;

.field public final synthetic d:Ljava/lang/String;

.field public final synthetic e:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;


# direct methods
.method public synthetic constructor <init>(LB4/a;LUX0/k;LVb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->a:LB4/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->b:LUX0/k;

    iput-object p3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->c:LVb1/a;

    iput-object p4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->d:Ljava/lang/String;

    iput-object p5, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->e:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->a:LB4/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->b:LUX0/k;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->c:LVb1/a;

    iget-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->d:Ljava/lang/String;

    iget-object v4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/x;->e:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    move-object v5, p1

    check-cast v5, Ljava/util/List;

    invoke-static/range {v0 .. v5}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularGamesCategoryContainerDelegateKt;->e(LB4/a;LUX0/k;LVb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
