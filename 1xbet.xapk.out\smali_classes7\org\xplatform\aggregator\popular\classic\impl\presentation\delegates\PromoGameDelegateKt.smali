.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a+\u0010\u0007\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00060\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "LCb1/a;",
        "aggregatorPopularItemsClickListener",
        "",
        "screenName",
        "LA4/c;",
        "",
        "LVX0/i;",
        "e",
        "(LCb1/a;Ljava/lang/String;)LA4/c;",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/h;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;->f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LCb1/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;->h(LCb1/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;->g(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;->i(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(LCb1/a;Ljava/lang/String;)LA4/c;
    .locals 3
    .param p0    # LCb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LCb1/a;",
            "Ljava/lang/String;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/C;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/C;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;

    .line 7
    .line 8
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;-><init>(LCb1/a;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt$promoGameDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt$promoGameDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt$promoGameDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt$promoGameDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v2, LB4/b;

    .line 19
    .line 20
    invoke-direct {v2, v0, p0, v1, p1}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v2
.end method

.method public static final f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/h;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/h;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/h;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final g(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;
    .locals 3

    .line 1
    iget-object v0, p2, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit/utils/debounce/Interval;->INTERVAL_500:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 4
    .line 5
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;

    .line 6
    .line 7
    invoke-direct {v2, p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;-><init>(LCb1/a;Ljava/lang/String;LB4/a;)V

    .line 8
    .line 9
    .line 10
    invoke-static {v0, v1, v2}, LN11/f;->m(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;)Landroid/view/View$OnClickListener;

    .line 11
    .line 12
    .line 13
    invoke-virtual {p2}, LB4/a;->e()LL2/a;

    .line 14
    .line 15
    .line 16
    move-result-object p0

    .line 17
    check-cast p0, LIb1/h;

    .line 18
    .line 19
    iget-object p0, p0, LIb1/h;->b:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 20
    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    if-eqz p1, :cond_2

    .line 26
    .line 27
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    sget v1, Lpb/d;->isTablet:I

    .line 36
    .line 37
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getBoolean(I)Z

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    if-eqz v0, :cond_0

    .line 42
    .line 43
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    sget v1, Lpb/f;->size_190:I

    .line 52
    .line 53
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    goto :goto_0

    .line 58
    :cond_0
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 59
    .line 60
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 61
    .line 62
    .line 63
    move-result-object v1

    .line 64
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/g;->y(Landroid/content/Context;)Z

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    if-eqz v0, :cond_1

    .line 69
    .line 70
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    sget v1, Lpb/f;->size_216:I

    .line 79
    .line 80
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 81
    .line 82
    .line 83
    move-result v0

    .line 84
    goto :goto_0

    .line 85
    :cond_1
    invoke-virtual {p2}, LB4/a;->g()Landroid/content/Context;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    sget v1, Lpb/f;->size_250:I

    .line 94
    .line 95
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 96
    .line 97
    .line 98
    move-result v0

    .line 99
    :goto_0
    iput v0, p1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 100
    .line 101
    invoke-virtual {p0, p1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 102
    .line 103
    .line 104
    new-instance p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/F;

    .line 105
    .line 106
    invoke-direct {p0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/F;-><init>(LB4/a;)V

    .line 107
    .line 108
    .line 109
    invoke-virtual {p2, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 110
    .line 111
    .line 112
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 113
    .line 114
    return-object p0

    .line 115
    :cond_2
    new-instance p0, Ljava/lang/NullPointerException;

    .line 116
    .line 117
    const-string p1, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 118
    .line 119
    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 120
    .line 121
    .line 122
    throw p0
.end method

.method public static final h(LCb1/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p2}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    check-cast p2, LCb1/b;

    .line 6
    .line 7
    invoke-interface {p0, p1, p2}, LCb1/a;->V1(Ljava/lang/String;LCb1/b;)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final i(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 12

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LIb1/h;

    .line 6
    .line 7
    sget-object v0, LCX0/l;->a:LCX0/l;

    .line 8
    .line 9
    iget-object v1, p1, LIb1/h;->b:Lcom/google/android/material/imageview/ShapeableImageView;

    .line 10
    .line 11
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    check-cast v2, LCb1/b;

    .line 16
    .line 17
    invoke-virtual {v2}, LCb1/b;->f()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    sget v3, Lpb/g;->ic_aggregator_placeholder:I

    .line 22
    .line 23
    const/4 v4, 0x0

    .line 24
    new-array v6, v4, [LYW0/d;

    .line 25
    .line 26
    const/16 v10, 0xec

    .line 27
    .line 28
    const/4 v11, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    const/4 v8, 0x0

    .line 32
    const/4 v9, 0x0

    .line 33
    invoke-static/range {v0 .. v11}, LCX0/l;->w(LCX0/l;Landroid/widget/ImageView;Ljava/lang/String;IIZ[LYW0/d;LYW0/c;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    iget-object v0, p1, LIb1/h;->d:Landroid/widget/TextView;

    .line 37
    .line 38
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    check-cast v1, LCb1/b;

    .line 43
    .line 44
    invoke-virtual {v1}, LCb1/b;->e()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 49
    .line 50
    .line 51
    iget-object p1, p1, LIb1/h;->c:Landroid/widget/TextView;

    .line 52
    .line 53
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    check-cast v0, LCb1/b;

    .line 58
    .line 59
    invoke-virtual {v0}, LCb1/b;->o()Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    sget-object v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt$a;->a:[I

    .line 64
    .line 65
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    aget v0, v1, v0

    .line 70
    .line 71
    const/4 v1, 0x1

    .line 72
    if-ne v0, v1, :cond_0

    .line 73
    .line 74
    iget-object p0, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 75
    .line 76
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 77
    .line 78
    .line 79
    move-result-object p0

    .line 80
    sget v0, Lpb/k;->slot_game_of_the_week:I

    .line 81
    .line 82
    invoke-virtual {p0, v0}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object p0

    .line 86
    goto :goto_0

    .line 87
    :cond_0
    iget-object p0, p0, Landroidx/recyclerview/widget/RecyclerView$D;->itemView:Landroid/view/View;

    .line 88
    .line 89
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 90
    .line 91
    .line 92
    move-result-object p0

    .line 93
    sget v0, Lpb/k;->livecasino_game_of_the_week:I

    .line 94
    .line 95
    invoke-virtual {p0, v0}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object p0

    .line 99
    :goto_0
    invoke-virtual {p1, p0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 100
    .line 101
    .line 102
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 103
    .line 104
    return-object p0
.end method
