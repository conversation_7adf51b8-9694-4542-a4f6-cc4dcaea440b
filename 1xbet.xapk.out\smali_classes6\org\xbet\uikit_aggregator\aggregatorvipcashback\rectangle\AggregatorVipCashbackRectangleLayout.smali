.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;
.super Landroid/widget/LinearLayout;
.source "SourceFile"

# interfaces
.implements Lg31/j;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Landroid/widget/LinearLayout;",
        "Lg31/j<",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0001\u0018\u00002\u00020\u00012\u0008\u0012\u0004\u0012\u00020\u00030\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\n\u0008\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\u0008\u0008\u0003\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0019\u0010\u000f\u001a\u00020\u000e2\u0008\u0010\r\u001a\u0004\u0018\u00010\u000cH\u0016\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0003H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u000f\u0010\u0016\u001a\u00020\u0015H\u0016\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0019\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0018H\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\u0017\u0010\u001c\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0014R\u0014\u0010 \u001a\u00020\u001d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010$\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010#R\u0016\u0010(\u001a\u00020%8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0016\u0010)\u001a\u00020%8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\'R\u0016\u0010-\u001a\u00020*8\u0002@\u0002X\u0082.\u00a2\u0006\u0006\n\u0004\u0008+\u0010,R\u0016\u00101\u001a\u00020.8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0016\u00103\u001a\u00020.8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00082\u00100R\u0016\u00106\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u00105R*\u0010?\u001a\u0002072\u0006\u00108\u001a\u0002078\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u00089\u0010:\u001a\u0004\u0008;\u0010<\"\u0004\u0008=\u0010>R*\u0010C\u001a\u0002072\u0006\u00108\u001a\u0002078\u0016@VX\u0096\u000e\u00a2\u0006\u0012\n\u0004\u0008@\u0010:\u001a\u0004\u0008A\u0010<\"\u0004\u0008B\u0010>R*\u0010E\u001a\u00020\u000e2\u0006\u00108\u001a\u00020\u000e8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008D\u00105\u001a\u0004\u0008E\u0010F\"\u0004\u0008G\u0010HR\u0014\u0010L\u001a\u00020I8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008J\u0010K\u00a8\u0006M"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;",
        "Landroid/widget/LinearLayout;",
        "Lg31/j;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Landroid/view/MotionEvent;",
        "event",
        "",
        "onInterceptTouchEvent",
        "(Landroid/view/MotionEvent;)Z",
        "state",
        "",
        "setState",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V",
        "Landroid/widget/ProgressBar;",
        "getProgressBar",
        "()Landroid/widget/ProgressBar;",
        "Landroid/widget/TextView;",
        "getCurrentProgressTextView",
        "()Landroid/widget/TextView;",
        "getMaxProgressTextView",
        "d",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;",
        "progressBar",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "b",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "rvLevels",
        "LR11/c;",
        "c",
        "LR11/c;",
        "typeVerticalItemDecoration",
        "typeHorizontalItemDecoration",
        "Lh31/c;",
        "e",
        "Lh31/c;",
        "adapter",
        "",
        "f",
        "F",
        "touchStartX",
        "g",
        "touchStartY",
        "h",
        "Z",
        "isTouchEventScrolling",
        "",
        "value",
        "i",
        "J",
        "getProgress",
        "()J",
        "setProgress",
        "(J)V",
        "progress",
        "j",
        "getMaxProgress",
        "setMaxProgress",
        "maxProgress",
        "k",
        "isVertical",
        "()Z",
        "setVertical",
        "(Z)V",
        "Landroid/view/View;",
        "getView",
        "()Landroid/view/View;",
        "view",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroidx/recyclerview/widget/RecyclerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:LR11/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:LR11/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:Lh31/c;

.field public f:F

.field public g:F

.field public h:Z

.field public i:J

.field public j:J

.field public k:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 18
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move-object/from16 v0, p0

    .line 4
    invoke-direct/range {p0 .. p3}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    const/4 v5, 0x6

    const/4 v6, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-object/from16 v2, p1

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 6
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    move-result v3

    invoke-virtual {v1, v3}, Landroid/view/View;->setId(I)V

    .line 7
    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 8
    new-instance v3, Landroidx/recyclerview/widget/RecyclerView;

    invoke-direct {v3, v2}, Landroidx/recyclerview/widget/RecyclerView;-><init>(Landroid/content/Context;)V

    .line 9
    invoke-virtual {v3, v4}, Landroidx/recyclerview/widget/RecyclerView;->setClipToPadding(Z)V

    .line 10
    new-instance v5, Landroidx/recyclerview/widget/LinearLayoutManager;

    invoke-direct {v5, v2, v4, v4}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;IZ)V

    invoke-virtual {v3, v5}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 11
    iput-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 12
    new-instance v6, LR11/c;

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_8:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v7

    .line 14
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_8:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v8

    const/16 v12, 0x14

    const/4 v13, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    .line 15
    invoke-direct/range {v6 .. v13}, LR11/c;-><init>(IIIIZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v6, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->c:LR11/c;

    .line 16
    new-instance v7, LR11/c;

    .line 17
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_4:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v8

    .line 18
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_4:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v9

    .line 19
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_8:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v11

    const/16 v16, 0xd4

    const/16 v17, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/4 v14, 0x0

    const/4 v15, 0x0

    .line 20
    invoke-direct/range {v7 .. v17}, LR11/c;-><init>(IIIIIILkotlin/jvm/functions/Function1;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v7, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->d:LR11/c;

    const/4 v4, 0x1

    .line 21
    iput-boolean v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->k:Z

    .line 22
    invoke-virtual {v0, v4}, Landroid/view/View;->setClipToOutline(Z)V

    .line 23
    sget v5, LlZ0/h;->rounded_background_16_content:I

    invoke-static {v2, v5}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 24
    invoke-virtual {v0, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    const/4 v2, -0x1

    const/4 v4, -0x2

    .line 25
    invoke-virtual {v0, v3, v2, v4}, Landroid/view/ViewGroup;->addView(Landroid/view/View;II)V

    .line 26
    new-instance v3, Landroid/widget/LinearLayout$LayoutParams;

    invoke-direct {v3, v2, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    .line 27
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v4, LlZ0/g;->space_12:I

    invoke-virtual {v2, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, v3, Landroid/widget/LinearLayout$LayoutParams;->topMargin:I

    .line 28
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v4, LlZ0/g;->space_16:I

    invoke-virtual {v2, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    invoke-virtual {v3, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 29
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v4, LlZ0/g;->space_16:I

    invoke-virtual {v2, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    invoke-virtual {v3, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 30
    invoke-virtual {v0, v1, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 31
    new-instance v1, Lh31/d;

    invoke-direct {v1}, Lh31/d;-><init>()V

    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->c(Landroid/view/View;)V

    return-void
.end method

.method public static synthetic b(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->e(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V

    return-void
.end method

.method public static final c(Landroid/view/View;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    instance-of v0, p0, Landroid/view/View;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    check-cast p0, Landroid/view/View;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 p0, 0x0

    .line 13
    :goto_0
    if-eqz p0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0}, Landroid/view/View;->performClick()Z

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public static final e(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->d(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final d(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 8
    .line 9
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;->a()Ljava/util/List;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    invoke-interface {p1, v1}, Ljava/util/List;->listIterator(I)Ljava/util/ListIterator;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    :cond_0
    invoke-interface {p1}, Ljava/util/ListIterator;->hasPrevious()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    invoke-interface {p1}, Ljava/util/ListIterator;->previous()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    check-cast v1, Lh31/b;

    .line 32
    .line 33
    invoke-virtual {v1}, Lh31/b;->b()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-nez v1, :cond_0

    .line 38
    .line 39
    invoke-interface {p1}, Ljava/util/ListIterator;->nextIndex()I

    .line 40
    .line 41
    .line 42
    move-result p1

    .line 43
    goto :goto_0

    .line 44
    :cond_1
    const/4 p1, -0x1

    .line 45
    :goto_0
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    sget v2, LlZ0/g;->space_12:I

    .line 50
    .line 51
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    invoke-virtual {v0, p1, v1}, Landroidx/recyclerview/widget/LinearLayoutManager;->scrollToPositionWithOffset(II)V

    .line 56
    .line 57
    .line 58
    return-void
.end method

.method public getCurrentProgressTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getCurrentProgressTextView()Landroid/widget/TextView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public getMaxProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->j:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public getMaxProgressTextView()Landroid/widget/TextView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getMaxProgressTextView()Landroid/widget/TextView;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public getProgress()J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->i:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public getProgressBar()Landroid/widget/ProgressBar;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->getProgressBar()Landroid/widget/ProgressBar;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public getView()Landroid/view/View;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    return-object p0
.end method

.method public onInterceptTouchEvent(Landroid/view/MotionEvent;)Z
    .locals 5

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getAction()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    const/4 v1, 0x0

    .line 14
    if-nez v0, :cond_1

    .line 15
    .line 16
    goto :goto_1

    .line 17
    :cond_1
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    if-nez v2, :cond_2

    .line 22
    .line 23
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getX()F

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->f:F

    .line 28
    .line 29
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getY()F

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->g:F

    .line 34
    .line 35
    iput-boolean v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->h:Z

    .line 36
    .line 37
    goto :goto_3

    .line 38
    :cond_2
    :goto_1
    const/4 v2, 0x1

    .line 39
    if-nez v0, :cond_3

    .line 40
    .line 41
    goto :goto_2

    .line 42
    :cond_3
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v3

    .line 46
    const/4 v4, 0x2

    .line 47
    if-ne v3, v4, :cond_4

    .line 48
    .line 49
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getX()F

    .line 50
    .line 51
    .line 52
    move-result v0

    .line 53
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->f:F

    .line 54
    .line 55
    sub-float/2addr v0, v3

    .line 56
    invoke-virtual {p1}, Landroid/view/MotionEvent;->getY()F

    .line 57
    .line 58
    .line 59
    move-result p1

    .line 60
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->g:F

    .line 61
    .line 62
    sub-float/2addr p1, v3

    .line 63
    invoke-static {p1}, Ljava/lang/Math;->abs(F)F

    .line 64
    .line 65
    .line 66
    move-result p1

    .line 67
    invoke-static {v0}, Ljava/lang/Math;->abs(F)F

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    cmpl-float p1, p1, v0

    .line 72
    .line 73
    if-lez p1, :cond_6

    .line 74
    .line 75
    iput-boolean v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->h:Z

    .line 76
    .line 77
    goto :goto_3

    .line 78
    :cond_4
    :goto_2
    if-nez v0, :cond_5

    .line 79
    .line 80
    goto :goto_3

    .line 81
    :cond_5
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 82
    .line 83
    .line 84
    move-result p1

    .line 85
    if-ne p1, v2, :cond_6

    .line 86
    .line 87
    iget-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->h:Z

    .line 88
    .line 89
    if-nez p1, :cond_6

    .line 90
    .line 91
    invoke-virtual {p0}, Landroid/view/View;->performClick()Z

    .line 92
    .line 93
    .line 94
    return v2

    .line 95
    :cond_6
    :goto_3
    return v1
.end method

.method public setMaxProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->j:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->setMaxProgress(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public setProgress(J)V
    .locals 1

    .line 1
    iput-wide p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->i:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->a:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackProgressBar;->setProgress(J)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V
    .locals 3
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->e:Lh31/c;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    :cond_0
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;->a()Ljava/util/List;

    move-result-object v1

    new-instance v2, Lh31/e;

    invoke-direct {v2, p0, p1}, Lh31/e;-><init>(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V

    invoke-virtual {v0, v1, v2}, Landroidx/recyclerview/widget/s;->r(Ljava/util/List;Ljava/lang/Runnable;)V

    .line 3
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;->c()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setProgress(J)V

    .line 4
    invoke-virtual {p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;->b()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setMaxProgress(J)V

    return-void
.end method

.method public bridge synthetic setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c;)V
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->setState(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/c$b;)V

    return-void
.end method

.method public final setVertical(Z)V
    .locals 2

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->k:Z

    .line 2
    .line 3
    new-instance v0, Lh31/c;

    .line 4
    .line 5
    invoke-direct {v0, p1}, Lh31/c;-><init>(Z)V

    .line 6
    .line 7
    .line 8
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->e:Lh31/c;

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 11
    .line 12
    invoke-virtual {v1, v0}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 16
    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->c:LR11/c;

    .line 18
    .line 19
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->removeItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 23
    .line 24
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->d:LR11/c;

    .line 25
    .line 26
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->removeItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 27
    .line 28
    .line 29
    if-eqz p1, :cond_0

    .line 30
    .line 31
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->c:LR11/c;

    .line 34
    .line 35
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 36
    .line 37
    .line 38
    return-void

    .line 39
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 40
    .line 41
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/rectangle/AggregatorVipCashbackRectangleLayout;->d:LR11/c;

    .line 42
    .line 43
    invoke-virtual {p1, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 44
    .line 45
    .line 46
    return-void
.end method
