.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00b2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0001\u0018\u0000 r2\u00020\u0001:\u0001sB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u0011H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0003J\u0017\u0010\u0016\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u0018H\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u0017\u0010\u001f\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010#\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020!H\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u0017\u0010\'\u001a\u00020\u00062\u0006\u0010&\u001a\u00020%H\u0002\u00a2\u0006\u0004\u0008\'\u0010(J\u000f\u0010)\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008)\u0010\u0003J\u0017\u0010,\u001a\u00020\u00062\u0006\u0010+\u001a\u00020*H\u0002\u00a2\u0006\u0004\u0008,\u0010-J\u000f\u0010.\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008.\u0010\u0003J\u000f\u0010/\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008/\u0010\u0003J\u000f\u00100\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00080\u0010\u0003J\u0019\u00103\u001a\u00020\u00062\u0008\u00102\u001a\u0004\u0018\u000101H\u0014\u00a2\u0006\u0004\u00083\u00104J\u000f\u00105\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00085\u0010\u0003J\u000f\u00106\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00086\u0010\u0003J\u000f\u00107\u001a\u00020\u0006H\u0016\u00a2\u0006\u0004\u00087\u0010\u0003J\u000f\u00108\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00088\u0010\u0003J\u000f\u00109\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u00089\u0010\u0003R\u001b\u0010?\u001a\u00020:8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008;\u0010<\u001a\u0004\u0008=\u0010>R\"\u0010G\u001a\u00020@8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008A\u0010B\u001a\u0004\u0008C\u0010D\"\u0004\u0008E\u0010FR\"\u0010O\u001a\u00020H8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008I\u0010J\u001a\u0004\u0008K\u0010L\"\u0004\u0008M\u0010NR\"\u0010W\u001a\u00020P8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008Q\u0010R\u001a\u0004\u0008S\u0010T\"\u0004\u0008U\u0010VR\"\u0010_\u001a\u00020X8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008Y\u0010Z\u001a\u0004\u0008[\u0010\\\"\u0004\u0008]\u0010^R\"\u0010g\u001a\u00020`8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008a\u0010b\u001a\u0004\u0008c\u0010d\"\u0004\u0008e\u0010fR\u001e\u0010k\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010h8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u001b\u0010q\u001a\u00020l8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008m\u0010n\u001a\u0004\u0008o\u0010p\u00a8\u0006t"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "LaV0/a;",
        "balanceUiState",
        "",
        "R2",
        "(LaV0/a;)V",
        "LaV0/b;",
        "state",
        "S2",
        "(LaV0/b;)V",
        "LZU0/a;",
        "action",
        "Q2",
        "(LZU0/a;)V",
        "LZU0/a$f;",
        "i3",
        "(LZU0/a$f;)V",
        "c3",
        "LZU0/a$b;",
        "e3",
        "(LZU0/a$b;)V",
        "LZU0/a$c;",
        "f3",
        "(LZU0/a$c;)V",
        "LZU0/a$e;",
        "h3",
        "(LZU0/a$e;)V",
        "LZU0/a$d;",
        "g3",
        "(LZU0/a$d;)V",
        "LIU0/a;",
        "totoTaxStatusModel",
        "T2",
        "(LIU0/a;)V",
        "",
        "message",
        "j3",
        "(Ljava/lang/String;)V",
        "U2",
        "",
        "changeBalanceAvailable",
        "Z2",
        "(Z)V",
        "b3",
        "d3",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "s2",
        "onResume",
        "onDestroyView",
        "v2",
        "x2",
        "LOU0/h;",
        "i0",
        "LRc/c;",
        "K2",
        "()LOU0/h;",
        "binding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "j0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "P2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LTZ0/a;",
        "k0",
        "LTZ0/a;",
        "J2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "LzX0/k;",
        "l0",
        "LzX0/k;",
        "M2",
        "()LzX0/k;",
        "setSnackbarManager",
        "(LzX0/k;)V",
        "snackbarManager",
        "Lck/a;",
        "m0",
        "Lck/a;",
        "L2",
        "()Lck/a;",
        "setChangeBalanceDialogProvider",
        "(Lck/a;)V",
        "changeBalanceDialogProvider",
        "LAX0/b;",
        "n0",
        "LAX0/b;",
        "N2",
        "()LAX0/b;",
        "setSuccessBetAlertManager",
        "(LAX0/b;)V",
        "successBetAlertManager",
        "Lkotlin/Function0;",
        "o0",
        "Lkotlin/jvm/functions/Function0;",
        "balanceCallback",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;",
        "b1",
        "Lkotlin/j;",
        "O2",
        "()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;",
        "viewModel",
        "k1",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final k1:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic v1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final x1:I


# instance fields
.field public final b1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public k0:LTZ0/a;

.field public l0:LzX0/k;

.field public m0:Lck/a;

.field public n0:LAX0/b;

.field public o0:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-string v1, "getBinding()Lorg/xbet/toto_bet/impl/databinding/FragmentSimpleDsBetTotoBetBinding;"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const-class v3, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    .line 7
    .line 8
    const-string v4, "binding"

    .line 9
    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    const/4 v1, 0x1

    .line 18
    new-array v1, v1, [Lkotlin/reflect/m;

    .line 19
    .line 20
    aput-object v0, v1, v2

    .line 21
    .line 22
    sput-object v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->v1:[Lkotlin/reflect/m;

    .line 23
    .line 24
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$a;

    .line 25
    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->k1:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$a;

    .line 31
    .line 32
    const/16 v0, 0x8

    .line 33
    .line 34
    sput v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->x1:I

    .line 35
    .line 36
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, LNU0/b;->fragment_simple_ds_bet_toto_bet:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$binding$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$binding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/d;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/d;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 17
    .line 18
    .line 19
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$1;

    .line 20
    .line 21
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 22
    .line 23
    .line 24
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 25
    .line 26
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$2;

    .line 27
    .line 28
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    const-class v2, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    invoke-direct {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$4;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->b1:Lkotlin/j;

    .line 57
    .line 58
    return-void
.end method

.method public static synthetic A2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->k3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(ZLorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->a3(ZLorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic C2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->V2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic D2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->W2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic E2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)LOU0/h;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic F2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LZU0/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->Q2(LZU0/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LaV0/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->R2(LaV0/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic H2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LaV0/b;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->S2(LaV0/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic I2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LIU0/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->T2(LIU0/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final U2()V
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/e;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 4
    .line 5
    .line 6
    const-string v1, "REQUEST_INSUFFICIENT_FOUNDS_DIALOG_KEY"

    .line 7
    .line 8
    invoke-static {p0, v1, v0}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 9
    .line 10
    .line 11
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/f;

    .line 12
    .line 13
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/f;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 14
    .line 15
    .line 16
    invoke-static {p0, v1, v0}, LVZ0/c;->f(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public static final V2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->k4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final W2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 6
    .line 7
    const/4 v0, 0x1

    .line 8
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setMakeBetButtonEnabled(Z)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final X2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->i4()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final Y2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->o0:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    if-eqz p0, :cond_0

    .line 4
    .line 5
    invoke-interface {p0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public static final a3(ZLorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 13

    .line 1
    if-eqz p0, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    iget-object p0, p0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 8
    .line 9
    invoke-virtual {p0}, Landroid/view/View;->clearFocus()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->L2()Lck/a;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->TEMPORARY:Lorg/xbet/balance/model/BalanceScreenType;

    .line 17
    .line 18
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 19
    .line 20
    .line 21
    move-result-object v5

    .line 22
    const/16 v11, 0x2ce

    .line 23
    .line 24
    const/4 v12, 0x0

    .line 25
    const/4 v2, 0x0

    .line 26
    const/4 v3, 0x0

    .line 27
    const/4 v4, 0x0

    .line 28
    const/4 v6, 0x0

    .line 29
    const/4 v7, 0x0

    .line 30
    const/4 v8, 0x0

    .line 31
    const-string v9, "REQUEST_CHANGE_BALANCE_DIALOG_KEY"

    .line 32
    .line 33
    const/4 v10, 0x0

    .line 34
    invoke-static/range {v0 .. v12}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    goto :goto_0

    .line 38
    :cond_0
    invoke-virtual {p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 39
    .line 40
    .line 41
    move-result-object p0

    .line 42
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->k4()V

    .line 43
    .line 44
    .line 45
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 46
    .line 47
    return-object p0
.end method

.method private final b3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 6
    .line 7
    sget v1, Lpb/k;->change_balance_account:I

    .line 8
    .line 9
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->setChangeBalanceBtnText(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method private final j3(Ljava/lang/String;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->J2()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 8
    .line 9
    sget v3, Lpb/k;->error:I

    .line 10
    .line 11
    invoke-virtual {v0, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    sget v4, Lpb/k;->replenish:I

    .line 16
    .line 17
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    sget v4, Lpb/k;->cancel:I

    .line 22
    .line 23
    invoke-virtual {v0, v4}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v6

    .line 27
    sget-object v13, Lorg/xbet/uikit/components/dialog/AlertType;->WARNING:Lorg/xbet/uikit/components/dialog/AlertType;

    .line 28
    .line 29
    const/16 v15, 0xbd0

    .line 30
    .line 31
    const/16 v16, 0x0

    .line 32
    .line 33
    const/4 v7, 0x0

    .line 34
    const-string v8, "REQUEST_INSUFFICIENT_FOUNDS_DIALOG_KEY"

    .line 35
    .line 36
    const/4 v9, 0x0

    .line 37
    const/4 v10, 0x0

    .line 38
    const/4 v11, 0x0

    .line 39
    const/4 v12, 0x0

    .line 40
    const/4 v14, 0x0

    .line 41
    move-object/from16 v4, p1

    .line 42
    .line 43
    invoke-direct/range {v2 .. v16}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 47
    .line 48
    .line 49
    move-result-object v3

    .line 50
    invoke-virtual {v1, v2, v3}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public static final k3(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->P2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->X2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->Y2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Landroid/view/View;)V

    return-void
.end method


# virtual methods
.method public final J2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final K2()LOU0/h;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->v1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LOU0/h;

    .line 13
    .line 14
    return-object v0
.end method

.method public final L2()Lck/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->m0:Lck/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final M2()LzX0/k;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->l0:LzX0/k;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final N2()LAX0/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->n0:LAX0/b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->b1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final P2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final Q2(LZU0/a;)V
    .locals 1

    .line 1
    instance-of v0, p1, LZU0/a$d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LZU0/a$d;

    .line 6
    .line 7
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->g3(LZU0/a$d;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    instance-of v0, p1, LZU0/a$c;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    check-cast p1, LZU0/a$c;

    .line 16
    .line 17
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->f3(LZU0/a$c;)V

    .line 18
    .line 19
    .line 20
    return-void

    .line 21
    :cond_1
    instance-of v0, p1, LZU0/a$e;

    .line 22
    .line 23
    if-eqz v0, :cond_2

    .line 24
    .line 25
    check-cast p1, LZU0/a$e;

    .line 26
    .line 27
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->h3(LZU0/a$e;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_2
    instance-of v0, p1, LZU0/a$b;

    .line 32
    .line 33
    if-eqz v0, :cond_3

    .line 34
    .line 35
    check-cast p1, LZU0/a$b;

    .line 36
    .line 37
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->e3(LZU0/a$b;)V

    .line 38
    .line 39
    .line 40
    return-void

    .line 41
    :cond_3
    sget-object v0, LZU0/a$a;->a:LZU0/a$a;

    .line 42
    .line 43
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    if-eqz v0, :cond_4

    .line 48
    .line 49
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->c3()V

    .line 50
    .line 51
    .line 52
    return-void

    .line 53
    :cond_4
    instance-of v0, p1, LZU0/a$f;

    .line 54
    .line 55
    if-eqz v0, :cond_5

    .line 56
    .line 57
    check-cast p1, LZU0/a$f;

    .line 58
    .line 59
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->i3(LZU0/a$f;)V

    .line 60
    .line 61
    .line 62
    return-void

    .line 63
    :cond_5
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 64
    .line 65
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 66
    .line 67
    .line 68
    throw p1
.end method

.method public final R2(LaV0/a;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, LaV0/a;->e()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->b3()V

    .line 8
    .line 9
    .line 10
    goto :goto_0

    .line 11
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->d3()V

    .line 12
    .line 13
    .line 14
    :goto_0
    invoke-virtual {p1}, LaV0/a;->e()Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    invoke-virtual {p0, v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->Z2(Z)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p1}, LaV0/a;->d()Z

    .line 22
    .line 23
    .line 24
    move-result v0

    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget-object p1, p1, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 32
    .line 33
    invoke-virtual {p1}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->u()V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v0, v0, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 42
    .line 43
    invoke-virtual {v0}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->v()V

    .line 44
    .line 45
    .line 46
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    iget-object v0, v0, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 51
    .line 52
    invoke-virtual {p1}, LaV0/a;->c()LoZ0/e;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->setModel(LoZ0/e;)V

    .line 57
    .line 58
    .line 59
    return-void
.end method

.method public final S2(LaV0/b;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 6
    .line 7
    invoke-virtual {p1}, LaV0/b;->a()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const/4 v2, 0x0

    .line 12
    const/4 v3, 0x2

    .line 13
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setText$default(Lorg/xbet/ui_common/viewcomponents/views/BetInputView;Ljava/lang/String;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 21
    .line 22
    invoke-virtual {p1}, LaV0/b;->e()Z

    .line 23
    .line 24
    .line 25
    move-result v1

    .line 26
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->d(Z)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LaV0/b;->e()Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-nez v0, :cond_0

    .line 34
    .line 35
    invoke-virtual {p1}, LaV0/b;->b()LvX0/e;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    invoke-virtual {v0, v1}, LvX0/e;->g(Landroid/content/Context;)Landroid/text/Spannable;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    iget-object v1, v1, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 52
    .line 53
    invoke-virtual {v1, v0}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setBetLimitsText(Landroid/text/Spannable;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    iget-object v1, v1, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 61
    .line 62
    invoke-virtual {p1}, LaV0/b;->d()Z

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-virtual {v1, v2, v0}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->L(ZLjava/lang/String;)V

    .line 71
    .line 72
    .line 73
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    iget-object v0, v0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 78
    .line 79
    invoke-virtual {p1}, LaV0/b;->c()Z

    .line 80
    .line 81
    .line 82
    move-result p1

    .line 83
    invoke-virtual {v0, p1}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setMakeBetButtonEnabled(Z)V

    .line 84
    .line 85
    .line 86
    :cond_0
    return-void
.end method

.method public final T2(LIU0/a;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, LIU0/a;->d()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v0, v0, LOU0/h;->c:Lorg/xbet/ui_common/viewcomponents/views/TaxExpandableSpoiler;

    .line 12
    .line 13
    invoke-virtual {v0}, Lorg/xbet/ui_common/viewcomponents/views/TaxExpandableSpoiler;->q()V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iget-object v0, v0, LOU0/h;->c:Lorg/xbet/ui_common/viewcomponents/views/TaxExpandableSpoiler;

    .line 21
    .line 22
    const/4 v1, 0x0

    .line 23
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1}, LIU0/a;->a()Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    if-lez p1, :cond_0

    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iget-object p1, p1, LOU0/h;->c:Lorg/xbet/ui_common/viewcomponents/views/TaxExpandableSpoiler;

    .line 41
    .line 42
    invoke-virtual {p1}, Lorg/xbet/ui_common/viewcomponents/views/TaxExpandableSpoiler;->setEmptyContent()V

    .line 43
    .line 44
    .line 45
    :cond_0
    return-void
.end method

.method public final Z2(Z)V
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;

    .line 2
    .line 3
    invoke-direct {v0, p1, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/a;-><init>(ZLorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 4
    .line 5
    .line 6
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->o0:Lkotlin/jvm/functions/Function0;

    .line 7
    .line 8
    return-void
.end method

.method public final c3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    const/4 v1, 0x1

    .line 16
    invoke-virtual {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 24
    .line 25
    const/4 v1, 0x0

    .line 26
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setMakeBetButtonEnabled(Z)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final d3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 6
    .line 7
    sget v1, Lpb/k;->refill_account:I

    .line 8
    .line 9
    invoke-virtual {p0, v1}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->setChangeBalanceBtnText(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final e3(LZU0/a$b;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p1}, LZU0/a$b;->a()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->j3(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final f3(LZU0/a$c;)V
    .locals 14

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    :goto_0
    if-eqz v0, :cond_1

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    invoke-virtual {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->M2()LzX0/k;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    invoke-virtual {p1}, LZU0/a$c;->a()Ly01/g;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    iget-object v6, p1, LOU0/h;->d:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 32
    .line 33
    const/16 v12, 0x1f4

    .line 34
    .line 35
    const/4 v13, 0x0

    .line 36
    const/4 v5, 0x0

    .line 37
    const/4 v7, 0x0

    .line 38
    const/4 v8, 0x0

    .line 39
    const/4 v9, 0x0

    .line 40
    const/4 v10, 0x0

    .line 41
    const/4 v11, 0x0

    .line 42
    move-object v4, p0

    .line 43
    invoke-static/range {v2 .. v13}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    invoke-virtual {p1}, Lcom/google/android/material/snackbar/BaseTransientBottomBar;->getView()Landroid/view/View;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    sget v1, Lpb/f;->elevation_16:I

    .line 56
    .line 57
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    .line 58
    .line 59
    .line 60
    move-result v0

    .line 61
    invoke-virtual {p1, v0}, Landroid/view/View;->setElevation(F)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public final g3(LZU0/a$d;)V
    .locals 15

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    instance-of v1, v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v2

    .line 14
    :goto_0
    if-eqz v0, :cond_1

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-virtual {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 18
    .line 19
    .line 20
    :cond_1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->M2()LzX0/k;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    new-instance v4, Ly01/g;

    .line 25
    .line 26
    sget-object v5, Ly01/i$c;->a:Ly01/i$c;

    .line 27
    .line 28
    invoke-virtual/range {p1 .. p1}, LZU0/a$d;->a()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v6

    .line 32
    const/16 v11, 0x3c

    .line 33
    .line 34
    const/4 v12, 0x0

    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x0

    .line 38
    const/4 v10, 0x0

    .line 39
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 40
    .line 41
    .line 42
    const/16 v13, 0x1fc

    .line 43
    .line 44
    const/4 v14, 0x0

    .line 45
    const/4 v6, 0x0

    .line 46
    const/4 v8, 0x0

    .line 47
    const/4 v9, 0x0

    .line 48
    const/4 v11, 0x0

    .line 49
    move-object v5, p0

    .line 50
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 51
    .line 52
    .line 53
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    instance-of v1, v0, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 58
    .line 59
    if-eqz v1, :cond_2

    .line 60
    .line 61
    move-object v2, v0

    .line 62
    check-cast v2, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 63
    .line 64
    :cond_2
    if-eqz v2, :cond_3

    .line 65
    .line 66
    invoke-virtual {v2}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 67
    .line 68
    .line 69
    :cond_3
    return-void
.end method

.method public final h3(LZU0/a$e;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 8
    .line 9
    const/4 v3, 0x0

    .line 10
    if-eqz v2, :cond_0

    .line 11
    .line 12
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    move-object v1, v3

    .line 16
    :goto_0
    if-eqz v1, :cond_1

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-virtual {v1, v2}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->C(Z)V

    .line 20
    .line 21
    .line 22
    :cond_1
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->N2()LAX0/b;

    .line 23
    .line 24
    .line 25
    move-result-object v1

    .line 26
    new-instance v4, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;

    .line 27
    .line 28
    sget v2, Lpb/k;->bet_processed_successfully:I

    .line 29
    .line 30
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    invoke-virtual/range {p1 .. p1}, LZU0/a$e;->d()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v6

    .line 38
    sget v2, Lpb/k;->history:I

    .line 39
    .line 40
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v7

    .line 44
    sget v2, Lpb/k;->continue_action:I

    .line 45
    .line 46
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    sget v2, Lpb/k;->bet_sum:I

    .line 51
    .line 52
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v10

    .line 56
    const/16 v13, 0xd0

    .line 57
    .line 58
    const/4 v14, 0x0

    .line 59
    const/4 v9, 0x0

    .line 60
    const/4 v11, 0x0

    .line 61
    const/4 v12, 0x0

    .line 62
    invoke-direct/range {v4 .. v14}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 63
    .line 64
    .line 65
    sget v2, Lpb/k;->toto_name:I

    .line 66
    .line 67
    invoke-virtual {v0, v2}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v2

    .line 71
    invoke-virtual/range {p1 .. p1}, LZU0/a$e;->f()Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v5

    .line 75
    new-instance v6, Ljava/lang/StringBuilder;

    .line 76
    .line 77
    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    .line 78
    .line 79
    .line 80
    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 81
    .line 82
    .line 83
    const-string v2, ": "

    .line 84
    .line 85
    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 86
    .line 87
    .line 88
    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    .line 90
    .line 91
    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v8

    .line 95
    invoke-virtual/range {p1 .. p1}, LZU0/a$e;->e()Ljava/lang/String;

    .line 96
    .line 97
    .line 98
    move-result-object v9

    .line 99
    invoke-virtual/range {p1 .. p1}, LZU0/a$e;->c()Ljava/lang/String;

    .line 100
    .line 101
    .line 102
    move-result-object v12

    .line 103
    sget-object v13, Ll8/j;->a:Ll8/j;

    .line 104
    .line 105
    invoke-virtual/range {p1 .. p1}, LZU0/a$e;->b()D

    .line 106
    .line 107
    .line 108
    move-result-wide v14

    .line 109
    const/16 v17, 0x2

    .line 110
    .line 111
    const/16 v18, 0x0

    .line 112
    .line 113
    const/16 v16, 0x0

    .line 114
    .line 115
    invoke-static/range {v13 .. v18}, Ll8/j;->g(Ll8/j;DLcom/xbet/onexcore/utils/ValueType;ILjava/lang/Object;)Ljava/lang/String;

    .line 116
    .line 117
    .line 118
    move-result-object v11

    .line 119
    invoke-virtual/range {p1 .. p1}, LZU0/a$e;->a()J

    .line 120
    .line 121
    .line 122
    move-result-wide v5

    .line 123
    new-instance v7, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;

    .line 124
    .line 125
    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 126
    .line 127
    .line 128
    move-result-object v15

    .line 129
    const/16 v19, 0x424

    .line 130
    .line 131
    const/16 v20, 0x0

    .line 132
    .line 133
    const/4 v10, 0x0

    .line 134
    const/4 v13, 0x0

    .line 135
    const-string v14, "REQUEST_SUCCESS_BET_KEY"

    .line 136
    .line 137
    const-string v16, "TOTO"

    .line 138
    .line 139
    const/16 v17, 0x0

    .line 140
    .line 141
    invoke-direct/range {v7 .. v20}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;ZLjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 142
    .line 143
    .line 144
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 145
    .line 146
    .line 147
    move-result-object v2

    .line 148
    invoke-virtual {v2}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 149
    .line 150
    .line 151
    move-result-object v2

    .line 152
    invoke-virtual {v1, v4, v7, v2}, LAX0/b;->d(Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;Landroidx/fragment/app/FragmentManager;)V

    .line 153
    .line 154
    .line 155
    invoke-virtual {v0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 156
    .line 157
    .line 158
    move-result-object v1

    .line 159
    instance-of v2, v1, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 160
    .line 161
    if-eqz v2, :cond_2

    .line 162
    .line 163
    move-object v3, v1

    .line 164
    check-cast v3, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 165
    .line 166
    :cond_2
    if-eqz v3, :cond_3

    .line 167
    .line 168
    invoke-virtual {v3}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 169
    .line 170
    .line 171
    :cond_3
    return-void
.end method

.method public final i3(LZU0/a$f;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LOU0/h;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;

    .line 10
    .line 11
    invoke-direct {v1, v0, p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;-><init>(Landroid/view/View;Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LZU0/a$f;)V

    .line 12
    .line 13
    .line 14
    invoke-static {v0, v1}, Landroidx/core/view/N;->a(Landroid/view/View;Ljava/lang/Runnable;)Landroidx/core/view/N;

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public onDestroyView()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->o0:Lkotlin/jvm/functions/Function0;

    .line 3
    .line 4
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onDestroyView()V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public onResume()V
    .locals 0

    .line 1
    invoke-super {p0}, LXW0/a;->onResume()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public s2()V
    .locals 0

    .line 1
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->U2()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    iget-object p1, p1, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 12
    .line 13
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/b;

    .line 14
    .line 15
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setOnMakeBetButtonClickListener(Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    iget-object p1, p1, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 26
    .line 27
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onInitView$2;

    .line 28
    .line 29
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onInitView$2;-><init>(Ljava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->setAddDepositClickListener(Lkotlin/jvm/functions/Function0;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iget-object p1, p1, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 44
    .line 45
    new-instance v0, Lorg/xbet/ui_common/viewcomponents/views/BetInputView$b;

    .line 46
    .line 47
    const/16 v1, 0xd

    .line 48
    .line 49
    const/4 v2, 0x2

    .line 50
    invoke-direct {v0, v1, v2}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView$b;-><init>(II)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setFormatParams(Lorg/xbet/ui_common/viewcomponents/views/BetInputView$b;)V

    .line 54
    .line 55
    .line 56
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    iget-object p1, p1, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 61
    .line 62
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onInitView$3;

    .line 63
    .line 64
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onInitView$3;-><init>(Ljava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setAfterTextChangedListener(Lkotlin/jvm/functions/Function1;)V

    .line 72
    .line 73
    .line 74
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->K2()LOU0/h;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iget-object p1, p1, LOU0/h;->b:Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;

    .line 79
    .line 80
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/c;

    .line 81
    .line 82
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/c;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/viewcomponents/views/MakeBetBalanceViewDs;->setChangeBalanceClickListener(Landroid/view/View$OnClickListener;)V

    .line 86
    .line 87
    .line 88
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-super {p0}, LXW0/a;->u2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    instance-of v1, v0, LQW0/b;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    if-eqz v1, :cond_0

    .line 16
    .line 17
    check-cast v0, LQW0/b;

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    move-object v0, v2

    .line 21
    :goto_0
    const-class v1, LTU0/d;

    .line 22
    .line 23
    if-eqz v0, :cond_3

    .line 24
    .line 25
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    check-cast v0, LBc/a;

    .line 34
    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    check-cast v0, LQW0/a;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    move-object v0, v2

    .line 45
    :goto_1
    instance-of v3, v0, LTU0/d;

    .line 46
    .line 47
    if-nez v3, :cond_2

    .line 48
    .line 49
    goto :goto_2

    .line 50
    :cond_2
    move-object v2, v0

    .line 51
    :goto_2
    check-cast v2, LTU0/d;

    .line 52
    .line 53
    if-eqz v2, :cond_3

    .line 54
    .line 55
    invoke-static {p0}, LQW0/h;->b(Landroidx/fragment/app/Fragment;)LwX0/c;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {v2, v0}, LTU0/d;->a(LwX0/c;)LTU0/c;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-interface {v0, p0}, LTU0/c;->c(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)V

    .line 64
    .line 65
    .line 66
    return-void

    .line 67
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 68
    .line 69
    new-instance v2, Ljava/lang/StringBuilder;

    .line 70
    .line 71
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 72
    .line 73
    .line 74
    const-string v3, "Cannot create dependency "

    .line 75
    .line 76
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw v0
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->Y3()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->a4()Lkotlinx/coroutines/flow/e;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->X3()Lkotlinx/coroutines/flow/f0;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$3;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v5

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v7, 0x0

    .line 108
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 109
    .line 110
    .line 111
    invoke-virtual {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->O2()Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetDsViewModel;->W3()Lkotlinx/coroutines/flow/e;

    .line 116
    .line 117
    .line 118
    move-result-object v8

    .line 119
    new-instance v11, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$4;

    .line 120
    .line 121
    invoke-direct {v11, v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$4;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;Lkotlin/coroutines/e;)V

    .line 122
    .line 123
    .line 124
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 125
    .line 126
    .line 127
    move-result-object v9

    .line 128
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    new-instance v4, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 133
    .line 134
    move-object v7, v4

    .line 135
    invoke-direct/range {v7 .. v12}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 136
    .line 137
    .line 138
    const/4 v5, 0x3

    .line 139
    const/4 v6, 0x0

    .line 140
    const/4 v2, 0x0

    .line 141
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 142
    .line 143
    .line 144
    return-void
.end method

.method public x2()V
    .locals 0

    .line 1
    return-void
.end method
