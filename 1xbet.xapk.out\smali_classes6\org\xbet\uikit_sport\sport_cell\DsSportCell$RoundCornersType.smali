.class public final enum Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/sport_cell/DsSportCell;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x4019
    name = "RoundCornersType"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0010\u0008\n\u0002\u0008\r\u0008\u0086\u0081\u0002\u0018\u0000 \t2\u0008\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\nB\u0011\u0008\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0003\u0010\u0006\u001a\u0004\u0008\u0007\u0010\u0008j\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;",
        "",
        "",
        "value",
        "<init>",
        "(Ljava/lang/String;II)V",
        "I",
        "getValue",
        "()I",
        "Companion",
        "a",
        "ROUND_ZERO",
        "ROUND_8",
        "ROUND_16",
        "ROUND_360",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

.field public static final Companion:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final enum ROUND_16:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

.field public static final enum ROUND_360:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

.field public static final enum ROUND_8:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

.field public static final enum ROUND_ZERO:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;


# instance fields
.field private final value:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 2
    .line 3
    const-string v1, "ROUND_ZERO"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;-><init>(Ljava/lang/String;II)V

    .line 7
    .line 8
    .line 9
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_ZERO:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 10
    .line 11
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 12
    .line 13
    const-string v1, "ROUND_8"

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;-><init>(Ljava/lang/String;II)V

    .line 17
    .line 18
    .line 19
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_8:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 20
    .line 21
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 22
    .line 23
    const-string v1, "ROUND_16"

    .line 24
    .line 25
    const/4 v2, 0x2

    .line 26
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;-><init>(Ljava/lang/String;II)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_16:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 30
    .line 31
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 32
    .line 33
    const-string v1, "ROUND_360"

    .line 34
    .line 35
    const/4 v2, 0x3

    .line 36
    invoke-direct {v0, v1, v2, v2}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;-><init>(Ljava/lang/String;II)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_360:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 40
    .line 41
    invoke-static {}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->a()[Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->$VALUES:[Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 46
    .line 47
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->$ENTRIES:Lkotlin/enums/a;

    .line 52
    .line 53
    new-instance v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;

    .line 54
    .line 55
    const/4 v1, 0x0

    .line 56
    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 57
    .line 58
    .line 59
    sput-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->Companion:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;

    .line 60
    .line 61
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->value:I

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;
    .locals 3

    .line 1
    const/4 v0, 0x4

    new-array v0, v0, [Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_ZERO:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_8:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_16:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_360:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->$VALUES:[Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getValue()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->value:I

    .line 2
    .line 3
    return v0
.end method
