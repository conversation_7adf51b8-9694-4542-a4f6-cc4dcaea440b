.class public final Lo11/a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON><PERSON>/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a\u0019\u0010\u0003\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u00a8\u0006\u0005"
    }
    d2 = {
        "Landroidx/compose/ui/l;",
        "Lp11/c;",
        "config",
        "a",
        "(Landroidx/compose/ui/l;Lp11/c;)Landroidx/compose/ui/l;",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Landroidx/compose/ui/l;Lp11/c;)Landroidx/compose/ui/l;
    .locals 1
    .param p0    # Landroidx/compose/ui/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Lp11/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/uikit/compose/components/skeleton/node/item/DsSkeletonEffectItemNodeElement;

    .line 2
    .line 3
    invoke-direct {v0, p1}, Lorg/xbet/uikit/compose/components/skeleton/node/item/DsSkeletonEffectItemNodeElement;-><init>(Lp11/c;)V

    .line 4
    .line 5
    .line 6
    invoke-interface {p0, v0}, Landroidx/compose/ui/l;->q0(Landroidx/compose/ui/l;)Landroidx/compose/ui/l;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    return-object p0
.end method
