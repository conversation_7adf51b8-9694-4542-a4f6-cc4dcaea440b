.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;,
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0011\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0001\u0018\u0000 >2\u00020\u0001:\u0002?@BA\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0013\u0010\u0014\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0013\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u0012\u00a2\u0006\u0004\u0008\u0017\u0010\u0015J\u0013\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u00130\u0012\u00a2\u0006\u0004\u0008\u0018\u0010\u0015J\u0015\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u0019\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\r\u0010\u001d\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0010\u0010\u001f\u001a\u00020\u001aH\u0082@\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010!\u001a\u00020\u001aH\u0002\u00a2\u0006\u0004\u0008!\u0010\u001eJ\u0017\u0010$\u001a\u00020\u001a2\u0006\u0010#\u001a\u00020\"H\u0002\u00a2\u0006\u0004\u0008$\u0010%R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010)R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008,\u0010-R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u001a\u00107\u001a\u0008\u0012\u0004\u0012\u00020\u0016048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00085\u00106R\u001a\u0010;\u001a\u0008\u0012\u0004\u0012\u00020\u0013088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u0010:R\u001a\u0010=\u001a\u0008\u0012\u0004\u0012\u00020\u0013088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010:\u00a8\u0006A"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/l;",
        "makeBetWithPromoScenario",
        "Lm8/a;",
        "dispatchers",
        "LHX0/e;",
        "resourceManager",
        "Lfk/m;",
        "getPrimaryBalanceUseCase",
        "Lfk/r;",
        "hasUserMultipleBalancesUseCase",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "getSelectedTotoUseCase",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "<init>",
        "(Lorg/xbet/toto_bet/makebet/domain/usecase/l;Lm8/a;LHX0/e;Lfk/m;Lfk/r;Lorg/xbet/toto_bet/makebet/domain/usecase/e;Lorg/xbet/ui_common/utils/internet/a;)V",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "D3",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
        "B3",
        "C3",
        "promoCode",
        "",
        "H3",
        "(Ljava/lang/String;)V",
        "F3",
        "()V",
        "I3",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "J3",
        "Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;",
        "throwable",
        "E3",
        "(Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;)V",
        "v1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/l;",
        "x1",
        "Lm8/a;",
        "y1",
        "LHX0/e;",
        "F1",
        "Lfk/m;",
        "H1",
        "Lfk/r;",
        "I1",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/e;",
        "P1",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "S1",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "actionStreamState",
        "Lkotlinx/coroutines/flow/V;",
        "V1",
        "Lkotlinx/coroutines/flow/V;",
        "promoCodeBetEnableStreamState",
        "b2",
        "helperTitleStream",
        "v2",
        "a",
        "b",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final v2:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final x2:I


# instance fields
.field public final F1:Lfk/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lfk/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lorg/xbet/toto_bet/makebet/domain/usecase/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$b;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->v2:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$b;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->x2:I

    return-void
.end method

.method public constructor <init>(Lorg/xbet/toto_bet/makebet/domain/usecase/l;Lm8/a;LHX0/e;Lfk/m;Lfk/r;Lorg/xbet/toto_bet/makebet/domain/usecase/e;Lorg/xbet/ui_common/utils/internet/a;)V
    .locals 8
    .param p1    # Lorg/xbet/toto_bet/makebet/domain/usecase/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lfk/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lfk/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xbet/toto_bet/makebet/domain/usecase/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->v1:Lorg/xbet/toto_bet/makebet/domain/usecase/l;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->x1:Lm8/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y1:LHX0/e;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->F1:Lfk/m;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->H1:Lfk/r;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->I1:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 17
    .line 18
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 19
    .line 20
    const/4 p2, 0x3

    .line 21
    const/4 p3, 0x0

    .line 22
    const/4 p4, 0x0

    .line 23
    invoke-direct {p1, p3, p4, p2, p4}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 24
    .line 25
    .line 26
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 27
    .line 28
    const-string p1, ""

    .line 29
    .line 30
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->V1:Lkotlinx/coroutines/flow/V;

    .line 35
    .line 36
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/a;

    .line 47
    .line 48
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/a;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)V

    .line 49
    .line 50
    .line 51
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$2;

    .line 52
    .line 53
    invoke-direct {v5, p0, p4}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 54
    .line 55
    .line 56
    const/16 v6, 0xe

    .line 57
    .line 58
    const/4 v7, 0x0

    .line 59
    const/4 v2, 0x0

    .line 60
    const/4 v3, 0x0

    .line 61
    const/4 v4, 0x0

    .line 62
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public static final synthetic A3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->J3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final G3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    instance-of v0, p1, Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;

    .line 6
    .line 7
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->E3(Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;)V

    .line 8
    .line 9
    .line 10
    goto :goto_1

    .line 11
    :cond_0
    instance-of v0, p1, Ljava/net/SocketTimeoutException;

    .line 12
    .line 13
    if-nez v0, :cond_3

    .line 14
    .line 15
    instance-of v0, p1, Ljava/net/UnknownHostException;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 21
    .line 22
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;

    .line 23
    .line 24
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    if-nez p1, :cond_2

    .line 29
    .line 30
    const-string p1, ""

    .line 31
    .line 32
    :cond_2
    invoke-direct {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 36
    .line 37
    .line 38
    goto :goto_1

    .line 39
    :cond_3
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->J3()V

    .line 40
    .line 41
    .line 42
    :goto_1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 43
    .line 44
    return-object p0
.end method

.method public static synthetic p3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->r3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->G3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final r3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;

    .line 4
    .line 5
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    if-nez p1, :cond_0

    .line 10
    .line 11
    const-string p1, ""

    .line 12
    .line 13
    :cond_0
    invoke-direct {v0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;-><init>(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/internet/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->P1:Lorg/xbet/ui_common/utils/internet/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lfk/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->F1:Lfk/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->I1:Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/l;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->v1:Lorg/xbet/toto_bet/makebet/domain/usecase/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->V1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->I3(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final B3()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    const-wide/16 v1, 0x64

    .line 4
    .line 5
    invoke-static {v0, v1, v2}, Lkotlinx/coroutines/flow/g;->z(Lkotlinx/coroutines/flow/e;J)Lkotlinx/coroutines/flow/e;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final C3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final D3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->V1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final E3(Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Lcom/xbet/onexcore/data/network/vnc_xenvelope/ServerVncXenvelopeException;->getErrorResponse()Lh8/a;

    .line 2
    .line 3
    .line 4
    const/4 p1, 0x0

    .line 5
    throw p1
.end method

.method public final F3()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->x1:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/b;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final H3(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->V1:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final I3(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x1

    .line 34
    if-eqz v2, :cond_2

    .line 35
    .line 36
    if-ne v2, v3, :cond_1

    .line 37
    .line 38
    iget-object v0, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->L$0:Ljava/lang/Object;

    .line 39
    .line 40
    check-cast v0, Lkotlinx/coroutines/flow/V;

    .line 41
    .line 42
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->b2:Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->H1:Lfk/r;

    .line 60
    .line 61
    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->L$0:Ljava/lang/Object;

    .line 62
    .line 63
    iput v3, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$setHelperTitleTextStream$1;->label:I

    .line 64
    .line 65
    invoke-interface {v2, v0}, Lfk/r;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object v0

    .line 69
    if-ne v0, v1, :cond_3

    .line 70
    .line 71
    return-object v1

    .line 72
    :cond_3
    move-object v4, v0

    .line 73
    move-object v0, p1

    .line 74
    move-object p1, v4

    .line 75
    :goto_1
    check-cast p1, Ljava/lang/Boolean;

    .line 76
    .line 77
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 78
    .line 79
    .line 80
    move-result p1

    .line 81
    const/4 v1, 0x0

    .line 82
    if-eqz p1, :cond_4

    .line 83
    .line 84
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y1:LHX0/e;

    .line 85
    .line 86
    sget v2, Lpb/k;->ds_bet_promo_description:I

    .line 87
    .line 88
    new-array v1, v1, [Ljava/lang/Object;

    .line 89
    .line 90
    invoke-interface {p1, v2, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 91
    .line 92
    .line 93
    move-result-object p1

    .line 94
    goto :goto_2

    .line 95
    :cond_4
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y1:LHX0/e;

    .line 96
    .line 97
    sget v2, Lpb/k;->bet_promo_description:I

    .line 98
    .line 99
    new-array v1, v1, [Ljava/lang/Object;

    .line 100
    .line 101
    invoke-interface {p1, v2, v1}, LHX0/e;->l(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    :goto_2
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 106
    .line 107
    .line 108
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 109
    .line 110
    return-object p1
.end method

.method public final J3()V
    .locals 11

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->S1:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;

    .line 4
    .line 5
    new-instance v2, Ly01/g;

    .line 6
    .line 7
    sget-object v3, Ly01/i$a;->a:Ly01/i$a;

    .line 8
    .line 9
    iget-object v4, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y1:LHX0/e;

    .line 10
    .line 11
    sget v5, Lpb/k;->no_connection_title_with_hyphen:I

    .line 12
    .line 13
    const/4 v6, 0x0

    .line 14
    new-array v7, v6, [Ljava/lang/Object;

    .line 15
    .line 16
    invoke-interface {v4, v5, v7}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v4

    .line 20
    iget-object v5, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y1:LHX0/e;

    .line 21
    .line 22
    sget v7, Lpb/k;->no_connection_description:I

    .line 23
    .line 24
    new-array v6, v6, [Ljava/lang/Object;

    .line 25
    .line 26
    invoke-interface {v5, v7, v6}, LHX0/e;->a(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object v5

    .line 30
    const/16 v9, 0x38

    .line 31
    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v6, 0x0

    .line 34
    const/4 v7, 0x0

    .line 35
    const/4 v8, 0x0

    .line 36
    invoke-direct/range {v2 .. v10}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 37
    .line 38
    .line 39
    invoke-direct {v1, v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;-><init>(Ly01/g;)V

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    return-void
.end method
