.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;
.super Landroid/widget/ProgressBar;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$a;,
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;,
        Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008,\u0008\u0001\u0018\u0000 Z2\u00020\u0001:\u0002[!B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0003\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ/\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\u0006\u0010\u000c\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u0011H\u0014\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001f\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u001f\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u001a\u001a\u00020\u0006H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ!\u0010\u001d\u001a\u00020\u000e2\u0006\u0010\u0003\u001a\u00020\u00022\u0008\u0010\u0005\u001a\u0004\u0018\u00010\u0004H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010!\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u001fH\u0002\u00a2\u0006\u0004\u0008!\u0010\"R\u0014\u0010%\u001a\u00020#8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010$R\u0014\u0010(\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\'R\u0014\u0010)\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\'R\u0014\u0010+\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010\'R\u0014\u0010/\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010.R\u0016\u00103\u001a\u0002008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u0016\u00105\u001a\u0002008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u00102R\u0016\u00106\u001a\u0002008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u000b\u00102R*\u00109\u001a\u00020#2\u0006\u00107\u001a\u00020#8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00088\u0010$\u001a\u0004\u00089\u0010:\"\u0004\u0008;\u0010<R*\u0010C\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u00068\u0006@FX\u0087\u000e\u00a2\u0006\u0012\n\u0004\u0008=\u0010>\u001a\u0004\u0008?\u0010@\"\u0004\u0008A\u0010BR*\u0010G\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u00068\u0006@FX\u0087\u000e\u00a2\u0006\u0012\n\u0004\u0008D\u0010>\u001a\u0004\u0008E\u0010@\"\u0004\u0008F\u0010BR*\u0010M\u001a\u0002002\u0006\u00107\u001a\u0002008\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008H\u00102\u001a\u0004\u0008I\u0010J\"\u0004\u0008K\u0010LR*\u0010Q\u001a\u0002002\u0006\u00107\u001a\u0002008\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008N\u00102\u001a\u0004\u0008O\u0010J\"\u0004\u0008P\u0010LR*\u0010W\u001a\u00020\u001f2\u0006\u00107\u001a\u00020\u001f8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008R\u0010S\u001a\u0004\u0008T\u0010U\"\u0004\u0008V\u0010\"R\u0014\u0010Y\u001a\u0002008BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\u0008X\u0010J\u00a8\u0006\\"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;",
        "Landroid/widget/ProgressBar;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "w",
        "h",
        "oldw",
        "oldh",
        "",
        "onSizeChanged",
        "(IIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "size",
        "measureSpec",
        "c",
        "(II)I",
        "b",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;",
        "direction",
        "a",
        "(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;)V",
        "",
        "Z",
        "isRtl",
        "Landroid/graphics/Paint;",
        "Landroid/graphics/Paint;",
        "trackPaint",
        "progressPaint",
        "d",
        "shortProgressIndicatorPaint",
        "Landroid/graphics/RectF;",
        "e",
        "Landroid/graphics/RectF;",
        "circularProgressRect",
        "",
        "f",
        "F",
        "centerX",
        "g",
        "centerY",
        "radius",
        "value",
        "i",
        "isProgressRounded",
        "()Z",
        "setProgressRounded",
        "(Z)V",
        "j",
        "I",
        "getProgressColor",
        "()I",
        "setProgressColor",
        "(I)V",
        "progressColor",
        "k",
        "getTrackColor",
        "setTrackColor",
        "trackColor",
        "l",
        "getProgressWidth",
        "()F",
        "setProgressWidth",
        "(F)V",
        "progressWidth",
        "m",
        "getTrackWidth",
        "setTrackWidth",
        "trackWidth",
        "n",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;",
        "getProgressDirection",
        "()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;",
        "setProgressDirection",
        "progressDirection",
        "getCircleLength",
        "circleLength",
        "o",
        "Direction",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final o:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final p:I


# instance fields
.field public final a:Z

.field public final b:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:F

.field public g:F

.field public h:F

.field public i:Z

.field public j:I

.field public k:I

.field public l:F

.field public m:F

.field public n:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->o:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->p:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 4
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/ProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {}, LQ0/a;->c()LQ0/a;

    move-result-object p3

    invoke-virtual {p3}, LQ0/a;->h()Z

    move-result p3

    iput-boolean p3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->a:Z

    .line 6
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    const/4 v1, 0x1

    .line 7
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 8
    sget-object v2, Landroid/graphics/Paint$Style;->STROKE:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    const/4 v3, 0x0

    .line 9
    invoke-virtual {v0, v3}, Landroid/graphics/Paint;->setColor(I)V

    .line 10
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->b:Landroid/graphics/Paint;

    .line 11
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 12
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 13
    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 14
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 15
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0}, Landroid/graphics/Paint;-><init>()V

    .line 16
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 17
    sget-object v2, Landroid/graphics/Paint$Style;->FILL:Landroid/graphics/Paint$Style;

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setStyle(Landroid/graphics/Paint$Style;)V

    .line 18
    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->d:Landroid/graphics/Paint;

    .line 19
    new-instance v0, Landroid/graphics/RectF;

    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->e:Landroid/graphics/RectF;

    .line 20
    iput-boolean v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->i:Z

    .line 21
    sget v0, LlZ0/d;->uikitPrimary:I

    const/4 v1, 0x0

    const/4 v2, 0x2

    invoke-static {p1, v0, v1, v2, v1}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->j:I

    .line 22
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_4:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 23
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_4:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->m:F

    .line 24
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;->CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    iput-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->n:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 25
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->b(Landroid/content/Context;Landroid/util/AttributeSet;)V

    if-eqz p3, :cond_0

    const/high16 p1, -0x40800000    # -1.0f

    goto :goto_0

    :cond_0
    const/high16 p1, 0x3f800000    # 1.0f

    .line 26
    :goto_0
    invoke-virtual {p0, p1}, Landroid/view/View;->setScaleX(F)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getCircleLength()F
    .locals 2

    .line 1
    const v0, 0x40c90fdb

    .line 2
    .line 3
    .line 4
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->h:F

    .line 5
    .line 6
    mul-float v1, v1, v0

    .line 7
    .line 8
    return v1
.end method


# virtual methods
.method public final a(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;)V
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$b;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_1

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-ne p1, v0, :cond_0

    .line 14
    .line 15
    const/high16 p1, -0x40800000    # -1.0f

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 19
    .line 20
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 21
    .line 22
    .line 23
    throw p1

    .line 24
    :cond_1
    const/high16 p1, 0x3f800000    # 1.0f

    .line 25
    .line 26
    :goto_0
    invoke-virtual {p0, p1}, Landroid/view/View;->setScaleX(F)V

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public final b(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 2

    .line 1
    sget-object v0, LS11/h;->AggregatorVipCashbackCircularProgressBar:[I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-virtual {p1, p2, v0, v1, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    sget p2, LS11/h;->AggregatorVipCashbackCircularProgressBar_vipCashbackProgressThickness:I

    .line 9
    .line 10
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 11
    .line 12
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getDimension(IF)F

    .line 13
    .line 14
    .line 15
    move-result p2

    .line 16
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setProgressWidth(F)V

    .line 17
    .line 18
    .line 19
    sget p2, LS11/h;->AggregatorVipCashbackCircularProgressBar_vipCashbackProgressColor:I

    .line 20
    .line 21
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->j:I

    .line 22
    .line 23
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 24
    .line 25
    .line 26
    move-result p2

    .line 27
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setProgressColor(I)V

    .line 28
    .line 29
    .line 30
    sget p2, LS11/h;->AggregatorVipCashbackCircularProgressBar_vipCashbackTrackThickness:I

    .line 31
    .line 32
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->m:F

    .line 33
    .line 34
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getDimension(IF)F

    .line 35
    .line 36
    .line 37
    move-result p2

    .line 38
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setTrackWidth(F)V

    .line 39
    .line 40
    .line 41
    sget p2, LS11/h;->AggregatorVipCashbackCircularProgressBar_vipCashbackTrackColor:I

    .line 42
    .line 43
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->k:I

    .line 44
    .line 45
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getColor(II)I

    .line 46
    .line 47
    .line 48
    move-result p2

    .line 49
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setTrackColor(I)V

    .line 50
    .line 51
    .line 52
    sget p2, LS11/h;->AggregatorVipCashbackCircularProgressBar_vipCashbackIsProgressRounded:I

    .line 53
    .line 54
    iget-boolean v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->i:Z

    .line 55
    .line 56
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    .line 57
    .line 58
    .line 59
    move-result p2

    .line 60
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setProgressRounded(Z)V

    .line 61
    .line 62
    .line 63
    sget p2, LS11/h;->AggregatorVipCashbackCircularProgressBar_vipCashbackProgressDirection:I

    .line 64
    .line 65
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;->CLOCKWISE:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 66
    .line 67
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    invoke-virtual {p1, p2, v0}, Landroid/content/res/TypedArray;->getInt(II)I

    .line 72
    .line 73
    .line 74
    move-result p2

    .line 75
    invoke-static {}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;->getEntries()Lkotlin/enums/a;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    if-ltz p2, :cond_0

    .line 80
    .line 81
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 82
    .line 83
    .line 84
    move-result v1

    .line 85
    if-ge p2, v1, :cond_0

    .line 86
    .line 87
    invoke-interface {v0, p2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object p2

    .line 91
    goto :goto_0

    .line 92
    :cond_0
    iget-object p2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->n:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 93
    .line 94
    :goto_0
    check-cast p2, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 95
    .line 96
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->setProgressDirection(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;)V

    .line 97
    .line 98
    .line 99
    invoke-virtual {p1}, Landroid/content/res/TypedArray;->recycle()V

    .line 100
    .line 101
    .line 102
    return-void
.end method

.method public final c(II)I
    .locals 2

    .line 1
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getMode(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    const/high16 v1, -0x80000000

    .line 10
    .line 11
    if-eq v0, v1, :cond_1

    .line 12
    .line 13
    const/high16 v1, 0x40000000    # 2.0f

    .line 14
    .line 15
    if-eq v0, v1, :cond_0

    .line 16
    .line 17
    return p1

    .line 18
    :cond_0
    return p2

    .line 19
    :cond_1
    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    return p1
.end method

.method public final getProgressColor()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->j:I

    .line 2
    .line 3
    return v0
.end method

.method public final getProgressDirection()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->n:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getProgressWidth()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 2
    .line 3
    return v0
.end method

.method public final getTrackColor()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->k:I

    .line 2
    .line 3
    return v0
.end method

.method public final getTrackWidth()F
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->m:F

    .line 2
    .line 3
    return v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 10
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super/range {p0 .. p1}, Landroid/widget/ProgressBar;->onDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->f:F

    .line 5
    .line 6
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->g:F

    .line 7
    .line 8
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->h:F

    .line 9
    .line 10
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->b:Landroid/graphics/Paint;

    .line 11
    .line 12
    invoke-virtual {p1, v0, v2, v3, v4}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 13
    .line 14
    .line 15
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->getCircleLength()F

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    div-float/2addr v0, v2

    .line 22
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 23
    .line 24
    const/high16 v3, 0x43b40000    # 360.0f

    .line 25
    .line 26
    mul-float v2, v2, v3

    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->getCircleLength()F

    .line 29
    .line 30
    .line 31
    move-result v4

    .line 32
    div-float/2addr v2, v4

    .line 33
    const/high16 v4, 0x40000000    # 2.0f

    .line 34
    .line 35
    div-float/2addr v2, v4

    .line 36
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getProgress()I

    .line 37
    .line 38
    .line 39
    move-result v5

    .line 40
    int-to-float v5, v5

    .line 41
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getMax()I

    .line 42
    .line 43
    .line 44
    move-result v6

    .line 45
    int-to-float v6, v6

    .line 46
    div-float/2addr v5, v6

    .line 47
    const/4 v6, 0x0

    .line 48
    cmpl-float v0, v5, v0

    .line 49
    .line 50
    if-lez v0, :cond_2

    .line 51
    .line 52
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getProgress()I

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    int-to-float v0, v0

    .line 57
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getMax()I

    .line 58
    .line 59
    .line 60
    move-result v4

    .line 61
    int-to-float v4, v4

    .line 62
    div-float/2addr v0, v4

    .line 63
    const/high16 v4, 0x3f800000    # 1.0f

    .line 64
    .line 65
    cmpl-float v0, v0, v4

    .line 66
    .line 67
    if-ltz v0, :cond_0

    .line 68
    .line 69
    :goto_0
    move v0, v2

    .line 70
    goto :goto_1

    .line 71
    :cond_0
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getProgress()I

    .line 72
    .line 73
    .line 74
    move-result v0

    .line 75
    int-to-float v0, v0

    .line 76
    mul-float v0, v0, v3

    .line 77
    .line 78
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getMax()I

    .line 79
    .line 80
    .line 81
    move-result v3

    .line 82
    int-to-float v3, v3

    .line 83
    div-float/2addr v0, v3

    .line 84
    const/4 v3, 0x2

    .line 85
    int-to-float v3, v3

    .line 86
    mul-float v3, v3, v2

    .line 87
    .line 88
    sub-float v3, v0, v3

    .line 89
    .line 90
    goto :goto_0

    .line 91
    :goto_1
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->e:Landroid/graphics/RectF;

    .line 92
    .line 93
    const/high16 v4, 0x43870000    # 270.0f

    .line 94
    .line 95
    add-float/2addr v0, v4

    .line 96
    cmpg-float v4, v3, v6

    .line 97
    .line 98
    if-gez v4, :cond_1

    .line 99
    .line 100
    const/4 v4, 0x0

    .line 101
    goto :goto_2

    .line 102
    :cond_1
    move v4, v3

    .line 103
    :goto_2
    const/4 v5, 0x0

    .line 104
    iget-object v6, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 105
    .line 106
    move-object v1, p1

    .line 107
    move v3, v0

    .line 108
    invoke-virtual/range {v1 .. v6}, Landroid/graphics/Canvas;->drawArc(Landroid/graphics/RectF;FFZLandroid/graphics/Paint;)V

    .line 109
    .line 110
    .line 111
    return-void

    .line 112
    :cond_2
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 113
    .line 114
    .line 115
    move-result v9

    .line 116
    :try_start_0
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->f:F

    .line 117
    .line 118
    invoke-virtual {p1, v0, v6}, Landroid/graphics/Canvas;->translate(FF)V

    .line 119
    .line 120
    .line 121
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->getCircleLength()F

    .line 122
    .line 123
    .line 124
    move-result v0

    .line 125
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getProgress()I

    .line 126
    .line 127
    .line 128
    move-result v2

    .line 129
    int-to-float v2, v2

    .line 130
    invoke-virtual {p0}, Landroid/widget/ProgressBar;->getMax()I

    .line 131
    .line 132
    .line 133
    move-result v3

    .line 134
    int-to-float v3, v3

    .line 135
    div-float/2addr v2, v3

    .line 136
    mul-float v0, v0, v2

    .line 137
    .line 138
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 139
    .line 140
    invoke-virtual {v2}, Landroid/graphics/Paint;->getStrokeWidth()F

    .line 141
    .line 142
    .line 143
    move-result v5

    .line 144
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 145
    .line 146
    invoke-virtual {v2}, Landroid/graphics/Paint;->getStrokeWidth()F

    .line 147
    .line 148
    .line 149
    move-result v2

    .line 150
    div-float v6, v2, v4

    .line 151
    .line 152
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 153
    .line 154
    invoke-virtual {v2}, Landroid/graphics/Paint;->getStrokeWidth()F

    .line 155
    .line 156
    .line 157
    move-result v2

    .line 158
    div-float v7, v2, v4

    .line 159
    .line 160
    iget-object v8, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->d:Landroid/graphics/Paint;

    .line 161
    .line 162
    const/4 v2, 0x0

    .line 163
    const/4 v3, 0x0

    .line 164
    move-object v1, p1

    .line 165
    move v4, v0

    .line 166
    invoke-virtual/range {v1 .. v8}, Landroid/graphics/Canvas;->drawRoundRect(FFFFFFLandroid/graphics/Paint;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 167
    .line 168
    .line 169
    invoke-virtual {p1, v9}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 170
    .line 171
    .line 172
    return-void

    .line 173
    :catchall_0
    move-exception v0

    .line 174
    invoke-virtual {p1, v9}, Landroid/graphics/Canvas;->restoreToCount(I)V

    .line 175
    .line 176
    .line 177
    throw v0
.end method

.method public onMeasure(II)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget v1, LlZ0/g;->size_52:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c(II)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-virtual {p0, v0, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c(II)I

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 27
    .line 28
    .line 29
    move-result p2

    .line 30
    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    int-to-float p1, p1

    .line 35
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->h:F

    .line 36
    .line 37
    return-void
.end method

.method public onSizeChanged(IIII)V
    .locals 6

    .line 1
    int-to-float v0, p1

    .line 2
    const/high16 v1, 0x40000000    # 2.0f

    .line 3
    .line 4
    div-float/2addr v0, v1

    .line 5
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->f:F

    .line 6
    .line 7
    int-to-float v0, p2

    .line 8
    div-float/2addr v0, v1

    .line 9
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->g:F

    .line 10
    .line 11
    invoke-static {p1, p2}, Ljava/lang/Math;->min(II)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    int-to-float v0, v0

    .line 16
    div-float/2addr v0, v1

    .line 17
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->m:F

    .line 18
    .line 19
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 20
    .line 21
    invoke-static {v2, v3}, Ljava/lang/Math;->max(FF)F

    .line 22
    .line 23
    .line 24
    move-result v2

    .line 25
    div-float/2addr v2, v1

    .line 26
    sub-float/2addr v0, v2

    .line 27
    iput v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->h:F

    .line 28
    .line 29
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->e:Landroid/graphics/RectF;

    .line 30
    .line 31
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->f:F

    .line 32
    .line 33
    sub-float v3, v2, v0

    .line 34
    .line 35
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->g:F

    .line 36
    .line 37
    sub-float v5, v4, v0

    .line 38
    .line 39
    add-float/2addr v2, v0

    .line 40
    add-float/2addr v4, v0

    .line 41
    invoke-virtual {v1, v3, v5, v2, v4}, Landroid/graphics/RectF;->set(FFFF)V

    .line 42
    .line 43
    .line 44
    invoke-super {p0, p1, p2, p3, p4}, Landroid/widget/ProgressBar;->onSizeChanged(IIII)V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public final setProgressColor(I)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->j:I

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->d:Landroid/graphics/Paint;

    .line 9
    .line 10
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final setProgressDirection(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->n:Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->a(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar$Direction;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setProgressRounded(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->i:Z

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    sget-object p1, Landroid/graphics/Paint$Cap;->ROUND:Landroid/graphics/Paint$Cap;

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    sget-object p1, Landroid/graphics/Paint$Cap;->BUTT:Landroid/graphics/Paint$Cap;

    .line 11
    .line 12
    :goto_0
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setStrokeCap(Landroid/graphics/Paint$Cap;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public final setProgressWidth(F)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->l:F

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->c:Landroid/graphics/Paint;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setTrackColor(I)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->k:I

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->b:Landroid/graphics/Paint;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setColor(I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setTrackWidth(F)V
    .locals 1

    .line 1
    iput p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->m:F

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashback/progressbar/AggregatorVipCashbackCircularProgressBar;->b:Landroid/graphics/Paint;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/graphics/Paint;->setStrokeWidth(F)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 9
    .line 10
    .line 11
    return-void
.end method
