.class public final synthetic Lm11/F;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lm11/H$h;


# direct methods
.method public synthetic constructor <init>(Lm11/H$h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm11/F;->a:Lm11/H$h;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lm11/F;->a:Lm11/H$h;

    check-cast p1, Landroidx/compose/ui/input/pointer/A;

    check-cast p2, Lb0/f;

    invoke-static {v0, p1, p2}, Lm11/G$a;->a(Lm11/H$h;Landroidx/compose/ui/input/pointer/A;Lb0/f;)<PERSON><PERSON><PERSON>/Unit;

    move-result-object p1

    return-object p1
.end method
