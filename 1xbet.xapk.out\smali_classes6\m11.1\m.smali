.class public final synthetic Lm11/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lm11/H$g;


# direct methods
.method public synthetic constructor <init>(ZLm11/H$g;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lm11/m;->a:Z

    iput-object p2, p0, Lm11/m;->b:Lm11/H$g;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-boolean v0, p0, Lm11/m;->a:Z

    iget-object v1, p0, Lm11/m;->b:Lm11/H$g;

    invoke-static {v0, v1}, Lm11/G;->a(ZLm11/H$g;)L<PERSON><PERSON>/Unit;

    move-result-object v0

    return-object v0
.end method
