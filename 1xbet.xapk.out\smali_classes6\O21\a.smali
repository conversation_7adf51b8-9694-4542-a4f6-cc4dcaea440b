.class public final synthetic LO21/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;

.field public final synthetic b:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$b;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO21/a;->a:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;

    iput-object p2, p0, LO21/a;->b:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$b;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LO21/a;->a:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;

    iget-object v1, p0, LO21/a;->b:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$b;

    invoke-static {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;->e(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$b;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
