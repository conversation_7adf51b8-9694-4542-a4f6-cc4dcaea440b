.class public final LMY0/k;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0004\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0007\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u0005\u001a3\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u0001H\u0000\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u001a\u0013\u0010\t\u001a\u00020\u0000*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\t\u0010\n\u001a\u001b\u0010\r\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u000c\u001a\u00020\u000bH\u0000\u00a2\u0006\u0004\u0008\r\u0010\u000e\u001a#\u0010\u0011\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\u000f\u001a\u00020\u000b2\u0006\u0010\u0010\u001a\u00020\u000bH\u0000\u00a2\u0006\u0004\u0008\u0011\u0010\u0012\u001a\u001b\u0010\u0015\u001a\u00020\u000b*\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u0013H\u0000\u00a2\u0006\u0004\u0008\u0015\u0010\u0016\u001a\u001b\u0010\u0017\u001a\u00020\u000b*\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u0013H\u0000\u00a2\u0006\u0004\u0008\u0017\u0010\u0016\u00a8\u0006\u0018"
    }
    d2 = {
        "Landroid/graphics/RectF;",
        "",
        "left",
        "top",
        "right",
        "bottom",
        "",
        "e",
        "(Landroid/graphics/RectF;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V",
        "a",
        "(Landroid/graphics/RectF;)Landroid/graphics/RectF;",
        "",
        "degrees",
        "d",
        "(Landroid/graphics/RectF;F)Landroid/graphics/RectF;",
        "x",
        "y",
        "f",
        "(Landroid/graphics/RectF;FF)Landroid/graphics/RectF;",
        "",
        "isLtr",
        "c",
        "(Landroid/graphics/RectF;Z)F",
        "b",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Landroid/graphics/RectF;)Landroid/graphics/RectF;
    .locals 1
    .param p0    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Landroid/graphics/RectF;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroid/graphics/RectF;-><init>(Landroid/graphics/RectF;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final b(Landroid/graphics/RectF;Z)F
    .locals 0
    .param p0    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget p0, p0, Landroid/graphics/RectF;->right:F

    .line 4
    .line 5
    return p0

    .line 6
    :cond_0
    iget p0, p0, Landroid/graphics/RectF;->left:F

    .line 7
    .line 8
    return p0
.end method

.method public static final c(Landroid/graphics/RectF;Z)F
    .locals 0
    .param p0    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget p0, p0, Landroid/graphics/RectF;->left:F

    .line 4
    .line 5
    return p0

    .line 6
    :cond_0
    iget p0, p0, Landroid/graphics/RectF;->right:F

    .line 7
    .line 8
    return p0
.end method

.method public static final d(Landroid/graphics/RectF;F)Landroid/graphics/RectF;
    .locals 9
    .param p0    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/high16 v0, 0x43340000    # 180.0f

    .line 2
    .line 3
    rem-float v0, p1, v0

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    cmpg-float v0, v0, v1

    .line 7
    .line 8
    if-nez v0, :cond_0

    .line 9
    .line 10
    return-object p0

    .line 11
    :cond_0
    const/high16 v0, 0x42b40000    # 90.0f

    .line 12
    .line 13
    rem-float v0, p1, v0

    .line 14
    .line 15
    const/4 v2, 0x2

    .line 16
    cmpg-float v0, v0, v1

    .line 17
    .line 18
    if-nez v0, :cond_2

    .line 19
    .line 20
    invoke-virtual {p0}, Landroid/graphics/RectF;->width()F

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    invoke-virtual {p0}, Landroid/graphics/RectF;->height()F

    .line 25
    .line 26
    .line 27
    move-result v0

    .line 28
    cmpg-float p1, p1, v0

    .line 29
    .line 30
    if-nez p1, :cond_1

    .line 31
    .line 32
    return-object p0

    .line 33
    :cond_1
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerX()F

    .line 34
    .line 35
    .line 36
    move-result p1

    .line 37
    invoke-virtual {p0}, Landroid/graphics/RectF;->height()F

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    int-to-float v1, v2

    .line 42
    div-float/2addr v0, v1

    .line 43
    sub-float/2addr p1, v0

    .line 44
    invoke-static {p1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerY()F

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    invoke-virtual {p0}, Landroid/graphics/RectF;->width()F

    .line 53
    .line 54
    .line 55
    move-result v2

    .line 56
    div-float/2addr v2, v1

    .line 57
    sub-float/2addr v0, v2

    .line 58
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerX()F

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    invoke-virtual {p0}, Landroid/graphics/RectF;->height()F

    .line 67
    .line 68
    .line 69
    move-result v3

    .line 70
    div-float/2addr v3, v1

    .line 71
    add-float/2addr v2, v3

    .line 72
    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 73
    .line 74
    .line 75
    move-result-object v2

    .line 76
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerY()F

    .line 77
    .line 78
    .line 79
    move-result v3

    .line 80
    invoke-virtual {p0}, Landroid/graphics/RectF;->width()F

    .line 81
    .line 82
    .line 83
    move-result v4

    .line 84
    div-float/2addr v4, v1

    .line 85
    add-float/2addr v3, v4

    .line 86
    invoke-static {v3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-static {p0, p1, v0, v2, v1}, LMY0/k;->e(Landroid/graphics/RectF;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 91
    .line 92
    .line 93
    return-object p0

    .line 94
    :cond_2
    float-to-double v0, p1

    .line 95
    invoke-static {v0, v1}, Ljava/lang/Math;->toRadians(D)D

    .line 96
    .line 97
    .line 98
    move-result-wide v0

    .line 99
    invoke-static {v0, v1}, Ljava/lang/Math;->sin(D)D

    .line 100
    .line 101
    .line 102
    move-result-wide v3

    .line 103
    invoke-static {v0, v1}, Ljava/lang/Math;->cos(D)D

    .line 104
    .line 105
    .line 106
    move-result-wide v0

    .line 107
    invoke-virtual {p0}, Landroid/graphics/RectF;->width()F

    .line 108
    .line 109
    .line 110
    move-result p1

    .line 111
    float-to-double v5, p1

    .line 112
    mul-double v5, v5, v0

    .line 113
    .line 114
    invoke-static {v5, v6}, Ljava/lang/Math;->abs(D)D

    .line 115
    .line 116
    .line 117
    move-result-wide v5

    .line 118
    invoke-virtual {p0}, Landroid/graphics/RectF;->height()F

    .line 119
    .line 120
    .line 121
    move-result p1

    .line 122
    float-to-double v7, p1

    .line 123
    mul-double v7, v7, v3

    .line 124
    .line 125
    invoke-static {v7, v8}, Ljava/lang/Math;->abs(D)D

    .line 126
    .line 127
    .line 128
    move-result-wide v7

    .line 129
    add-double/2addr v5, v7

    .line 130
    invoke-virtual {p0}, Landroid/graphics/RectF;->width()F

    .line 131
    .line 132
    .line 133
    move-result p1

    .line 134
    float-to-double v7, p1

    .line 135
    mul-double v7, v7, v3

    .line 136
    .line 137
    invoke-static {v7, v8}, Ljava/lang/Math;->abs(D)D

    .line 138
    .line 139
    .line 140
    move-result-wide v3

    .line 141
    invoke-virtual {p0}, Landroid/graphics/RectF;->height()F

    .line 142
    .line 143
    .line 144
    move-result p1

    .line 145
    float-to-double v7, p1

    .line 146
    mul-double v7, v7, v0

    .line 147
    .line 148
    invoke-static {v7, v8}, Ljava/lang/Math;->abs(D)D

    .line 149
    .line 150
    .line 151
    move-result-wide v0

    .line 152
    add-double/2addr v3, v0

    .line 153
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerX()F

    .line 154
    .line 155
    .line 156
    move-result p1

    .line 157
    float-to-double v0, p1

    .line 158
    int-to-double v7, v2

    .line 159
    div-double/2addr v5, v7

    .line 160
    sub-double/2addr v0, v5

    .line 161
    invoke-static {v0, v1}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 162
    .line 163
    .line 164
    move-result-object p1

    .line 165
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerY()F

    .line 166
    .line 167
    .line 168
    move-result v0

    .line 169
    float-to-double v0, v0

    .line 170
    div-double/2addr v3, v7

    .line 171
    sub-double/2addr v0, v3

    .line 172
    invoke-static {v0, v1}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 173
    .line 174
    .line 175
    move-result-object v0

    .line 176
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerX()F

    .line 177
    .line 178
    .line 179
    move-result v1

    .line 180
    float-to-double v1, v1

    .line 181
    add-double/2addr v1, v5

    .line 182
    invoke-static {v1, v2}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    invoke-virtual {p0}, Landroid/graphics/RectF;->centerY()F

    .line 187
    .line 188
    .line 189
    move-result v2

    .line 190
    float-to-double v5, v2

    .line 191
    add-double/2addr v5, v3

    .line 192
    invoke-static {v5, v6}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 193
    .line 194
    .line 195
    move-result-object v2

    .line 196
    invoke-static {p0, p1, v0, v1, v2}, LMY0/k;->e(Landroid/graphics/RectF;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 197
    .line 198
    .line 199
    return-object p0
.end method

.method public static final e(Landroid/graphics/RectF;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V
    .locals 0
    .param p0    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p1    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, Ljava/lang/Number;->floatValue()F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-virtual {p2}, Ljava/lang/Number;->floatValue()F

    .line 6
    .line 7
    .line 8
    move-result p2

    .line 9
    invoke-virtual {p3}, Ljava/lang/Number;->floatValue()F

    .line 10
    .line 11
    .line 12
    move-result p3

    .line 13
    invoke-virtual {p4}, Ljava/lang/Number;->floatValue()F

    .line 14
    .line 15
    .line 16
    move-result p4

    .line 17
    invoke-virtual {p0, p1, p2, p3, p4}, Landroid/graphics/RectF;->set(FFFF)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public static final f(Landroid/graphics/RectF;FF)Landroid/graphics/RectF;
    .locals 1
    .param p0    # Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget v0, p0, Landroid/graphics/RectF;->left:F

    .line 2
    .line 3
    add-float/2addr v0, p1

    .line 4
    iput v0, p0, Landroid/graphics/RectF;->left:F

    .line 5
    .line 6
    iget v0, p0, Landroid/graphics/RectF;->top:F

    .line 7
    .line 8
    add-float/2addr v0, p2

    .line 9
    iput v0, p0, Landroid/graphics/RectF;->top:F

    .line 10
    .line 11
    iget v0, p0, Landroid/graphics/RectF;->right:F

    .line 12
    .line 13
    add-float/2addr v0, p1

    .line 14
    iput v0, p0, Landroid/graphics/RectF;->right:F

    .line 15
    .line 16
    iget p1, p0, Landroid/graphics/RectF;->bottom:F

    .line 17
    .line 18
    add-float/2addr p1, p2

    .line 19
    iput p1, p0, Landroid/graphics/RectF;->bottom:F

    .line 20
    .line 21
    return-object p0
.end method
