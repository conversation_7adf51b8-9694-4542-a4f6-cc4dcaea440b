.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournament_stages.TournamentStagesViewModel$requestInitialData$2"
    f = "TournamentStagesViewModel.kt"
    l = {
        0xcd
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->I3(JZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Ljava/lang/Throwable;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0003\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n"
    }
    d2 = {
        "<anonymous>",
        "",
        "it",
        ""
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->invoke(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 19
    .line 20
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 21
    .line 22
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw v1

    .line 26
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-object v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    .line 30
    .line 31
    invoke-static {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 32
    .line 33
    .line 34
    move-result-object v2

    .line 35
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$c$b;

    .line 36
    .line 37
    iget-object v5, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;

    .line 38
    .line 39
    invoke-static {v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;->s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel;)LSX0/c;

    .line 40
    .line 41
    .line 42
    move-result-object v6

    .line 43
    sget-object v7, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 44
    .line 45
    sget v12, Lpb/k;->data_retrieval_error:I

    .line 46
    .line 47
    const/16 v16, 0x1de

    .line 48
    .line 49
    const/16 v17, 0x0

    .line 50
    .line 51
    const/4 v8, 0x0

    .line 52
    const/4 v9, 0x0

    .line 53
    const/4 v10, 0x0

    .line 54
    const/4 v11, 0x0

    .line 55
    const/4 v13, 0x0

    .line 56
    const/4 v14, 0x0

    .line 57
    const/4 v15, 0x0

    .line 58
    invoke-static/range {v6 .. v17}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 59
    .line 60
    .line 61
    move-result-object v5

    .line 62
    invoke-direct {v4, v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$c$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 63
    .line 64
    .line 65
    iput v3, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages/TournamentStagesViewModel$requestInitialData$2;->label:I

    .line 66
    .line 67
    invoke-interface {v2, v4, v0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object v2

    .line 71
    if-ne v2, v1, :cond_2

    .line 72
    .line 73
    return-object v1

    .line 74
    :cond_2
    :goto_0
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 75
    .line 76
    return-object v1
.end method
