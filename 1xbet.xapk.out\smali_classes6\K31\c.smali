.class public final synthetic LK31/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Landroid/content/Context;

.field public final synthetic b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;


# direct methods
.method public synthetic constructor <init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LK31/c;->a:Landroid/content/Context;

    iput-object p2, p0, LK31/c;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LK31/c;->a:Landroid/content/Context;

    iget-object v1, p0, LK31/c;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    invoke-static {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->b(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)Landroidx/appcompat/widget/AppCompatImageView;

    move-result-object v0

    return-object v0
.end method
