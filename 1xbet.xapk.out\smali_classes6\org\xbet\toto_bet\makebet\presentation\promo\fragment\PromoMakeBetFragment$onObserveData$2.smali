.class final Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.promo.fragment.PromoMakeBetFragment$onObserveData$2"
    f = "PromoMakeBetFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;",
        "action",
        "",
        "<anonymous>",
        "(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->invoke(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 20

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_16

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a;

    .line 16
    .line 17
    sget-object v2, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$c;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$c;

    .line 18
    .line 19
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v4, 0x0

    .line 25
    if-eqz v2, :cond_4

    .line 26
    .line 27
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 28
    .line 29
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 34
    .line 35
    if-eqz v2, :cond_0

    .line 36
    .line 37
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_0
    move-object v1, v4

    .line 41
    :goto_0
    if-eqz v1, :cond_1

    .line 42
    .line 43
    invoke-virtual {v1, v3}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 44
    .line 45
    .line 46
    :cond_1
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 47
    .line 48
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 53
    .line 54
    if-eqz v2, :cond_2

    .line 55
    .line 56
    move-object v4, v1

    .line 57
    check-cast v4, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 58
    .line 59
    :cond_2
    if-eqz v4, :cond_3

    .line 60
    .line 61
    invoke-virtual {v4}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 62
    .line 63
    .line 64
    :cond_3
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 65
    .line 66
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->D2()LzX0/k;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    new-instance v3, Ly01/g;

    .line 71
    .line 72
    sget-object v4, Ly01/i$a;->a:Ly01/i$a;

    .line 73
    .line 74
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 75
    .line 76
    sget v5, Lpb/k;->toto_promocode_not_found:I

    .line 77
    .line 78
    invoke-virtual {v1, v5}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 79
    .line 80
    .line 81
    move-result-object v5

    .line 82
    const/16 v10, 0x3c

    .line 83
    .line 84
    const/4 v11, 0x0

    .line 85
    const/4 v6, 0x0

    .line 86
    const/4 v7, 0x0

    .line 87
    const/4 v8, 0x0

    .line 88
    const/4 v9, 0x0

    .line 89
    invoke-direct/range {v3 .. v11}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 90
    .line 91
    .line 92
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 93
    .line 94
    const/16 v12, 0x1fc

    .line 95
    .line 96
    const/4 v13, 0x0

    .line 97
    const/4 v5, 0x0

    .line 98
    const/4 v7, 0x0

    .line 99
    const/4 v8, 0x0

    .line 100
    const/4 v10, 0x0

    .line 101
    invoke-static/range {v2 .. v13}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 102
    .line 103
    .line 104
    goto/16 :goto_3

    .line 105
    .line 106
    :cond_4
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$e;

    .line 107
    .line 108
    if-eqz v2, :cond_9

    .line 109
    .line 110
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 111
    .line 112
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 113
    .line 114
    .line 115
    move-result-object v2

    .line 116
    instance-of v5, v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 117
    .line 118
    if-eqz v5, :cond_5

    .line 119
    .line 120
    check-cast v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 121
    .line 122
    goto :goto_1

    .line 123
    :cond_5
    move-object v2, v4

    .line 124
    :goto_1
    if-eqz v2, :cond_6

    .line 125
    .line 126
    invoke-virtual {v2, v3}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 127
    .line 128
    .line 129
    :cond_6
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 130
    .line 131
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 132
    .line 133
    .line 134
    move-result-object v2

    .line 135
    instance-of v3, v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 136
    .line 137
    if-eqz v3, :cond_7

    .line 138
    .line 139
    move-object v4, v2

    .line 140
    check-cast v4, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 141
    .line 142
    :cond_7
    if-eqz v4, :cond_8

    .line 143
    .line 144
    invoke-virtual {v4}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 145
    .line 146
    .line 147
    :cond_8
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 148
    .line 149
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->D2()LzX0/k;

    .line 150
    .line 151
    .line 152
    move-result-object v3

    .line 153
    new-instance v4, Ly01/g;

    .line 154
    .line 155
    sget-object v5, Ly01/i$c;->a:Ly01/i$c;

    .line 156
    .line 157
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$e;

    .line 158
    .line 159
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$e;->a()Ljava/lang/String;

    .line 160
    .line 161
    .line 162
    move-result-object v6

    .line 163
    const/16 v11, 0x3c

    .line 164
    .line 165
    const/4 v12, 0x0

    .line 166
    const/4 v7, 0x0

    .line 167
    const/4 v8, 0x0

    .line 168
    const/4 v9, 0x0

    .line 169
    const/4 v10, 0x0

    .line 170
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 171
    .line 172
    .line 173
    iget-object v5, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 174
    .line 175
    const/16 v13, 0x1fc

    .line 176
    .line 177
    const/4 v14, 0x0

    .line 178
    const/4 v6, 0x0

    .line 179
    const/4 v8, 0x0

    .line 180
    const/4 v9, 0x0

    .line 181
    const/4 v11, 0x0

    .line 182
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 183
    .line 184
    .line 185
    goto/16 :goto_3

    .line 186
    .line 187
    :cond_9
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$d;

    .line 188
    .line 189
    if-eqz v2, :cond_c

    .line 190
    .line 191
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 192
    .line 193
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 194
    .line 195
    .line 196
    move-result-object v2

    .line 197
    instance-of v5, v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 198
    .line 199
    if-eqz v5, :cond_a

    .line 200
    .line 201
    move-object v4, v2

    .line 202
    check-cast v4, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 203
    .line 204
    :cond_a
    if-eqz v4, :cond_b

    .line 205
    .line 206
    invoke-virtual {v4, v3}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 207
    .line 208
    .line 209
    :cond_b
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 210
    .line 211
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->D2()LzX0/k;

    .line 212
    .line 213
    .line 214
    move-result-object v3

    .line 215
    new-instance v4, Ly01/g;

    .line 216
    .line 217
    sget-object v5, Ly01/i$c;->a:Ly01/i$c;

    .line 218
    .line 219
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$d;

    .line 220
    .line 221
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$d;->a()Ljava/lang/String;

    .line 222
    .line 223
    .line 224
    move-result-object v6

    .line 225
    const/16 v11, 0x3c

    .line 226
    .line 227
    const/4 v12, 0x0

    .line 228
    const/4 v7, 0x0

    .line 229
    const/4 v8, 0x0

    .line 230
    const/4 v9, 0x0

    .line 231
    const/4 v10, 0x0

    .line 232
    invoke-direct/range {v4 .. v12}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 233
    .line 234
    .line 235
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 236
    .line 237
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->A2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)LOU0/f;

    .line 238
    .line 239
    .line 240
    move-result-object v1

    .line 241
    iget-object v7, v1, LOU0/f;->d:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 242
    .line 243
    iget-object v5, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 244
    .line 245
    const/16 v13, 0x1f4

    .line 246
    .line 247
    const/4 v14, 0x0

    .line 248
    const/4 v6, 0x0

    .line 249
    const/4 v8, 0x0

    .line 250
    const/4 v9, 0x0

    .line 251
    const/4 v11, 0x0

    .line 252
    invoke-static/range {v3 .. v14}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 253
    .line 254
    .line 255
    goto/16 :goto_3

    .line 256
    .line 257
    :cond_c
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$f;

    .line 258
    .line 259
    if-eqz v2, :cond_10

    .line 260
    .line 261
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 262
    .line 263
    invoke-virtual {v2}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 264
    .line 265
    .line 266
    move-result-object v2

    .line 267
    instance-of v5, v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 268
    .line 269
    if-eqz v5, :cond_d

    .line 270
    .line 271
    check-cast v2, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 272
    .line 273
    goto :goto_2

    .line 274
    :cond_d
    move-object v2, v4

    .line 275
    :goto_2
    if-eqz v2, :cond_e

    .line 276
    .line 277
    invoke-virtual {v2, v3}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 278
    .line 279
    .line 280
    :cond_e
    iget-object v2, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 281
    .line 282
    invoke-virtual {v2}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->E2()LAX0/b;

    .line 283
    .line 284
    .line 285
    move-result-object v2

    .line 286
    new-instance v5, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;

    .line 287
    .line 288
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 289
    .line 290
    sget v6, Lpb/k;->bet_processed_successfully:I

    .line 291
    .line 292
    invoke-virtual {v3, v6}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 293
    .line 294
    .line 295
    move-result-object v6

    .line 296
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$f;

    .line 297
    .line 298
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$f;->b()Ljava/lang/String;

    .line 299
    .line 300
    .line 301
    move-result-object v7

    .line 302
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 303
    .line 304
    sget v8, Lpb/k;->history:I

    .line 305
    .line 306
    invoke-virtual {v3, v8}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 307
    .line 308
    .line 309
    move-result-object v8

    .line 310
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 311
    .line 312
    sget v9, Lpb/k;->continue_action:I

    .line 313
    .line 314
    invoke-virtual {v3, v9}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 315
    .line 316
    .line 317
    move-result-object v9

    .line 318
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 319
    .line 320
    sget v10, Lpb/k;->bet_sum:I

    .line 321
    .line 322
    invoke-virtual {v3, v10}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 323
    .line 324
    .line 325
    move-result-object v11

    .line 326
    const/16 v14, 0xd0

    .line 327
    .line 328
    const/4 v15, 0x0

    .line 329
    const/4 v10, 0x0

    .line 330
    const/4 v12, 0x0

    .line 331
    const/4 v13, 0x0

    .line 332
    invoke-direct/range {v5 .. v15}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 333
    .line 334
    .line 335
    new-instance v6, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;

    .line 336
    .line 337
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 338
    .line 339
    sget v7, Lpb/k;->promo:I

    .line 340
    .line 341
    invoke-virtual {v3, v7}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 342
    .line 343
    .line 344
    move-result-object v3

    .line 345
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$f;->d()Ljava/lang/String;

    .line 346
    .line 347
    .line 348
    move-result-object v7

    .line 349
    new-instance v8, Ljava/lang/StringBuilder;

    .line 350
    .line 351
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 352
    .line 353
    .line 354
    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 355
    .line 356
    .line 357
    const-string v3, ": "

    .line 358
    .line 359
    invoke-virtual {v8, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 360
    .line 361
    .line 362
    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 363
    .line 364
    .line 365
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 366
    .line 367
    .line 368
    move-result-object v7

    .line 369
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$f;->c()Ljava/lang/String;

    .line 370
    .line 371
    .line 372
    move-result-object v8

    .line 373
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$f;->a()J

    .line 374
    .line 375
    .line 376
    move-result-wide v9

    .line 377
    invoke-static {v9, v10}, LHc/a;->f(J)Ljava/lang/Long;

    .line 378
    .line 379
    .line 380
    move-result-object v14

    .line 381
    const/16 v18, 0x43c

    .line 382
    .line 383
    const/16 v19, 0x0

    .line 384
    .line 385
    const/4 v9, 0x0

    .line 386
    const/4 v10, 0x0

    .line 387
    const/4 v11, 0x0

    .line 388
    const-string v13, "REQUEST_SUCCESS_BET_KEY"

    .line 389
    .line 390
    const-string v15, "TOTO"

    .line 391
    .line 392
    const/16 v16, 0x0

    .line 393
    .line 394
    const/16 v17, 0x0

    .line 395
    .line 396
    invoke-direct/range {v6 .. v19}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;ZLjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 397
    .line 398
    .line 399
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 400
    .line 401
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 402
    .line 403
    .line 404
    move-result-object v1

    .line 405
    invoke-virtual {v1}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 406
    .line 407
    .line 408
    move-result-object v1

    .line 409
    invoke-virtual {v2, v5, v6, v1}, LAX0/b;->d(Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;Landroidx/fragment/app/FragmentManager;)V

    .line 410
    .line 411
    .line 412
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 413
    .line 414
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 415
    .line 416
    .line 417
    move-result-object v1

    .line 418
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 419
    .line 420
    if-eqz v2, :cond_f

    .line 421
    .line 422
    move-object v4, v1

    .line 423
    check-cast v4, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 424
    .line 425
    :cond_f
    if-eqz v4, :cond_14

    .line 426
    .line 427
    invoke-virtual {v4}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 428
    .line 429
    .line 430
    goto :goto_3

    .line 431
    :cond_10
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$b;

    .line 432
    .line 433
    if-eqz v2, :cond_13

    .line 434
    .line 435
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 436
    .line 437
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 438
    .line 439
    .line 440
    move-result-object v1

    .line 441
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 442
    .line 443
    if-eqz v2, :cond_11

    .line 444
    .line 445
    move-object v4, v1

    .line 446
    check-cast v4, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 447
    .line 448
    :cond_11
    if-eqz v4, :cond_12

    .line 449
    .line 450
    const/4 v1, 0x1

    .line 451
    invoke-virtual {v4, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 452
    .line 453
    .line 454
    :cond_12
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 455
    .line 456
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->A2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)LOU0/f;

    .line 457
    .line 458
    .line 459
    move-result-object v1

    .line 460
    iget-object v1, v1, LOU0/f;->c:Lcom/google/android/material/button/MaterialButton;

    .line 461
    .line 462
    invoke-virtual {v1, v3}, Landroid/view/View;->setEnabled(Z)V

    .line 463
    .line 464
    .line 465
    goto :goto_3

    .line 466
    :cond_13
    sget-object v2, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$a;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel$a$a;

    .line 467
    .line 468
    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 469
    .line 470
    .line 471
    move-result v1

    .line 472
    if-eqz v1, :cond_15

    .line 473
    .line 474
    :cond_14
    :goto_3
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment$onObserveData$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;

    .line 475
    .line 476
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->B2(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;

    .line 477
    .line 478
    .line 479
    move-result-object v1

    .line 480
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetViewModel;->B3()V

    .line 481
    .line 482
    .line 483
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 484
    .line 485
    return-object v1

    .line 486
    :cond_15
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 487
    .line 488
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 489
    .line 490
    .line 491
    throw v1

    .line 492
    :cond_16
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 493
    .line 494
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 495
    .line 496
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 497
    .line 498
    .line 499
    throw v1
.end method
