.class public final synthetic Lorg/xbet/toto_bet/outcomes/presentation/components/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:J

.field public final synthetic b:Landroidx/compose/runtime/r1;

.field public final synthetic c:Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;


# direct methods
.method public synthetic constructor <init>(JLandroidx/compose/runtime/r1;Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/v;->a:J

    iput-object p3, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/v;->b:Landroidx/compose/runtime/r1;

    iput-object p4, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/v;->c:Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-wide v0, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/v;->a:J

    iget-object v2, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/v;->b:Landroidx/compose/runtime/r1;

    iget-object v3, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/v;->c:Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;

    check-cast p1, Landroidx/compose/foundation/lazy/t;

    invoke-static {v0, v1, v2, v3, p1}, Lorg/xbet/toto_bet/outcomes/presentation/components/TotoOutcomesScreenComponentKt$TotoOutcomesScreenComponent$3;->a(JLandroidx/compose/runtime/r1;Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
