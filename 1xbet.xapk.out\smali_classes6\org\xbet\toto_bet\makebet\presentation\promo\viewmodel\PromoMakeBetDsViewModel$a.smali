.class public interface abstract Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$a;,
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;,
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;,
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;,
        Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u0008v\u0018\u00002\u00020\u0001:\u0005\u0002\u0003\u0004\u0005\u0006\u0082\u0001\u0005\u0007\u0008\t\n\u000b\u00a8\u0006\u000c"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a;",
        "",
        "d",
        "c",
        "e",
        "a",
        "b",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$a;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$b;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$c;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$d;",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation
