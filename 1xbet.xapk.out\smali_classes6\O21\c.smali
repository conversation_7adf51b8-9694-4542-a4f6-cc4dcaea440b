.class public final LO21/c;
.super Landroidx/recyclerview/widget/RecyclerView$o;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u000b\n\u0002\u0010\u000b\n\u0002\u0008\u0007\u0008\u0001\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ/\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001b\u0010\u0017\u001a\u00020\u0012*\u00020\n2\u0006\u0010\u0016\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\u001b\u0010\u001a\u001a\u00020\u0012*\u00020\n2\u0006\u0010\u0019\u001a\u00020\u0015H\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0018R\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001b\u0010\u001c\u001a\u0004\u0008\u001b\u0010\u001dR\u0017\u0010\u0007\u001a\u00020\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008\u001e\u0010 R\u0014\u0010#\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\"R\u0014\u0010%\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010$R\u0014\u0010\'\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010$\u00a8\u0006("
    }
    d2 = {
        "LO21/c;",
        "Landroidx/recyclerview/widget/RecyclerView$o;",
        "Landroid/content/Context;",
        "context",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
        "style",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;",
        "type",
        "<init>",
        "(Landroid/content/Context;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;)V",
        "Landroid/graphics/Rect;",
        "outRect",
        "Landroid/view/View;",
        "view",
        "Landroidx/recyclerview/widget/RecyclerView;",
        "parent",
        "Landroidx/recyclerview/widget/RecyclerView$z;",
        "state",
        "",
        "getItemOffsets",
        "(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V",
        "",
        "start",
        "i",
        "(Landroid/graphics/Rect;I)V",
        "end",
        "h",
        "f",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
        "()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
        "g",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;",
        "()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;",
        "",
        "Z",
        "isRtl",
        "I",
        "innerHorizontalPadding",
        "j",
        "verticalPadding",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final f:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:Z

.field public final i:I

.field public final j:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$o;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, LO21/c;->f:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 5
    .line 6
    iput-object p3, p0, LO21/c;->g:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 7
    .line 8
    invoke-static {}, LQ0/a;->c()LQ0/a;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, LQ0/a;->h()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    iput-boolean v0, p0, LO21/c;->h:Z

    .line 17
    .line 18
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandMExtended:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 19
    .line 20
    if-eq p2, v0, :cond_0

    .line 21
    .line 22
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandM:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 23
    .line 24
    if-ne p2, v1, :cond_1

    .line 25
    .line 26
    :cond_0
    sget-object v1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;->Vertical:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 27
    .line 28
    if-ne p3, v1, :cond_1

    .line 29
    .line 30
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 31
    .line 32
    .line 33
    move-result-object v1

    .line 34
    sget v2, LlZ0/g;->space_4:I

    .line 35
    .line 36
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 37
    .line 38
    .line 39
    move-result v1

    .line 40
    goto :goto_0

    .line 41
    :cond_1
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    sget v2, LlZ0/g;->space_8:I

    .line 46
    .line 47
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    :goto_0
    iput v1, p0, LO21/c;->i:I

    .line 52
    .line 53
    if-eq p2, v0, :cond_2

    .line 54
    .line 55
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->BrandM:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 56
    .line 57
    if-ne p2, v0, :cond_3

    .line 58
    .line 59
    :cond_2
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;->Vertical:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 60
    .line 61
    if-ne p3, p2, :cond_3

    .line 62
    .line 63
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    sget p2, LlZ0/g;->space_4:I

    .line 68
    .line 69
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    goto :goto_1

    .line 74
    :cond_3
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    sget p2, LlZ0/g;->space_8:I

    .line 79
    .line 80
    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    :goto_1
    iput p1, p0, LO21/c;->j:I

    .line 85
    .line 86
    return-void
.end method


# virtual methods
.method public final f()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LO21/c;->f:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LO21/c;->g:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 2
    .line 3
    return-object v0
.end method

.method public getItemOffsets(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V
    .locals 1
    .param p1    # Landroid/graphics/Rect;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroidx/recyclerview/widget/RecyclerView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Landroidx/recyclerview/widget/RecyclerView$z;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2, p3, p4}, Landroidx/recyclerview/widget/RecyclerView$o;->getItemOffsets(Landroid/graphics/Rect;Landroid/view/View;Landroidx/recyclerview/widget/RecyclerView;Landroidx/recyclerview/widget/RecyclerView$z;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p3, p2}, Landroidx/recyclerview/widget/RecyclerView;->getChildAdapterPosition(Landroid/view/View;)I

    .line 5
    .line 6
    .line 7
    move-result p2

    .line 8
    const/4 p4, -0x1

    .line 9
    if-ne p2, p4, :cond_0

    .line 10
    .line 11
    goto :goto_1

    .line 12
    :cond_0
    iget-object p4, p0, LO21/c;->g:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 13
    .line 14
    sget-object v0, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;->Horizontal:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 15
    .line 16
    if-ne p4, v0, :cond_1

    .line 17
    .line 18
    if-lez p2, :cond_4

    .line 19
    .line 20
    iget p2, p0, LO21/c;->i:I

    .line 21
    .line 22
    invoke-virtual {p0, p1, p2}, LO21/c;->i(Landroid/graphics/Rect;I)V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :cond_1
    invoke-virtual {p3}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 27
    .line 28
    .line 29
    move-result-object p3

    .line 30
    instance-of p4, p3, Landroidx/recyclerview/widget/GridLayoutManager;

    .line 31
    .line 32
    if-eqz p4, :cond_2

    .line 33
    .line 34
    check-cast p3, Landroidx/recyclerview/widget/GridLayoutManager;

    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_2
    const/4 p3, 0x0

    .line 38
    :goto_0
    if-eqz p3, :cond_4

    .line 39
    .line 40
    invoke-virtual {p3}, Landroidx/recyclerview/widget/GridLayoutManager;->G()I

    .line 41
    .line 42
    .line 43
    move-result p3

    .line 44
    rem-int p4, p2, p3

    .line 45
    .line 46
    div-int/2addr p2, p3

    .line 47
    if-lez p2, :cond_3

    .line 48
    .line 49
    iget p2, p0, LO21/c;->j:I

    .line 50
    .line 51
    iput p2, p1, Landroid/graphics/Rect;->top:I

    .line 52
    .line 53
    :cond_3
    iget p2, p0, LO21/c;->i:I

    .line 54
    .line 55
    mul-int p2, p2, p4

    .line 56
    .line 57
    div-int/2addr p2, p3

    .line 58
    invoke-virtual {p0, p1, p2}, LO21/c;->i(Landroid/graphics/Rect;I)V

    .line 59
    .line 60
    .line 61
    iget p2, p0, LO21/c;->i:I

    .line 62
    .line 63
    add-int/lit8 p4, p4, 0x1

    .line 64
    .line 65
    mul-int p4, p4, p2

    .line 66
    .line 67
    div-int/2addr p4, p3

    .line 68
    sub-int/2addr p2, p4

    .line 69
    invoke-virtual {p0, p1, p2}, LO21/c;->h(Landroid/graphics/Rect;I)V

    .line 70
    .line 71
    .line 72
    :cond_4
    :goto_1
    return-void
.end method

.method public final h(Landroid/graphics/Rect;I)V
    .locals 1

    .line 1
    iget-boolean v0, p0, LO21/c;->h:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iput p2, p1, Landroid/graphics/Rect;->left:I

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    iput p2, p1, Landroid/graphics/Rect;->right:I

    .line 9
    .line 10
    return-void
.end method

.method public final i(Landroid/graphics/Rect;I)V
    .locals 1

    .line 1
    iget-boolean v0, p0, LO21/c;->h:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iput p2, p1, Landroid/graphics/Rect;->right:I

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    iput p2, p1, Landroid/graphics/Rect;->left:I

    .line 9
    .line 10
    return-void
.end method
