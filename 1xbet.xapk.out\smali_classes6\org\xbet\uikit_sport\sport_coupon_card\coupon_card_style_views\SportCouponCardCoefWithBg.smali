.class public final Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements LU31/j;
.implements LU31/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\r\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u00022\u00020\u0003B\'\u0008\u0007\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\n\u0008\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\u0008\u0008\u0002\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0019\u0010\u0012\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\u0011\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u0019\u0010\u0015\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\u0014\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0013J\u0019\u0010\u0017\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\u0016\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0013J\u0011\u0010\u0019\u001a\u0004\u0018\u00010\u0018H\u0016\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u001d\u001a\u00020\u000e2\u0006\u0010\u001c\u001a\u00020\u001bH\u0016\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ%\u0010\"\u001a\u00020\u000e2\u0014\u0010!\u001a\u0010\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020\u000e\u0018\u00010\u001fH\u0016\u00a2\u0006\u0004\u0008\"\u0010#J%\u0010$\u001a\u00020\u000e2\u0014\u0010!\u001a\u0010\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020\u000e\u0018\u00010\u001fH\u0016\u00a2\u0006\u0004\u0008$\u0010#J\u0019\u0010\u0015\u001a\u00020\u000e2\u0008\u0010\u0014\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008\u0015\u0010&J\u0019\u0010(\u001a\u00020\u000e2\u0008\u0010\'\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008(\u0010&J\u0019\u0010\u0012\u001a\u00020\u000e2\u0008\u0010\u0011\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008\u0012\u0010&J\u0019\u0010*\u001a\u00020\u000e2\u0008\u0008\u0001\u0010)\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008*\u0010\u0013J\u0019\u0010,\u001a\u00020\u000e2\u0008\u0010+\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008,\u0010&J!\u0010.\u001a\u00020\u000e2\u0008\u0010-\u001a\u0004\u0018\u00010%2\u0006\u0010\r\u001a\u00020\u000cH\u0016\u00a2\u0006\u0004\u0008.\u0010/J\u0019\u00101\u001a\u00020\u000e2\u0008\u00100\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u00081\u0010&J\u0019\u0010\u0017\u001a\u00020\u000e2\u0008\u0010\u0016\u001a\u0004\u0018\u00010%H\u0016\u00a2\u0006\u0004\u0008\u0017\u0010&J\u001b\u00103\u001a\u00020\u000e2\n\u0008\u0001\u00102\u001a\u0004\u0018\u00010\u0008H\u0016\u00a2\u0006\u0004\u00083\u00104J!\u00106\u001a\u00020\u000e2\u0008\u0010\u0011\u001a\u0004\u0018\u00010%2\u0008\u00105\u001a\u0004\u0018\u00010%\u00a2\u0006\u0004\u00086\u00107J\u0017\u0010(\u001a\u00020\u000e2\u0008\u0008\u0001\u0010\'\u001a\u00020\u0008\u00a2\u0006\u0004\u0008(\u0010\u0013R\u001b\u0010=\u001a\u0002088BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00089\u0010:\u001a\u0004\u0008;\u0010<R\u0014\u0010A\u001a\u00020>8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0017\u0010G\u001a\u00020B8\u0006\u00a2\u0006\u000c\n\u0004\u0008C\u0010D\u001a\u0004\u0008E\u0010F\u00a8\u0006H"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "LU31/j;",
        "LU31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "",
        "setMarketCoefficientState",
        "(Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "title",
        "setTitle",
        "(I)V",
        "subtitle",
        "setSubTitle",
        "error",
        "setError",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "LX31/c;",
        "couponCardUiModel",
        "setModel",
        "(LX31/c;)V",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "",
        "(Ljava/lang/CharSequence;)V",
        "tag",
        "setTagText",
        "tagColor",
        "setTagColor",
        "description",
        "setMarketDescription",
        "coef",
        "setMarketCoefficient",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "bonusTitle",
        "setCouponBonusTitle",
        "styleResId",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "coefficient",
        "setMarket",
        "(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V",
        "Lorg/xbet/uikit/utils/e;",
        "a",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "LC31/e;",
        "b",
        "LC31/e;",
        "binding",
        "Lorg/xbet/uikit/components/market/view/MarketCoupon;",
        "c",
        "Lorg/xbet/uikit/components/market/view/MarketCoupon;",
        "getMarket",
        "()Lorg/xbet/uikit/components/market/view/MarketCoupon;",
        "market",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LC31/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit/components/market/view/MarketCoupon;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 5
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    new-instance v0, LV31/l;

    invoke-direct {v0, p0}, LV31/l;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->a:Lkotlin/j;

    .line 7
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object v0

    invoke-static {v0, p0}, LC31/e;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/e;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 8
    iget-object v0, v0, LC31/e;->d:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 9
    sget-object v0, Lm31/g;->SportCouponCardView:[I

    const/4 v1, 0x0

    .line 10
    invoke-virtual {p1, p2, v0, p3, v1}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v0

    .line 11
    sget v2, Lm31/g;->SportCouponCardView_title:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v2

    const/4 v3, 0x0

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    goto :goto_0

    :cond_0
    move-object v2, v3

    :goto_0
    const-string v4, ""

    if-nez v2, :cond_1

    move-object v2, v4

    :cond_1
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTitle(Ljava/lang/CharSequence;)V

    .line 12
    sget v2, Lm31/g;->SportCouponCardView_subtitle:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v2

    if-eqz v2, :cond_2

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    goto :goto_1

    :cond_2
    move-object v2, v3

    :goto_1
    if-nez v2, :cond_3

    move-object v2, v4

    :cond_3
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 13
    sget v2, Lm31/g;->SportCouponCardView_tag:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v2

    if-eqz v2, :cond_4

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    goto :goto_2

    :cond_4
    move-object v2, v3

    :goto_2
    if-nez v2, :cond_5

    move-object v2, v4

    :cond_5
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTagText(Ljava/lang/CharSequence;)V

    .line 14
    sget v2, Lm31/g;->SportCouponCardView_error:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v2

    if-eqz v2, :cond_6

    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    goto :goto_3

    :cond_6
    move-object v2, v3

    :goto_3
    if-nez v2, :cond_7

    move-object v2, v4

    :cond_7
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setError(Ljava/lang/CharSequence;)V

    .line 15
    sget v2, Lm31/g;->SportCouponCardView_couponMarketStyle:I

    invoke-virtual {v0, v2, v1}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setMarketStyle(Ljava/lang/Integer;)V

    .line 16
    sget v1, Lm31/g;->SportCouponCardView_marketTitle:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-static {v0, p1, v1}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object v1

    if-eqz v1, :cond_8

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_4

    :cond_8
    move-object v1, v3

    :goto_4
    if-nez v1, :cond_9

    move-object v1, v4

    .line 17
    :cond_9
    sget v2, Lm31/g;->SportCouponCardView_marketCoefficient:I

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-static {v0, p1, v2}, Lorg/xbet/uikit/utils/I;->e(Landroid/content/res/TypedArray;Landroid/content/Context;Ljava/lang/Integer;)Ljava/lang/CharSequence;

    move-result-object p1

    if-eqz p1, :cond_a

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v3

    :cond_a
    if-nez v3, :cond_b

    goto :goto_5

    :cond_b
    move-object v4, v3

    .line 18
    :goto_5
    invoke-virtual {p0, v1, v4}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 19
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object p1

    invoke-virtual {p1, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCouponCardStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic s(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->v(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method private final setError(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setError(Ljava/lang/CharSequence;)V

    return-void
.end method

.method private final setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 2
    .line 3
    iget-object v0, v0, LC31/e;->d:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->setCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method private final setSubTitle(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setSubTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method private final setTitle(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public static synthetic t(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->w(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic u(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->x(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V

    return-void
.end method

.method public static final v(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final w(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final x(Lkotlin/jvm/functions/Function1;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final getMarket()Lorg/xbet/uikit/components/market/view/MarketCoupon;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 2
    .line 3
    return-object v0
.end method

.method public setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 2
    .line 3
    iget-object v0, v0, LC31/e;->b:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    new-instance v1, LV31/m;

    .line 8
    .line 9
    invoke-direct {v1, p1}, LV31/m;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v1, 0x0

    .line 14
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method public setCouponBonusTitle(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTitle(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setError(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->c:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->c:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {v0}, Landroid/widget/TextView;->getText()Ljava/lang/CharSequence;

    move-result-object p1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-lez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    const/16 v1, 0x8

    .line 3
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public final setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->setDescriptionMarket(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 7
    .line 8
    invoke-virtual {p1, p2}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->setCoefficientMarket(Ljava/lang/CharSequence;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->setCoefficientMarket(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 7
    .line 8
    invoke-virtual {p1, p2}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->setCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->c:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->setDescriptionMarket(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setMarketStyle(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 4
    .line 5
    iget-object v0, v0, LC31/e;->d:Lorg/xbet/uikit/components/market/view/MarketCoupon;

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/market/view/MarketCoupon;->c(I)V

    .line 12
    .line 13
    .line 14
    :cond_0
    return-void
.end method

.method public setModel(LX31/c;)V
    .locals 2
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LX31/c;->m()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTagText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, LX31/c;->l()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTagColor(I)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1}, LX31/c;->o()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTitle(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1}, LX31/c;->k()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/c;->c()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setError(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1}, LX31/c;->f()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p1}, LX31/c;->e()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {p1}, LX31/c;->h()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setMarketStyle(Ljava/lang/Integer;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p1}, LX31/c;->b()Lorg/xbet/uikit/components/market/base/CoefficientState;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 63
    .line 64
    .line 65
    return-void
.end method

.method public setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 2
    .line 3
    iget-object v0, v0, LC31/e;->e:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    new-instance v1, LV31/n;

    .line 8
    .line 9
    invoke-direct {v1, p1}, LV31/n;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v1, 0x0

    .line 14
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 18
    .line 19
    iget-object v0, v0, LC31/e;->e:Lorg/xbet/uikit/components/buttons/DSButton;

    .line 20
    .line 21
    const/4 v1, 0x0

    .line 22
    if-eqz p1, :cond_1

    .line 23
    .line 24
    const/4 p1, 0x1

    .line 25
    goto :goto_1

    .line 26
    :cond_1
    const/4 p1, 0x0

    .line 27
    :goto_1
    if-eqz p1, :cond_2

    .line 28
    .line 29
    goto :goto_2

    .line 30
    :cond_2
    const/16 v1, 0x8

    .line 31
    .line 32
    :goto_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public setSubTitle(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->f:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->f:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {v0}, Landroid/widget/TextView;->getText()Ljava/lang/CharSequence;

    move-result-object p1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-lez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    const/16 v1, 0x8

    .line 3
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public setTagColor(I)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    .line 2
    .line 3
    iget-object v0, v0, LC31/e;->g:Lorg/xbet/uikit/components/tag/Tag;

    .line 4
    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    const/4 v2, 0x0

    .line 10
    const/4 v3, 0x2

    .line 11
    invoke-static {v1, p1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-static {v0, p1}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final setTagText(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->setTagText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setTagText(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->g:Lorg/xbet/uikit/components/tag/Tag;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->g:Lorg/xbet/uikit/components/tag/Tag;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    invoke-virtual {v0}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    move-result-object p1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result p1

    if-lez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    const/16 v1, 0x8

    .line 3
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public setTitle(Ljava/lang/CharSequence;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->h:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 2
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardCoefWithBg;->b:LC31/e;

    iget-object v0, v0, LC31/e;->h:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    const/16 v1, 0x8

    .line 3
    :goto_1
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method
