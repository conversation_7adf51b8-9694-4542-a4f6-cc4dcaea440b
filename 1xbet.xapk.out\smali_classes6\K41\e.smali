.class public final LK41/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "LK41/d;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LRf0/f;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lcom/google/gson/Gson;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LRf0/f;",
            ">;",
            "LBc/a<",
            "Lcom/google/gson/Gson;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LK41/e;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, LK41/e;->b:LBc/a;

    .line 7
    .line 8
    return-void
.end method

.method public static a(LBc/a;LBc/a;)LK41/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "LRf0/f;",
            ">;",
            "LBc/a<",
            "Lcom/google/gson/Gson;",
            ">;)",
            "LK41/e;"
        }
    .end annotation

    .line 1
    new-instance v0, LK41/e;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LK41/e;-><init>(LBc/a;LBc/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(LRf0/f;Lcom/google/gson/Gson;)LK41/d;
    .locals 1

    .line 1
    new-instance v0, LK41/d;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, LK41/d;-><init>(LRf0/f;Lcom/google/gson/Gson;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()LK41/d;
    .locals 2

    .line 1
    iget-object v0, p0, LK41/e;->a:LBc/a;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LRf0/f;

    .line 8
    .line 9
    iget-object v1, p0, LK41/e;->b:LBc/a;

    .line 10
    .line 11
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lcom/google/gson/Gson;

    .line 16
    .line 17
    invoke-static {v0, v1}, LK41/e;->c(LRf0/f;Lcom/google/gson/Gson;)LK41/d;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LK41/e;->b()LK41/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
