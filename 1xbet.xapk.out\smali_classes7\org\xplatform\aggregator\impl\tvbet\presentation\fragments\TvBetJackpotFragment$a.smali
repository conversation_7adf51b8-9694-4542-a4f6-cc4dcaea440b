.class public final Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0008\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J-\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0008\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\r\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\r\u0010\u000eR\u0014\u0010\u000f\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u000eR\u0014\u0010\u0010\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010\u000eR\u0014\u0010\u0011\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u000eR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014\u00a8\u0006\u0015"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;",
        "",
        "<init>",
        "()V",
        "",
        "bannerUrl",
        "bannerTranslateId",
        "",
        "showNavBar",
        "fromAggregator",
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;",
        "a",
        "(Ljava/lang/String;Ljava/lang/String;ZZ)Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;",
        "BANNER_URL",
        "Ljava/lang/String;",
        "BANNER_TRANSLATE_ID",
        "SHOW_NAVBAR",
        "FROM_AGGREGATOR",
        "",
        "RESULT_PAGE",
        "I",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;Ljava/lang/String;ZZ)Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0, p3}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->B2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Z)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, p4}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->A2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;Z)V

    .line 10
    .line 11
    .line 12
    new-instance p3, Landroid/os/Bundle;

    .line 13
    .line 14
    invoke-direct {p3}, Landroid/os/Bundle;-><init>()V

    .line 15
    .line 16
    .line 17
    const-string p4, "BANNER_URL"

    .line 18
    .line 19
    invoke-virtual {p3, p4, p1}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    const-string p1, "BANNER_TRANSLATE_ID"

    .line 23
    .line 24
    invoke-virtual {p3, p1, p2}, Landroid/os/BaseBundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {v0, p3}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 28
    .line 29
    .line 30
    return-object v0
.end method
