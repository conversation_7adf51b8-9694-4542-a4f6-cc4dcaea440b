.class public final synthetic LN01/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/r;->a:Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LN01/r;->a:Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;

    invoke-static {v0}, Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;->t(Lorg/xbet/uikit/components/toolbar/base/styles/TitleNavigationBar;)V

    return-void
.end method
