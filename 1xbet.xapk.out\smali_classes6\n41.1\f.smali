.class public final synthetic Ln41/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln41/f;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Ln41/f;->a:Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    invoke-static {v0}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->d(Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Landroid/view/View;

    move-result-object v0

    return-object v0
.end method
