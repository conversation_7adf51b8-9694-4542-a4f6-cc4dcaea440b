.class interface abstract Lcom/google/common/cache/LongAddable;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build Lcom/google/common/annotations/GwtCompatible;
.end annotation

.annotation runtime Lcom/google/common/cache/ElementTypesAreNonnullByDefault;
.end annotation


# virtual methods
.method public abstract add(J)V
.end method

.method public abstract increment()V
.end method

.method public abstract sum()J
.end method
