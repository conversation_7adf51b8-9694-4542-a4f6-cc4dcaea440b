.class public final synthetic Lorg/xbet/toto_bet/outcomes/presentation/components/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;

.field public final synthetic b:J

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;JI)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/u;->a:Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;

    iput-wide p2, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/u;->b:J

    iput p4, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/u;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/u;->a:Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;

    iget-wide v1, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/u;->b:J

    iget v3, p0, Lorg/xbet/toto_bet/outcomes/presentation/components/u;->c:I

    move-object v4, p1

    check-cast v4, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v5

    invoke-static/range {v0 .. v5}, Lorg/xbet/toto_bet/outcomes/presentation/components/TotoOutcomesScreenComponentKt;->a(Lorg/xbet/toto_bet/outcomes/presentation/viewmodel/TotoBetAccurateOutcomesViewModel;JILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
