.class public final Lorg/xbet/uikit_sport/express_card/ExpressEventItem;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LG31/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/express_card/ExpressEventItem$a;,
        Lorg/xbet/uikit_sport/express_card/ExpressEventItem$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000e\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0007\n\u0002\u0008\u0005\u0008\u0001\u0018\u0000 I2\u00020\u00012\u00020\u0002:\u0001$B\u000f\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006B\u0019\u0008\u0016\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0005\u0010\tJ\u001f\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000c\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ7\u0010\u0016\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0017\u0010\u001a\u001a\u00020\r2\u0006\u0010\u0019\u001a\u00020\u0018H\u0014\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u0015\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u0017\u0010!\u001a\u00020\r2\u0006\u0010 \u001a\u00020\u001fH\u0016\u00a2\u0006\u0004\u0008!\u0010\"J\u0017\u0010$\u001a\u00020\r2\u0006\u0010#\u001a\u00020\u001fH\u0016\u00a2\u0006\u0004\u0008$\u0010\"J\u0017\u0010&\u001a\u00020\r2\u0006\u0010%\u001a\u00020\nH\u0016\u00a2\u0006\u0004\u0008&\u0010\'J\u000f\u0010(\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008(\u0010)R\u0014\u0010,\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008$\u0010+R\u0014\u0010/\u001a\u00020-8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010.R\u0016\u00102\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00080\u00101R\u0016\u0010\u001c\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u00104R\u0014\u00108\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010:\u001a\u0002058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00089\u00107R\u0016\u0010<\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008;\u00101R\u0016\u0010>\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008=\u00101R\u0016\u0010@\u001a\u00020\n8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008?\u00101R\u0016\u0010D\u001a\u00020A8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010H\u001a\u00020E8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010G\u00a8\u0006J"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/express_card/ExpressEventItem;",
        "Landroid/widget/FrameLayout;",
        "LG31/a;",
        "Landroid/content/Context;",
        "context",
        "<init>",
        "(Landroid/content/Context;)V",
        "Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;",
        "sportEventItemStyle",
        "(Landroid/content/Context;Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "style",
        "setStyle",
        "(Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V",
        "",
        "coef",
        "setCoef",
        "(Ljava/lang/String;)V",
        "url",
        "a",
        "resId",
        "setSportIcon",
        "(I)V",
        "b",
        "()V",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "sportIcon",
        "Landroid/widget/TextView;",
        "Landroid/widget/TextView;",
        "eventCoef",
        "c",
        "I",
        "iconSize",
        "d",
        "Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;",
        "Landroid/graphics/Paint;",
        "e",
        "Landroid/graphics/Paint;",
        "circleBackgroundPaint",
        "f",
        "rectangleBackgroundPaint",
        "g",
        "layoutHeight",
        "h",
        "topPadding",
        "i",
        "coefTopPadding",
        "Landroid/graphics/RectF;",
        "j",
        "Landroid/graphics/RectF;",
        "roundedRectangleBackground",
        "",
        "k",
        "F",
        "cornerRadius",
        "l",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final l:Lorg/xbet/uikit_sport/express_card/ExpressEventItem$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final m:I


# instance fields
.field public final a:Lorg/xbet/uikit/components/views/LoadableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:I

.field public d:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Landroid/graphics/Paint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:I

.field public h:I

.field public i:I

.field public j:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:F


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->l:Lorg/xbet/uikit_sport/express_card/ExpressEventItem$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->m:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 11
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->size_24:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 3
    sget-object v0, Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;->LARGE:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    iput-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->d:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 4
    new-instance v0, Landroid/graphics/Paint;

    const/4 v1, 0x1

    invoke-direct {v0, v1}, Landroid/graphics/Paint;-><init>(I)V

    .line 5
    sget v2, LlZ0/d;->uikitBackground:I

    const/4 v3, 0x0

    const/4 v4, 0x2

    invoke-static {p1, v2, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 6
    iput-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->e:Landroid/graphics/Paint;

    .line 7
    new-instance v0, Landroid/graphics/Paint;

    invoke-direct {v0, v1}, Landroid/graphics/Paint;-><init>(I)V

    .line 8
    sget v2, LlZ0/d;->uikitBackgroundGroupSecondary:I

    invoke-static {p1, v2, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-virtual {v0, v2}, Landroid/graphics/Paint;->setColor(I)V

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->f:Landroid/graphics/Paint;

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_58:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->g:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_8:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->i:I

    .line 13
    new-instance v0, Landroid/graphics/RectF;

    const/4 v2, 0x0

    invoke-direct {v0, v2, v2, v2, v2}, Landroid/graphics/RectF;-><init>(FFFF)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->j:Landroid/graphics/RectF;

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_10:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    int-to-float v0, v0

    iput v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->k:F

    .line 15
    new-instance v5, Lorg/xbet/uikit/components/views/LoadableImageView;

    const/4 v9, 0x6

    const/4 v10, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    move-object v6, p1

    invoke-direct/range {v5 .. v10}, Lorg/xbet/uikit/components/views/LoadableImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    const/4 p1, 0x3

    .line 16
    invoke-virtual {v5, p1}, Landroid/view/View;->setLayoutDirection(I)V

    .line 17
    sget v0, LlZ0/d;->uikitSecondary:I

    invoke-static {v6, v0, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v0

    invoke-virtual {v5, v0}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 18
    iput-object v5, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 19
    new-instance v0, Landroid/widget/TextView;

    invoke-direct {v0, v6}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    const/16 v2, 0x11

    .line 20
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v2, 0x4

    .line 21
    invoke-virtual {v0, v2}, Landroid/view/View;->setTextAlignment(I)V

    .line 22
    sget v2, LlZ0/n;->TextStyle_Caption_Bold_L_TextPrimary:I

    invoke-static {v0, v2}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    const/4 v2, 0x0

    .line 23
    invoke-virtual {v0, v2}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 24
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 25
    invoke-virtual {v0, p1}, Landroid/view/View;->setLayoutDirection(I)V

    .line 26
    sget-object p1, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 27
    iput-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 28
    invoke-virtual {p0, v5}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 29
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 30
    invoke-virtual {p0, v2}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 31
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;-><init>(Landroid/content/Context;)V

    .line 32
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->setStyle(Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V

    return-void
.end method


# virtual methods
.method public a(Ljava/lang/String;)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, LlZ0/h;->ic_glyph_category_new:I

    .line 8
    .line 9
    invoke-static {v1, v2}, Lg/a;->b(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    const/16 v5, 0xc

    .line 14
    .line 15
    const/4 v6, 0x0

    .line 16
    const/4 v3, 0x0

    .line 17
    const/4 v4, 0x0

    .line 18
    move-object v1, p1

    .line 19
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->X(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final b()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 2
    .line 3
    const/16 v1, 0xc

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    const/16 v3, 0x8

    .line 7
    .line 8
    invoke-static {v0, v3, v1, v2, v2}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->d:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_sport/express_card/ExpressEventItem$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    aget v0, v1, v0

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    const/4 v2, 0x2

    .line 13
    if-eq v0, v1, :cond_1

    .line 14
    .line 15
    if-eq v0, v2, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->j:Landroid/graphics/RectF;

    .line 19
    .line 20
    iget v1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->k:F

    .line 21
    .line 22
    iget-object v2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->f:Landroid/graphics/Paint;

    .line 23
    .line 24
    invoke-virtual {p1, v0, v1, v1, v2}, Landroid/graphics/Canvas;->drawRoundRect(Landroid/graphics/RectF;FFLandroid/graphics/Paint;)V

    .line 25
    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 29
    .line 30
    .line 31
    move-result v0

    .line 32
    int-to-float v0, v0

    .line 33
    int-to-float v1, v2

    .line 34
    div-float/2addr v0, v1

    .line 35
    iget v1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 36
    .line 37
    int-to-float v1, v1

    .line 38
    iget v3, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 39
    .line 40
    div-int/2addr v3, v2

    .line 41
    int-to-float v2, v3

    .line 42
    add-float/2addr v1, v2

    .line 43
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    sget v3, LlZ0/g;->size_18:I

    .line 48
    .line 49
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 50
    .line 51
    .line 52
    move-result v2

    .line 53
    int-to-float v2, v2

    .line 54
    iget-object v3, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->e:Landroid/graphics/Paint;

    .line 55
    .line 56
    invoke-virtual {p1, v0, v1, v2, v3}, Landroid/graphics/Canvas;->drawCircle(FFFLandroid/graphics/Paint;)V

    .line 57
    .line 58
    .line 59
    :goto_0
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onDraw(Landroid/graphics/Canvas;)V

    .line 60
    .line 61
    .line 62
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    div-int/lit8 p1, p1, 0x2

    .line 6
    .line 7
    iget p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 8
    .line 9
    div-int/lit8 p3, p2, 0x2

    .line 10
    .line 11
    sub-int/2addr p1, p3

    .line 12
    iget-object p3, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 13
    .line 14
    iget p4, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 15
    .line 16
    add-int p5, p1, p2

    .line 17
    .line 18
    add-int/2addr p2, p4

    .line 19
    invoke-virtual {p3, p1, p4, p5, p2}, Landroid/view/View;->layout(IIII)V

    .line 20
    .line 21
    .line 22
    iget p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 23
    .line 24
    iget p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 25
    .line 26
    add-int/2addr p1, p2

    .line 27
    iget p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->i:I

    .line 28
    .line 29
    add-int/2addr p1, p2

    .line 30
    iget-object p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 31
    .line 32
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 33
    .line 34
    .line 35
    move-result p3

    .line 36
    iget-object p4, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 37
    .line 38
    invoke-virtual {p4}, Landroid/view/View;->getMeasuredHeight()I

    .line 39
    .line 40
    .line 41
    move-result p4

    .line 42
    add-int/2addr p4, p1

    .line 43
    const/4 p5, 0x0

    .line 44
    invoke-virtual {p2, p5, p1, p3, p4}, Landroid/view/View;->layout(IIII)V

    .line 45
    .line 46
    .line 47
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->j:Landroid/graphics/RectF;

    .line 48
    .line 49
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 50
    .line 51
    .line 52
    move-result-object p2

    .line 53
    sget p3, LlZ0/g;->space_4:I

    .line 54
    .line 55
    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 56
    .line 57
    .line 58
    move-result p2

    .line 59
    int-to-float p2, p2

    .line 60
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 61
    .line 62
    .line 63
    move-result p3

    .line 64
    int-to-float p3, p3

    .line 65
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 66
    .line 67
    .line 68
    move-result-object p4

    .line 69
    sget p5, LlZ0/g;->size_36:I

    .line 70
    .line 71
    invoke-virtual {p4, p5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 72
    .line 73
    .line 74
    move-result p4

    .line 75
    int-to-float p4, p4

    .line 76
    const/4 p5, 0x0

    .line 77
    invoke-virtual {p1, p5, p2, p3, p4}, Landroid/graphics/RectF;->set(FFFF)V

    .line 78
    .line 79
    .line 80
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    iget-object p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    sget v1, LlZ0/g;->size_20:I

    .line 12
    .line 13
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    const/high16 v1, 0x40000000    # 2.0f

    .line 18
    .line 19
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    sget v3, LlZ0/g;->size_20:I

    .line 28
    .line 29
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result v2

    .line 33
    invoke-static {v2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    invoke-virtual {p2, v0, v2}, Landroid/view/View;->measure(II)V

    .line 38
    .line 39
    .line 40
    iget-object p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 41
    .line 42
    invoke-virtual {p0}, Landroid/view/View;->getPaddingLeft()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    sub-int v0, p1, v0

    .line 47
    .line 48
    invoke-virtual {p0}, Landroid/view/View;->getPaddingRight()I

    .line 49
    .line 50
    .line 51
    move-result v2

    .line 52
    sub-int/2addr v0, v2

    .line 53
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    const/4 v1, 0x0

    .line 58
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 59
    .line 60
    .line 61
    move-result v1

    .line 62
    invoke-virtual {p2, v0, v1}, Landroid/view/View;->measure(II)V

    .line 63
    .line 64
    .line 65
    iget p2, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->g:I

    .line 66
    .line 67
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 68
    .line 69
    .line 70
    return-void
.end method

.method public setCoef(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setSportIcon(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->a:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setStyle(Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;)V
    .locals 3
    .param p1    # Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->d:Lorg/xbet/uikit_sport/express_card/SportEventItemStyle;

    .line 2
    .line 3
    sget-object v0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem$b;->a:[I

    .line 4
    .line 5
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    aget p1, v0, p1

    .line 10
    .line 11
    const/4 v0, 0x1

    .line 12
    const/4 v1, 0x2

    .line 13
    if-eq p1, v0, :cond_3

    .line 14
    .line 15
    if-eq p1, v1, :cond_2

    .line 16
    .line 17
    const/4 v0, 0x3

    .line 18
    if-eq p1, v0, :cond_1

    .line 19
    .line 20
    const/4 v0, 0x4

    .line 21
    if-ne p1, v0, :cond_0

    .line 22
    .line 23
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    sget v0, LlZ0/g;->space_46:I

    .line 28
    .line 29
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->g:I

    .line 34
    .line 35
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    sget v0, LlZ0/g;->size_20:I

    .line 40
    .line 41
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 42
    .line 43
    .line 44
    move-result p1

    .line 45
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 46
    .line 47
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    sget v0, LlZ0/g;->space_4:I

    .line 52
    .line 53
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 54
    .line 55
    .line 56
    move-result p1

    .line 57
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 58
    .line 59
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    sget v0, LlZ0/g;->space_4:I

    .line 64
    .line 65
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 66
    .line 67
    .line 68
    move-result p1

    .line 69
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->i:I

    .line 70
    .line 71
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 72
    .line 73
    sget v0, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 74
    .line 75
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 76
    .line 77
    .line 78
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b()V

    .line 79
    .line 80
    .line 81
    return-void

    .line 82
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 83
    .line 84
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 85
    .line 86
    .line 87
    throw p1

    .line 88
    :cond_1
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    sget v0, LlZ0/g;->size_58:I

    .line 93
    .line 94
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 95
    .line 96
    .line 97
    move-result p1

    .line 98
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->g:I

    .line 99
    .line 100
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    sget v0, LlZ0/g;->size_24:I

    .line 105
    .line 106
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 107
    .line 108
    .line 109
    move-result p1

    .line 110
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 111
    .line 112
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 113
    .line 114
    .line 115
    move-result-object p1

    .line 116
    sget v0, LlZ0/g;->space_8:I

    .line 117
    .line 118
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 119
    .line 120
    .line 121
    move-result p1

    .line 122
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 123
    .line 124
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 125
    .line 126
    .line 127
    move-result-object p1

    .line 128
    sget v0, LlZ0/g;->space_4:I

    .line 129
    .line 130
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 131
    .line 132
    .line 133
    move-result p1

    .line 134
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->i:I

    .line 135
    .line 136
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 137
    .line 138
    sget v0, LlZ0/n;->TextStyle_Caption_Bold_L_TextPrimary:I

    .line 139
    .line 140
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 141
    .line 142
    .line 143
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b()V

    .line 144
    .line 145
    .line 146
    return-void

    .line 147
    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    sget v0, LlZ0/g;->size_58:I

    .line 152
    .line 153
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 154
    .line 155
    .line 156
    move-result p1

    .line 157
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->g:I

    .line 158
    .line 159
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 160
    .line 161
    .line 162
    move-result-object p1

    .line 163
    sget v0, LlZ0/g;->size_20:I

    .line 164
    .line 165
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 166
    .line 167
    .line 168
    move-result p1

    .line 169
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 170
    .line 171
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    sget v0, LlZ0/g;->space_10:I

    .line 176
    .line 177
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 178
    .line 179
    .line 180
    move-result p1

    .line 181
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 182
    .line 183
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 184
    .line 185
    .line 186
    move-result-object p1

    .line 187
    sget v0, LlZ0/g;->space_10:I

    .line 188
    .line 189
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 190
    .line 191
    .line 192
    move-result p1

    .line 193
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->i:I

    .line 194
    .line 195
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 196
    .line 197
    sget v0, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 198
    .line 199
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 200
    .line 201
    .line 202
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b()V

    .line 203
    .line 204
    .line 205
    return-void

    .line 206
    :cond_3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    sget v0, LlZ0/g;->size_60:I

    .line 211
    .line 212
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 213
    .line 214
    .line 215
    move-result p1

    .line 216
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->g:I

    .line 217
    .line 218
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 219
    .line 220
    .line 221
    move-result-object p1

    .line 222
    sget v0, LlZ0/g;->size_20:I

    .line 223
    .line 224
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 225
    .line 226
    .line 227
    move-result p1

    .line 228
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->c:I

    .line 229
    .line 230
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 231
    .line 232
    .line 233
    move-result-object p1

    .line 234
    sget v0, LlZ0/g;->space_12:I

    .line 235
    .line 236
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 237
    .line 238
    .line 239
    move-result p1

    .line 240
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->h:I

    .line 241
    .line 242
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 243
    .line 244
    .line 245
    move-result-object p1

    .line 246
    sget v0, LlZ0/g;->space_10:I

    .line 247
    .line 248
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 249
    .line 250
    .line 251
    move-result p1

    .line 252
    iput p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->i:I

    .line 253
    .line 254
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 255
    .line 256
    sget v0, LlZ0/n;->TextStyle_Caption_Medium_M_TextPrimary:I

    .line 257
    .line 258
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 259
    .line 260
    .line 261
    iget-object p1, p0, Lorg/xbet/uikit_sport/express_card/ExpressEventItem;->b:Landroid/widget/TextView;

    .line 262
    .line 263
    const/16 v0, 0x8

    .line 264
    .line 265
    const/16 v2, 0xa

    .line 266
    .line 267
    invoke-static {p1, v0, v2, v1, v1}, LX0/o;->h(Landroid/widget/TextView;IIII)V

    .line 268
    .line 269
    .line 270
    return-void
.end method
