.class public final Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lwb1/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0018\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0096@\u00a2\u0006\u0004\u0008\u000b\u0010\u000cR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010\rR\u001b\u0010\u0013\u001a\u00020\u000e8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010\u0010\u001a\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0014"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;",
        "Lwb1/a;",
        "Lc8/h;",
        "requestParamsDataSource",
        "Lf8/g;",
        "serviceGenerator",
        "<init>",
        "(Lc8/h;Lf8/g;)V",
        "",
        "currencyId",
        "Lvb1/a;",
        "a",
        "(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lc8/h;",
        "Ltb1/a;",
        "b",
        "Lkotlin/j;",
        "c",
        "()Ltb1/a;",
        "service",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lc8/h;Lf8/g;)V
    .locals 0
    .param p1    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lf8/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->a:Lc8/h;

    .line 5
    .line 6
    new-instance p1, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/a;

    .line 7
    .line 8
    invoke-direct {p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/a;-><init>(Lf8/g;)V

    .line 9
    .line 10
    .line 11
    invoke-static {p1}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->b:Lkotlin/j;

    .line 16
    .line 17
    return-void
.end method

.method public static synthetic b(Lf8/g;)Ltb1/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->d(Lf8/g;)Ltb1/a;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Lf8/g;)Ltb1/a;
    .locals 1

    .line 1
    const-class v0, Ltb1/a;

    .line 2
    .line 3
    invoke-static {v0}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {p0, v0}, Lf8/g;->c(Lkotlin/reflect/d;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    check-cast p0, Ltb1/a;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public a(Ljava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lvb1/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p2, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p2

    .line 6
    check-cast v0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;->label:I

    .line 18
    .line 19
    :goto_0
    move-object v6, v0

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;

    .line 22
    .line 23
    invoke-direct {v0, p0, p2}, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;-><init>(Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;Lkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :goto_1
    iget-object p2, v6, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;->result:Ljava/lang/Object;

    .line 28
    .line 29
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iget v1, v6, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;->label:I

    .line 34
    .line 35
    const/4 v2, 0x1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    if-ne v1, v2, :cond_1

    .line 39
    .line 40
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    goto :goto_2

    .line 44
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 45
    .line 46
    const-string p2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 47
    .line 48
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p1

    .line 52
    :cond_2
    invoke-static {p2}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 53
    .line 54
    .line 55
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->c()Ltb1/a;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    iget-object p2, p0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->a:Lc8/h;

    .line 60
    .line 61
    invoke-interface {p2}, Lc8/h;->b()I

    .line 62
    .line 63
    .line 64
    move-result p2

    .line 65
    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->a:Lc8/h;

    .line 66
    .line 67
    invoke-interface {v3}, Lc8/h;->c()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    iput v2, v6, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl$getTvBetJackpotInfo$1;->label:I

    .line 72
    .line 73
    const/4 v5, 0x0

    .line 74
    const/16 v7, 0x8

    .line 75
    .line 76
    const/4 v8, 0x0

    .line 77
    move-object v4, p1

    .line 78
    move v2, p2

    .line 79
    invoke-static/range {v1 .. v8}, Ltb1/a$a;->a(Ltb1/a;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 80
    .line 81
    .line 82
    move-result-object p2

    .line 83
    if-ne p2, v0, :cond_3

    .line 84
    .line 85
    return-object v0

    .line 86
    :cond_3
    :goto_2
    check-cast p2, Lsb1/a;

    .line 87
    .line 88
    invoke-static {p2}, Lrb1/a;->f(Lsb1/a;)Lvb1/a;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    return-object p1
.end method

.method public final c()Ltb1/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/data/repositories/TvBetJackpotRepositoryImpl;->b:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ltb1/a;

    .line 8
    .line 9
    return-object v0
.end method
