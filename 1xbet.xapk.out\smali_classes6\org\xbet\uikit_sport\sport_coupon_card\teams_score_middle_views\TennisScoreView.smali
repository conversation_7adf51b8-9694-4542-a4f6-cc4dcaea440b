.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LZ31/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0086\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0010\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0010\u000b\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0018\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008!\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ!\u0010\u000f\u001a\u00020\u000e*\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u0006\u0010\r\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0011\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0013\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000bH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001d\u0010\u0016\u001a\u00020\u000e2\u000c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000bH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u001d\u0010\u0018\u001a\u00020\u00072\u000c\u0010\u0015\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000bH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u000f\u0010\u001a\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0012J\u000f\u0010\u001b\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0012J\u000f\u0010\u001c\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u000f\u0010\u001e\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0012J1\u0010&\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u001f2\u0008\u0010\"\u001a\u0004\u0018\u00010!2\u0006\u0010$\u001a\u00020#2\u0006\u0010%\u001a\u00020#H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J!\u0010+\u001a\u0004\u0018\u00010!2\u0006\u0010)\u001a\u00020(2\u0006\u0010*\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008+\u0010,J%\u00100\u001a\u00020\u000e2\u000c\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b2\u0006\u0010/\u001a\u00020.H\u0002\u00a2\u0006\u0004\u00080\u00101J\u0015\u00102\u001a\u00020\u0007*\u0004\u0018\u00010!H\u0002\u00a2\u0006\u0004\u00082\u00103J\u0015\u00104\u001a\u00020\u0007*\u0004\u0018\u00010!H\u0002\u00a2\u0006\u0004\u00084\u00103J\u001f\u00107\u001a\u00020\u000e2\u0006\u00105\u001a\u00020\u00072\u0006\u00106\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u00087\u00108J7\u0010?\u001a\u00020\u000e2\u0006\u0010:\u001a\u0002092\u0006\u0010;\u001a\u00020\u00072\u0006\u0010<\u001a\u00020\u00072\u0006\u0010=\u001a\u00020\u00072\u0006\u0010>\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008?\u0010@J\u0017\u0010A\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020\u001fH\u0014\u00a2\u0006\u0004\u0008A\u0010BJ\u0017\u0010E\u001a\u00020\u000e2\u0006\u0010D\u001a\u00020CH\u0016\u00a2\u0006\u0004\u0008E\u0010FR\u0016\u0010I\u001a\u00020(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0016\u0010K\u001a\u00020(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008J\u0010HR\u0016\u0010M\u001a\u00020(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008L\u0010HR\u0016\u0010Q\u001a\u00020N8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0016\u0010T\u001a\u00020.8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0016\u0010U\u001a\u00020.8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010SR\u0016\u0010V\u001a\u00020.8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010SR\u0014\u0010X\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010WR\u0014\u0010Z\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010WR\u0014\u0010[\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u0010WR\u0014\u0010\\\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u0010WR\u0014\u0010]\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010WR\u0014\u0010^\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008+\u0010WR\u0014\u0010_\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010WR!\u0010b\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u000f\u0010`\u001a\u0004\u0008a\u0010\u0014R!\u0010d\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010`\u001a\u0004\u0008c\u0010\u0014R!\u0010f\u001a\u0008\u0012\u0004\u0012\u00020\u000c0\u000b8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0011\u0010`\u001a\u0004\u0008e\u0010\u0014R\u001b\u0010k\u001a\u00020g8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008h\u0010`\u001a\u0004\u0008i\u0010jR#\u0010q\u001a\n m*\u0004\u0018\u00010l0l8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008n\u0010`\u001a\u0004\u0008o\u0010pR\u0014\u0010t\u001a\u00020r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00080\u0010sR\u0016\u0010v\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008u\u0010WR\u0016\u0010x\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008w\u0010WR\u0018\u0010{\u001a\u0004\u0018\u00010!8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008y\u0010zR\u0018\u0010}\u001a\u0004\u0018\u00010!8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008|\u0010zR\u0018\u0010\u007f\u001a\u0004\u0018\u00010!8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008~\u0010zR\u0018\u0010\u0081\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0080\u0001\u0010WR\u0018\u0010\u0083\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0082\u0001\u0010WR\u0018\u0010\u0085\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0084\u0001\u0010WR\u0018\u0010\u0087\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0086\u0001\u0010WR\u0018\u0010\u0089\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0088\u0001\u0010WR\u0018\u0010\u008b\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u008a\u0001\u0010WR\u0018\u0010\u008d\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u008c\u0001\u0010WR\u0018\u0010\u008f\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u008e\u0001\u0010WR\u0018\u0010\u0091\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u0090\u0001\u0010WR\u0017\u0010\u0092\u0001\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008W\u0010W\u00a8\u0006\u0093\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;",
        "Landroid/widget/FrameLayout;",
        "LZ31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
        "leftPosition",
        "",
        "o",
        "(Ljava/util/List;I)V",
        "q",
        "()V",
        "getNewScoreCellsColumn",
        "()Ljava/util/List;",
        "scoreCellsColumn",
        "p",
        "(Ljava/util/List;)V",
        "l",
        "(Ljava/util/List;)I",
        "g",
        "f",
        "getTotalMeasuredHeight",
        "()I",
        "n",
        "Landroid/graphics/Canvas;",
        "canvas",
        "Landroid/text/StaticLayout;",
        "textStaticLayout",
        "",
        "positionX",
        "positionY",
        "h",
        "(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V",
        "",
        "scoreCellsTitle",
        "width",
        "m",
        "(Ljava/lang/String;I)Landroid/text/StaticLayout;",
        "cellsColumn",
        "LX31/d;",
        "scoreColumnModel",
        "t",
        "(Ljava/util/List;LX31/d;)V",
        "j",
        "(Landroid/text/StaticLayout;)I",
        "k",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "a",
        "Ljava/lang/String;",
        "gameScoreCellTitle",
        "b",
        "setScoreCellTitle",
        "c",
        "totalScoreCellTitle",
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;",
        "d",
        "Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;",
        "teamServe",
        "e",
        "LX31/d;",
        "gameScoreUiModelsColumn",
        "setScoreUiModelsColumn",
        "totalScoreUiModelsColumn",
        "I",
        "serveIndicatorSize",
        "i",
        "scoreCellWidth",
        "scoreCellHeight",
        "space2",
        "space4",
        "scoreCellsBetweenVerticalMargin",
        "serveIndicatorOccupiedWidth",
        "Lkotlin/j;",
        "getGameScoreCellsColumn",
        "gameScoreCellsColumn",
        "getSetScoreCellsColumn",
        "setScoreCellsColumn",
        "getTotalScoreCellsColumn",
        "totalScoreCellsColumn",
        "Landroid/widget/ImageView;",
        "r",
        "getServeIndicator",
        "()Landroid/widget/ImageView;",
        "serveIndicator",
        "Landroid/view/animation/Animation;",
        "kotlin.jvm.PlatformType",
        "s",
        "getRotateAnimation",
        "()Landroid/view/animation/Animation;",
        "rotateAnimation",
        "Landroid/text/TextPaint;",
        "Landroid/text/TextPaint;",
        "scoreCellsColumnTitleTextPaint",
        "u",
        "topScoreCellTopPosition",
        "v",
        "bottomScoreCellTopPosition",
        "w",
        "Landroid/text/StaticLayout;",
        "gameScoreCellsTitleStaticLayout",
        "x",
        "setScoreCellsTitleStaticLayout",
        "y",
        "totalScoreCellsTitleStaticLayout",
        "z",
        "serveIndicatorTopPosition",
        "A",
        "gameScoreCellsColumnOccupiedWidth",
        "B",
        "setScoreCellsColumnOccupiedWidth",
        "C",
        "totalScoreCellsColumnOccupiedWidth",
        "D",
        "gameScoreCellLeftPosition",
        "E",
        "setScoreCellLeftPosition",
        "F",
        "totalScoreCellLeftPosition",
        "G",
        "gameScoreTitleLeftPosition",
        "H",
        "setScoreTitleLeftPosition",
        "totalScoreTitleLeftPosition",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public A:I

.field public B:I

.field public C:I

.field public D:I

.field public E:I

.field public F:I

.field public G:I

.field public H:I

.field public I:I

.field public a:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:LX31/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:LX31/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:LX31/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final t:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public u:I

.field public v:I

.field public w:Landroid/text/StaticLayout;

.field public x:Landroid/text/StaticLayout;

.field public y:Landroid/text/StaticLayout;

.field public z:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    const-string p2, ""

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->a:Ljava/lang/String;

    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->b:Ljava/lang/String;

    .line 7
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->c:Ljava/lang/String;

    .line 8
    sget-object p2, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->NO_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 9
    sget-object p2, LX31/d;->f:LX31/d$a;

    invoke-virtual {p2}, LX31/d$a;->a()LX31/d;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->e:LX31/d;

    .line 10
    invoke-virtual {p2}, LX31/d$a;->a()LX31/d;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f:LX31/d;

    .line 11
    invoke-virtual {p2}, LX31/d$a;->a()LX31/d;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g:LX31/d;

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_14:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->h:I

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_28:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->i:I

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->size_24:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->j:I

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p3

    sget v0, LlZ0/g;->space_2:I

    invoke-virtual {p3, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p3

    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k:I

    .line 16
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_4:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l:I

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->space_8:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->m:I

    mul-int/lit8 p3, p3, 0x2

    add-int/2addr p3, p2

    .line 18
    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->n:I

    .line 19
    sget-object p2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    new-instance p3, LZ31/e;

    invoke-direct {p3, p0}, LZ31/e;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)V

    invoke-static {p2, p3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->o:Lkotlin/j;

    .line 20
    new-instance p3, LZ31/f;

    invoke-direct {p3, p0}, LZ31/f;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)V

    invoke-static {p2, p3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->p:Lkotlin/j;

    .line 21
    new-instance p3, LZ31/g;

    invoke-direct {p3, p0}, LZ31/g;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)V

    invoke-static {p2, p3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->q:Lkotlin/j;

    .line 22
    new-instance p3, LZ31/h;

    invoke-direct {p3, p1, p0}, LZ31/h;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)V

    invoke-static {p2, p3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p3

    iput-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->r:Lkotlin/j;

    .line 23
    new-instance p3, LZ31/i;

    invoke-direct {p3, p1}, LZ31/i;-><init>(Landroid/content/Context;)V

    invoke-static {p2, p3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->s:Lkotlin/j;

    .line 24
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2}, Landroid/text/TextPaint;-><init>()V

    const/4 p3, 0x1

    .line 25
    invoke-virtual {p2, p3}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 26
    sget p3, LlZ0/n;->TextStyle_Caption_Regular_M_TextPrimary:I

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 27
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t:Landroid/text/TextPaint;

    const/4 p1, 0x0

    .line 28
    invoke-virtual {p0, p1}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->v(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->u(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;)Landroid/view/animation/Animation;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->r(Landroid/content/Context;)Landroid/view/animation/Animation;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->i(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Landroid/widget/ImageView;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->s(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Landroid/widget/ImageView;

    move-result-object p0

    return-object p0
.end method

.method private final getGameScoreCellsColumn()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->o:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/util/List;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getNewScoreCellsColumn()Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget v2, Lm31/f;->Widget_ScoreCell_TennisScore:I

    .line 8
    .line 9
    invoke-direct {v1, v0, v2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 10
    .line 11
    .line 12
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 13
    .line 14
    const/4 v4, 0x6

    .line 15
    const/4 v5, 0x0

    .line 16
    const/4 v2, 0x0

    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 19
    .line 20
    .line 21
    move-object v6, v0

    .line 22
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 23
    .line 24
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 25
    .line 26
    .line 27
    const/4 v1, 0x2

    .line 28
    new-array v1, v1, [Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 29
    .line 30
    const/4 v2, 0x0

    .line 31
    aput-object v6, v1, v2

    .line 32
    .line 33
    const/4 v2, 0x1

    .line 34
    aput-object v0, v1, v2

    .line 35
    .line 36
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v2

    .line 48
    if-eqz v2, :cond_0

    .line 49
    .line 50
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    check-cast v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 55
    .line 56
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 57
    .line 58
    .line 59
    goto :goto_0

    .line 60
    :cond_0
    return-object v0
.end method

.method private final getRotateAnimation()Landroid/view/animation/Animation;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->s:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/view/animation/Animation;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getServeIndicator()Landroid/widget/ImageView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->r:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/widget/ImageView;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSetScoreCellsColumn()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->p:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/util/List;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTotalMeasuredHeight()I
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->y:Landroid/text/StaticLayout;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->j(Landroid/text/StaticLayout;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->a:Ljava/lang/String;

    .line 12
    .line 13
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    const/4 v2, 0x0

    .line 18
    if-lez v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->b:Ljava/lang/String;

    .line 22
    .line 23
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    if-lez v1, :cond_1

    .line 28
    .line 29
    goto :goto_0

    .line 30
    :cond_1
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->c:Ljava/lang/String;

    .line 31
    .line 32
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    if-lez v1, :cond_2

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_2
    move-object v0, v2

    .line 40
    :goto_0
    const/4 v1, 0x0

    .line 41
    if-eqz v0, :cond_3

    .line 42
    .line 43
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    goto :goto_1

    .line 48
    :cond_3
    const/4 v0, 0x0

    .line 49
    :goto_1
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l:I

    .line 50
    .line 51
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->j:I

    .line 52
    .line 53
    mul-int/lit8 v4, v4, 0x2

    .line 54
    .line 55
    add-int/2addr v3, v4

    .line 56
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->m:I

    .line 57
    .line 58
    add-int/2addr v3, v4

    .line 59
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 60
    .line 61
    .line 62
    move-result-object v3

    .line 63
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 64
    .line 65
    if-gtz v4, :cond_4

    .line 66
    .line 67
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 68
    .line 69
    if-gtz v4, :cond_4

    .line 70
    .line 71
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->C:I

    .line 72
    .line 73
    if-lez v4, :cond_5

    .line 74
    .line 75
    :cond_4
    move-object v2, v3

    .line 76
    :cond_5
    if-eqz v2, :cond_6

    .line 77
    .line 78
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 79
    .line 80
    .line 81
    move-result v1

    .line 82
    :cond_6
    add-int/2addr v0, v1

    .line 83
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l:I

    .line 84
    .line 85
    add-int/2addr v0, v1

    .line 86
    return v0
.end method

.method private final getTotalScoreCellsColumn()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->q:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Ljava/util/List;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final i(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getNewScoreCellsColumn()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final r(Landroid/content/Context;)Landroid/view/animation/Animation;
    .locals 1

    .line 1
    sget v0, Lm31/a;->coupon_card_game_indicator_rotate_animation:I

    .line 2
    .line 3
    invoke-static {p0, v0}, Landroid/view/animation/AnimationUtils;->loadAnimation(Landroid/content/Context;I)Landroid/view/animation/Animation;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    return-object p0
.end method

.method public static final s(Landroid/content/Context;Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Landroid/widget/ImageView;
    .locals 4

    .line 1
    new-instance v0, Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 4
    .line 5
    .line 6
    sget v1, LlZ0/h;->ic_game_indicator:I

    .line 7
    .line 8
    invoke-static {p0, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v0, v1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 13
    .line 14
    .line 15
    sget v1, LlZ0/d;->uikitSecondary:I

    .line 16
    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x2

    .line 19
    invoke-static {p0, v1, v2, v3, v2}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    .line 20
    .line 21
    .line 22
    move-result p0

    .line 23
    invoke-virtual {v0, p0}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 27
    .line 28
    .line 29
    return-object v0
.end method

.method public static final u(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getNewScoreCellsColumn()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final v(Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getNewScoreCellsColumn()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public final f()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->y:Landroid/text/StaticLayout;

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->j(Landroid/text/StaticLayout;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l:I

    .line 8
    .line 9
    add-int/2addr v0, v1

    .line 10
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->u:I

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->j:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->m:I

    .line 16
    .line 17
    add-int/2addr v0, v1

    .line 18
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->v:I

    .line 19
    .line 20
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->n:I

    .line 21
    .line 22
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 27
    .line 28
    sget-object v2, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->NO_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 29
    .line 30
    if-eq v1, v2, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_0
    const/4 v0, 0x0

    .line 34
    :goto_0
    if-eqz v0, :cond_1

    .line 35
    .line 36
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    goto :goto_1

    .line 41
    :cond_1
    const/4 v0, 0x0

    .line 42
    :goto_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k:I

    .line 43
    .line 44
    add-int v2, v0, v1

    .line 45
    .line 46
    iput v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->D:I

    .line 47
    .line 48
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 49
    .line 50
    add-int v3, v0, v2

    .line 51
    .line 52
    add-int/2addr v3, v1

    .line 53
    iput v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->E:I

    .line 54
    .line 55
    add-int/2addr v0, v2

    .line 56
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 57
    .line 58
    add-int/2addr v0, v2

    .line 59
    add-int/2addr v0, v1

    .line 60
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->F:I

    .line 61
    .line 62
    return-void
.end method

.method public final g()V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->n:I

    .line 2
    .line 3
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 8
    .line 9
    sget-object v2, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->NO_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 10
    .line 11
    if-eq v1, v2, :cond_0

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    :goto_0
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    const/4 v0, 0x0

    .line 23
    :goto_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 24
    .line 25
    if-lez v1, :cond_2

    .line 26
    .line 27
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->w:Landroid/text/StaticLayout;

    .line 28
    .line 29
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 30
    .line 31
    .line 32
    move-result v1

    .line 33
    if-lez v1, :cond_2

    .line 34
    .line 35
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 36
    .line 37
    div-int/lit8 v1, v1, 0x2

    .line 38
    .line 39
    add-int/2addr v1, v0

    .line 40
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->w:Landroid/text/StaticLayout;

    .line 41
    .line 42
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 43
    .line 44
    .line 45
    move-result v2

    .line 46
    div-int/lit8 v2, v2, 0x2

    .line 47
    .line 48
    sub-int/2addr v1, v2

    .line 49
    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->G:I

    .line 50
    .line 51
    :cond_2
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 52
    .line 53
    if-lez v1, :cond_3

    .line 54
    .line 55
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->x:Landroid/text/StaticLayout;

    .line 56
    .line 57
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 58
    .line 59
    .line 60
    move-result v1

    .line 61
    if-lez v1, :cond_3

    .line 62
    .line 63
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 64
    .line 65
    add-int/2addr v1, v0

    .line 66
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 67
    .line 68
    div-int/lit8 v2, v2, 0x2

    .line 69
    .line 70
    add-int/2addr v1, v2

    .line 71
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->x:Landroid/text/StaticLayout;

    .line 72
    .line 73
    invoke-virtual {p0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 74
    .line 75
    .line 76
    move-result v2

    .line 77
    div-int/lit8 v2, v2, 0x2

    .line 78
    .line 79
    sub-int/2addr v1, v2

    .line 80
    iput v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->H:I

    .line 81
    .line 82
    :cond_3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->C:I

    .line 83
    .line 84
    if-lez v1, :cond_4

    .line 85
    .line 86
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->y:Landroid/text/StaticLayout;

    .line 87
    .line 88
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 89
    .line 90
    .line 91
    move-result v1

    .line 92
    if-lez v1, :cond_4

    .line 93
    .line 94
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 95
    .line 96
    add-int/2addr v0, v1

    .line 97
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 98
    .line 99
    add-int/2addr v0, v1

    .line 100
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->C:I

    .line 101
    .line 102
    div-int/lit8 v1, v1, 0x2

    .line 103
    .line 104
    add-int/2addr v0, v1

    .line 105
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->y:Landroid/text/StaticLayout;

    .line 106
    .line 107
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 108
    .line 109
    .line 110
    move-result v1

    .line 111
    div-int/lit8 v1, v1, 0x2

    .line 112
    .line 113
    sub-int/2addr v0, v1

    .line 114
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->I:I

    .line 115
    .line 116
    :cond_4
    return-void
.end method

.method public final h(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k(Landroid/text/StaticLayout;)I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    sub-int/2addr v0, v1

    .line 17
    int-to-float v0, v0

    .line 18
    sub-float p3, v0, p3

    .line 19
    .line 20
    :cond_0
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 21
    .line 22
    .line 23
    invoke-virtual {p1, p3, p4}, Landroid/graphics/Canvas;->translate(FF)V

    .line 24
    .line 25
    .line 26
    if-eqz p2, :cond_1

    .line 27
    .line 28
    invoke-virtual {p2, p1}, Landroid/text/Layout;->draw(Landroid/graphics/Canvas;)V

    .line 29
    .line 30
    .line 31
    :cond_1
    invoke-virtual {p1}, Landroid/graphics/Canvas;->restore()V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final j(Landroid/text/StaticLayout;)I
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/text/Layout;->getHeight()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    return p1
.end method

.method public final k(Landroid/text/StaticLayout;)I
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/text/Layout;->getWidth()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    return p1
.end method

.method public final l(Ljava/util/List;)I
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;)I"
        }
    .end annotation

    .line 1
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 6
    .line 7
    const/4 v0, 0x0

    .line 8
    if-eqz p1, :cond_0

    .line 9
    .line 10
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 p1, 0x0

    .line 16
    :goto_0
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k:I

    .line 17
    .line 18
    mul-int/lit8 v1, v1, 0x2

    .line 19
    .line 20
    add-int/2addr v1, p1

    .line 21
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    if-lez p1, :cond_1

    .line 26
    .line 27
    goto :goto_1

    .line 28
    :cond_1
    const/4 v1, 0x0

    .line 29
    :goto_1
    if-eqz v1, :cond_2

    .line 30
    .line 31
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    return p1

    .line 36
    :cond_2
    return v0
.end method

.method public final m(Ljava/lang/String;I)Landroid/text/StaticLayout;
    .locals 15

    .line 1
    invoke-interface/range {p1 .. p1}, Ljava/lang/CharSequence;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-lez v0, :cond_0

    .line 6
    .line 7
    invoke-static/range {p1 .. p1}, Lorg/xbet/uikit/utils/g;->a(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t:Landroid/text/TextPaint;

    .line 12
    .line 13
    sget-object v10, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 14
    .line 15
    const/16 v13, 0xdf0

    .line 16
    .line 17
    const/4 v14, 0x0

    .line 18
    const/4 v4, 0x1

    .line 19
    const/4 v5, 0x0

    .line 20
    const/4 v6, 0x0

    .line 21
    const/4 v7, 0x0

    .line 22
    const/4 v8, 0x0

    .line 23
    const/4 v9, 0x0

    .line 24
    const/4 v11, 0x0

    .line 25
    const/4 v12, 0x0

    .line 26
    move/from16 v3, p2

    .line 27
    .line 28
    invoke-static/range {v1 .. v14}, Lorg/xbet/uikit/utils/H;->d(Ljava/lang/CharSequence;Landroid/text/TextPaint;IIIIFFZLandroid/text/TextUtils$TruncateAt;ILandroid/text/Layout$Alignment;ILjava/lang/Object;)Landroid/text/StaticLayout;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    return-object v0

    .line 33
    :cond_0
    const/4 v0, 0x0

    .line 34
    return-object v0
.end method

.method public final n()V
    .locals 5

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->a:Ljava/lang/String;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-lez v1, :cond_0

    .line 7
    .line 8
    move-object v1, v0

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    move-object v1, v2

    .line 11
    :goto_0
    const-string v3, ""

    .line 12
    .line 13
    if-nez v1, :cond_1

    .line 14
    .line 15
    move-object v1, v3

    .line 16
    :cond_1
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t:Landroid/text/TextPaint;

    .line 17
    .line 18
    invoke-virtual {v4, v0}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 19
    .line 20
    .line 21
    move-result v0

    .line 22
    float-to-int v0, v0

    .line 23
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 24
    .line 25
    invoke-static {v0, v4}, Ljava/lang/Math;->min(II)I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-virtual {p0, v1, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->m(Ljava/lang/String;I)Landroid/text/StaticLayout;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->w:Landroid/text/StaticLayout;

    .line 34
    .line 35
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->b:Ljava/lang/String;

    .line 36
    .line 37
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 38
    .line 39
    if-lez v1, :cond_2

    .line 40
    .line 41
    move-object v2, v0

    .line 42
    :cond_2
    if-nez v2, :cond_3

    .line 43
    .line 44
    goto :goto_1

    .line 45
    :cond_3
    move-object v3, v2

    .line 46
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t:Landroid/text/TextPaint;

    .line 47
    .line 48
    invoke-virtual {v1, v0}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    float-to-int v0, v0

    .line 53
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 54
    .line 55
    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    .line 56
    .line 57
    .line 58
    move-result v0

    .line 59
    invoke-virtual {p0, v3, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->m(Ljava/lang/String;I)Landroid/text/StaticLayout;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->x:Landroid/text/StaticLayout;

    .line 64
    .line 65
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->c:Ljava/lang/String;

    .line 66
    .line 67
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t:Landroid/text/TextPaint;

    .line 68
    .line 69
    invoke-virtual {v1, v0}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    float-to-int v1, v1

    .line 74
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->C:I

    .line 75
    .line 76
    invoke-static {v1, v2}, Ljava/lang/Math;->min(II)I

    .line 77
    .line 78
    .line 79
    move-result v1

    .line 80
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->m(Ljava/lang/String;I)Landroid/text/StaticLayout;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->y:Landroid/text/StaticLayout;

    .line 85
    .line 86
    return-void
.end method

.method public final o(Ljava/util/List;I)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;I)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    const/4 v0, 0x0

    .line 6
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    add-int/lit8 v2, v0, 0x1

    .line 17
    .line 18
    if-gez v0, :cond_0

    .line 19
    .line 20
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 21
    .line 22
    .line 23
    :cond_0
    move-object v4, v1

    .line 24
    check-cast v4, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 25
    .line 26
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->u:I

    .line 27
    .line 28
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    if-nez v0, :cond_1

    .line 33
    .line 34
    goto :goto_1

    .line 35
    :cond_1
    const/4 v1, 0x0

    .line 36
    :goto_1
    if-eqz v1, :cond_2

    .line 37
    .line 38
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    :goto_2
    move v6, v0

    .line 43
    goto :goto_3

    .line 44
    :cond_2
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->v:I

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :goto_3
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    add-int v7, p2, v0

    .line 52
    .line 53
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    add-int v8, v6, v0

    .line 58
    .line 59
    move-object v3, p0

    .line 60
    move v5, p2

    .line 61
    invoke-static/range {v3 .. v8}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 62
    .line 63
    .line 64
    move v0, v2

    .line 65
    goto :goto_0

    .line 66
    :cond_3
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-lez v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-lez v0, :cond_0

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->w:Landroid/text/StaticLayout;

    .line 14
    .line 15
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->G:I

    .line 16
    .line 17
    int-to-float v1, v1

    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->h(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->x:Landroid/text/StaticLayout;

    .line 23
    .line 24
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->H:I

    .line 25
    .line 26
    int-to-float v1, v1

    .line 27
    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->h(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->y:Landroid/text/StaticLayout;

    .line 31
    .line 32
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->I:I

    .line 33
    .line 34
    int-to-float v1, v1

    .line 35
    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->h(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V

    .line 36
    .line 37
    .line 38
    :cond_0
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 6

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-lez p1, :cond_4

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-lez p1, :cond_4

    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->e:LX31/d;

    .line 14
    .line 15
    invoke-virtual {p1}, LX31/d;->a()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    if-lez p1, :cond_0

    .line 24
    .line 25
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->e:LX31/d;

    .line 26
    .line 27
    invoke-virtual {p1}, LX31/d;->c()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    if-lez p1, :cond_0

    .line 36
    .line 37
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getGameScoreCellsColumn()Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->D:I

    .line 42
    .line 43
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->o(Ljava/util/List;I)V

    .line 44
    .line 45
    .line 46
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f:LX31/d;

    .line 47
    .line 48
    invoke-virtual {p1}, LX31/d;->a()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    if-lez p1, :cond_1

    .line 57
    .line 58
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f:LX31/d;

    .line 59
    .line 60
    invoke-virtual {p1}, LX31/d;->c()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 65
    .line 66
    .line 67
    move-result p1

    .line 68
    if-lez p1, :cond_1

    .line 69
    .line 70
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getSetScoreCellsColumn()Ljava/util/List;

    .line 71
    .line 72
    .line 73
    move-result-object p1

    .line 74
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->E:I

    .line 75
    .line 76
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->o(Ljava/util/List;I)V

    .line 77
    .line 78
    .line 79
    :cond_1
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g:LX31/d;

    .line 80
    .line 81
    invoke-virtual {p1}, LX31/d;->a()Ljava/lang/String;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    if-lez p1, :cond_2

    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g:LX31/d;

    .line 92
    .line 93
    invoke-virtual {p1}, LX31/d;->c()Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 98
    .line 99
    .line 100
    move-result p1

    .line 101
    if-lez p1, :cond_2

    .line 102
    .line 103
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getTotalScoreCellsColumn()Ljava/util/List;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->F:I

    .line 108
    .line 109
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->o(Ljava/util/List;I)V

    .line 110
    .line 111
    .line 112
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 113
    .line 114
    sget-object p2, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->NO_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 115
    .line 116
    if-eq p1, p2, :cond_3

    .line 117
    .line 118
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getServeIndicator()Landroid/widget/ImageView;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->k:I

    .line 123
    .line 124
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->z:I

    .line 125
    .line 126
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->h:I

    .line 127
    .line 128
    add-int v4, v2, p1

    .line 129
    .line 130
    add-int v5, v3, p1

    .line 131
    .line 132
    move-object v0, p0

    .line 133
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 134
    .line 135
    .line 136
    :cond_3
    invoke-virtual {p0}, Landroid/view/View;->invalidate()V

    .line 137
    .line 138
    .line 139
    :cond_4
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->q()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->n()V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f()V

    .line 11
    .line 12
    .line 13
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 14
    .line 15
    sget-object p2, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->FIRST_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 16
    .line 17
    if-ne p1, p2, :cond_0

    .line 18
    .line 19
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->u:I

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->v:I

    .line 23
    .line 24
    :goto_0
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->j:I

    .line 25
    .line 26
    div-int/lit8 p2, p2, 0x2

    .line 27
    .line 28
    add-int/2addr p1, p2

    .line 29
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->h:I

    .line 30
    .line 31
    div-int/lit8 p2, p2, 0x2

    .line 32
    .line 33
    sub-int/2addr p1, p2

    .line 34
    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->z:I

    .line 35
    .line 36
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->n:I

    .line 37
    .line 38
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 43
    .line 44
    sget-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->NO_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 45
    .line 46
    if-eq p2, v0, :cond_1

    .line 47
    .line 48
    goto :goto_1

    .line 49
    :cond_1
    const/4 p1, 0x0

    .line 50
    :goto_1
    if-eqz p1, :cond_2

    .line 51
    .line 52
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    goto :goto_2

    .line 57
    :cond_2
    const/4 p1, 0x0

    .line 58
    :goto_2
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 59
    .line 60
    add-int/2addr p1, p2

    .line 61
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 62
    .line 63
    add-int/2addr p1, p2

    .line 64
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->C:I

    .line 65
    .line 66
    add-int/2addr p1, p2

    .line 67
    const/high16 p2, 0x40000000    # 2.0f

    .line 68
    .line 69
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getTotalMeasuredHeight()I

    .line 74
    .line 75
    .line 76
    move-result v0

    .line 77
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 78
    .line 79
    .line 80
    move-result p2

    .line 81
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method public final p(Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    check-cast v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 16
    .line 17
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->i:I

    .line 18
    .line 19
    const/high16 v2, 0x40000000    # 2.0f

    .line 20
    .line 21
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    const/4 v2, 0x0

    .line 26
    invoke-static {v2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 31
    .line 32
    .line 33
    goto :goto_0

    .line 34
    :cond_0
    return-void
.end method

.method public final q()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->e:LX31/d;

    .line 2
    .line 3
    invoke-virtual {v0}, LX31/d;->a()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-lez v0, :cond_0

    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->e:LX31/d;

    .line 14
    .line 15
    invoke-virtual {v0}, LX31/d;->c()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-lez v0, :cond_0

    .line 24
    .line 25
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getGameScoreCellsColumn()Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->p(Ljava/util/List;)V

    .line 30
    .line 31
    .line 32
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getGameScoreCellsColumn()Ljava/util/List;

    .line 33
    .line 34
    .line 35
    move-result-object v0

    .line 36
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l(Ljava/util/List;)I

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->A:I

    .line 41
    .line 42
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f:LX31/d;

    .line 43
    .line 44
    invoke-virtual {v0}, LX31/d;->a()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 49
    .line 50
    .line 51
    move-result v0

    .line 52
    if-lez v0, :cond_1

    .line 53
    .line 54
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f:LX31/d;

    .line 55
    .line 56
    invoke-virtual {v0}, LX31/d;->c()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 61
    .line 62
    .line 63
    move-result v0

    .line 64
    if-lez v0, :cond_1

    .line 65
    .line 66
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getSetScoreCellsColumn()Ljava/util/List;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->p(Ljava/util/List;)V

    .line 71
    .line 72
    .line 73
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getSetScoreCellsColumn()Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l(Ljava/util/List;)I

    .line 78
    .line 79
    .line 80
    move-result v0

    .line 81
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->B:I

    .line 82
    .line 83
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g:LX31/d;

    .line 84
    .line 85
    invoke-virtual {v0}, LX31/d;->a()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object v0

    .line 89
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 90
    .line 91
    .line 92
    move-result v0

    .line 93
    if-lez v0, :cond_2

    .line 94
    .line 95
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g:LX31/d;

    .line 96
    .line 97
    invoke-virtual {v0}, LX31/d;->c()Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v0

    .line 101
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 102
    .line 103
    .line 104
    move-result v0

    .line 105
    if-lez v0, :cond_2

    .line 106
    .line 107
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getTotalScoreCellsColumn()Ljava/util/List;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->p(Ljava/util/List;)V

    .line 112
    .line 113
    .line 114
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getTotalScoreCellsColumn()Ljava/util/List;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->l(Ljava/util/List;)I

    .line 119
    .line 120
    .line 121
    move-result v0

    .line 122
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->C:I

    .line 123
    .line 124
    :cond_2
    return-void
.end method

.method public setScoreUiModel(LX31/a;)V
    .locals 2
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/a$f;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LX31/a$f;

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    :goto_0
    if-nez p1, :cond_1

    .line 10
    .line 11
    return-void

    .line 12
    :cond_1
    invoke-virtual {p1}, LX31/a$f;->a()LX31/d;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {v0}, LX31/d;->e()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->a:Ljava/lang/String;

    .line 21
    .line 22
    invoke-virtual {p1}, LX31/a$f;->a()LX31/d;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->e:LX31/d;

    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getGameScoreCellsColumn()Ljava/util/List;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {p1}, LX31/a$f;->a()LX31/d;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t(Ljava/util/List;LX31/d;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {p1}, LX31/a$f;->c()LX31/d;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-virtual {v0}, LX31/d;->e()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->b:Ljava/lang/String;

    .line 48
    .line 49
    invoke-virtual {p1}, LX31/a$f;->c()LX31/d;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->f:LX31/d;

    .line 54
    .line 55
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getSetScoreCellsColumn()Ljava/util/List;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    invoke-virtual {p1}, LX31/a$f;->c()LX31/d;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t(Ljava/util/List;LX31/d;)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p1}, LX31/a$f;->d()LX31/d;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    invoke-virtual {v0}, LX31/d;->e()Ljava/lang/String;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->c:Ljava/lang/String;

    .line 75
    .line 76
    invoke-virtual {p1}, LX31/a$f;->d()LX31/d;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->g:LX31/d;

    .line 81
    .line 82
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getTotalScoreCellsColumn()Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    invoke-virtual {p1}, LX31/a$f;->d()LX31/d;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->t(Ljava/util/List;LX31/d;)V

    .line 91
    .line 92
    .line 93
    invoke-virtual {p1}, LX31/a$f;->b()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->d:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 98
    .line 99
    sget-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;->NO_TEAM_SERVE:Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamServe;

    .line 100
    .line 101
    if-eq p1, v0, :cond_2

    .line 102
    .line 103
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getServeIndicator()Landroid/widget/ImageView;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getRotateAnimation()Landroid/view/animation/Animation;

    .line 108
    .line 109
    .line 110
    move-result-object v0

    .line 111
    invoke-virtual {p1, v0}, Landroid/view/View;->startAnimation(Landroid/view/animation/Animation;)V

    .line 112
    .line 113
    .line 114
    goto :goto_1

    .line 115
    :cond_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/TennisScoreView;->getServeIndicator()Landroid/widget/ImageView;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    invoke-virtual {p1}, Landroid/view/View;->clearAnimation()V

    .line 120
    .line 121
    .line 122
    :goto_1
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 123
    .line 124
    .line 125
    return-void
.end method

.method public final t(Ljava/util/List;LX31/d;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
            ">;",
            "LX31/d;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LX31/d;->a()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-lez v0, :cond_3

    .line 10
    .line 11
    invoke-virtual {p2}, LX31/d;->c()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-lez v0, :cond_3

    .line 20
    .line 21
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    check-cast v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 26
    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    invoke-virtual {p2}, LX31/d;->a()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScore(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    :cond_0
    const/4 v0, 0x1

    .line 37
    invoke-static {p1, v0}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    check-cast v1, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 42
    .line 43
    if-eqz v1, :cond_1

    .line 44
    .line 45
    invoke-virtual {p2}, LX31/d;->c()Ljava/lang/String;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScore(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    :cond_1
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    check-cast v1, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 57
    .line 58
    if-eqz v1, :cond_2

    .line 59
    .line 60
    invoke-virtual {p2}, LX31/d;->b()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 61
    .line 62
    .line 63
    move-result-object v2

    .line 64
    invoke-virtual {v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V

    .line 65
    .line 66
    .line 67
    :cond_2
    invoke-static {p1, v0}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    check-cast p1, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 72
    .line 73
    if-eqz p1, :cond_3

    .line 74
    .line 75
    invoke-virtual {p2}, LX31/d;->d()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 76
    .line 77
    .line 78
    move-result-object p2

    .line 79
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V

    .line 80
    .line 81
    .line 82
    :cond_3
    return-void
.end method
