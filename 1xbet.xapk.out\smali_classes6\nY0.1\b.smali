.class public final LnY0/b;
.super LNY0/a;
.source "SourceFile"

# interfaces
.implements LnY0/a;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Position:",
        "LmY0/d;",
        ">",
        "LNY0/a;",
        "LnY0/a<",
        "TPosition;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0007\u0018\u0000*\u0008\u0008\u0000\u0010\u0002*\u00020\u00012\u0008\u0012\u0004\u0012\u00028\u00000\u00032\u00020\u0004B\u000f\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008B\t\u0008\u0016\u00a2\u0006\u0004\u0008\u0007\u0010\tB\u001b\u0008\u0016\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0008\u0008\u0002\u0010\r\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u0007\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "LnY0/b;",
        "LmY0/d;",
        "Position",
        "LnY0/a;",
        "LNY0/a;",
        "Ljava/text/DecimalFormat;",
        "decimalFormat",
        "<init>",
        "(Ljava/text/DecimalFormat;)V",
        "()V",
        "",
        "pattern",
        "Ljava/math/RoundingMode;",
        "roundingMode",
        "(Ljava/lang/String;Ljava/math/RoundingMode;)V",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 3

    const/4 v0, 0x0

    const/4 v1, 0x2

    .line 2
    const-string v2, "#"

    invoke-direct {p0, v2, v0, v1, v0}, LnY0/b;-><init>(Ljava/lang/String;Ljava/math/RoundingMode;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/math/RoundingMode;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/math/RoundingMode;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    sget-object v0, LNY0/a;->b:LNY0/a$a;

    invoke-virtual {v0, p1, p2}, LNY0/a$a;->a(Ljava/lang/String;Ljava/math/RoundingMode;)Ljava/text/DecimalFormat;

    move-result-object p1

    invoke-direct {p0, p1}, LnY0/b;-><init>(Ljava/text/DecimalFormat;)V

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Ljava/math/RoundingMode;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    .line 3
    sget-object p2, Ljava/math/RoundingMode;->HALF_UP:Ljava/math/RoundingMode;

    .line 4
    :cond_0
    invoke-direct {p0, p1, p2}, LnY0/b;-><init>(Ljava/lang/String;Ljava/math/RoundingMode;)V

    return-void
.end method

.method public constructor <init>(Ljava/text/DecimalFormat;)V
    .locals 0
    .param p1    # Ljava/text/DecimalFormat;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1}, LNY0/a;-><init>(Ljava/text/DecimalFormat;)V

    return-void
.end method
