.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LZ31/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u001a\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u000f\u0010\u000b\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u001f\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\r\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J7\u0010\u0018\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0017\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u001aH\u0016\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\u001f\u0010!\u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008!\u0010\"J\u000f\u0010#\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010%\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008%\u0010$R\u0014\u0010)\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\'\u0010(R\u0014\u0010+\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010(R\u001b\u0010/\u001a\u00020\u001e8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008%\u0010,\u001a\u0004\u0008-\u0010.R\u001b\u00102\u001a\u00020\u001e8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00080\u0010,\u001a\u0004\u00081\u0010.R\u0016\u00104\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008#\u00103R\u0016\u00105\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008!\u00103R\u0014\u00107\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00103R\u0014\u00109\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00103R\u0014\u0010;\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u00103R\u0014\u0010=\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u00103R\u0014\u0010?\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u00103\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;",
        "Landroid/widget/FrameLayout;",
        "LZ31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "getTotalWidth",
        "()I",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;",
        "victoryIndicatorView",
        "scoreCellMiddlePosition",
        "f",
        "(Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;I)V",
        "e",
        "()V",
        "c",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
        "a",
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;",
        "firstTeamScoreCellView",
        "b",
        "secondTeamScoreCellView",
        "Lkotlin/j;",
        "getFirstTeamVictoryIndicatorView",
        "()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;",
        "firstTeamVictoryIndicatorView",
        "d",
        "getSecondTeamVictoryIndicatorView",
        "secondTeamVictoryIndicatorView",
        "I",
        "maxScoreLength",
        "scoreCellsWidth",
        "g",
        "minimalCellWidth",
        "h",
        "widthIncreaseStepPerDigit",
        "i",
        "scoreCellsBetweenMargin",
        "j",
        "scoreCellAndVictoryIndicatorBetweenMargin",
        "k",
        "scoreCellsVerticalPadding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:I

.field public f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 6
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 7
    sget p2, Lm31/f;->Widget_ScoreCell_SmallTeamLogo:I

    .line 8
    invoke-direct {v1, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 10
    new-instance v1, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 11
    new-instance v2, Landroid/view/ContextThemeWrapper;

    .line 12
    sget p2, Lm31/f;->Widget_ScoreCell_SmallTeamLogo:I

    .line 13
    invoke-direct {v2, p1, p2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    const/4 v5, 0x6

    const/4 v6, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    .line 14
    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 15
    new-instance p2, LZ31/c;

    invoke-direct {p2, p1}, LZ31/c;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->c:Lkotlin/j;

    .line 16
    new-instance p2, LZ31/d;

    invoke-direct {p2, p1}, LZ31/d;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->d:Lkotlin/j;

    .line 17
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->size_24:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->g:I

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->h:I

    .line 19
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->i:I

    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->j:I

    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_4:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Landroid/content/Context;)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->g(Landroid/content/Context;)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/content/Context;)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->d(Landroid/content/Context;)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    move-result-object p0

    return-object p0
.end method

.method public static final d(Landroid/content/Context;)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 2
    .line 3
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 4
    .line 5
    sget v2, Lm31/f;->Widget_SportVictoryIndicatorCompact_Theme:I

    .line 6
    .line 7
    invoke-direct {v1, p0, v2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 8
    .line 9
    .line 10
    const/4 v4, 0x6

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 15
    .line 16
    .line 17
    return-object v0
.end method

.method public static final g(Landroid/content/Context;)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 2
    .line 3
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 4
    .line 5
    sget v2, Lm31/f;->Widget_SportVictoryIndicatorCompact_Theme:I

    .line 6
    .line 7
    invoke-direct {v1, p0, v2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 8
    .line 9
    .line 10
    const/4 v4, 0x6

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 15
    .line 16
    .line 17
    return-object v0
.end method

.method private final getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->d:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getTotalWidth()I
    .locals 2

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f:I

    .line 22
    .line 23
    add-int/2addr v1, v0

    .line 24
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->j:I

    .line 25
    .line 26
    add-int/2addr v1, v0

    .line 27
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f:I

    .line 32
    .line 33
    if-lez v1, :cond_0

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    const/4 v0, 0x0

    .line 37
    :goto_0
    if-eqz v0, :cond_1

    .line 38
    .line 39
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 40
    .line 41
    .line 42
    move-result v0

    .line 43
    return v0

    .line 44
    :cond_1
    const/4 v0, 0x0

    .line 45
    return v0
.end method


# virtual methods
.method public final c()V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->e:I

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    if-gt v0, v1, :cond_0

    .line 5
    .line 6
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->g:I

    .line 7
    .line 8
    goto :goto_0

    .line 9
    :cond_0
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->g:I

    .line 10
    .line 11
    sub-int/2addr v0, v1

    .line 12
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->h:I

    .line 13
    .line 14
    mul-int v0, v0, v1

    .line 15
    .line 16
    add-int/2addr v0, v2

    .line 17
    :goto_0
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f:I

    .line 18
    .line 19
    return-void
.end method

.method public final e()V
    .locals 8

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->j:I

    .line 22
    .line 23
    add-int v4, v0, v1

    .line 24
    .line 25
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 26
    .line 27
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    .line 28
    .line 29
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 30
    .line 31
    .line 32
    move-result v6

    .line 33
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 34
    .line 35
    .line 36
    move-result v7

    .line 37
    move-object v2, p0

    .line 38
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 39
    .line 40
    .line 41
    iget-object v3, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 42
    .line 43
    iget v0, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    .line 44
    .line 45
    iget-object v1, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 46
    .line 47
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    add-int/2addr v0, v1

    .line 52
    iget v1, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->i:I

    .line 53
    .line 54
    add-int v5, v0, v1

    .line 55
    .line 56
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 57
    .line 58
    .line 59
    move-result v6

    .line 60
    iget v0, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    .line 61
    .line 62
    iget-object v1, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 63
    .line 64
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 65
    .line 66
    .line 67
    move-result v1

    .line 68
    add-int/2addr v0, v1

    .line 69
    iget v1, v2, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->i:I

    .line 70
    .line 71
    add-int/2addr v0, v1

    .line 72
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 73
    .line 74
    .line 75
    move-result v1

    .line 76
    add-int v7, v0, v1

    .line 77
    .line 78
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 79
    .line 80
    .line 81
    return-void
.end method

.method public final f(Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;I)V
    .locals 7

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    div-int/lit8 v0, v0, 0x2

    .line 6
    .line 7
    sub-int v4, p2, v0

    .line 8
    .line 9
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    div-int/lit8 v0, v0, 0x2

    .line 14
    .line 15
    add-int v6, p2, v0

    .line 16
    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredWidth()I

    .line 19
    .line 20
    .line 21
    move-result v5

    .line 22
    move-object v1, p0

    .line 23
    move-object v2, p1

    .line 24
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 25
    .line 26
    .line 27
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    if-lez p1, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    if-lez p1, :cond_0

    .line 12
    .line 13
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    .line 14
    .line 15
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 16
    .line 17
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 18
    .line 19
    .line 20
    move-result p2

    .line 21
    div-int/lit8 p2, p2, 0x2

    .line 22
    .line 23
    add-int/2addr p1, p2

    .line 24
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    .line 25
    .line 26
    iget-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 27
    .line 28
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 29
    .line 30
    .line 31
    move-result p3

    .line 32
    add-int/2addr p2, p3

    .line 33
    iget p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->i:I

    .line 34
    .line 35
    add-int/2addr p2, p3

    .line 36
    iget-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 37
    .line 38
    invoke-virtual {p3}, Landroid/view/View;->getMeasuredHeight()I

    .line 39
    .line 40
    .line 41
    move-result p3

    .line 42
    div-int/lit8 p3, p3, 0x2

    .line 43
    .line 44
    add-int/2addr p2, p3

    .line 45
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 46
    .line 47
    .line 48
    move-result-object p3

    .line 49
    invoke-virtual {p0, p3, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f(Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;I)V

    .line 50
    .line 51
    .line 52
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-virtual {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f(Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;I)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->e()V

    .line 60
    .line 61
    .line 62
    :cond_0
    return-void
.end method

.method public onMeasure(II)V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->c()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 5
    .line 6
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f:I

    .line 7
    .line 8
    const/high16 v2, 0x40000000    # 2.0f

    .line 9
    .line 10
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    invoke-virtual {v0, v1, p2}, Landroid/view/View;->measure(II)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 18
    .line 19
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->f:I

    .line 20
    .line 21
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    invoke-virtual {v0, v1, p2}, Landroid/view/View;->measure(II)V

    .line 26
    .line 27
    .line 28
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 33
    .line 34
    .line 35
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-virtual {v0, p1, p2}, Landroid/view/View;->measure(II)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 43
    .line 44
    invoke-virtual {p1}, Landroid/view/View;->getMeasuredHeight()I

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 49
    .line 50
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 51
    .line 52
    .line 53
    move-result p2

    .line 54
    add-int/2addr p1, p2

    .line 55
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->i:I

    .line 56
    .line 57
    add-int/2addr p1, p2

    .line 58
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->k:I

    .line 59
    .line 60
    add-int/2addr p1, p2

    .line 61
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 66
    .line 67
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 68
    .line 69
    .line 70
    move-result p2

    .line 71
    if-gtz p2, :cond_1

    .line 72
    .line 73
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 74
    .line 75
    invoke-virtual {p2}, Landroid/view/View;->getMeasuredHeight()I

    .line 76
    .line 77
    .line 78
    move-result p2

    .line 79
    if-lez p2, :cond_0

    .line 80
    .line 81
    goto :goto_0

    .line 82
    :cond_0
    const/4 p1, 0x0

    .line 83
    :cond_1
    :goto_0
    if-eqz p1, :cond_2

    .line 84
    .line 85
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 86
    .line 87
    .line 88
    move-result p1

    .line 89
    goto :goto_1

    .line 90
    :cond_2
    const/4 p1, 0x0

    .line 91
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getTotalWidth()I

    .line 92
    .line 93
    .line 94
    move-result p2

    .line 95
    invoke-static {p2, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 96
    .line 97
    .line 98
    move-result p2

    .line 99
    invoke-static {p1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 100
    .line 101
    .line 102
    move-result p1

    .line 103
    invoke-virtual {p0, p2, p1}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 104
    .line 105
    .line 106
    return-void
.end method

.method public setScoreUiModel(LX31/a;)V
    .locals 3
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/a$b;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LX31/a$b;

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    :goto_0
    if-nez p1, :cond_1

    .line 10
    .line 11
    return-void

    .line 12
    :cond_1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 13
    .line 14
    invoke-virtual {p1}, LX31/a$b;->a()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScore(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 22
    .line 23
    invoke-virtual {p1}, LX31/a$b;->d()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScore(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 31
    .line 32
    invoke-virtual {p1}, LX31/a$b;->b()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V

    .line 37
    .line 38
    .line 39
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 40
    .line 41
    invoke-virtual {p1}, LX31/a$b;->e()Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;->setTeamScoreState(Lorg/xbet/uikit_sport/sport_coupon_card/models/TeamScoreState;)V

    .line 46
    .line 47
    .line 48
    invoke-virtual {p1}, LX31/a$b;->a()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    invoke-virtual {p1}, LX31/a$b;->d()Ljava/lang/String;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    .line 61
    .line 62
    .line 63
    move-result v1

    .line 64
    invoke-static {v0, v1}, Ljava/lang/Math;->max(II)I

    .line 65
    .line 66
    .line 67
    move-result v0

    .line 68
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->e:I

    .line 69
    .line 70
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    invoke-virtual {p1}, LX31/a$b;->c()LX31/g;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    invoke-virtual {v1}, LX31/g;->a()I

    .line 79
    .line 80
    .line 81
    move-result v1

    .line 82
    invoke-virtual {p1}, LX31/a$b;->c()LX31/g;

    .line 83
    .line 84
    .line 85
    move-result-object v2

    .line 86
    invoke-virtual {v2}, LX31/g;->b()I

    .line 87
    .line 88
    .line 89
    move-result v2

    .line 90
    invoke-virtual {v0, v1, v2}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 91
    .line 92
    .line 93
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    invoke-virtual {p1}, LX31/a$b;->f()LX31/g;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    invoke-virtual {v1}, LX31/g;->a()I

    .line 102
    .line 103
    .line 104
    move-result v1

    .line 105
    invoke-virtual {p1}, LX31/a$b;->f()LX31/g;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    invoke-virtual {p1}, LX31/g;->b()I

    .line 110
    .line 111
    .line 112
    move-result p1

    .line 113
    invoke-virtual {v0, v1, p1}, Lorg/xbet/uikit_sport/victoryindiacator/BaseVictoryIndicator;->setTotalAndWinCounts(II)V

    .line 114
    .line 115
    .line 116
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 117
    .line 118
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    if-nez p1, :cond_2

    .line 123
    .line 124
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->a:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 125
    .line 126
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 127
    .line 128
    .line 129
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 130
    .line 131
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    if-nez p1, :cond_3

    .line 136
    .line 137
    iget-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->b:Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/ScoreCellView;

    .line 138
    .line 139
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 140
    .line 141
    .line 142
    :cond_3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    if-nez p1, :cond_4

    .line 151
    .line 152
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getFirstTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 153
    .line 154
    .line 155
    move-result-object p1

    .line 156
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 157
    .line 158
    .line 159
    :cond_4
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 160
    .line 161
    .line 162
    move-result-object p1

    .line 163
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 164
    .line 165
    .line 166
    move-result-object p1

    .line 167
    if-nez p1, :cond_5

    .line 168
    .line 169
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_score_middle_views/SmallTeamLogoCyberScoreView;->getSecondTeamVictoryIndicatorView()Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicatorCompact;

    .line 170
    .line 171
    .line 172
    move-result-object p1

    .line 173
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 174
    .line 175
    .line 176
    :cond_5
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 177
    .line 178
    .line 179
    return-void
.end method
