.class public final Lorg/xbet/ui_common/circleindicator/b$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/ui_common/circleindicator/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003JS\u0010\r\u001a\u00020\u000c2\u0008\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u00042\u0008\u0008\u0002\u0010\t\u001a\u00020\u00042\u0008\u0008\u0002\u0010\n\u001a\u00020\u00042\u0008\u0008\u0002\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\r\u0010\u000eR\u0014\u0010\u000f\u001a\u00020\u00048\u0002X\u0082T\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\u0010\u00a8\u0006\u0011"
    }
    d2 = {
        "Lorg/xbet/ui_common/circleindicator/b$a;",
        "",
        "<init>",
        "()V",
        "",
        "margin",
        "width",
        "height",
        "resId",
        "reverseResId",
        "backgroundResId",
        "unselectedBackgroundResId",
        "Lorg/xbet/ui_common/circleindicator/b;",
        "a",
        "(IIIIIII)Lorg/xbet/ui_common/circleindicator/b;",
        "DEFAULT_INDICATOR_WIDTH",
        "I",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/circleindicator/b$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(IIIIIII)Lorg/xbet/ui_common/circleindicator/b;
    .locals 9
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/ui_common/circleindicator/b;

    .line 2
    .line 3
    const/4 v1, 0x5

    .line 4
    if-gez p1, :cond_0

    .line 5
    .line 6
    invoke-static {v1}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 7
    .line 8
    .line 9
    move-result p1

    .line 10
    :cond_0
    if-gez p2, :cond_1

    .line 11
    .line 12
    invoke-static {v1}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 13
    .line 14
    .line 15
    move-result p2

    .line 16
    :cond_1
    move v2, p2

    .line 17
    if-gez p3, :cond_2

    .line 18
    .line 19
    invoke-static {v1}, Lorg/xbet/ui_common/utils/ExtensionsKt;->q(I)I

    .line 20
    .line 21
    .line 22
    move-result p3

    .line 23
    :cond_2
    move v3, p3

    .line 24
    if-nez p4, :cond_3

    .line 25
    .line 26
    sget p4, Lpb/b;->scale_with_alpha:I

    .line 27
    .line 28
    :cond_3
    move v4, p4

    .line 29
    if-nez p6, :cond_4

    .line 30
    .line 31
    sget p2, Lpb/g;->white_radius:I

    .line 32
    .line 33
    move v6, p2

    .line 34
    goto :goto_0

    .line 35
    :cond_4
    move v6, p6

    .line 36
    :goto_0
    if-nez p7, :cond_5

    .line 37
    .line 38
    move v7, p6

    .line 39
    goto :goto_1

    .line 40
    :cond_5
    move/from16 v7, p7

    .line 41
    .line 42
    :goto_1
    const/4 v8, 0x0

    .line 43
    move v1, p1

    .line 44
    move v5, p5

    .line 45
    invoke-direct/range {v0 .. v8}, Lorg/xbet/ui_common/circleindicator/b;-><init>(IIIIIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 46
    .line 47
    .line 48
    return-object v0
.end method
