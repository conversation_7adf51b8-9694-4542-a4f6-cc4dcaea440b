.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/bottom/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\u001d\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\tH\u0002\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0015\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\t\u00a2\u0006\u0004\u0008\u000e\u0010\rJ\u0015\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\t\u00a2\u0006\u0004\u0008\u0010\u0010\rJ\u0017\u0010\u0013\u001a\u00020\u000b2\u0008\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u001b\u0010\u0018\u001a\u00020\u000b2\u000c\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00160\u0015\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u001d\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001c\u00a8\u0006\u001e"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/bottom/c;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "visible",
        "",
        "t",
        "(Z)V",
        "setAccordionVisibility",
        "expanded",
        "setExpanded",
        "Landroid/view/View$OnClickListener;",
        "listener",
        "setAccordionClickListener",
        "(Landroid/view/View$OnClickListener;)V",
        "",
        "LL11/b;",
        "marketGroups",
        "setMarketGroups",
        "(Ljava/util/List;)V",
        "LC31/h;",
        "a",
        "LC31/h;",
        "binding",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime Lkotlin/e;
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/h;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/h;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    const/4 p1, 0x0

    .line 5
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->setAccordionClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic s(Landroid/view/View$OnClickListener;LC31/h;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->u(Landroid/view/View$OnClickListener;LC31/h;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;Landroid/view/View;)V

    return-void
.end method

.method private final t(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 2
    .line 3
    iget-object v0, v0, LC31/h;->d:Landroid/widget/LinearLayout;

    .line 4
    .line 5
    invoke-static {v0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-static {v0}, Lkotlin/sequences/SequencesKt___SequencesKt;->C(Lkotlin/sequences/Sequence;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    goto :goto_3

    .line 16
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 17
    .line 18
    iget-object v0, v0, LC31/h;->d:Landroid/widget/LinearLayout;

    .line 19
    .line 20
    invoke-static {v0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    const/4 v1, 0x1

    .line 25
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->J(Lkotlin/sequences/Sequence;I)Lkotlin/sequences/Sequence;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    invoke-interface {v0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_2

    .line 38
    .line 39
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    check-cast v1, Landroid/view/View;

    .line 44
    .line 45
    if-eqz p1, :cond_1

    .line 46
    .line 47
    const/4 v2, 0x0

    .line 48
    goto :goto_1

    .line 49
    :cond_1
    const/16 v2, 0x8

    .line 50
    .line 51
    :goto_1
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 52
    .line 53
    .line 54
    goto :goto_0

    .line 55
    :cond_2
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 56
    .line 57
    iget-object v0, v0, LC31/h;->d:Landroid/widget/LinearLayout;

    .line 58
    .line 59
    invoke-static {v0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-static {v0}, Lkotlin/sequences/SequencesKt___SequencesKt;->S(Lkotlin/sequences/Sequence;)Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    check-cast v0, Landroid/view/View;

    .line 68
    .line 69
    instance-of v1, v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;

    .line 70
    .line 71
    if-eqz v1, :cond_3

    .line 72
    .line 73
    check-cast v0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;

    .line 74
    .line 75
    goto :goto_2

    .line 76
    :cond_3
    const/4 v0, 0x0

    .line 77
    :goto_2
    if-eqz v0, :cond_4

    .line 78
    .line 79
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->s(Z)V

    .line 80
    .line 81
    .line 82
    :cond_4
    :goto_3
    return-void
.end method

.method public static final u(Landroid/view/View$OnClickListener;LC31/h;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;Landroid/view/View;)V
    .locals 0

    .line 1
    if-eqz p0, :cond_0

    .line 2
    .line 3
    invoke-interface {p0, p3}, Landroid/view/View$OnClickListener;->onClick(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    :cond_0
    iget-object p0, p1, LC31/h;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 9
    .line 10
    .line 11
    move-result p3

    .line 12
    xor-int/lit8 p3, p3, 0x1

    .line 13
    .line 14
    invoke-virtual {p0, p3}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded(Z)V

    .line 15
    .line 16
    .line 17
    iget-object p0, p1, LC31/h;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 20
    .line 21
    .line 22
    move-result p0

    .line 23
    invoke-direct {p2, p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->t(Z)V

    .line 24
    .line 25
    .line 26
    return-void
.end method


# virtual methods
.method public final setAccordionClickListener(Landroid/view/View$OnClickListener;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 2
    .line 3
    iget-object v1, v0, LC31/h;->c:Landroid/view/View;

    .line 4
    .line 5
    new-instance v2, Lorg/xbet/uikit_sport/eventcard/bottom/h;

    .line 6
    .line 7
    invoke-direct {v2, p1, v0, p0}, Lorg/xbet/uikit_sport/eventcard/bottom/h;-><init>(Landroid/view/View$OnClickListener;LC31/h;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {v1, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final setAccordionVisibility(Z)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 2
    .line 3
    iget-object v0, v0, LC31/h;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 4
    .line 5
    if-nez p1, :cond_0

    .line 6
    .line 7
    const/4 v1, 0x4

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/4 v1, 0x0

    .line 10
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 14
    .line 15
    iget-object v0, v0, LC31/h;->c:Landroid/view/View;

    .line 16
    .line 17
    invoke-virtual {v0, p1}, Landroid/view/View;->setClickable(Z)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method public final setExpanded(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 2
    .line 3
    iget-object v0, v0, LC31/h;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded(Z)V

    .line 6
    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 9
    .line 10
    iget-object p1, p1, LC31/h;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 11
    .line 12
    invoke-virtual {p1}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->t(Z)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public final setMarketGroups(Ljava/util/List;)V
    .locals 8
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LL11/b;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 2
    .line 3
    iget-object v0, v0, LC31/h;->d:Landroid/widget/LinearLayout;

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/ViewGroup;->getChildCount()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    :goto_0
    if-ge v0, v1, :cond_0

    .line 14
    .line 15
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 16
    .line 17
    iget-object v2, v2, LC31/h;->d:Landroid/widget/LinearLayout;

    .line 18
    .line 19
    new-instance v3, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;

    .line 20
    .line 21
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 22
    .line 23
    .line 24
    move-result-object v4

    .line 25
    const/4 v5, 0x2

    .line 26
    const/4 v6, 0x0

    .line 27
    invoke-direct {v3, v4, v6, v5, v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v2, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 31
    .line 32
    .line 33
    add-int/lit8 v0, v0, 0x1

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 37
    .line 38
    iget-object v0, v0, LC31/h;->d:Landroid/widget/LinearLayout;

    .line 39
    .line 40
    invoke-static {v0}, Landroidx/core/view/ViewGroupKt;->b(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    sget-object v1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable$a;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable$a;

    .line 45
    .line 46
    invoke-static {v0, v1}, Lkotlin/sequences/SequencesKt___SequencesKt;->O(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Lkotlin/sequences/Sequence;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    invoke-interface {v0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    const/4 v1, 0x0

    .line 55
    const/4 v2, 0x0

    .line 56
    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 57
    .line 58
    .line 59
    move-result v3

    .line 60
    if-eqz v3, :cond_5

    .line 61
    .line 62
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v3

    .line 66
    add-int/lit8 v4, v2, 0x1

    .line 67
    .line 68
    if-gez v2, :cond_1

    .line 69
    .line 70
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 71
    .line 72
    .line 73
    :cond_1
    check-cast v3, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;

    .line 74
    .line 75
    invoke-static {p1, v2}, Lkotlin/collections/CollectionsKt;->z0(Ljava/util/List;I)Ljava/lang/Object;

    .line 76
    .line 77
    .line 78
    move-result-object v5

    .line 79
    check-cast v5, LL11/b;

    .line 80
    .line 81
    if-eqz v5, :cond_2

    .line 82
    .line 83
    invoke-virtual {v5}, LL11/b;->d()Ljava/lang/CharSequence;

    .line 84
    .line 85
    .line 86
    move-result-object v6

    .line 87
    invoke-virtual {v3, v6}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->setHeader(Ljava/lang/CharSequence;)V

    .line 88
    .line 89
    .line 90
    invoke-virtual {v5}, LL11/b;->a()Ljava/util/List;

    .line 91
    .line 92
    .line 93
    move-result-object v6

    .line 94
    invoke-virtual {v5}, LL11/b;->b()Ljava/util/List;

    .line 95
    .line 96
    .line 97
    move-result-object v7

    .line 98
    invoke-virtual {v5}, LL11/b;->c()Ljava/util/List;

    .line 99
    .line 100
    .line 101
    move-result-object v5

    .line 102
    invoke-virtual {v3, v6, v7, v5}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketGroupView;->setMarketColumns(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V

    .line 103
    .line 104
    .line 105
    :cond_2
    invoke-interface {p1}, Ljava/util/List;->size()I

    .line 106
    .line 107
    .line 108
    move-result v5

    .line 109
    if-ge v2, v5, :cond_3

    .line 110
    .line 111
    const/4 v2, 0x1

    .line 112
    goto :goto_2

    .line 113
    :cond_3
    const/4 v2, 0x0

    .line 114
    :goto_2
    if-eqz v2, :cond_4

    .line 115
    .line 116
    const/4 v2, 0x0

    .line 117
    goto :goto_3

    .line 118
    :cond_4
    const/16 v2, 0x8

    .line 119
    .line 120
    :goto_3
    invoke-virtual {v3, v2}, Landroid/view/View;->setVisibility(I)V

    .line 121
    .line 122
    .line 123
    move v2, v4

    .line 124
    goto :goto_1

    .line 125
    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->a:LC31/h;

    .line 126
    .line 127
    iget-object p1, p1, LC31/h;->b:Lorg/xbet/uikit/components/accordion/Accordion;

    .line 128
    .line 129
    invoke-virtual {p1}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 130
    .line 131
    .line 132
    move-result p1

    .line 133
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomMarketExpandable;->t(Z)V

    .line 134
    .line 135
    .line 136
    return-void
.end method
