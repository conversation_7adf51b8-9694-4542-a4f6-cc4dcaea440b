.class public final LM71/a$d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LM71/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LM71/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "d"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LM71/a$d$b;,
        LM71/a$d$a;,
        LM71/a$d$e;,
        LM71/a$d$f;,
        LM71/a$d$d;,
        LM71/a$d$i;,
        LM71/a$d$k;,
        LM71/a$d$n;,
        LM71/a$d$m;,
        LM71/a$d$h;,
        LM71/a$d$g;,
        LM71/a$d$c;,
        LM71/a$d$o;,
        LM71/a$d$l;,
        LM71/a$d$j;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/b;",
            ">;"
        }
    .end annotation
.end field

.field public A0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/witch/data/repositories/WitchRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/h;",
            ">;"
        }
    .end annotation
.end field

.field public B0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/e;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/d;",
            ">;"
        }
    .end annotation
.end field

.field public C0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/c;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/j;",
            ">;"
        }
    .end annotation
.end field

.field public D0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/g;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/q;",
            ">;"
        }
    .end annotation
.end field

.field public E0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/q;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/l;",
            ">;"
        }
    .end annotation
.end field

.field public F0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/o;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/f;",
            ">;"
        }
    .end annotation
.end field

.field public G0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/m;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/d;",
            ">;"
        }
    .end annotation
.end field

.field public H0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/k;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/b;",
            ">;"
        }
    .end annotation
.end field

.field public I0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/i;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/c;",
            ">;"
        }
    .end annotation
.end field

.field public J0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LP71/a;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/AddCommandScenario;",
            ">;"
        }
    .end annotation
.end field

.field public K0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/witch/presentation/game/WitchGameViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/e;",
            ">;"
        }
    .end annotation
.end field

.field public L0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Li8/j;",
            ">;"
        }
    .end annotation
.end field

.field public M:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/y;",
            ">;"
        }
    .end annotation
.end field

.field public N:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/g;",
            ">;"
        }
    .end annotation
.end field

.field public O:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/j;",
            ">;"
        }
    .end annotation
.end field

.field public P:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/e;",
            ">;"
        }
    .end annotation
.end field

.field public Q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/UnfinishedGameLoadedScenario;",
            ">;"
        }
    .end annotation
.end field

.field public R:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/f;",
            ">;"
        }
    .end annotation
.end field

.field public S:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/x;",
            ">;"
        }
    .end annotation
.end field

.field public T:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/u;",
            ">;"
        }
    .end annotation
.end field

.field public U:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/q;",
            ">;"
        }
    .end annotation
.end field

.field public V:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/n;",
            ">;"
        }
    .end annotation
.end field

.field public W:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/h;",
            ">;"
        }
    .end annotation
.end field

.field public X:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/j;",
            ">;"
        }
    .end annotation
.end field

.field public Y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/a;",
            ">;"
        }
    .end annotation
.end field

.field public Z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVv/h;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LQv/v;

.field public a0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LKv/a;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LQv/w;

.field public b0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/E;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LM71/a$d;

.field public c0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/k;",
            ">;"
        }
    .end annotation
.end field

.field public d:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public d0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/A;",
            ">;"
        }
    .end annotation
.end field

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/b;",
            ">;"
        }
    .end annotation
.end field

.field public e0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWv/d;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LDg/c;",
            ">;"
        }
    .end annotation
.end field

.field public f0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWv/a;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public g0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/w;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public h0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LTv/e;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LxX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public i0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lak/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LUv/a;",
            ">;"
        }
    .end annotation
.end field

.field public j0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/d;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/a;",
            ">;"
        }
    .end annotation
.end field

.field public k0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/e;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexcore/utils/ext/c;",
            ">;"
        }
    .end annotation
.end field

.field public l0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_info/G;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LWv/b;",
            ">;"
        }
    .end annotation
.end field

.field public m0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/GetCurrencyUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/h;",
            ">;"
        }
    .end annotation
.end field

.field public n0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/j;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/a;",
            ">;"
        }
    .end annotation
.end field

.field public o0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/user/c;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public p0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/IsBalanceForGamesSectionScenario;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public q0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/s;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/data_source/f;",
            ">;"
        }
    .end annotation
.end field

.field public r0:Lorg/xbet/core/presentation/holder/s;

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/data_source/d;",
            ">;"
        }
    .end annotation
.end field

.field public s0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LQv/a$s;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/data_source/c;",
            ">;"
        }
    .end annotation
.end field

.field public t0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/a;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/data/repositories/FactorsRepository;",
            ">;"
        }
    .end annotation
.end field

.field public u0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/p;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/f;",
            ">;"
        }
    .end annotation
.end field

.field public v0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/o;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/balance/c;",
            ">;"
        }
    .end annotation
.end field

.field public w0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/StartGameIfPossibleScenario;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bonus/e;",
            ">;"
        }
    .end annotation
.end field

.field public x0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LI71/c;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/bet/b;",
            ">;"
        }
    .end annotation
.end field

.field public y0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LI71/a;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/core/domain/usecases/game_state/l;",
            ">;"
        }
    .end annotation
.end field

.field public z0:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LM71/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LM71/a$d;->c:LM71/a$d;

    .line 4
    iput-object p3, p0, LM71/a$d;->a:LQv/v;

    .line 5
    iput-object p2, p0, LM71/a$d;->b:LQv/w;

    .line 6
    invoke-virtual {p0, p1, p2, p3, p4}, LM71/a$d;->Y(LM71/d;LQv/w;LQv/v;LwX0/c;)V

    .line 7
    invoke-virtual {p0, p1, p2, p3, p4}, LM71/a$d;->Z(LM71/d;LQv/w;LQv/v;LwX0/c;)V

    .line 8
    invoke-virtual {p0, p1, p2, p3, p4}, LM71/a$d;->a0(LM71/d;LQv/w;LQv/v;LwX0/c;)V

    .line 9
    invoke-virtual {p0, p1, p2, p3, p4}, LM71/a$d;->b0(LM71/d;LQv/w;LQv/v;LwX0/c;)V

    return-void
.end method

.method public synthetic constructor <init>(LM71/d;LQv/w;LQv/v;LwX0/c;LM71/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, LM71/a$d;-><init>(LM71/d;LQv/w;LQv/v;LwX0/c;)V

    return-void
.end method

.method public static bridge synthetic A(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->G:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic B(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->H:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic C(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->x:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic D(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->m:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic E(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->m0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic F(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->v:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic G(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->O:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic H(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->E:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic I(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->B:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic J(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->M:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic K(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->q0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic L(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->c0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic M(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->T:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic N(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->v0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic O(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->n:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic P(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->D:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic Q(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->u0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic R(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->F:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic S(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->z:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic T(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->U:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic U(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->V:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic V(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->l0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic W(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->w0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic X(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->g0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic d(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->d:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic e(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->i0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic f(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->i:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic g(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->g:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic h(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->h:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic i(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->u:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic j(LM71/a$d;)LQv/v;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->a:LQv/v;

    return-object p0
.end method

.method public static bridge synthetic k(LM71/a$d;)LQv/w;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->b:LQv/w;

    return-object p0
.end method

.method public static bridge synthetic l(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->j:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic m(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->L0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic n(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->f:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic o(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->K:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic p(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->o:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic q(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->y:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic r(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->k:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic s(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->t0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic t(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->j0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic u(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->h0:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic v(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->P:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic w(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->w:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic x(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->L:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic y(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->A:Ldagger/internal/h;

    return-object p0
.end method

.method public static bridge synthetic z(LM71/a$d;)Ldagger/internal/h;
    .locals 0

    .line 1
    iget-object p0, p0, LM71/a$d;->C:Ldagger/internal/h;

    return-object p0
.end method


# virtual methods
.method public final Y(LM71/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 2

    .line 1
    new-instance p1, LM71/a$d$b;

    .line 2
    .line 3
    invoke-direct {p1, p3}, LM71/a$d$b;-><init>(LQv/v;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, LM71/a$d;->d:Ldagger/internal/h;

    .line 7
    .line 8
    new-instance p1, LM71/a$d$a;

    .line 9
    .line 10
    invoke-direct {p1, p3}, LM71/a$d$a;-><init>(LQv/v;)V

    .line 11
    .line 12
    .line 13
    iput-object p1, p0, LM71/a$d;->e:Ldagger/internal/h;

    .line 14
    .line 15
    invoke-static {p1}, LDg/d;->a(LBc/a;)LDg/d;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    iput-object p1, p0, LM71/a$d;->f:Ldagger/internal/h;

    .line 20
    .line 21
    new-instance p1, LM71/a$d$e;

    .line 22
    .line 23
    invoke-direct {p1, p3}, LM71/a$d$e;-><init>(LQv/v;)V

    .line 24
    .line 25
    .line 26
    iput-object p1, p0, LM71/a$d;->g:Ldagger/internal/h;

    .line 27
    .line 28
    new-instance p1, LM71/a$d$f;

    .line 29
    .line 30
    invoke-direct {p1, p3}, LM71/a$d$f;-><init>(LQv/v;)V

    .line 31
    .line 32
    .line 33
    iput-object p1, p0, LM71/a$d;->h:Ldagger/internal/h;

    .line 34
    .line 35
    new-instance p1, LM71/a$d$d;

    .line 36
    .line 37
    invoke-direct {p1, p3}, LM71/a$d$d;-><init>(LQv/v;)V

    .line 38
    .line 39
    .line 40
    iput-object p1, p0, LM71/a$d;->i:Ldagger/internal/h;

    .line 41
    .line 42
    new-instance p1, LM71/a$d$i;

    .line 43
    .line 44
    invoke-direct {p1, p3}, LM71/a$d$i;-><init>(LQv/v;)V

    .line 45
    .line 46
    .line 47
    iput-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 48
    .line 49
    invoke-static {p2, p1}, LQv/F;->a(LQv/w;LBc/a;)LQv/F;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    iput-object p1, p0, LM71/a$d;->k:Ldagger/internal/h;

    .line 58
    .line 59
    new-instance p1, LM71/a$d$k;

    .line 60
    .line 61
    invoke-direct {p1, p3}, LM71/a$d$k;-><init>(LQv/v;)V

    .line 62
    .line 63
    .line 64
    iput-object p1, p0, LM71/a$d;->l:Ldagger/internal/h;

    .line 65
    .line 66
    iget-object p4, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 67
    .line 68
    invoke-static {p2, p4, p1}, LQv/X;->a(LQv/w;LBc/a;LBc/a;)LQv/X;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    iput-object p1, p0, LM71/a$d;->m:Ldagger/internal/h;

    .line 77
    .line 78
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 79
    .line 80
    invoke-static {p2, p1}, LQv/H0;->a(LQv/w;LBc/a;)LQv/H0;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    iput-object p1, p0, LM71/a$d;->n:Ldagger/internal/h;

    .line 89
    .line 90
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 91
    .line 92
    invoke-static {p2, p1}, LQv/D;->a(LQv/w;LBc/a;)LQv/D;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    iput-object p1, p0, LM71/a$d;->o:Ldagger/internal/h;

    .line 101
    .line 102
    new-instance p1, LM71/a$d$n;

    .line 103
    .line 104
    invoke-direct {p1, p3}, LM71/a$d$n;-><init>(LQv/v;)V

    .line 105
    .line 106
    .line 107
    iput-object p1, p0, LM71/a$d;->p:Ldagger/internal/h;

    .line 108
    .line 109
    new-instance p1, LM71/a$d$m;

    .line 110
    .line 111
    invoke-direct {p1, p3}, LM71/a$d$m;-><init>(LQv/v;)V

    .line 112
    .line 113
    .line 114
    iput-object p1, p0, LM71/a$d;->q:Ldagger/internal/h;

    .line 115
    .line 116
    invoke-static {p1}, Lorg/xbet/core/data/data_source/g;->a(LBc/a;)Lorg/xbet/core/data/data_source/g;

    .line 117
    .line 118
    .line 119
    move-result-object p1

    .line 120
    iput-object p1, p0, LM71/a$d;->r:Ldagger/internal/h;

    .line 121
    .line 122
    new-instance p1, LM71/a$d$h;

    .line 123
    .line 124
    invoke-direct {p1, p3}, LM71/a$d$h;-><init>(LQv/v;)V

    .line 125
    .line 126
    .line 127
    iput-object p1, p0, LM71/a$d;->s:Ldagger/internal/h;

    .line 128
    .line 129
    new-instance p1, LM71/a$d$g;

    .line 130
    .line 131
    invoke-direct {p1, p3}, LM71/a$d$g;-><init>(LQv/v;)V

    .line 132
    .line 133
    .line 134
    iput-object p1, p0, LM71/a$d;->t:Ldagger/internal/h;

    .line 135
    .line 136
    iget-object p3, p0, LM71/a$d;->p:Ldagger/internal/h;

    .line 137
    .line 138
    iget-object p4, p0, LM71/a$d;->h:Ldagger/internal/h;

    .line 139
    .line 140
    iget-object v0, p0, LM71/a$d;->r:Ldagger/internal/h;

    .line 141
    .line 142
    iget-object v1, p0, LM71/a$d;->s:Ldagger/internal/h;

    .line 143
    .line 144
    invoke-static {p3, p4, v0, v1, p1}, Lorg/xbet/core/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/core/data/repositories/a;

    .line 145
    .line 146
    .line 147
    move-result-object p1

    .line 148
    iput-object p1, p0, LM71/a$d;->u:Ldagger/internal/h;

    .line 149
    .line 150
    invoke-static {p2, p1}, LQv/Z;->a(LQv/w;LBc/a;)LQv/Z;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    iput-object p1, p0, LM71/a$d;->v:Ldagger/internal/h;

    .line 159
    .line 160
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 161
    .line 162
    invoke-static {p2, p1}, LQv/N;->a(LQv/w;LBc/a;)LQv/N;

    .line 163
    .line 164
    .line 165
    move-result-object p1

    .line 166
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 167
    .line 168
    .line 169
    move-result-object p1

    .line 170
    iput-object p1, p0, LM71/a$d;->w:Ldagger/internal/h;

    .line 171
    .line 172
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 173
    .line 174
    invoke-static {p2, p1}, LQv/V;->a(LQv/w;LBc/a;)LQv/V;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 179
    .line 180
    .line 181
    move-result-object p1

    .line 182
    iput-object p1, p0, LM71/a$d;->x:Ldagger/internal/h;

    .line 183
    .line 184
    iget-object p3, p0, LM71/a$d;->v:Ldagger/internal/h;

    .line 185
    .line 186
    iget-object p4, p0, LM71/a$d;->w:Ldagger/internal/h;

    .line 187
    .line 188
    iget-object v0, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 189
    .line 190
    invoke-static {p2, p3, p4, p1, v0}, LQv/E;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/E;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 195
    .line 196
    .line 197
    move-result-object p1

    .line 198
    iput-object p1, p0, LM71/a$d;->y:Ldagger/internal/h;

    .line 199
    .line 200
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 201
    .line 202
    invoke-static {p2, p1}, LQv/U0;->a(LQv/w;LBc/a;)LQv/U0;

    .line 203
    .line 204
    .line 205
    move-result-object p1

    .line 206
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    iput-object p1, p0, LM71/a$d;->z:Ldagger/internal/h;

    .line 211
    .line 212
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 213
    .line 214
    invoke-static {p2, p1}, LQv/P;->a(LQv/w;LBc/a;)LQv/P;

    .line 215
    .line 216
    .line 217
    move-result-object p1

    .line 218
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 219
    .line 220
    .line 221
    move-result-object p1

    .line 222
    iput-object p1, p0, LM71/a$d;->A:Ldagger/internal/h;

    .line 223
    .line 224
    iget-object p1, p0, LM71/a$d;->j:Ldagger/internal/h;

    .line 225
    .line 226
    invoke-static {p2, p1}, LQv/x0;->a(LQv/w;LBc/a;)LQv/x0;

    .line 227
    .line 228
    .line 229
    move-result-object p1

    .line 230
    invoke-static {p1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    .line 231
    .line 232
    .line 233
    move-result-object p1

    .line 234
    iput-object p1, p0, LM71/a$d;->B:Ldagger/internal/h;

    .line 235
    .line 236
    return-void
.end method

.method public final Z(LM71/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 21

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    .line 1
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/Q;->a(LQv/w;LBc/a;)LQv/Q;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->C:Ldagger/internal/h;

    .line 2
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/M0;->a(LQv/w;LBc/a;)LQv/M0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->D:Ldagger/internal/h;

    .line 3
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/j0;->a(LQv/w;LBc/a;)LQv/j0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->E:Ldagger/internal/h;

    .line 4
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/R0;->a(LQv/w;LBc/a;)LQv/R0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->F:Ldagger/internal/h;

    .line 5
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/S;->a(LQv/w;LBc/a;)LQv/S;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->G:Ldagger/internal/h;

    .line 6
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/T;->a(LQv/w;LBc/a;)LQv/T;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->H:Ldagger/internal/h;

    .line 7
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/z;->a(LQv/w;LBc/a;)LQv/z;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->I:Ldagger/internal/h;

    .line 8
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/K;->a(LQv/w;LBc/a;)LQv/K;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->J:Ldagger/internal/h;

    move-object/from16 v20, v2

    .line 9
    iget-object v2, v0, LM71/a$d;->m:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->n:Ldagger/internal/h;

    iget-object v4, v0, LM71/a$d;->j:Ldagger/internal/h;

    iget-object v5, v0, LM71/a$d;->o:Ldagger/internal/h;

    iget-object v6, v0, LM71/a$d;->y:Ldagger/internal/h;

    iget-object v7, v0, LM71/a$d;->z:Ldagger/internal/h;

    iget-object v8, v0, LM71/a$d;->A:Ldagger/internal/h;

    iget-object v9, v0, LM71/a$d;->B:Ldagger/internal/h;

    iget-object v10, v0, LM71/a$d;->C:Ldagger/internal/h;

    iget-object v11, v0, LM71/a$d;->D:Ldagger/internal/h;

    iget-object v12, v0, LM71/a$d;->E:Ldagger/internal/h;

    iget-object v13, v0, LM71/a$d;->k:Ldagger/internal/h;

    iget-object v14, v0, LM71/a$d;->F:Ldagger/internal/h;

    iget-object v15, v0, LM71/a$d;->x:Ldagger/internal/h;

    iget-object v1, v0, LM71/a$d;->G:Ldagger/internal/h;

    move-object/from16 v16, v1

    iget-object v1, v0, LM71/a$d;->H:Ldagger/internal/h;

    move-object/from16 v17, v1

    iget-object v1, v0, LM71/a$d;->I:Ldagger/internal/h;

    move-object/from16 v18, v1

    iget-object v1, v0, LM71/a$d;->w:Ldagger/internal/h;

    move-object/from16 v19, v1

    move-object/from16 v1, p2

    invoke-static/range {v1 .. v20}, LQv/x;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/x;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->K:Ldagger/internal/h;

    .line 10
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/O;->a(LQv/w;LBc/a;)LQv/O;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->L:Ldagger/internal/h;

    .line 11
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/z0;->a(LQv/w;LBc/a;)LQv/z0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->M:Ldagger/internal/h;

    .line 12
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/u0;->a(LQv/w;LBc/a;)LQv/u0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->N:Ldagger/internal/h;

    .line 13
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/c0;->a(LQv/w;LBc/a;)LQv/c0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v5

    iput-object v5, v0, LM71/a$d;->O:Ldagger/internal/h;

    .line 14
    iget-object v2, v0, LM71/a$d;->K:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->M:Ldagger/internal/h;

    iget-object v4, v0, LM71/a$d;->N:Ldagger/internal/h;

    iget-object v6, v0, LM71/a$d;->L:Ldagger/internal/h;

    invoke-static/range {v1 .. v6}, LQv/L;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/L;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v7

    iput-object v7, v0, LM71/a$d;->P:Ldagger/internal/h;

    .line 15
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->k:Ldagger/internal/h;

    iget-object v4, v0, LM71/a$d;->K:Ldagger/internal/h;

    iget-object v5, v0, LM71/a$d;->L:Ldagger/internal/h;

    iget-object v6, v0, LM71/a$d;->w:Ldagger/internal/h;

    invoke-static/range {v1 .. v7}, LQv/b1;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/b1;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->Q:Ldagger/internal/h;

    .line 16
    iget-object v3, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2, v3}, LQv/t0;->a(LQv/w;LBc/a;LBc/a;)LQv/t0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->R:Ldagger/internal/h;

    .line 17
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v2}, Lorg/xbet/core/domain/usecases/y;->a(LBc/a;)Lorg/xbet/core/domain/usecases/y;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->S:Ldagger/internal/h;

    .line 18
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/E0;->a(LQv/w;LBc/a;)LQv/E0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->T:Ldagger/internal/h;

    .line 19
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/W0;->a(LQv/w;LBc/a;)LQv/W0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->U:Ldagger/internal/h;

    .line 20
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/X0;->a(LQv/w;LBc/a;)LQv/X0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->V:Ldagger/internal/h;

    .line 21
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/w0;->a(LQv/w;LBc/a;)LQv/w0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->W:Ldagger/internal/h;

    .line 22
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/Q0;->a(LQv/w;LBc/a;)LQv/Q0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->X:Ldagger/internal/h;

    .line 23
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/y;->a(LQv/w;LBc/a;)LQv/y;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->Y:Ldagger/internal/h;

    .line 24
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/K0;->a(LQv/w;LBc/a;)LQv/K0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->Z:Ldagger/internal/h;

    .line 25
    iget-object v2, v0, LM71/a$d;->t:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/M;->a(LQv/w;LBc/a;)LQv/M;

    move-result-object v1

    invoke-static {v1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v1

    iput-object v1, v0, LM71/a$d;->a0:Ldagger/internal/h;

    return-void
.end method

.method public a()LQv/a$a;
    .locals 3

    .line 1
    new-instance v0, LM71/a$b;

    .line 2
    .line 3
    iget-object v1, p0, LM71/a$d;->c:LM71/a$d;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v0, v1, v2}, LM71/a$b;-><init>(LM71/a$d;LM71/b;)V

    .line 7
    .line 8
    .line 9
    return-object v0
.end method

.method public final a0(LM71/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 47

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    move-object/from16 v8, p3

    .line 1
    iget-object v2, v0, LM71/a$d;->a0:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/V0;->a(LQv/w;LBc/a;)LQv/V0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->b0:Ldagger/internal/h;

    .line 2
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/D0;->a(LQv/w;LBc/a;)LQv/D0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->c0:Ldagger/internal/h;

    .line 3
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/G0;->a(LQv/w;LBc/a;)LQv/G0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->d0:Ldagger/internal/h;

    .line 4
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/S0;->a(LQv/w;LBc/a;)LQv/S0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->e0:Ldagger/internal/h;

    .line 5
    iget-object v3, v0, LM71/a$d;->K:Ldagger/internal/h;

    invoke-static {v1, v2, v3}, LQv/J;->a(LQv/w;LBc/a;LBc/a;)LQv/J;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->f0:Ldagger/internal/h;

    .line 6
    iget-object v2, v0, LM71/a$d;->K:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->O:Ldagger/internal/h;

    iget-object v4, v0, LM71/a$d;->L:Ldagger/internal/h;

    iget-object v5, v0, LM71/a$d;->M:Ldagger/internal/h;

    iget-object v6, v0, LM71/a$d;->N:Ldagger/internal/h;

    invoke-static/range {v1 .. v6}, LQv/a1;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/a1;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->g0:Ldagger/internal/h;

    .line 7
    invoke-static/range {p1 .. p1}, LM71/e;->a(LM71/d;)LM71/e;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->h0:Ldagger/internal/h;

    .line 8
    new-instance v2, LM71/a$d$c;

    invoke-direct {v2, v8}, LM71/a$d$c;-><init>(LQv/v;)V

    iput-object v2, v0, LM71/a$d;->i0:Ldagger/internal/h;

    .line 9
    iget-object v2, v0, LM71/a$d;->K:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->M:Ldagger/internal/h;

    invoke-static {v1, v2, v3}, LQv/H;->a(LQv/w;LBc/a;LBc/a;)LQv/H;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->j0:Ldagger/internal/h;

    .line 10
    iget-object v2, v0, LM71/a$d;->a0:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/I;->a(LQv/w;LBc/a;)LQv/I;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->k0:Ldagger/internal/h;

    .line 11
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/Y0;->a(LQv/w;LBc/a;)LQv/Y0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->l0:Ldagger/internal/h;

    .line 12
    iget-object v2, v0, LM71/a$d;->i0:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2, v3}, LQv/Y;->a(LQv/w;LBc/a;LBc/a;)LQv/Y;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->m0:Ldagger/internal/h;

    .line 13
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v2}, Lorg/xbet/core/domain/usecases/k;->a(LBc/a;)Lorg/xbet/core/domain/usecases/k;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->n0:Ldagger/internal/h;

    .line 14
    new-instance v2, LM71/a$d$o;

    invoke-direct {v2, v8}, LM71/a$d$o;-><init>(LQv/v;)V

    iput-object v2, v0, LM71/a$d;->o0:Ldagger/internal/h;

    .line 15
    iget-object v3, v0, LM71/a$d;->i0:Ldagger/internal/h;

    invoke-static {v3, v2}, Lorg/xbet/core/domain/usecases/balance/g;->a(LBc/a;LBc/a;)Lorg/xbet/core/domain/usecases/balance/g;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->p0:Ldagger/internal/h;

    .line 16
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/C0;->a(LQv/w;LBc/a;)LQv/C0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->q0:Ldagger/internal/h;

    .line 17
    iget-object v9, v0, LM71/a$d;->d:Ldagger/internal/h;

    iget-object v10, v0, LM71/a$d;->f:Ldagger/internal/h;

    iget-object v11, v0, LM71/a$d;->g:Ldagger/internal/h;

    iget-object v12, v0, LM71/a$d;->h:Ldagger/internal/h;

    iget-object v13, v0, LM71/a$d;->i:Ldagger/internal/h;

    iget-object v14, v0, LM71/a$d;->R:Ldagger/internal/h;

    iget-object v15, v0, LM71/a$d;->C:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->S:Ldagger/internal/h;

    iget-object v4, v0, LM71/a$d;->T:Ldagger/internal/h;

    iget-object v5, v0, LM71/a$d;->U:Ldagger/internal/h;

    iget-object v6, v0, LM71/a$d;->E:Ldagger/internal/h;

    iget-object v7, v0, LM71/a$d;->K:Ldagger/internal/h;

    move-object/from16 v46, v2

    iget-object v2, v0, LM71/a$d;->x:Ldagger/internal/h;

    move-object/from16 v21, v2

    iget-object v2, v0, LM71/a$d;->w:Ldagger/internal/h;

    move-object/from16 v22, v2

    iget-object v2, v0, LM71/a$d;->V:Ldagger/internal/h;

    move-object/from16 v23, v2

    iget-object v2, v0, LM71/a$d;->W:Ldagger/internal/h;

    move-object/from16 v24, v2

    iget-object v2, v0, LM71/a$d;->X:Ldagger/internal/h;

    move-object/from16 v25, v2

    iget-object v2, v0, LM71/a$d;->Y:Ldagger/internal/h;

    move-object/from16 v26, v2

    iget-object v2, v0, LM71/a$d;->Z:Ldagger/internal/h;

    move-object/from16 v27, v2

    iget-object v2, v0, LM71/a$d;->b0:Ldagger/internal/h;

    move-object/from16 v28, v2

    iget-object v2, v0, LM71/a$d;->B:Ldagger/internal/h;

    move-object/from16 v29, v2

    iget-object v2, v0, LM71/a$d;->k:Ldagger/internal/h;

    move-object/from16 v30, v2

    iget-object v2, v0, LM71/a$d;->M:Ldagger/internal/h;

    move-object/from16 v31, v2

    iget-object v2, v0, LM71/a$d;->c0:Ldagger/internal/h;

    move-object/from16 v32, v2

    iget-object v2, v0, LM71/a$d;->d0:Ldagger/internal/h;

    move-object/from16 v33, v2

    iget-object v2, v0, LM71/a$d;->f0:Ldagger/internal/h;

    move-object/from16 v34, v2

    iget-object v2, v0, LM71/a$d;->g0:Ldagger/internal/h;

    move-object/from16 v35, v2

    iget-object v2, v0, LM71/a$d;->h0:Ldagger/internal/h;

    move-object/from16 v36, v2

    iget-object v2, v0, LM71/a$d;->i0:Ldagger/internal/h;

    move-object/from16 v37, v2

    iget-object v2, v0, LM71/a$d;->j0:Ldagger/internal/h;

    move-object/from16 v38, v2

    iget-object v2, v0, LM71/a$d;->D:Ldagger/internal/h;

    move-object/from16 v39, v2

    iget-object v2, v0, LM71/a$d;->k0:Ldagger/internal/h;

    move-object/from16 v40, v2

    iget-object v2, v0, LM71/a$d;->l0:Ldagger/internal/h;

    move-object/from16 v41, v2

    iget-object v2, v0, LM71/a$d;->m0:Ldagger/internal/h;

    move-object/from16 v42, v2

    iget-object v2, v0, LM71/a$d;->l:Ldagger/internal/h;

    move-object/from16 v43, v2

    iget-object v2, v0, LM71/a$d;->n0:Ldagger/internal/h;

    move-object/from16 v44, v2

    iget-object v2, v0, LM71/a$d;->p0:Ldagger/internal/h;

    move-object/from16 v45, v2

    move-object/from16 v16, v3

    move-object/from16 v17, v4

    move-object/from16 v18, v5

    move-object/from16 v19, v6

    move-object/from16 v20, v7

    invoke-static/range {v9 .. v46}, Lorg/xbet/core/presentation/holder/s;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/core/presentation/holder/s;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->r0:Lorg/xbet/core/presentation/holder/s;

    .line 18
    invoke-static {v2}, LQv/s;->c(Lorg/xbet/core/presentation/holder/s;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->s0:Ldagger/internal/h;

    .line 19
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/G;->a(LQv/w;LBc/a;)LQv/G;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->t0:Ldagger/internal/h;

    .line 20
    iget-object v2, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2}, LQv/N0;->a(LQv/w;LBc/a;)LQv/N0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v2

    iput-object v2, v0, LM71/a$d;->u0:Ldagger/internal/h;

    .line 21
    iget-object v3, v0, LM71/a$d;->j:Ldagger/internal/h;

    invoke-static {v1, v2, v3}, LQv/F0;->a(LQv/w;LBc/a;LBc/a;)LQv/F0;

    move-result-object v2

    invoke-static {v2}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v7

    iput-object v7, v0, LM71/a$d;->v0:Ldagger/internal/h;

    .line 22
    iget-object v2, v0, LM71/a$d;->K:Ldagger/internal/h;

    iget-object v3, v0, LM71/a$d;->i0:Ldagger/internal/h;

    iget-object v4, v0, LM71/a$d;->t0:Ldagger/internal/h;

    iget-object v5, v0, LM71/a$d;->H:Ldagger/internal/h;

    iget-object v6, v0, LM71/a$d;->y:Ldagger/internal/h;

    invoke-static/range {v1 .. v7}, LQv/Z0;->a(LQv/w;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)LQv/Z0;

    move-result-object v1

    invoke-static {v1}, Ldagger/internal/j;->a(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v1

    iput-object v1, v0, LM71/a$d;->w0:Ldagger/internal/h;

    .line 23
    iget-object v1, v0, LM71/a$d;->q:Ldagger/internal/h;

    invoke-static {v1}, LI71/d;->a(LBc/a;)LI71/d;

    move-result-object v1

    iput-object v1, v0, LM71/a$d;->x0:Ldagger/internal/h;

    .line 24
    invoke-static/range {p1 .. p1}, LM71/f;->a(LM71/d;)LM71/f;

    move-result-object v1

    invoke-static {v1}, Ldagger/internal/c;->d(Ldagger/internal/h;)Ldagger/internal/h;

    move-result-object v1

    iput-object v1, v0, LM71/a$d;->y0:Ldagger/internal/h;

    .line 25
    new-instance v1, LM71/a$d$l;

    invoke-direct {v1, v8}, LM71/a$d$l;-><init>(LQv/v;)V

    iput-object v1, v0, LM71/a$d;->z0:Ldagger/internal/h;

    return-void
.end method

.method public b(Lorg/xbet/witch/presentation/game/WitchGameFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LM71/a$d;->c0(Lorg/xbet/witch/presentation/game/WitchGameFragment;)Lorg/xbet/witch/presentation/game/WitchGameFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final b0(LM71/d;LQv/w;LQv/v;LwX0/c;)V
    .locals 21

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, LM71/a$d;->x0:Ldagger/internal/h;

    .line 4
    .line 5
    iget-object v2, v0, LM71/a$d;->y0:Ldagger/internal/h;

    .line 6
    .line 7
    iget-object v3, v0, LM71/a$d;->z0:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object v4, v0, LM71/a$d;->p:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static {v1, v2, v3, v4}, Lorg/xbet/witch/data/repositories/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/witch/data/repositories/a;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    iput-object v1, v0, LM71/a$d;->A0:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static {v1}, LP71/f;->a(LBc/a;)LP71/f;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    iput-object v1, v0, LM71/a$d;->B0:Ldagger/internal/h;

    .line 22
    .line 23
    iget-object v2, v0, LM71/a$d;->H:Ldagger/internal/h;

    .line 24
    .line 25
    iget-object v3, v0, LM71/a$d;->w:Ldagger/internal/h;

    .line 26
    .line 27
    iget-object v4, v0, LM71/a$d;->x:Ldagger/internal/h;

    .line 28
    .line 29
    invoke-static {v1, v2, v3, v4}, LP71/d;->a(LBc/a;LBc/a;LBc/a;LBc/a;)LP71/d;

    .line 30
    .line 31
    .line 32
    move-result-object v1

    .line 33
    iput-object v1, v0, LM71/a$d;->C0:Ldagger/internal/h;

    .line 34
    .line 35
    iget-object v1, v0, LM71/a$d;->A0:Ldagger/internal/h;

    .line 36
    .line 37
    invoke-static {v1}, LP71/h;->a(LBc/a;)LP71/h;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iput-object v1, v0, LM71/a$d;->D0:Ldagger/internal/h;

    .line 42
    .line 43
    iget-object v1, v0, LM71/a$d;->A0:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static {v1}, LP71/r;->a(LBc/a;)LP71/r;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    iput-object v1, v0, LM71/a$d;->E0:Ldagger/internal/h;

    .line 50
    .line 51
    iget-object v2, v0, LM71/a$d;->w:Ldagger/internal/h;

    .line 52
    .line 53
    invoke-static {v1, v2}, LP71/p;->a(LBc/a;LBc/a;)LP71/p;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    iput-object v1, v0, LM71/a$d;->F0:Ldagger/internal/h;

    .line 58
    .line 59
    iget-object v1, v0, LM71/a$d;->A0:Ldagger/internal/h;

    .line 60
    .line 61
    invoke-static {v1}, LP71/n;->a(LBc/a;)LP71/n;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iput-object v1, v0, LM71/a$d;->G0:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object v2, v0, LM71/a$d;->w:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {v1, v2}, LP71/l;->a(LBc/a;LBc/a;)LP71/l;

    .line 70
    .line 71
    .line 72
    move-result-object v1

    .line 73
    iput-object v1, v0, LM71/a$d;->H0:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object v1, v0, LM71/a$d;->A0:Ldagger/internal/h;

    .line 76
    .line 77
    invoke-static {v1}, LP71/j;->a(LBc/a;)LP71/j;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    iput-object v1, v0, LM71/a$d;->I0:Ldagger/internal/h;

    .line 82
    .line 83
    iget-object v1, v0, LM71/a$d;->A0:Ldagger/internal/h;

    .line 84
    .line 85
    invoke-static {v1}, LP71/b;->a(LBc/a;)LP71/b;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    iput-object v1, v0, LM71/a$d;->J0:Ldagger/internal/h;

    .line 90
    .line 91
    iget-object v2, v0, LM71/a$d;->j0:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object v3, v0, LM71/a$d;->T:Ldagger/internal/h;

    .line 94
    .line 95
    iget-object v4, v0, LM71/a$d;->k:Ldagger/internal/h;

    .line 96
    .line 97
    iget-object v5, v0, LM71/a$d;->m:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object v6, v0, LM71/a$d;->z:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object v7, v0, LM71/a$d;->h:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object v8, v0, LM71/a$d;->w0:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object v9, v0, LM71/a$d;->K:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v10, v0, LM71/a$d;->C0:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object v11, v0, LM71/a$d;->Q:Ldagger/internal/h;

    .line 110
    .line 111
    iget-object v12, v0, LM71/a$d;->D0:Ldagger/internal/h;

    .line 112
    .line 113
    iget-object v13, v0, LM71/a$d;->u0:Ldagger/internal/h;

    .line 114
    .line 115
    iget-object v14, v0, LM71/a$d;->J:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object v15, v0, LM71/a$d;->F0:Ldagger/internal/h;

    .line 118
    .line 119
    move-object/from16 v18, v1

    .line 120
    .line 121
    iget-object v1, v0, LM71/a$d;->H0:Ldagger/internal/h;

    .line 122
    .line 123
    move-object/from16 v16, v1

    .line 124
    .line 125
    iget-object v1, v0, LM71/a$d;->I0:Ldagger/internal/h;

    .line 126
    .line 127
    move-object/from16 v17, v1

    .line 128
    .line 129
    iget-object v1, v0, LM71/a$d;->x:Ldagger/internal/h;

    .line 130
    .line 131
    move-object/from16 v19, v1

    .line 132
    .line 133
    iget-object v1, v0, LM71/a$d;->m0:Ldagger/internal/h;

    .line 134
    .line 135
    move-object/from16 v20, v1

    .line 136
    .line 137
    invoke-static/range {v2 .. v20}, Lorg/xbet/witch/presentation/game/h;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/witch/presentation/game/h;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    iput-object v1, v0, LM71/a$d;->K0:Ldagger/internal/h;

    .line 142
    .line 143
    new-instance v1, LM71/a$d$j;

    .line 144
    .line 145
    move-object/from16 v2, p3

    .line 146
    .line 147
    invoke-direct {v1, v2}, LM71/a$d$j;-><init>(LQv/v;)V

    .line 148
    .line 149
    .line 150
    iput-object v1, v0, LM71/a$d;->L0:Ldagger/internal/h;

    .line 151
    .line 152
    return-void
.end method

.method public c(Lorg/xbet/witch/presentation/holder/WitchHolderFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LM71/a$d;->d0(Lorg/xbet/witch/presentation/holder/WitchHolderFragment;)Lorg/xbet/witch/presentation/holder/WitchHolderFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c0(Lorg/xbet/witch/presentation/game/WitchGameFragment;)Lorg/xbet/witch/presentation/game/WitchGameFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LM71/a$d;->f0()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/witch/presentation/game/f;->a(Lorg/xbet/witch/presentation/game/WitchGameFragment;Landroidx/lifecycle/e0$c;)V

    .line 6
    .line 7
    .line 8
    return-object p1
.end method

.method public final d0(Lorg/xbet/witch/presentation/holder/WitchHolderFragment;)Lorg/xbet/witch/presentation/holder/WitchHolderFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LM71/a$d;->a:LQv/v;

    .line 2
    .line 3
    invoke-interface {v0}, LQv/v;->e()LTZ0/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-static {v0}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    check-cast v0, LTZ0/a;

    .line 12
    .line 13
    invoke-static {p1, v0}, Lorg/xbet/core/presentation/holder/n;->a(Lorg/xbet/core/presentation/holder/OnexGamesHolderFragment;LTZ0/a;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, LM71/a$d;->s0:Ldagger/internal/h;

    .line 17
    .line 18
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    check-cast v0, LQv/a$s;

    .line 23
    .line 24
    invoke-static {p1, v0}, Lorg/xbet/witch/presentation/holder/c;->a(Lorg/xbet/witch/presentation/holder/WitchHolderFragment;LQv/a$s;)V

    .line 25
    .line 26
    .line 27
    return-object p1
.end method

.method public final e0()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/witch/presentation/game/WitchGameViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LM71/a$d;->K0:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final f0()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LM71/a$d;->e0()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
