.class public interface abstract Lcom/google/android/gms/internal/measurement/zznh;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/internal/measurement/zzni;


# virtual methods
.method public abstract zzcA()Lcom/google/android/gms/internal/measurement/zzng;
.end method

.method public abstract zzcB(Lcom/google/android/gms/internal/measurement/zzlk;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract zzcb()Lcom/google/android/gms/internal/measurement/zzld;
.end method

.method public abstract zzcf()I
.end method
