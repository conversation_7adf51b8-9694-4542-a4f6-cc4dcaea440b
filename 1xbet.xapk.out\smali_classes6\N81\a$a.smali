.class public final LN81/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN81/i;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN81/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LN81/a$a$a;,
        LN81/a$a$c;,
        LN81/a$a$b;,
        LN81/a$a$d;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/n;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/analytics/domain/scope/E;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/b;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/d;",
            ">;"
        }
    .end annotation
.end field

.field public F:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lv81/u;",
            ">;"
        }
    .end annotation
.end field

.field public G:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public H:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public I:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public J:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lo9/a;",
            ">;"
        }
    .end annotation
.end field

.field public K:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lp9/g;",
            ">;"
        }
    .end annotation
.end field

.field public L:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LzX0/k;

.field public final b:LTZ0/a;

.field public final c:Lorg/xbet/analytics/domain/scope/E;

.field public final d:LN81/a$a;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lek/d;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJ81/c;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LJ81/a;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/c;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/f;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/p;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/l;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksHistoryWrapperScenario;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/r;",
            ">;"
        }
    .end annotation
.end field

.field public y:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetHistoryTasksScenario;",
            ">;"
        }
    .end annotation
.end field

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LR81/z;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;)V
    .locals 3

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LN81/a$a;->d:LN81/a$a;

    move-object/from16 v0, p16

    .line 4
    iput-object v0, p0, LN81/a$a;->a:LzX0/k;

    move-object/from16 v1, p20

    .line 5
    iput-object v1, p0, LN81/a$a;->b:LTZ0/a;

    move-object/from16 v2, p19

    .line 6
    iput-object v2, p0, LN81/a$a;->c:Lorg/xbet/analytics/domain/scope/E;

    .line 7
    invoke-virtual/range {p0 .. p25}, LN81/a$a;->c(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;)V

    .line 8
    invoke-virtual/range {p0 .. p25}, LN81/a$a;->d(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;LN81/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p25}, LN81/a$a;-><init>(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LN81/a$a;->f(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskMoreInfoDialog;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LN81/a$a;->e(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskMoreInfoDialog;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskMoreInfoDialog;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;)V
    .locals 0

    .line 1
    invoke-static/range {p25 .. p25}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LN81/a$a;->e:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LN81/a$a;->f:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LN81/a$a;->g:Ldagger/internal/h;

    .line 18
    .line 19
    new-instance p3, LN81/a$a$a;

    .line 20
    .line 21
    invoke-direct {p3, p1}, LN81/a$a$a;-><init>(LQW0/c;)V

    .line 22
    .line 23
    .line 24
    iput-object p3, p0, LN81/a$a;->h:Ldagger/internal/h;

    .line 25
    .line 26
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    iput-object p1, p0, LN81/a$a;->i:Ldagger/internal/h;

    .line 31
    .line 32
    new-instance p1, LN81/a$a$c;

    .line 33
    .line 34
    invoke-direct {p1, p2}, LN81/a$a$c;-><init>(Lak/a;)V

    .line 35
    .line 36
    .line 37
    iput-object p1, p0, LN81/a$a;->j:Ldagger/internal/h;

    .line 38
    .line 39
    invoke-static {p13}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iput-object p1, p0, LN81/a$a;->k:Ldagger/internal/h;

    .line 44
    .line 45
    invoke-static {p12}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 46
    .line 47
    .line 48
    move-result-object p1

    .line 49
    iput-object p1, p0, LN81/a$a;->l:Ldagger/internal/h;

    .line 50
    .line 51
    invoke-static {p10}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iput-object p1, p0, LN81/a$a;->m:Ldagger/internal/h;

    .line 56
    .line 57
    invoke-static {p1}, LJ81/d;->a(LBc/a;)LJ81/d;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    iput-object p1, p0, LN81/a$a;->n:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, LN81/a$a;->o:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static {p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iput-object p1, p0, LN81/a$a;->p:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object p2, p0, LN81/a$a;->n:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object p3, p0, LN81/a$a;->o:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object p4, p0, LN81/a$a;->h:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static {p2, p3, p1, p4}, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/a;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static {p1}, LR81/d;->a(LBc/a;)LR81/d;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LN81/a$a;->r:Ldagger/internal/h;

    .line 92
    .line 93
    iget-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 94
    .line 95
    invoke-static {p1}, LR81/g;->a(LBc/a;)LR81/g;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iput-object p1, p0, LN81/a$a;->s:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 102
    .line 103
    invoke-static {p1}, LR81/q;->a(LBc/a;)LR81/q;

    .line 104
    .line 105
    .line 106
    move-result-object p1

    .line 107
    iput-object p1, p0, LN81/a$a;->t:Ldagger/internal/h;

    .line 108
    .line 109
    iget-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 110
    .line 111
    invoke-static {p1}, LR81/m;->a(LBc/a;)LR81/m;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    iput-object p1, p0, LN81/a$a;->u:Ldagger/internal/h;

    .line 116
    .line 117
    iget-object p2, p0, LN81/a$a;->r:Ldagger/internal/h;

    .line 118
    .line 119
    iget-object p3, p0, LN81/a$a;->s:Ldagger/internal/h;

    .line 120
    .line 121
    iget-object p4, p0, LN81/a$a;->t:Ldagger/internal/h;

    .line 122
    .line 123
    invoke-static {p2, p3, p4, p1}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/a;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    iput-object p1, p0, LN81/a$a;->v:Ldagger/internal/h;

    .line 128
    .line 129
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    iput-object p1, p0, LN81/a$a;->w:Ldagger/internal/h;

    .line 134
    .line 135
    iget-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 136
    .line 137
    invoke-static {p1}, LR81/s;->a(LBc/a;)LR81/s;

    .line 138
    .line 139
    .line 140
    move-result-object p1

    .line 141
    iput-object p1, p0, LN81/a$a;->x:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object p2, p0, LN81/a$a;->w:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object p3, p0, LN81/a$a;->j:Ldagger/internal/h;

    .line 146
    .line 147
    invoke-static {p2, p3, p1}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/b;->a(LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/b;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    iput-object p1, p0, LN81/a$a;->y:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 154
    .line 155
    invoke-static {p1}, LR81/A;->a(LBc/a;)LR81/A;

    .line 156
    .line 157
    .line 158
    move-result-object p1

    .line 159
    iput-object p1, p0, LN81/a$a;->z:Ldagger/internal/h;

    .line 160
    .line 161
    iget-object p1, p0, LN81/a$a;->q:Ldagger/internal/h;

    .line 162
    .line 163
    invoke-static {p1}, LR81/o;->a(LBc/a;)LR81/o;

    .line 164
    .line 165
    .line 166
    move-result-object p1

    .line 167
    iput-object p1, p0, LN81/a$a;->A:Ldagger/internal/h;

    .line 168
    .line 169
    invoke-static/range {p18 .. p18}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 170
    .line 171
    .line 172
    move-result-object p1

    .line 173
    iput-object p1, p0, LN81/a$a;->B:Ldagger/internal/h;

    .line 174
    .line 175
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 176
    .line 177
    .line 178
    move-result-object p1

    .line 179
    iput-object p1, p0, LN81/a$a;->C:Ldagger/internal/h;

    .line 180
    .line 181
    return-void
.end method

.method public final d(LQW0/c;Lak/a;Lz81/a;LwX0/C;Lc8/h;Lorg/xbet/ui_common/utils/M;LHX0/e;LRf0/l;Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lf8/g;LxX0/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/remoteconfig/domain/usecases/i;LJ81/a;LzX0/k;LwX0/c;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LTZ0/a;Lo9/a;Lv81/b;Lv81/d;Lv81/u;LwX0/c;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p2

    .line 4
    .line 5
    invoke-static/range {p22 .. p22}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    iput-object v2, v0, LN81/a$a;->D:Ldagger/internal/h;

    .line 10
    .line 11
    invoke-static/range {p23 .. p23}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    iput-object v2, v0, LN81/a$a;->E:Ldagger/internal/h;

    .line 16
    .line 17
    invoke-static/range {p24 .. p24}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    iput-object v2, v0, LN81/a$a;->F:Ldagger/internal/h;

    .line 22
    .line 23
    new-instance v2, LN81/a$a$b;

    .line 24
    .line 25
    invoke-direct {v2, v1}, LN81/a$a$b;-><init>(Lak/a;)V

    .line 26
    .line 27
    .line 28
    iput-object v2, v0, LN81/a$a;->G:Ldagger/internal/h;

    .line 29
    .line 30
    new-instance v2, LN81/a$a$d;

    .line 31
    .line 32
    invoke-direct {v2, v1}, LN81/a$a$d;-><init>(Lak/a;)V

    .line 33
    .line 34
    .line 35
    iput-object v2, v0, LN81/a$a;->H:Ldagger/internal/h;

    .line 36
    .line 37
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    iput-object v1, v0, LN81/a$a;->I:Ldagger/internal/h;

    .line 42
    .line 43
    invoke-static/range {p21 .. p21}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    iput-object v1, v0, LN81/a$a;->J:Ldagger/internal/h;

    .line 48
    .line 49
    invoke-static {v1}, Lp9/h;->a(LBc/a;)Lp9/h;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    iput-object v1, v0, LN81/a$a;->K:Ldagger/internal/h;

    .line 54
    .line 55
    iget-object v2, v0, LN81/a$a;->e:Ldagger/internal/h;

    .line 56
    .line 57
    iget-object v3, v0, LN81/a$a;->f:Ldagger/internal/h;

    .line 58
    .line 59
    iget-object v4, v0, LN81/a$a;->g:Ldagger/internal/h;

    .line 60
    .line 61
    iget-object v5, v0, LN81/a$a;->h:Ldagger/internal/h;

    .line 62
    .line 63
    iget-object v6, v0, LN81/a$a;->i:Ldagger/internal/h;

    .line 64
    .line 65
    iget-object v7, v0, LN81/a$a;->j:Ldagger/internal/h;

    .line 66
    .line 67
    iget-object v8, v0, LN81/a$a;->k:Ldagger/internal/h;

    .line 68
    .line 69
    iget-object v9, v0, LN81/a$a;->l:Ldagger/internal/h;

    .line 70
    .line 71
    iget-object v10, v0, LN81/a$a;->v:Ldagger/internal/h;

    .line 72
    .line 73
    iget-object v11, v0, LN81/a$a;->y:Ldagger/internal/h;

    .line 74
    .line 75
    iget-object v12, v0, LN81/a$a;->z:Ldagger/internal/h;

    .line 76
    .line 77
    iget-object v13, v0, LN81/a$a;->A:Ldagger/internal/h;

    .line 78
    .line 79
    iget-object v14, v0, LN81/a$a;->B:Ldagger/internal/h;

    .line 80
    .line 81
    iget-object v15, v0, LN81/a$a;->C:Ldagger/internal/h;

    .line 82
    .line 83
    move-object/from16 p21, v1

    .line 84
    .line 85
    iget-object v1, v0, LN81/a$a;->D:Ldagger/internal/h;

    .line 86
    .line 87
    move-object/from16 p15, v1

    .line 88
    .line 89
    iget-object v1, v0, LN81/a$a;->E:Ldagger/internal/h;

    .line 90
    .line 91
    move-object/from16 p16, v1

    .line 92
    .line 93
    iget-object v1, v0, LN81/a$a;->F:Ldagger/internal/h;

    .line 94
    .line 95
    move-object/from16 p17, v1

    .line 96
    .line 97
    iget-object v1, v0, LN81/a$a;->G:Ldagger/internal/h;

    .line 98
    .line 99
    move-object/from16 p18, v1

    .line 100
    .line 101
    iget-object v1, v0, LN81/a$a;->H:Ldagger/internal/h;

    .line 102
    .line 103
    move-object/from16 p19, v1

    .line 104
    .line 105
    iget-object v1, v0, LN81/a$a;->I:Ldagger/internal/h;

    .line 106
    .line 107
    move-object/from16 p20, v1

    .line 108
    .line 109
    move-object/from16 p1, v2

    .line 110
    .line 111
    move-object/from16 p2, v3

    .line 112
    .line 113
    move-object/from16 p3, v4

    .line 114
    .line 115
    move-object/from16 p4, v5

    .line 116
    .line 117
    move-object/from16 p5, v6

    .line 118
    .line 119
    move-object/from16 p6, v7

    .line 120
    .line 121
    move-object/from16 p7, v8

    .line 122
    .line 123
    move-object/from16 p8, v9

    .line 124
    .line 125
    move-object/from16 p9, v10

    .line 126
    .line 127
    move-object/from16 p10, v11

    .line 128
    .line 129
    move-object/from16 p11, v12

    .line 130
    .line 131
    move-object/from16 p12, v13

    .line 132
    .line 133
    move-object/from16 p13, v14

    .line 134
    .line 135
    move-object/from16 p14, v15

    .line 136
    .line 137
    invoke-static/range {p1 .. p21}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/H;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/H;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    iput-object v1, v0, LN81/a$a;->L:Ldagger/internal/h;

    .line 142
    .line 143
    return-void
.end method

.method public final e(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskMoreInfoDialog;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskMoreInfoDialog;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LN81/a$a;->c:Lorg/xbet/analytics/domain/scope/E;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/c;->a(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTaskMoreInfoDialog;Lorg/xbet/analytics/domain/scope/E;)V

    .line 4
    .line 5
    .line 6
    return-object p1
.end method

.method public final f(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;)Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LN81/a$a;->h()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/u;->c(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;Landroidx/lifecycle/e0$c;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LN81/a$a;->a:LzX0/k;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/u;->b(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;LzX0/k;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LN81/a$a;->b:LTZ0/a;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/u;->a(Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksFragment;LTZ0/a;)V

    .line 16
    .line 17
    .line 18
    return-object p1
.end method

.method public final g()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/DailyTasksViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LN81/a$a;->L:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final h()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/a$a;->g()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
