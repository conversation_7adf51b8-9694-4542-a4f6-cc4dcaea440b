.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Z

.field public final synthetic d:Landroidx/compose/ui/l;

.field public final synthetic e:Lkotlin/jvm/functions/Function0;

.field public final synthetic f:I

.field public final synthetic g:I


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->a:Ljava/lang/String;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->b:Ljava/lang/String;

    iput-boolean p3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->c:Z

    iput-object p4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->d:Landroidx/compose/ui/l;

    iput-object p5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->e:Lkotlin/jvm/functions/Function0;

    iput p6, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->f:I

    iput p7, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->g:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->b:Ljava/lang/String;

    iget-boolean v2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->c:Z

    iget-object v3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->d:Landroidx/compose/ui/l;

    iget-object v4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->e:Lkotlin/jvm/functions/Function0;

    iget v5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->f:I

    iget v6, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/d;->g:I

    move-object v7, p1

    check-cast v7, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v8

    invoke-static/range {v0 .. v8}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->m(Ljava/lang/String;Ljava/lang/String;ZLandroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
