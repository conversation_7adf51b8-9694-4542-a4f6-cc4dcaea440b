.class public final Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0001\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\u0008H\u0086B\u00a2\u0006\u0004\u0008\t\u0010\nR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u000bR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\r\u00a8\u0006\u000e"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;",
        "",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/h;",
        "getTaxStatusUseCase",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "getProfileUseCase",
        "<init>",
        "(Lorg/xbet/toto_bet/makebet/domain/usecase/h;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;)V",
        "LIU0/a;",
        "a",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lorg/xbet/toto_bet/makebet/domain/usecase/h;",
        "b",
        "Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/toto_bet/makebet/domain/usecase/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lorg/xbet/toto_bet/makebet/domain/usecase/h;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;)V
    .locals 0
    .param p1    # Lorg/xbet/toto_bet/makebet/domain/usecase/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;->a:Lorg/xbet/toto_bet/makebet/domain/usecase/h;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;->b:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p1    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "LIU0/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    instance-of v0, p1, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;

    .line 7
    .line 8
    iget v1, v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;->label:I

    .line 9
    .line 10
    const/high16 v2, -0x80000000

    .line 11
    .line 12
    and-int v3, v1, v2

    .line 13
    .line 14
    if-eqz v3, :cond_0

    .line 15
    .line 16
    sub-int/2addr v1, v2

    .line 17
    iput v1, v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;->label:I

    .line 18
    .line 19
    goto :goto_0

    .line 20
    :cond_0
    new-instance v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;

    .line 21
    .line 22
    invoke-direct {v0, p0, p1}, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;-><init>(Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;Lkotlin/coroutines/e;)V

    .line 23
    .line 24
    .line 25
    :goto_0
    iget-object p1, v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;->result:Ljava/lang/Object;

    .line 26
    .line 27
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    iget v2, v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;->label:I

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    const/4 v4, 0x2

    .line 35
    const/4 v5, 0x1

    .line 36
    if-eqz v2, :cond_3

    .line 37
    .line 38
    if-eq v2, v5, :cond_2

    .line 39
    .line 40
    if-ne v2, v4, :cond_1

    .line 41
    .line 42
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    return-object p1

    .line 46
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 47
    .line 48
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 49
    .line 50
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    throw p1

    .line 54
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 59
    .line 60
    .line 61
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;->b:Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;

    .line 62
    .line 63
    iput v5, v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;->label:I

    .line 64
    .line 65
    invoke-virtual {p1, v3, v0}, Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;->c(ZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    if-ne p1, v1, :cond_4

    .line 70
    .line 71
    goto :goto_2

    .line 72
    :cond_4
    :goto_1
    check-cast p1, Le9/a;

    .line 73
    .line 74
    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario;->a:Lorg/xbet/toto_bet/makebet/domain/usecase/h;

    .line 75
    .line 76
    invoke-virtual {p1}, Le9/a;->w()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v5

    .line 80
    invoke-static {v5}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object v5

    .line 84
    if-eqz v5, :cond_5

    .line 85
    .line 86
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 87
    .line 88
    .line 89
    move-result v3

    .line 90
    :cond_5
    invoke-virtual {p1}, Le9/a;->u()J

    .line 91
    .line 92
    .line 93
    move-result-wide v5

    .line 94
    iput v4, v0, Lorg/xbet/toto_bet/makebet/domain/usecase/GetTaxStatusScenario$invoke$1;->label:I

    .line 95
    .line 96
    invoke-virtual {v2, v3, v5, v6, v0}, Lorg/xbet/toto_bet/makebet/domain/usecase/h;->a(IJLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    if-ne p1, v1, :cond_6

    .line 101
    .line 102
    :goto_2
    return-object v1

    .line 103
    :cond_6
    return-object p1
.end method
