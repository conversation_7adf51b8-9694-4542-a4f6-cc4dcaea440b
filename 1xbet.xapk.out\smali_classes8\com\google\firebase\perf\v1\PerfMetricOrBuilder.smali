.class public interface abstract Lcom/google/firebase/perf/v1/PerfMetricOrBuilder;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract c()Lcom/google/firebase/perf/v1/NetworkRequestMetric;
.end method

.method public abstract e()Lcom/google/firebase/perf/v1/GaugeMetric;
.end method

.method public abstract f()Z
.end method

.method public abstract g()Z
.end method

.method public abstract h()Z
.end method

.method public abstract k()Lcom/google/firebase/perf/v1/TraceMetric;
.end method
