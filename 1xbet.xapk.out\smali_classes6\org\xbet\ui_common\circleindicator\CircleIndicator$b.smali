.class public final Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;
.super Landroidx/recyclerview/widget/RecyclerView$i;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/ui_common/circleindicator/CircleIndicator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0019\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0004*\u0001\u0000\u0008\n\u0018\u00002\u00020\u0001J\u001f\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u0008\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0002H\u0016\u00a2\u0006\u0004\u0008\u0008\u0010\u0007\u00a8\u0006\t"
    }
    d2 = {
        "org/xbet/ui_common/circleindicator/CircleIndicator$b",
        "Landroidx/recyclerview/widget/RecyclerView$i;",
        "",
        "positionStart",
        "itemCount",
        "",
        "onItemRangeInserted",
        "(II)V",
        "onItemRangeRemoved",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;


# direct methods
.method public constructor <init>(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-direct {p0}, Landroidx/recyclerview/widget/RecyclerView$i;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public onItemRangeInserted(II)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-static {p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/recyclerview/widget/RecyclerView;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    if-eqz p2, :cond_0

    .line 8
    .line 9
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$LayoutManager;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 p2, 0x0

    .line 15
    :goto_0
    invoke-static {p1, p2}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->d(Lorg/xbet/ui_common/circleindicator/CircleIndicator;Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)I

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    invoke-static {p1, p2}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 20
    .line 21
    .line 22
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 23
    .line 24
    invoke-static {p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/recyclerview/widget/RecyclerView;

    .line 25
    .line 26
    .line 27
    move-result-object p2

    .line 28
    if-eqz p2, :cond_1

    .line 29
    .line 30
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 31
    .line 32
    .line 33
    move-result-object p2

    .line 34
    if-eqz p2, :cond_1

    .line 35
    .line 36
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 37
    .line 38
    .line 39
    move-result p2

    .line 40
    goto :goto_1

    .line 41
    :cond_1
    const/4 p2, 0x0

    .line 42
    :goto_1
    invoke-static {p1, p2}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public onItemRangeRemoved(II)V
    .locals 0

    .line 1
    iget-object p2, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 2
    .line 3
    invoke-static {p2, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->b(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    invoke-static {p2, p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->g(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 8
    .line 9
    .line 10
    iget-object p1, p0, Lorg/xbet/ui_common/circleindicator/CircleIndicator$b;->a:Lorg/xbet/ui_common/circleindicator/CircleIndicator;

    .line 11
    .line 12
    invoke-static {p1}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->c(Lorg/xbet/ui_common/circleindicator/CircleIndicator;)Landroidx/recyclerview/widget/RecyclerView;

    .line 13
    .line 14
    .line 15
    move-result-object p2

    .line 16
    if-eqz p2, :cond_0

    .line 17
    .line 18
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView;->getAdapter()Landroidx/recyclerview/widget/RecyclerView$Adapter;

    .line 19
    .line 20
    .line 21
    move-result-object p2

    .line 22
    if-eqz p2, :cond_0

    .line 23
    .line 24
    invoke-virtual {p2}, Landroidx/recyclerview/widget/RecyclerView$Adapter;->getItemCount()I

    .line 25
    .line 26
    .line 27
    move-result p2

    .line 28
    goto :goto_0

    .line 29
    :cond_0
    const/4 p2, 0x0

    .line 30
    :goto_0
    invoke-static {p1, p2}, Lorg/xbet/ui_common/circleindicator/CircleIndicator;->h(Lorg/xbet/ui_common/circleindicator/CircleIndicator;I)V

    .line 31
    .line 32
    .line 33
    return-void
.end method
