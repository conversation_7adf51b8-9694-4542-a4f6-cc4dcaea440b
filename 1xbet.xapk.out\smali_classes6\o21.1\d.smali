.class public final synthetic Lo21/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lo21/d;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lo21/d;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;

    check-cast p1, Landroid/graphics/drawable/Drawable;

    invoke-static {v0, p1}, Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;->a(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/view/DsBackgroundIllustrationView;Landroid/graphics/drawable/Drawable;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
