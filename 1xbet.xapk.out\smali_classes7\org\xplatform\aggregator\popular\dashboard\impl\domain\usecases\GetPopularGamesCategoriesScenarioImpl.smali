.class public final Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LUb1/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0010\t\n\u0002\u0008\u0018\u0008\u0000\u0018\u0000 >2\u00020\u0001:\u0001\u0019BA\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J,\u0010\u0019\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00180\u00170\u00162\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u0014H\u0096\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ#\u0010\u001b\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00180\u00170\u00162\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ#\u0010\u001d\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00180\u00170\u00162\u0006\u0010\u0013\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001cJM\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u00172\u000c\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u00172\u000c\u0010 \u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u00172\u000c\u0010!\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u00172\u000c\u0010\"\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u0017H\u0002\u00a2\u0006\u0004\u0008#\u0010$J+\u0010%\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00180\u00170\u0016*\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00180\u00170\u0016H\u0002\u00a2\u0006\u0004\u0008%\u0010&J3\u0010+\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001e0\u00170\u00162\u0006\u0010(\u001a\u00020\'2\u0006\u0010)\u001a\u00020\'2\u0006\u0010*\u001a\u00020\u0012H\u0002\u00a2\u0006\u0004\u0008+\u0010,J&\u0010-\u001a\u0008\u0012\u0004\u0012\u00020\u001e0\u00172\u0006\u0010)\u001a\u00020\'2\u0006\u0010*\u001a\u00020\u0012H\u0082@\u00a2\u0006\u0004\u0008-\u0010.J\u001b\u0010/\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u001e0\u00170\u0016H\u0002\u00a2\u0006\u0004\u0008/\u00100R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u00101R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00082\u00103R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00084\u00105R\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00086\u00107R\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00088\u00109R\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008:\u0010;R\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=\u00a8\u0006?"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;",
        "LUb1/b;",
        "Lv81/r;",
        "getPopularGamesUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Li8/j;",
        "getServiceUseCase",
        "Lcom/xbet/onexuser/domain/user/c;",
        "userInteractor",
        "Lv81/m;",
        "getFavoriteGamesFlowUseCase",
        "Lv81/f;",
        "clearFavoritesCacheUseCase",
        "Lv81/j;",
        "getCategoriesUseCase",
        "<init>",
        "(Lv81/r;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;Lcom/xbet/onexuser/domain/user/c;Lv81/m;Lv81/f;Lv81/j;)V",
        "",
        "limitLoadGames",
        "",
        "isVirtual",
        "Lkotlinx/coroutines/flow/e;",
        "",
        "LTb1/a;",
        "a",
        "(IZ)Lkotlinx/coroutines/flow/e;",
        "m",
        "(I)Lkotlinx/coroutines/flow/e;",
        "l",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "slotsExclusive",
        "aggregatorLive",
        "slotsPopular",
        "aggregatorPopular",
        "k",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;",
        "q",
        "(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;",
        "",
        "partitionId",
        "categoryId",
        "limit",
        "o",
        "(JJI)Lkotlinx/coroutines/flow/e;",
        "p",
        "(JILkotlin/coroutines/e;)Ljava/lang/Object;",
        "n",
        "()Lkotlinx/coroutines/flow/e;",
        "Lv81/r;",
        "b",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "c",
        "Li8/j;",
        "d",
        "Lcom/xbet/onexuser/domain/user/c;",
        "e",
        "Lv81/m;",
        "f",
        "Lv81/f;",
        "g",
        "Lv81/j;",
        "h",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final h:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:Lv81/r;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Li8/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Lcom/xbet/onexuser/domain/user/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Lv81/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final f:Lv81/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Lv81/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->h:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$a;

    return-void
.end method

.method public constructor <init>(Lv81/r;Lorg/xbet/remoteconfig/domain/usecases/i;Li8/j;Lcom/xbet/onexuser/domain/user/c;Lv81/m;Lv81/f;Lv81/j;)V
    .locals 0
    .param p1    # Lv81/r;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Li8/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lcom/xbet/onexuser/domain/user/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lv81/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lv81/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lv81/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->a:Lv81/r;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->c:Li8/j;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->d:Lcom/xbet/onexuser/domain/user/c;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->e:Lv81/m;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->f:Lv81/f;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->g:Lv81/j;

    .line 17
    .line 18
    return-void
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->k(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lv81/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->f:Lv81/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lkotlinx/coroutines/flow/e;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->n()Lkotlinx/coroutines/flow/e;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lv81/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->g:Lv81/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lv81/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->e:Lv81/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lorg/xbet/remoteconfig/domain/usecases/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Li8/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->c:Li8/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic i(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)Lcom/xbet/onexuser/domain/user/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->d:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic j(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;JILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->p(JILkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public a(IZ)Lkotlinx/coroutines/flow/e;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(IZ)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    if-eqz p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->m(I)Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1

    .line 8
    :cond_0
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->l(I)Lkotlinx/coroutines/flow/e;

    .line 9
    .line 10
    .line 11
    move-result-object p1

    .line 12
    return-object p1
.end method

.method public final k(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;
    .locals 12
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;)",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 6
    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 8
    .line 9
    .line 10
    move-result-wide v3

    .line 11
    sget-object v7, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->EXCLUSIVE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 12
    .line 13
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 14
    .line 15
    .line 16
    move-result-object v8

    .line 17
    new-instance v2, LTb1/a;

    .line 18
    .line 19
    const-string v5, ""

    .line 20
    .line 21
    const/4 v9, 0x0

    .line 22
    move-object v6, p1

    .line 23
    invoke-direct/range {v2 .. v9}, LTb1/a;-><init>(JLjava/lang/String;Ljava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/util/List;Z)V

    .line 24
    .line 25
    .line 26
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    sget-object p1, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 30
    .line 31
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 32
    .line 33
    .line 34
    move-result-wide v3

    .line 35
    sget-object v7, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->ONE_X_LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 36
    .line 37
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 38
    .line 39
    .line 40
    move-result-object v8

    .line 41
    new-instance v2, LTb1/a;

    .line 42
    .line 43
    const-string v5, ""

    .line 44
    .line 45
    move-object v6, p2

    .line 46
    invoke-direct/range {v2 .. v9}, LTb1/a;-><init>(JLjava/lang/String;Ljava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/util/List;Z)V

    .line 47
    .line 48
    .line 49
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 50
    .line 51
    .line 52
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 53
    .line 54
    .line 55
    move-result-wide v4

    .line 56
    sget-object v8, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->SLOTS_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 57
    .line 58
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 59
    .line 60
    .line 61
    move-result-object v9

    .line 62
    new-instance v3, LTb1/a;

    .line 63
    .line 64
    const-string v6, ""

    .line 65
    .line 66
    const/4 v10, 0x0

    .line 67
    move-object v7, p3

    .line 68
    invoke-direct/range {v3 .. v10}, LTb1/a;-><init>(JLjava/lang/String;Ljava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/util/List;Z)V

    .line 69
    .line 70
    .line 71
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 72
    .line 73
    .line 74
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 75
    .line 76
    .line 77
    move-result-wide v5

    .line 78
    sget-object v9, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->LIVE_AGGREGATOR_POPULAR:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 79
    .line 80
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v10

    .line 84
    new-instance v4, LTb1/a;

    .line 85
    .line 86
    const-string v7, ""

    .line 87
    .line 88
    const/4 v11, 0x0

    .line 89
    move-object/from16 v8, p4

    .line 90
    .line 91
    invoke-direct/range {v4 .. v11}, LTb1/a;-><init>(JLjava/lang/String;Ljava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/util/List;Z)V

    .line 92
    .line 93
    .line 94
    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 95
    .line 96
    .line 97
    invoke-static {v0}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 98
    .line 99
    .line 100
    move-result-object p1

    .line 101
    new-instance p2, Ljava/util/ArrayList;

    .line 102
    .line 103
    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V

    .line 104
    .line 105
    .line 106
    invoke-interface {p1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 111
    .line 112
    .line 113
    move-result v0

    .line 114
    if-eqz v0, :cond_1

    .line 115
    .line 116
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    move-object v1, v0

    .line 121
    check-cast v1, LTb1/a;

    .line 122
    .line 123
    invoke-virtual {v1}, LTb1/a;->c()Ljava/util/List;

    .line 124
    .line 125
    .line 126
    move-result-object v1

    .line 127
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 128
    .line 129
    .line 130
    move-result v1

    .line 131
    if-nez v1, :cond_0

    .line 132
    .line 133
    invoke-interface {p2, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 134
    .line 135
    .line 136
    goto :goto_0

    .line 137
    :cond_1
    return-object p2
.end method

.method public final l(I)Lkotlinx/coroutines/flow/e;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;>;"
        }
    .end annotation

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/api/model/PartitionType;->SLOTS:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 4
    .line 5
    .line 6
    move-result-wide v2

    .line 7
    const-wide/16 v4, 0x59

    .line 8
    .line 9
    move-object v1, p0

    .line 10
    move v6, p1

    .line 11
    invoke-virtual/range {v1 .. v6}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->o(JJI)Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    sget-object v7, Lorg/xplatform/aggregator/api/model/PartitionType;->LIVE_AGGREGATOR:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 16
    .line 17
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 18
    .line 19
    .line 20
    move-result-wide v2

    .line 21
    const-wide/16 v4, 0x4b

    .line 22
    .line 23
    invoke-virtual/range {v1 .. v6}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->o(JJI)Lkotlinx/coroutines/flow/e;

    .line 24
    .line 25
    .line 26
    move-result-object v8

    .line 27
    invoke-virtual {v0}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 28
    .line 29
    .line 30
    move-result-wide v2

    .line 31
    const-wide/16 v4, 0x11

    .line 32
    .line 33
    invoke-virtual/range {v1 .. v6}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->o(JJI)Lkotlinx/coroutines/flow/e;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    invoke-virtual {v7}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 38
    .line 39
    .line 40
    move-result-wide v2

    .line 41
    invoke-virtual/range {v1 .. v6}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->o(JJI)Lkotlinx/coroutines/flow/e;

    .line 42
    .line 43
    .line 44
    move-result-object v2

    .line 45
    new-instance v3, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;

    .line 46
    .line 47
    const/4 v4, 0x0

    .line 48
    invoke-direct {v3, p0, v4}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;-><init>(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;Lkotlin/coroutines/e;)V

    .line 49
    .line 50
    .line 51
    invoke-static {p1, v8, v0, v2, v3}, Lkotlinx/coroutines/flow/g;->q(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/p;)Lkotlinx/coroutines/flow/e;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;

    .line 56
    .line 57
    invoke-direct {v0, v4, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V

    .line 58
    .line 59
    .line 60
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    return-object p1
.end method

.method public final m(I)Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategoriesWithVirtual$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, p0, p1, v1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategoriesWithVirtual$1;-><init>(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;ILkotlin/coroutines/e;)V

    .line 5
    .line 6
    .line 7
    invoke-static {v0}, Lkotlinx/coroutines/flow/g;->V(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->q(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    return-object p1
.end method

.method public final n()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->d:Lcom/xbet/onexuser/domain/user/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/xbet/onexuser/domain/user/c;->d()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, v2, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getFavoritesStream$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    return-object v0
.end method

.method public final o(JJI)Lkotlinx/coroutines/flow/e;
    .locals 20
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JJI)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;"
        }
    .end annotation

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->a:Lv81/r;

    .line 4
    .line 5
    const-wide/16 v2, 0x0

    .line 6
    .line 7
    cmp-long v4, p3, v2

    .line 8
    .line 9
    if-eqz v4, :cond_0

    .line 10
    .line 11
    invoke-static/range {p3 .. p4}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    :goto_0
    move-object v4, v2

    .line 20
    goto :goto_1

    .line 21
    :cond_0
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v2

    .line 25
    goto :goto_0

    .line 26
    :goto_1
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 27
    .line 28
    .line 29
    move-result-object v5

    .line 30
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 31
    .line 32
    invoke-interface {v2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 33
    .line 34
    .line 35
    move-result-object v2

    .line 36
    invoke-virtual {v2}, Lek0/o;->o()Lek0/a;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    invoke-virtual {v2}, Lek0/a;->c()Z

    .line 41
    .line 42
    .line 43
    move-result v7

    .line 44
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->c:Li8/j;

    .line 45
    .line 46
    invoke-interface {v2}, Li8/j;->invoke()Ljava/lang/String;

    .line 47
    .line 48
    .line 49
    move-result-object v6

    .line 50
    const/4 v9, 0x0

    .line 51
    move-wide/from16 v2, p1

    .line 52
    .line 53
    move/from16 v8, p5

    .line 54
    .line 55
    invoke-interface/range {v1 .. v9}, Lv81/r;->a(JLjava/util/List;Ljava/util/List;Ljava/lang/String;ZIZ)Lkotlinx/coroutines/flow/e;

    .line 56
    .line 57
    .line 58
    move-result-object v10

    .line 59
    const/16 v18, 0x38

    .line 60
    .line 61
    const/16 v19, 0x0

    .line 62
    .line 63
    const-string v11, "GetPopularGamesCategoriesScenarioImpl.getGames"

    .line 64
    .line 65
    const/4 v12, 0x3

    .line 66
    const-wide/16 v13, 0x3

    .line 67
    .line 68
    const/4 v15, 0x0

    .line 69
    const/16 v16, 0x0

    .line 70
    .line 71
    const/16 v17, 0x0

    .line 72
    .line 73
    invoke-static/range {v10 .. v19}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->e(Lkotlinx/coroutines/flow/e;Ljava/lang/String;IJLjava/util/List;Ljava/util/List;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    new-instance v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getGames$1;

    .line 78
    .line 79
    const/4 v3, 0x0

    .line 80
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getGames$1;-><init>(Lkotlin/coroutines/e;)V

    .line 81
    .line 82
    .line 83
    invoke-static {v1, v2}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    return-object v1
.end method

.method public final p(JILkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 25
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(JI",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p4

    .line 4
    .line 5
    instance-of v2, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    move-object v2, v0

    .line 10
    check-cast v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;

    .line 11
    .line 12
    iget v3, v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;->label:I

    .line 13
    .line 14
    const/high16 v4, -0x80000000

    .line 15
    .line 16
    and-int v5, v3, v4

    .line 17
    .line 18
    if-eqz v5, :cond_0

    .line 19
    .line 20
    sub-int/2addr v3, v4

    .line 21
    iput v3, v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;->label:I

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    new-instance v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;

    .line 25
    .line 26
    invoke-direct {v2, v1, v0}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;-><init>(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    :goto_0
    iget-object v0, v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;->result:Ljava/lang/Object;

    .line 30
    .line 31
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    iget v4, v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;->label:I

    .line 36
    .line 37
    const/4 v5, 0x1

    .line 38
    if-eqz v4, :cond_2

    .line 39
    .line 40
    if-ne v4, v5, :cond_1

    .line 41
    .line 42
    :try_start_0
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 43
    .line 44
    .line 45
    goto :goto_1

    .line 46
    :catchall_0
    move-exception v0

    .line 47
    goto :goto_2

    .line 48
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw v0

    .line 56
    :cond_2
    invoke-static {v0}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    :try_start_1
    sget-object v0, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 60
    .line 61
    iget-object v6, v1, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->a:Lv81/r;

    .line 62
    .line 63
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 64
    .line 65
    .line 66
    move-result-object v9

    .line 67
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 68
    .line 69
    .line 70
    move-result-object v10

    .line 71
    iget-object v0, v1, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->b:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 72
    .line 73
    invoke-interface {v0}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    invoke-virtual {v0}, Lek0/o;->o()Lek0/a;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {v0}, Lek0/a;->c()Z

    .line 82
    .line 83
    .line 84
    move-result v12

    .line 85
    iget-object v0, v1, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->c:Li8/j;

    .line 86
    .line 87
    invoke-interface {v0}, Li8/j;->invoke()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v11

    .line 91
    const/4 v14, 0x0

    .line 92
    move-wide/from16 v7, p1

    .line 93
    .line 94
    move/from16 v13, p3

    .line 95
    .line 96
    invoke-interface/range {v6 .. v14}, Lv81/r;->a(JLjava/util/List;Ljava/util/List;Ljava/lang/String;ZIZ)Lkotlinx/coroutines/flow/e;

    .line 97
    .line 98
    .line 99
    move-result-object v15

    .line 100
    const-string v16, "GetPopularGamesCategoriesScenarioImpl.getVirtualGames"

    .line 101
    .line 102
    const/16 v23, 0x38

    .line 103
    .line 104
    const/16 v24, 0x0

    .line 105
    .line 106
    const/16 v17, 0x3

    .line 107
    .line 108
    const-wide/16 v18, 0x3

    .line 109
    .line 110
    const/16 v20, 0x0

    .line 111
    .line 112
    const/16 v21, 0x0

    .line 113
    .line 114
    const/16 v22, 0x0

    .line 115
    .line 116
    invoke-static/range {v15 .. v24}, Lcom/xbet/onexcore/utils/flows/FlowBuilderKt;->e(Lkotlinx/coroutines/flow/e;Ljava/lang/String;IJLjava/util/List;Ljava/util/List;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 117
    .line 118
    .line 119
    move-result-object v0

    .line 120
    new-instance v4, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$2$1;

    .line 121
    .line 122
    const/4 v6, 0x0

    .line 123
    invoke-direct {v4, v6}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$2$1;-><init>(Lkotlin/coroutines/e;)V

    .line 124
    .line 125
    .line 126
    invoke-static {v0, v4}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    iput v5, v2, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getVirtualGames$1;->label:I

    .line 131
    .line 132
    invoke-static {v0, v2}, Lkotlinx/coroutines/flow/g;->N(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    if-ne v0, v3, :cond_3

    .line 137
    .line 138
    return-object v3

    .line 139
    :cond_3
    :goto_1
    check-cast v0, Ljava/util/List;

    .line 140
    .line 141
    if-nez v0, :cond_4

    .line 142
    .line 143
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 144
    .line 145
    .line 146
    move-result-object v0

    .line 147
    :cond_4
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 148
    .line 149
    .line 150
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 151
    goto :goto_3

    .line 152
    :goto_2
    instance-of v2, v0, Ljava/net/SocketTimeoutException;

    .line 153
    .line 154
    if-nez v2, :cond_6

    .line 155
    .line 156
    instance-of v2, v0, Ljava/net/ConnectException;

    .line 157
    .line 158
    if-nez v2, :cond_6

    .line 159
    .line 160
    instance-of v2, v0, Ljava/net/UnknownHostException;

    .line 161
    .line 162
    if-nez v2, :cond_6

    .line 163
    .line 164
    instance-of v2, v0, Ljava/util/concurrent/CancellationException;

    .line 165
    .line 166
    if-nez v2, :cond_6

    .line 167
    .line 168
    instance-of v2, v0, Lcom/xbet/onexuser/domain/exceptions/NotValidRefreshTokenException;

    .line 169
    .line 170
    if-nez v2, :cond_6

    .line 171
    .line 172
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 173
    .line 174
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    move-result-object v0

    .line 178
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 179
    .line 180
    .line 181
    move-result-object v0

    .line 182
    :goto_3
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 183
    .line 184
    .line 185
    move-result-object v2

    .line 186
    invoke-static {v0}, Lkotlin/Result;->isFailure-impl(Ljava/lang/Object;)Z

    .line 187
    .line 188
    .line 189
    move-result v3

    .line 190
    if-eqz v3, :cond_5

    .line 191
    .line 192
    move-object v0, v2

    .line 193
    :cond_5
    return-object v0

    .line 194
    :cond_6
    throw v0
.end method

.method public final q(Lkotlinx/coroutines/flow/e;)Lkotlinx/coroutines/flow/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/e<",
            "+",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;>;)",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$subscribeOnFavoritesChanges$$inlined$flatMapLatest$1;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1, p0}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$subscribeOnFavoritesChanges$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;)V

    .line 5
    .line 6
    .line 7
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->C0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method
