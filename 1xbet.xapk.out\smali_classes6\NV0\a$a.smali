.class public final LNV0/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LNV0/c$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LNV0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(LNV0/b;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LNV0/a$a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lak/a;LQW0/c;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;)LNV0/c;
    .locals 13

    .line 1
    invoke-static {p1}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    invoke-static {p2}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    invoke-static/range {p3 .. p3}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    invoke-static/range {p4 .. p4}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    invoke-static/range {p5 .. p5}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    invoke-static/range {p6 .. p6}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    invoke-static/range {p7 .. p7}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    invoke-static/range {p8 .. p8}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    .line 24
    .line 25
    invoke-static/range {p9 .. p9}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    invoke-static/range {p10 .. p10}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 29
    .line 30
    .line 31
    invoke-static/range {p11 .. p11}, Ldagger/internal/g;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    new-instance v0, LNV0/a$b;

    .line 35
    .line 36
    const/4 v12, 0x0

    .line 37
    move-object v2, p1

    .line 38
    move-object v1, p2

    .line 39
    move-object/from16 v3, p3

    .line 40
    .line 41
    move-object/from16 v4, p4

    .line 42
    .line 43
    move-object/from16 v5, p5

    .line 44
    .line 45
    move-object/from16 v6, p6

    .line 46
    .line 47
    move-object/from16 v7, p7

    .line 48
    .line 49
    move-object/from16 v8, p8

    .line 50
    .line 51
    move-object/from16 v9, p9

    .line 52
    .line 53
    move-object/from16 v10, p10

    .line 54
    .line 55
    move-object/from16 v11, p11

    .line 56
    .line 57
    invoke-direct/range {v0 .. v12}, LNV0/a$b;-><init>(LQW0/c;Lak/a;LwX0/c;Ljava/lang/String;Ljava/lang/String;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LHX0/e;Lorg/xbet/ui_common/utils/M;Lc8/h;LNV0/b;)V

    .line 58
    .line 59
    .line 60
    return-object v0
.end method
