.class final Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.domain.usecases.GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1"
    f = "GetPopularGamesCategoriesScenario.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->p(Lkotlinx/coroutines/flow/e;ZZ)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Ln9/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LLb1/a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00002\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u0006\u0010\u0004\u001a\u00020\u0003H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "favorites",
        "Ln9/b;",
        "loginState",
        "LLb1/a;",
        "<anonymous>",
        "(Ljava/util/List;Ln9/b;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $showcaseCategories:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LLb1/a;",
            ">;"
        }
    .end annotation
.end field

.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(Ljava/util/List;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->$showcaseCategories:Ljava/util/List;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    check-cast p2, Ln9/b;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->invoke(Ljava/util/List;Ln9/b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/util/List;Ln9/b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ln9/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "LLb1/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->$showcaseCategories:Ljava/util/List;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;-><init>(Ljava/util/List;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_1

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    move-object v6, p1

    .line 14
    check-cast v6, Ljava/util/List;

    .line 15
    .line 16
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->L$1:Ljava/lang/Object;

    .line 17
    .line 18
    check-cast p1, Ln9/b;

    .line 19
    .line 20
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$subscribeOnFavoritesChanges$1$1;->$showcaseCategories:Ljava/util/List;

    .line 21
    .line 22
    new-instance v8, Ljava/util/ArrayList;

    .line 23
    .line 24
    const/16 v1, 0xa

    .line 25
    .line 26
    invoke-static {v0, v1}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    invoke-direct {v8, v1}, Ljava/util/ArrayList;-><init>(I)V

    .line 31
    .line 32
    .line 33
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 34
    .line 35
    .line 36
    move-result-object v9

    .line 37
    :goto_0
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    if-eqz v0, :cond_0

    .line 42
    .line 43
    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    check-cast v0, LLb1/a;

    .line 48
    .line 49
    invoke-virtual {v0}, LLb1/a;->c()J

    .line 50
    .line 51
    .line 52
    move-result-wide v1

    .line 53
    invoke-virtual {v0}, LLb1/a;->e()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v5

    .line 57
    invoke-virtual {v0}, LLb1/a;->b()Ljava/util/List;

    .line 58
    .line 59
    .line 60
    move-result-object v3

    .line 61
    invoke-virtual {p1}, Ln9/b;->c()Z

    .line 62
    .line 63
    .line 64
    move-result v7

    .line 65
    sget-object v4, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 66
    .line 67
    new-instance v0, LLb1/a;

    .line 68
    .line 69
    invoke-direct/range {v0 .. v7}, LLb1/a;-><init>(JLjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;Ljava/lang/String;Ljava/util/List;Z)V

    .line 70
    .line 71
    .line 72
    invoke-interface {v8, v0}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 73
    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_0
    return-object v8

    .line 77
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 78
    .line 79
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 80
    .line 81
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 82
    .line 83
    .line 84
    throw p1
.end method
