.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LHd/c;

.field public final synthetic b:Lkotlin/jvm/functions/Function1;


# direct methods
.method public synthetic constructor <init>(LHd/c;Lkotlin/jvm/functions/Function1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/a;->a:LHd/c;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/a;->b:Lkotlin/jvm/functions/Function1;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/a;->a:LHd/c;

    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/a;->b:Lkotlin/jvm/functions/Function1;

    check-cast p1, Landroidx/compose/foundation/lazy/t;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit_sport/compose/sport_game_events/DsSportGameEventsKt;->b(LHd/c;Lkotlin/jvm/functions/Function1;Landroidx/compose/foundation/lazy/t;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
