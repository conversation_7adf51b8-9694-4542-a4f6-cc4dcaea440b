.class final Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/p;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.dashboard.impl.domain.usecases.GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1"
    f = "GetPopularGamesCategoriesScenarioImpl.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->l(I)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/p<",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Ljava/util/List<",
        "+",
        "LTb1/a;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\u00060\u00002\u000c\u0010\u0002\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u000c\u0010\u0003\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u000c\u0010\u0004\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u00002\u000c\u0010\u0005\u001a\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0007\u0010\u0008"
    }
    d2 = {
        "",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "slotsExclusive",
        "aggregatorLive",
        "slotsPopular",
        "aggregatorPopular",
        "LTb1/a;",
        "<anonymous>",
        "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field synthetic L$2:Ljava/lang/Object;

.field synthetic L$3:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    const/4 p1, 0x5

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/util/List;

    check-cast p2, Ljava/util/List;

    check-cast p3, Ljava/util/List;

    check-cast p4, Ljava/util/List;

    check-cast p5, Lkotlin/coroutines/e;

    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->invoke(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Ljava/util/List<",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Ljava/util/List<",
            "LTb1/a;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    invoke-direct {v0, v1, p5}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;-><init>(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$1:Ljava/lang/Object;

    iput-object p3, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$2:Ljava/lang/Object;

    iput-object p4, v0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$3:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Ljava/util/List;

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$1:Ljava/lang/Object;

    .line 16
    .line 17
    check-cast v0, Ljava/util/List;

    .line 18
    .line 19
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$2:Ljava/lang/Object;

    .line 20
    .line 21
    check-cast v1, Ljava/util/List;

    .line 22
    .line 23
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->L$3:Ljava/lang/Object;

    .line 24
    .line 25
    check-cast v2, Ljava/util/List;

    .line 26
    .line 27
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl$getAggregatorCategories$1;->this$0:Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;

    .line 28
    .line 29
    invoke-static {v3, p1, v0, v1, v2}, Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;->b(Lorg/xplatform/aggregator/popular/dashboard/impl/domain/usecases/GetPopularGamesCategoriesScenarioImpl;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)Ljava/util/List;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    return-object p1

    .line 34
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 35
    .line 36
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 37
    .line 38
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw p1
.end method
