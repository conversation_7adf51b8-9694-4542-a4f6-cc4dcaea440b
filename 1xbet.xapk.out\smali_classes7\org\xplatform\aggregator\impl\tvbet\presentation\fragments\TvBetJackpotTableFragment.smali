.class public final Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0015\u0018\u0000 G2\u00020\u0001:\u0001HB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u000f\u0010\t\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\t\u0010\u0003J\u0019\u0010\u000c\u001a\u00020\u00062\u0008\u0010\u000b\u001a\u0004\u0018\u00010\nH\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u0003JI\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u000f2\u000e\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0012\u0018\u00010\u00112\u0006\u0010\u0014\u001a\u00020\u000f2\u0018\u0010\u0016\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u000f0\u00150\u0011H\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J!\u0010\u001b\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u00042\u0008\u0010\u001a\u001a\u0004\u0018\u00010\u0019H\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001cR\"\u0010$\u001a\u00020\u001d8\u0000@\u0000X\u0081.\u00a2\u0006\u0012\n\u0004\u0008\u001e\u0010\u001f\u001a\u0004\u0008 \u0010!\"\u0004\u0008\"\u0010#R\u001b\u0010*\u001a\u00020%8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008&\u0010\'\u001a\u0004\u0008(\u0010)R\u001b\u0010/\u001a\u00020+8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008,\u0010\'\u001a\u0004\u0008-\u0010.R\u0014\u00103\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u00102R\u001b\u00109\u001a\u0002048BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00085\u00106\u001a\u0004\u00087\u00108R+\u0010@\u001a\u00020\u00042\u0006\u0010:\u001a\u00020\u00048B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008;\u0010<\u001a\u0004\u0008=\u0010>\"\u0004\u0008?\u0010\u0008R+\u0010D\u001a\u00020\u00042\u0006\u0010:\u001a\u00020\u00048B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008A\u0010<\u001a\u0004\u0008B\u0010>\"\u0004\u0008C\u0010\u0008R\u0014\u0010F\u001a\u00020\u00048VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008E\u0010>\u00a8\u0006I"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "show",
        "",
        "d",
        "(Z)V",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "",
        "period",
        "",
        "Lvb1/c;",
        "items",
        "sum",
        "Lkotlin/Pair;",
        "dateList",
        "O2",
        "(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V",
        "Lorg/xbet/uikit/components/lottie/a;",
        "config",
        "P2",
        "(ZLorg/xbet/uikit/components/lottie/a;)V",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "i0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "L2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory$impl_aggregator_implRelease",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
        "j0",
        "Lkotlin/j;",
        "K2",
        "()Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;",
        "viewModel",
        "LWX0/c;",
        "k0",
        "H2",
        "()LWX0/c;",
        "chipAdapter",
        "Lyb1/a;",
        "l0",
        "Lyb1/a;",
        "tableAdapter",
        "LS91/l0;",
        "m0",
        "LRc/c;",
        "J2",
        "()LS91/l0;",
        "viewBinding",
        "<set-?>",
        "n0",
        "LeX0/a;",
        "G2",
        "()Z",
        "M2",
        "bundleShowNavBar",
        "o0",
        "I2",
        "N2",
        "fromAggregator",
        "r2",
        "showNavBar",
        "b1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final b1:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic k1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field public i0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final j0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:Lyb1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTvBetResultBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bundleShowNavBar"

    .line 20
    .line 21
    const-string v5, "getBundleShowNavBar()Z"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "fromAggregator"

    .line 33
    .line 34
    const-string v6, "getFromAggregator()Z"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    const/4 v3, 0x3

    .line 44
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v3, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v3, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v1, v3, v0

    .line 53
    .line 54
    sput-object v3, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k1:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$a;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->b1:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>()V
    .locals 6

    .line 1
    sget v0, Lu91/c;->fragment_tv_bet_result:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/d;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/d;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)V

    .line 9
    .line 10
    .line 11
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$1;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 17
    .line 18
    new-instance v3, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$2;

    .line 19
    .line 20
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 21
    .line 22
    .line 23
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    const-class v2, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 28
    .line 29
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    new-instance v3, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$3;

    .line 34
    .line 35
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 36
    .line 37
    .line 38
    new-instance v4, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$4;

    .line 39
    .line 40
    const/4 v5, 0x0

    .line 41
    invoke-direct {v4, v5, v1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->j0:Lkotlin/j;

    .line 49
    .line 50
    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/e;

    .line 51
    .line 52
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/e;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k0:Lkotlin/j;

    .line 60
    .line 61
    new-instance v0, Lyb1/a;

    .line 62
    .line 63
    invoke-direct {v0}, Lyb1/a;-><init>()V

    .line 64
    .line 65
    .line 66
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->l0:Lyb1/a;

    .line 67
    .line 68
    sget-object v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$viewBinding$2;

    .line 69
    .line 70
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 71
    .line 72
    .line 73
    move-result-object v0

    .line 74
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->m0:LRc/c;

    .line 75
    .line 76
    new-instance v0, LeX0/a;

    .line 77
    .line 78
    const-string v1, "SHOW_NAVBAR"

    .line 79
    .line 80
    const/4 v2, 0x1

    .line 81
    invoke-direct {v0, v1, v2}, LeX0/a;-><init>(Ljava/lang/String;Z)V

    .line 82
    .line 83
    .line 84
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->n0:LeX0/a;

    .line 85
    .line 86
    new-instance v0, LeX0/a;

    .line 87
    .line 88
    const-string v1, "FROM_AGGREGATOR"

    .line 89
    .line 90
    invoke-direct {v0, v1, v2}, LeX0/a;-><init>(Ljava/lang/String;Z)V

    .line 91
    .line 92
    .line 93
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->o0:LeX0/a;

    .line 94
    .line 95
    return-void
.end method

.method public static final synthetic A2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->M2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->N2(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->O2(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;ZLorg/xbet/uikit/components/lottie/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->P2(ZLorg/xbet/uikit/components/lottie/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->d(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final F2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)LWX0/c;
    .locals 2

    .line 1
    new-instance v0, LWX0/c;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$chipAdapter$2$1;

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->K2()Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$chipAdapter$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    invoke-direct {v0, v1}, LWX0/c;-><init>(Lkotlin/jvm/functions/Function1;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method private final G2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->n0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final H2()LWX0/c;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LWX0/c;

    .line 8
    .line 9
    return-object v0
.end method

.method private final I2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->o0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final M2(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->n0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final N2(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->o0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final Q2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->L2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final d(Z)V
    .locals 2

    .line 1
    const/16 v0, 0x8

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v1, v1, LS91/l0;->c:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 10
    .line 11
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    iget-object v1, v1, LS91/l0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 19
    .line 20
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    iget-object v1, v1, LS91/l0;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 28
    .line 29
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 30
    .line 31
    .line 32
    :cond_0
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    iget-object v1, v1, LS91/l0;->e:Landroid/widget/FrameLayout;

    .line 37
    .line 38
    if-eqz p1, :cond_1

    .line 39
    .line 40
    const/4 v0, 0x0

    .line 41
    :cond_1
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->Q2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)LWX0/c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->F2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)LWX0/c;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public final J2()LS91/l0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->m0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->k1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/l0;

    .line 13
    .line 14
    return-object v0
.end method

.method public final K2()Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->j0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final L2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final O2(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lvb1/c;",
            ">;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lkotlin/Pair<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;>;)V"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 3
    .line 4
    .line 5
    move-result-object v1

    .line 6
    instance-of v2, v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;

    .line 7
    .line 8
    if-eqz v2, :cond_0

    .line 9
    .line 10
    check-cast v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v1, 0x0

    .line 14
    :goto_0
    if-eqz v1, :cond_1

    .line 15
    .line 16
    invoke-virtual {v1, p3}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->L2(Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    :cond_1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->H2()LWX0/c;

    .line 20
    .line 21
    .line 22
    move-result-object p3

    .line 23
    invoke-virtual {p3, p4}, LUX0/e;->C(Ljava/util/List;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 27
    .line 28
    .line 29
    move-result-object p3

    .line 30
    iget-object p3, p3, LS91/l0;->g:Landroid/widget/TextView;

    .line 31
    .line 32
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 33
    .line 34
    .line 35
    move-result-object p4

    .line 36
    sget v1, Lpb/k;->tournament_table:I

    .line 37
    .line 38
    const/4 v2, 0x1

    .line 39
    new-array v2, v2, [Ljava/lang/Object;

    .line 40
    .line 41
    aput-object p1, v2, v0

    .line 42
    .line 43
    invoke-virtual {p4, v1, v2}, Landroid/content/Context;->getString(I[Ljava/lang/Object;)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    invoke-virtual {p3, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iget-object p1, p1, LS91/l0;->c:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 55
    .line 56
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 57
    .line 58
    .line 59
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    iget-object p1, p1, LS91/l0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 64
    .line 65
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 66
    .line 67
    .line 68
    if-eqz p2, :cond_2

    .line 69
    .line 70
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->l0:Lyb1/a;

    .line 71
    .line 72
    invoke-virtual {p1, p2}, LUX0/h;->B(Ljava/util/List;)V

    .line 73
    .line 74
    .line 75
    :cond_2
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->d(Z)V

    .line 76
    .line 77
    .line 78
    return-void
.end method

.method public final P2(ZLorg/xbet/uikit/components/lottie/a;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/l0;->c:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 6
    .line 7
    const/16 v1, 0x8

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-nez p1, :cond_0

    .line 11
    .line 12
    const/4 v3, 0x0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/16 v3, 0x8

    .line 15
    .line 16
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    iget-object v0, v0, LS91/l0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 24
    .line 25
    if-nez p1, :cond_1

    .line 26
    .line 27
    const/4 v3, 0x0

    .line 28
    goto :goto_1

    .line 29
    :cond_1
    const/16 v3, 0x8

    .line 30
    .line 31
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    if-eqz p2, :cond_2

    .line 35
    .line 36
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iget-object v0, v0, LS91/l0;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 41
    .line 42
    invoke-virtual {v0, p2}, Lorg/xbet/uikit/components/lottie/LottieView;->L(Lorg/xbet/uikit/components/lottie/a;)V

    .line 43
    .line 44
    .line 45
    :cond_2
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 46
    .line 47
    .line 48
    move-result-object p2

    .line 49
    iget-object p2, p2, LS91/l0;->d:Lorg/xbet/uikit/components/lottie/LottieView;

    .line 50
    .line 51
    if-eqz p1, :cond_3

    .line 52
    .line 53
    const/4 v0, 0x0

    .line 54
    goto :goto_2

    .line 55
    :cond_3
    const/16 v0, 0x8

    .line 56
    .line 57
    :goto_2
    invoke-virtual {p2, v0}, Landroid/view/View;->setVisibility(I)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 61
    .line 62
    .line 63
    move-result-object p2

    .line 64
    iget-object p2, p2, LS91/l0;->e:Landroid/widget/FrameLayout;

    .line 65
    .line 66
    if-nez p1, :cond_4

    .line 67
    .line 68
    const/4 v1, 0x0

    .line 69
    :cond_4
    invoke-virtual {p2, v1}, Landroid/view/View;->setVisibility(I)V

    .line 70
    .line 71
    .line 72
    return-void
.end method

.method public r2()Z
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->G2()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    return v0
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    invoke-super {p0, p1}, LXW0/a;->t2(Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->getResources()Landroid/content/res/Resources;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    sget v0, Lpb/f;->space_16:I

    .line 9
    .line 10
    invoke-virtual {p1, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    iget-object v0, v0, LS91/l0;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 19
    .line 20
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->H2()LWX0/c;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 25
    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    iget-object v0, v0, LS91/l0;->f:Landroidx/recyclerview/widget/RecyclerView;

    .line 32
    .line 33
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->l0:Lyb1/a;

    .line 34
    .line 35
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->J2()LS91/l0;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    iget-object v0, v0, LS91/l0;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 43
    .line 44
    new-instance v1, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/l;

    .line 45
    .line 46
    const/4 v2, 0x0

    .line 47
    invoke-direct {v1, p1, v2}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/l;-><init>(II)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public u2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    instance-of v1, v0, LQW0/b;

    .line 10
    .line 11
    const/4 v2, 0x0

    .line 12
    if-eqz v1, :cond_0

    .line 13
    .line 14
    check-cast v0, LQW0/b;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    move-object v0, v2

    .line 18
    :goto_0
    const-class v1, Lub1/d;

    .line 19
    .line 20
    if-eqz v0, :cond_3

    .line 21
    .line 22
    invoke-interface {v0}, LQW0/b;->O1()Ljava/util/Map;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    check-cast v0, LBc/a;

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    check-cast v0, LQW0/a;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_1
    move-object v0, v2

    .line 42
    :goto_1
    instance-of v3, v0, Lub1/d;

    .line 43
    .line 44
    if-nez v3, :cond_2

    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_2
    move-object v2, v0

    .line 48
    :goto_2
    check-cast v2, Lub1/d;

    .line 49
    .line 50
    if-eqz v2, :cond_3

    .line 51
    .line 52
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->I2()Z

    .line 53
    .line 54
    .line 55
    move-result v0

    .line 56
    invoke-virtual {v2, v0}, Lub1/d;->a(Z)Lub1/c;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-interface {v0, p0}, Lub1/c;->a(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;)V

    .line 61
    .line 62
    .line 63
    return-void

    .line 64
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 65
    .line 66
    new-instance v2, Ljava/lang/StringBuilder;

    .line 67
    .line 68
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 69
    .line 70
    .line 71
    const-string v3, "Cannot create dependency "

    .line 72
    .line 73
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 74
    .line 75
    .line 76
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 88
    .line 89
    .line 90
    throw v0
.end method

.method public v2()V
    .locals 12

    .line 1
    invoke-super {p0}, LXW0/a;->v2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->K2()Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel;->M3()Lkotlinx/coroutines/flow/V;

    .line 9
    .line 10
    .line 11
    move-result-object v2

    .line 12
    new-instance v5, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;

    .line 13
    .line 14
    const/4 v0, 0x0

    .line 15
    invoke-direct {v5, p0, v0}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    sget-object v4, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 19
    .line 20
    invoke-static {p0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 21
    .line 22
    .line 23
    move-result-object v3

    .line 24
    invoke-static {v3}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    new-instance v1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 29
    .line 30
    const/4 v6, 0x0

    .line 31
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 32
    .line 33
    .line 34
    const/4 v10, 0x3

    .line 35
    const/4 v11, 0x0

    .line 36
    const/4 v7, 0x0

    .line 37
    const/4 v8, 0x0

    .line 38
    move-object v6, v0

    .line 39
    move-object v9, v1

    .line 40
    invoke-static/range {v6 .. v11}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 41
    .line 42
    .line 43
    return-void
.end method
