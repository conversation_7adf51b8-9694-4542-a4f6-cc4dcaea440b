.class final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.simple.fragment.SimpleMakeBetFragment$observeActionStream$1"
    f = "SimpleMakeBetFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->Y2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
        "action",
        "",
        "<anonymous>",
        "(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    invoke-direct {v0, v1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;-><init>(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->invoke(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 23

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    iget v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->label:I

    .line 7
    .line 8
    if-nez v1, :cond_16

    .line 9
    .line 10
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->L$0:Ljava/lang/Object;

    .line 14
    .line 15
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;

    .line 16
    .line 17
    instance-of v2, v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 18
    .line 19
    if-eqz v2, :cond_0

    .line 20
    .line 21
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 22
    .line 23
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    iget-object v3, v3, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 28
    .line 29
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;

    .line 30
    .line 31
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->b()LvX0/e;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    iget-object v5, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 36
    .line 37
    invoke-virtual {v5}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 38
    .line 39
    .line 40
    move-result-object v5

    .line 41
    invoke-virtual {v4, v5}, LvX0/e;->f(Landroid/content/Context;)Landroid/text/Spannable;

    .line 42
    .line 43
    .line 44
    move-result-object v4

    .line 45
    invoke-virtual {v3, v4}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setUnderInputHintText(Landroid/text/Spannable;)V

    .line 46
    .line 47
    .line 48
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 49
    .line 50
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    iget-object v3, v3, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 55
    .line 56
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$a;->a()Z

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    invoke-virtual {v3, v1}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setActionsEnabled(Z)V

    .line 61
    .line 62
    .line 63
    goto/16 :goto_2

    .line 64
    .line 65
    :cond_0
    instance-of v3, v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;

    .line 66
    .line 67
    const/4 v4, 0x0

    .line 68
    const/4 v5, 0x0

    .line 69
    if-eqz v3, :cond_4

    .line 70
    .line 71
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 72
    .line 73
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 74
    .line 75
    .line 76
    move-result-object v3

    .line 77
    instance-of v6, v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 78
    .line 79
    if-eqz v6, :cond_1

    .line 80
    .line 81
    check-cast v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 82
    .line 83
    goto :goto_0

    .line 84
    :cond_1
    move-object v3, v5

    .line 85
    :goto_0
    if-eqz v3, :cond_2

    .line 86
    .line 87
    invoke-virtual {v3, v4}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 88
    .line 89
    .line 90
    :cond_2
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 91
    .line 92
    invoke-virtual {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->P2()LzX0/k;

    .line 93
    .line 94
    .line 95
    move-result-object v6

    .line 96
    new-instance v7, Ly01/g;

    .line 97
    .line 98
    sget-object v8, Ly01/i$c;->a:Ly01/i$c;

    .line 99
    .line 100
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;

    .line 101
    .line 102
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$f;->a()Ljava/lang/String;

    .line 103
    .line 104
    .line 105
    move-result-object v9

    .line 106
    const/16 v14, 0x3c

    .line 107
    .line 108
    const/4 v15, 0x0

    .line 109
    const/4 v10, 0x0

    .line 110
    const/4 v11, 0x0

    .line 111
    const/4 v12, 0x0

    .line 112
    const/4 v13, 0x0

    .line 113
    invoke-direct/range {v7 .. v15}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 114
    .line 115
    .line 116
    iget-object v8, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 117
    .line 118
    const/16 v16, 0x1fc

    .line 119
    .line 120
    const/16 v17, 0x0

    .line 121
    .line 122
    const/4 v9, 0x0

    .line 123
    const/4 v11, 0x0

    .line 124
    const/4 v12, 0x0

    .line 125
    const/4 v14, 0x0

    .line 126
    invoke-static/range {v6 .. v17}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 127
    .line 128
    .line 129
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 130
    .line 131
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 132
    .line 133
    .line 134
    move-result-object v1

    .line 135
    instance-of v3, v1, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 136
    .line 137
    if-eqz v3, :cond_3

    .line 138
    .line 139
    move-object v5, v1

    .line 140
    check-cast v5, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 141
    .line 142
    :cond_3
    if-eqz v5, :cond_13

    .line 143
    .line 144
    invoke-virtual {v5}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 145
    .line 146
    .line 147
    goto/16 :goto_2

    .line 148
    .line 149
    :cond_4
    instance-of v3, v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;

    .line 150
    .line 151
    if-eqz v3, :cond_7

    .line 152
    .line 153
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 154
    .line 155
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 156
    .line 157
    .line 158
    move-result-object v3

    .line 159
    instance-of v6, v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 160
    .line 161
    if-eqz v6, :cond_5

    .line 162
    .line 163
    move-object v5, v3

    .line 164
    check-cast v5, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 165
    .line 166
    :cond_5
    if-eqz v5, :cond_6

    .line 167
    .line 168
    invoke-virtual {v5, v4}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 169
    .line 170
    .line 171
    :cond_6
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 172
    .line 173
    invoke-virtual {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->P2()LzX0/k;

    .line 174
    .line 175
    .line 176
    move-result-object v4

    .line 177
    new-instance v5, Ly01/g;

    .line 178
    .line 179
    sget-object v6, Ly01/i$c;->a:Ly01/i$c;

    .line 180
    .line 181
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;

    .line 182
    .line 183
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$e;->a()Ljava/lang/String;

    .line 184
    .line 185
    .line 186
    move-result-object v7

    .line 187
    const/16 v12, 0x3c

    .line 188
    .line 189
    const/4 v13, 0x0

    .line 190
    const/4 v8, 0x0

    .line 191
    const/4 v9, 0x0

    .line 192
    const/4 v10, 0x0

    .line 193
    const/4 v11, 0x0

    .line 194
    invoke-direct/range {v5 .. v13}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 195
    .line 196
    .line 197
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 198
    .line 199
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 200
    .line 201
    .line 202
    move-result-object v1

    .line 203
    iget-object v8, v1, LOU0/g;->f:Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 204
    .line 205
    iget-object v6, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 206
    .line 207
    const/16 v14, 0x1f4

    .line 208
    .line 209
    const/4 v15, 0x0

    .line 210
    const/4 v7, 0x0

    .line 211
    const/4 v9, 0x0

    .line 212
    const/4 v10, 0x0

    .line 213
    const/4 v12, 0x0

    .line 214
    invoke-static/range {v4 .. v15}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 215
    .line 216
    .line 217
    goto/16 :goto_2

    .line 218
    .line 219
    :cond_7
    instance-of v3, v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;

    .line 220
    .line 221
    if-eqz v3, :cond_b

    .line 222
    .line 223
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 224
    .line 225
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 226
    .line 227
    .line 228
    move-result-object v3

    .line 229
    instance-of v6, v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 230
    .line 231
    if-eqz v6, :cond_8

    .line 232
    .line 233
    check-cast v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 234
    .line 235
    goto :goto_1

    .line 236
    :cond_8
    move-object v3, v5

    .line 237
    :goto_1
    if-eqz v3, :cond_9

    .line 238
    .line 239
    invoke-virtual {v3, v4}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 240
    .line 241
    .line 242
    :cond_9
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 243
    .line 244
    invoke-virtual {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->Q2()LAX0/b;

    .line 245
    .line 246
    .line 247
    move-result-object v3

    .line 248
    new-instance v6, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;

    .line 249
    .line 250
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 251
    .line 252
    sget v7, Lpb/k;->bet_processed_successfully:I

    .line 253
    .line 254
    invoke-virtual {v4, v7}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 255
    .line 256
    .line 257
    move-result-object v7

    .line 258
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;

    .line 259
    .line 260
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;->d()Ljava/lang/String;

    .line 261
    .line 262
    .line 263
    move-result-object v8

    .line 264
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 265
    .line 266
    sget v9, Lpb/k;->history:I

    .line 267
    .line 268
    invoke-virtual {v4, v9}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 269
    .line 270
    .line 271
    move-result-object v9

    .line 272
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 273
    .line 274
    sget v10, Lpb/k;->continue_action:I

    .line 275
    .line 276
    invoke-virtual {v4, v10}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 277
    .line 278
    .line 279
    move-result-object v10

    .line 280
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 281
    .line 282
    sget v11, Lpb/k;->bet_sum:I

    .line 283
    .line 284
    invoke-virtual {v4, v11}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 285
    .line 286
    .line 287
    move-result-object v12

    .line 288
    const/16 v15, 0xd0

    .line 289
    .line 290
    const/16 v16, 0x0

    .line 291
    .line 292
    const/4 v11, 0x0

    .line 293
    const/4 v13, 0x0

    .line 294
    const/4 v14, 0x0

    .line 295
    invoke-direct/range {v6 .. v16}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 296
    .line 297
    .line 298
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 299
    .line 300
    sget v7, Lpb/k;->toto_name:I

    .line 301
    .line 302
    invoke-virtual {v4, v7}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 303
    .line 304
    .line 305
    move-result-object v4

    .line 306
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;->f()Ljava/lang/String;

    .line 307
    .line 308
    .line 309
    move-result-object v7

    .line 310
    new-instance v8, Ljava/lang/StringBuilder;

    .line 311
    .line 312
    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    .line 313
    .line 314
    .line 315
    invoke-virtual {v8, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 316
    .line 317
    .line 318
    const-string v4, ": "

    .line 319
    .line 320
    invoke-virtual {v8, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 321
    .line 322
    .line 323
    invoke-virtual {v8, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 324
    .line 325
    .line 326
    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 327
    .line 328
    .line 329
    move-result-object v10

    .line 330
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;->e()Ljava/lang/String;

    .line 331
    .line 332
    .line 333
    move-result-object v11

    .line 334
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;->c()Ljava/lang/String;

    .line 335
    .line 336
    .line 337
    move-result-object v14

    .line 338
    sget-object v15, Ll8/j;->a:Ll8/j;

    .line 339
    .line 340
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;->b()D

    .line 341
    .line 342
    .line 343
    move-result-wide v16

    .line 344
    const/16 v19, 0x2

    .line 345
    .line 346
    const/16 v20, 0x0

    .line 347
    .line 348
    const/16 v18, 0x0

    .line 349
    .line 350
    invoke-static/range {v15 .. v20}, Ll8/j;->g(Ll8/j;DLcom/xbet/onexcore/utils/ValueType;ILjava/lang/Object;)Ljava/lang/String;

    .line 351
    .line 352
    .line 353
    move-result-object v13

    .line 354
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$g;->a()J

    .line 355
    .line 356
    .line 357
    move-result-wide v7

    .line 358
    new-instance v9, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;

    .line 359
    .line 360
    invoke-static {v7, v8}, LHc/a;->f(J)Ljava/lang/Long;

    .line 361
    .line 362
    .line 363
    move-result-object v17

    .line 364
    const/16 v21, 0x424

    .line 365
    .line 366
    const/16 v22, 0x0

    .line 367
    .line 368
    const/4 v12, 0x0

    .line 369
    const/4 v15, 0x0

    .line 370
    const-string v16, "REQUEST_SUCCESS_BET_KEY"

    .line 371
    .line 372
    const-string v18, "TOTO"

    .line 373
    .line 374
    const/16 v19, 0x0

    .line 375
    .line 376
    invoke-direct/range {v9 .. v22}, Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;ZLjava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 377
    .line 378
    .line 379
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 380
    .line 381
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 382
    .line 383
    .line 384
    move-result-object v1

    .line 385
    invoke-virtual {v1}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 386
    .line 387
    .line 388
    move-result-object v1

    .line 389
    invoke-virtual {v3, v6, v9, v1}, LAX0/b;->d(Lorg/xbet/uikit/components/successbetalert/model/SuccessBetStringModel;Lorg/xbet/uikit/components/successbetalert/model/SuccessBetAlertModel;Landroidx/fragment/app/FragmentManager;)V

    .line 390
    .line 391
    .line 392
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 393
    .line 394
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 395
    .line 396
    .line 397
    move-result-object v1

    .line 398
    instance-of v3, v1, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 399
    .line 400
    if-eqz v3, :cond_a

    .line 401
    .line 402
    move-object v5, v1

    .line 403
    check-cast v5, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;

    .line 404
    .line 405
    :cond_a
    if-eqz v5, :cond_13

    .line 406
    .line 407
    invoke-virtual {v5}, Lcom/google/android/material/bottomsheet/BottomSheetDialogFragment;->dismiss()V

    .line 408
    .line 409
    .line 410
    goto :goto_2

    .line 411
    :cond_b
    instance-of v3, v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;

    .line 412
    .line 413
    if-eqz v3, :cond_e

    .line 414
    .line 415
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 416
    .line 417
    invoke-virtual {v3}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 418
    .line 419
    .line 420
    move-result-object v3

    .line 421
    instance-of v6, v3, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 422
    .line 423
    if-eqz v6, :cond_c

    .line 424
    .line 425
    move-object v5, v3

    .line 426
    check-cast v5, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 427
    .line 428
    :cond_c
    if-eqz v5, :cond_d

    .line 429
    .line 430
    invoke-virtual {v5, v4}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 431
    .line 432
    .line 433
    :cond_d
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 434
    .line 435
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;

    .line 436
    .line 437
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$d;->a()Ljava/lang/String;

    .line 438
    .line 439
    .line 440
    move-result-object v1

    .line 441
    invoke-static {v3, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->K2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Ljava/lang/String;)V

    .line 442
    .line 443
    .line 444
    goto :goto_2

    .line 445
    :cond_e
    sget-object v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$c;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$c;

    .line 446
    .line 447
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 448
    .line 449
    .line 450
    move-result v3

    .line 451
    if-eqz v3, :cond_11

    .line 452
    .line 453
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 454
    .line 455
    invoke-virtual {v1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 456
    .line 457
    .line 458
    move-result-object v1

    .line 459
    instance-of v3, v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 460
    .line 461
    if-eqz v3, :cond_f

    .line 462
    .line 463
    move-object v5, v1

    .line 464
    check-cast v5, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;

    .line 465
    .line 466
    :cond_f
    if-eqz v5, :cond_10

    .line 467
    .line 468
    const/4 v1, 0x1

    .line 469
    invoke-virtual {v5, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBetBottomSheetDialog;->C(Z)V

    .line 470
    .line 471
    .line 472
    :cond_10
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 473
    .line 474
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 475
    .line 476
    .line 477
    move-result-object v1

    .line 478
    iget-object v1, v1, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 479
    .line 480
    invoke-virtual {v1, v4}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setActionsEnabled(Z)V

    .line 481
    .line 482
    .line 483
    goto :goto_2

    .line 484
    :cond_11
    instance-of v3, v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$h;

    .line 485
    .line 486
    if-eqz v3, :cond_12

    .line 487
    .line 488
    iget-object v3, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 489
    .line 490
    invoke-static {v3}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 491
    .line 492
    .line 493
    move-result-object v3

    .line 494
    invoke-virtual {v3}, LOU0/g;->b()Landroidx/coordinatorlayout/widget/CoordinatorLayout;

    .line 495
    .line 496
    .line 497
    move-result-object v3

    .line 498
    iget-object v4, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 499
    .line 500
    new-instance v5, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;

    .line 501
    .line 502
    invoke-direct {v5, v3, v4, v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;-><init>(Landroid/view/View;Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;)V

    .line 503
    .line 504
    .line 505
    invoke-static {v3, v5}, Landroidx/core/view/N;->a(Landroid/view/View;Ljava/lang/Runnable;)Landroidx/core/view/N;

    .line 506
    .line 507
    .line 508
    goto :goto_2

    .line 509
    :cond_12
    sget-object v3, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$b;

    .line 510
    .line 511
    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 512
    .line 513
    .line 514
    move-result v1

    .line 515
    if-eqz v1, :cond_15

    .line 516
    .line 517
    :cond_13
    :goto_2
    if-nez v2, :cond_14

    .line 518
    .line 519
    iget-object v1, v0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->this$0:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 520
    .line 521
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->H2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    .line 522
    .line 523
    .line 524
    move-result-object v1

    .line 525
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->u4()V

    .line 526
    .line 527
    .line 528
    :cond_14
    sget-object v1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 529
    .line 530
    return-object v1

    .line 531
    :cond_15
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    .line 532
    .line 533
    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 534
    .line 535
    .line 536
    throw v1

    .line 537
    :cond_16
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 538
    .line 539
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 540
    .line 541
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 542
    .line 543
    .line 544
    throw v1
.end method
