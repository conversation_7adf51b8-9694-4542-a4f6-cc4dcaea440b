.class public final Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LMb1/a;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000c\u0008\u0000\u0018\u00002\u00020\u0001B1\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ8\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0017R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001f\u00a8\u0006 "
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;",
        "LMb1/a;",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lc8/h;",
        "requestParamsDataSource",
        "LEb1/d;",
        "remoteDataSource",
        "LEb1/a;",
        "localDataSource",
        "LS8/a;",
        "profileLocalDataSource",
        "<init>",
        "(Lm8/a;Lc8/h;LEb1/d;LEb1/a;LS8/a;)V",
        "",
        "fromCache",
        "test",
        "brandsApi",
        "hasProvidersAggregator",
        "hasAggregatorBrandsFullInfo",
        "LBb1/a;",
        "a",
        "(ZZZZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "Lm8/a;",
        "b",
        "Lc8/h;",
        "c",
        "LEb1/d;",
        "d",
        "LEb1/a;",
        "e",
        "LS8/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lc8/h;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:LEb1/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:LEb1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:LS8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lm8/a;Lc8/h;LEb1/d;LEb1/a;LS8/a;)V
    .locals 0
    .param p1    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lc8/h;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LEb1/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LEb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # LS8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->a:Lm8/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->b:Lc8/h;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->c:LEb1/d;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->d:LEb1/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e:LS8/a;

    .line 13
    .line 14
    return-void
.end method

.method public static final synthetic b(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LEb1/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->d:LEb1/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LS8/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e:LS8/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LEb1/d;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->c:LEb1/d;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)Lc8/h;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->b:Lc8/h;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public a(ZZZZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 9
    .param p6    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZZZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "LBb1/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->a:Lm8/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;

    .line 8
    .line 9
    const/4 v8, 0x0

    .line 10
    move-object v3, p0

    .line 11
    move v2, p1

    .line 12
    move v4, p2

    .line 13
    move v5, p3

    .line 14
    move v6, p4

    .line 15
    move v7, p5

    .line 16
    invoke-direct/range {v1 .. v8}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;-><init>(ZLorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;ZZZZLkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0, v1, p6}, Lkotlinx/coroutines/h;->g(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    return-object p1
.end method
