.class public final LN11/c;
.super Lorg/xbet/uikit/utils/E;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u000e\u0008\u0001\u0018\u00002\u00020\u00012\u00020\u0002B/\u0012\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0003\u0012\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0005\u0012\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u0007\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0017\u0010\u000e\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u001a\u0010\u0004\u001a\u00020\u00038\u0000X\u0080\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0010\u0010\u0011\u001a\u0004\u0008\u0010\u0010\u0012R\u0014\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0014R \u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\t0\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016\u00a8\u0006\u0017"
    }
    d2 = {
        "LN11/c;",
        "Lorg/xbet/uikit/utils/E;",
        "Landroid/view/View$OnClickListener;",
        "Lorg/xbet/uikit/utils/debounce/Interval;",
        "minimumInterval",
        "",
        "globalClickTimestamp",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "",
        "block",
        "<init>",
        "(Lorg/xbet/uikit/utils/debounce/Interval;ZLkotlin/jvm/functions/Function1;)V",
        "clickedView",
        "onClick",
        "(Landroid/view/View;)V",
        "f",
        "Lorg/xbet/uikit/utils/debounce/Interval;",
        "()Lorg/xbet/uikit/utils/debounce/Interval;",
        "g",
        "Z",
        "h",
        "Lkotlin/jvm/functions/Function1;",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final f:Lorg/xbet/uikit/utils/debounce/Interval;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final g:Z

.field public final h:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit/utils/debounce/Interval;ZLkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit/utils/debounce/Interval;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/uikit/utils/debounce/Interval;",
            "Z",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit/utils/E;-><init>()V

    .line 4
    iput-object p1, p0, LN11/c;->f:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 5
    iput-boolean p2, p0, LN11/c;->g:Z

    .line 6
    iput-object p3, p0, LN11/c;->h:Lkotlin/jvm/functions/Function1;

    return-void
.end method

.method public synthetic constructor <init>(Lorg/xbet/uikit/utils/debounce/Interval;ZLkotlin/jvm/functions/Function1;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x1

    if-eqz p5, :cond_0

    .line 1
    sget-object p1, Lorg/xbet/uikit/utils/E;->b:Lorg/xbet/uikit/utils/E$a;

    invoke-virtual {p1}, Lorg/xbet/uikit/utils/E$a;->a()Lorg/xbet/uikit/utils/debounce/Interval;

    move-result-object p1

    :cond_0
    and-int/lit8 p4, p4, 0x2

    if-eqz p4, :cond_1

    const/4 p2, 0x0

    .line 2
    :cond_1
    invoke-direct {p0, p1, p2, p3}, LN11/c;-><init>(Lorg/xbet/uikit/utils/debounce/Interval;ZLkotlin/jvm/functions/Function1;)V

    return-void
.end method


# virtual methods
.method public final f()Lorg/xbet/uikit/utils/debounce/Interval;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LN11/c;->f:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 2
    .line 3
    return-object v0
.end method

.method public onClick(Landroid/view/View;)V
    .locals 7
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {}, Landroid/os/SystemClock;->uptimeMillis()J

    .line 2
    .line 3
    .line 4
    move-result-wide v0

    .line 5
    iget-boolean v2, p0, LN11/c;->g:Z

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    sget-object v2, Lorg/xbet/uikit/utils/E;->b:Lorg/xbet/uikit/utils/E$a;

    .line 10
    .line 11
    invoke-virtual {v2}, Lorg/xbet/uikit/utils/E$a;->b()J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit/utils/E;->d()J

    .line 17
    .line 18
    .line 19
    move-result-wide v2

    .line 20
    :goto_0
    sub-long v2, v0, v2

    .line 21
    .line 22
    iget-object v4, p0, LN11/c;->f:Lorg/xbet/uikit/utils/debounce/Interval;

    .line 23
    .line 24
    invoke-virtual {v4}, Lorg/xbet/uikit/utils/debounce/Interval;->getDelay()J

    .line 25
    .line 26
    .line 27
    move-result-wide v4

    .line 28
    cmp-long v6, v2, v4

    .line 29
    .line 30
    if-lez v6, :cond_1

    .line 31
    .line 32
    sget-object v2, Lorg/xbet/uikit/utils/E;->b:Lorg/xbet/uikit/utils/E$a;

    .line 33
    .line 34
    invoke-virtual {v2, v0, v1}, Lorg/xbet/uikit/utils/E$a;->c(J)V

    .line 35
    .line 36
    .line 37
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit/utils/E;->e(J)V

    .line 38
    .line 39
    .line 40
    iget-object v0, p0, LN11/c;->h:Lkotlin/jvm/functions/Function1;

    .line 41
    .line 42
    invoke-interface {v0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    :cond_1
    return-void
.end method
