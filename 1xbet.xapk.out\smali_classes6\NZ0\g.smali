.class public final synthetic LNZ0/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/chips/DsChip;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/chips/DsChip;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/g;->a:Lorg/xbet/uikit/components/chips/DsChip;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LNZ0/g;->a:Lorg/xbet/uikit/components/chips/DsChip;

    invoke-static {v0}, Lorg/xbet/uikit/components/chips/DsChip;->b(Lorg/xbet/uikit/components/chips/DsChip;)Z

    move-result v0

    invoke-static {v0}, <PERSON><PERSON><PERSON>/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method
