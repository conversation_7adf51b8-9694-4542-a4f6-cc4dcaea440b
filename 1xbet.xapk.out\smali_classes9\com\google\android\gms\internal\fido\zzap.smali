.class public final synthetic Lcom/google/android/gms/internal/fido/zzap;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final synthetic zza:Lcom/google/android/gms/internal/fido/zzap;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/fido/zzap;

    invoke-direct {v0}, Lcom/google/android/gms/internal/fido/zzap;-><init>()V

    sput-object v0, Lcom/google/android/gms/internal/fido/zzap;->zza:Lcom/google/android/gms/internal/fido/zzap;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
