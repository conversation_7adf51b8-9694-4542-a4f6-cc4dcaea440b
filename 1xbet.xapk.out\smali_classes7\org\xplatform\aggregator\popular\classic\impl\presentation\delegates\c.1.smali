.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0014\u0008\u0000\u0018\u0000 #2\u00020\u0001:\u0001\u001dB!\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\'\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\'\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u0013\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0012J\u001f\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0017\u0010\u0017\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J\'\u0010\u0019\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\'\u0010\u001b\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u001aJ\u0017\u0010\u001c\u001a\u00020\u00102\u0006\u0010\u000b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0018R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001eR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010 R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010\"\u00a8\u0006$"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;",
        "",
        "LTZ0/a;",
        "actionDialogManager",
        "LzX0/k;",
        "snackbarManager",
        "Lak/b;",
        "changeBalanceFeature",
        "<init>",
        "(LTZ0/a;LzX0/k;Lak/b;)V",
        "Landroidx/fragment/app/Fragment;",
        "fragment",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "LAb1/a;",
        "aggregatorPopularViewModel",
        "",
        "p",
        "(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V",
        "viewModel",
        "i",
        "n",
        "(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;)V",
        "m",
        "(Landroidx/fragment/app/Fragment;)V",
        "k",
        "(Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V",
        "o",
        "q",
        "a",
        "LTZ0/a;",
        "b",
        "LzX0/k;",
        "c",
        "Lak/b;",
        "d",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final d:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final a:LTZ0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LzX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lak/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->d:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c$a;

    return-void
.end method

.method public constructor <init>(LTZ0/a;LzX0/k;Lak/b;)V
    .locals 0
    .param p1    # LTZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LzX0/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lak/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->a:LTZ0/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->b:LzX0/k;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->c:Lak/b;

    .line 9
    .line 10
    return-void
.end method

.method public static synthetic a(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->j(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->l(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public static final synthetic c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;)LzX0/k;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->b:LzX0/k;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->m(Landroidx/fragment/app/Fragment;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->n(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic f(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->o(Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->p(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;Landroidx/fragment/app/Fragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->q(Landroidx/fragment/app/Fragment;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final j(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-interface {p0, p1}, LAb1/a;->d(Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final l(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 3

    .line 1
    const-string v0, "SELECT_BALANCE_REQUEST_KEY"

    .line 2
    .line 3
    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result p2

    .line 7
    if-nez p2, :cond_0

    .line 8
    .line 9
    goto :goto_1

    .line 10
    :cond_0
    const-string p2, "RESULT_ON_ITEM_SELECTED_LISTENER_KEY"

    .line 11
    .line 12
    invoke-virtual {p3, p2}, Landroid/os/BaseBundle;->containsKey(Ljava/lang/String;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_5

    .line 17
    .line 18
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 19
    .line 20
    const/16 v1, 0x21

    .line 21
    .line 22
    const/4 v2, 0x0

    .line 23
    if-lt v0, v1, :cond_1

    .line 24
    .line 25
    const-class v0, Lorg/xbet/balance/model/BalanceModel;

    .line 26
    .line 27
    invoke-static {p3, p2, v0}, Lcom/xbet/security/impl/presentation/phone/confirm/check/a;->a(Landroid/os/Bundle;Ljava/lang/String;Ljava/lang/Class;)Ljava/io/Serializable;

    .line 28
    .line 29
    .line 30
    move-result-object p2

    .line 31
    goto :goto_0

    .line 32
    :cond_1
    invoke-virtual {p3, p2}, Landroid/os/Bundle;->getSerializable(Ljava/lang/String;)Ljava/io/Serializable;

    .line 33
    .line 34
    .line 35
    move-result-object p2

    .line 36
    instance-of p3, p2, Lorg/xbet/balance/model/BalanceModel;

    .line 37
    .line 38
    if-nez p3, :cond_2

    .line 39
    .line 40
    move-object p2, v2

    .line 41
    :cond_2
    check-cast p2, Lorg/xbet/balance/model/BalanceModel;

    .line 42
    .line 43
    :goto_0
    instance-of p3, p2, Lorg/xbet/balance/model/BalanceModel;

    .line 44
    .line 45
    if-eqz p3, :cond_3

    .line 46
    .line 47
    move-object v2, p2

    .line 48
    check-cast v2, Lorg/xbet/balance/model/BalanceModel;

    .line 49
    .line 50
    :cond_3
    if-nez v2, :cond_4

    .line 51
    .line 52
    goto :goto_1

    .line 53
    :cond_4
    invoke-interface {p0, v2, p1}, LAb1/a;->f(Lorg/xbet/balance/model/BalanceModel;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 54
    .line 55
    .line 56
    :cond_5
    :goto_1
    return-void
.end method


# virtual methods
.method public final i(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    instance-of v0, p1, Landroidx/appcompat/app/AppCompatActivity;

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    check-cast p1, Landroidx/appcompat/app/AppCompatActivity;

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 p1, 0x0

    .line 13
    :goto_0
    if-eqz p1, :cond_1

    .line 14
    .line 15
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;

    .line 16
    .line 17
    invoke-direct {v0, p3, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;-><init>(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 18
    .line 19
    .line 20
    const-string p2, "REQUEST_ATTENTION_DIALOG_KEY"

    .line 21
    .line 22
    invoke-static {p1, p2, v0}, LVZ0/c;->d(Landroidx/appcompat/app/AppCompatActivity;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 23
    .line 24
    .line 25
    :cond_1
    return-void
.end method

.method public final k(Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;

    .line 6
    .line 7
    invoke-direct {v1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;-><init>(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 8
    .line 9
    .line 10
    const-string p2, "SELECT_BALANCE_REQUEST_KEY"

    .line 11
    .line 12
    invoke-virtual {v0, p2, p1, v1}, Landroidx/fragment/app/FragmentManager;->L1(Ljava/lang/String;Landroidx/lifecycle/w;Landroidx/fragment/app/J;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final m(Landroidx/fragment/app/Fragment;)V
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->b:LzX0/k;

    .line 2
    .line 3
    new-instance v1, Ly01/g;

    .line 4
    .line 5
    sget-object v2, Ly01/i$c;->a:Ly01/i$c;

    .line 6
    .line 7
    sget v3, Lpb/k;->get_balance_list_error:I

    .line 8
    .line 9
    invoke-virtual {p1, v3}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    const/16 v8, 0x3c

    .line 14
    .line 15
    const/4 v9, 0x0

    .line 16
    const/4 v4, 0x0

    .line 17
    const/4 v5, 0x0

    .line 18
    const/4 v6, 0x0

    .line 19
    const/4 v7, 0x0

    .line 20
    invoke-direct/range {v1 .. v9}, Ly01/g;-><init>(Ly01/i;Ljava/lang/String;Ljava/lang/String;Ly01/e;Ly01/f;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 21
    .line 22
    .line 23
    const/16 v10, 0x1fc

    .line 24
    .line 25
    const/4 v11, 0x0

    .line 26
    const/4 v3, 0x0

    .line 27
    const/4 v5, 0x0

    .line 28
    const/4 v6, 0x0

    .line 29
    const/4 v8, 0x0

    .line 30
    move-object v2, p1

    .line 31
    invoke-static/range {v0 .. v11}, LzX0/k;->x(LzX0/k;Ly01/g;Landroidx/fragment/app/Fragment;Landroid/view/Window;Landroid/view/View;ZZLkotlin/jvm/functions/Function0;ZLjava/lang/Integer;ILjava/lang/Object;)Ly01/d;

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final n(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getArguments()Landroid/os/Bundle;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const-string v1, "OPEN_GAME_ITEM"

    .line 6
    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {v0, v1, p2}, Landroid/os/Bundle;->putSerializable(Ljava/lang/String;Ljava/io/Serializable;)V

    .line 10
    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-static {v1, p2}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 14
    .line 15
    .line 16
    move-result-object p2

    .line 17
    const/4 v0, 0x1

    .line 18
    new-array v0, v0, [Lkotlin/Pair;

    .line 19
    .line 20
    const/4 v1, 0x0

    .line 21
    aput-object p2, v0, v1

    .line 22
    .line 23
    invoke-static {v0}, Landroidx/core/os/d;->b([Lkotlin/Pair;)Landroid/os/Bundle;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    invoke-virtual {p1, p2}, Landroidx/fragment/app/Fragment;->setArguments(Landroid/os/Bundle;)V

    .line 28
    .line 29
    .line 30
    :goto_0
    sget-object p2, LKW0/b;->a:LKW0/b;

    .line 31
    .line 32
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->a:LTZ0/a;

    .line 33
    .line 34
    invoke-virtual {p2, p1, v0}, LKW0/b;->c(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 35
    .line 36
    .line 37
    return-void
.end method

.method public final o(Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 14

    .line 1
    invoke-virtual/range {p0 .. p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->k(Landroidx/fragment/app/Fragment;LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->c:Lak/b;

    .line 5
    .line 6
    invoke-interface {v0}, Lak/b;->a()Lck/a;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    sget-object v2, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 11
    .line 12
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 13
    .line 14
    .line 15
    move-result-object v6

    .line 16
    const/16 v12, 0x26e

    .line 17
    .line 18
    const/4 v13, 0x0

    .line 19
    const/4 v3, 0x0

    .line 20
    const/4 v4, 0x0

    .line 21
    const/4 v5, 0x0

    .line 22
    const/4 v7, 0x0

    .line 23
    const/4 v8, 0x0

    .line 24
    const/4 v9, 0x0

    .line 25
    const-string v10, "SELECT_BALANCE_REQUEST_KEY"

    .line 26
    .line 27
    const/4 v11, 0x0

    .line 28
    invoke-static/range {v1 .. v13}, Lck/a$a;->a(Lck/a;Lorg/xbet/balance/model/BalanceScreenType;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Landroidx/fragment/app/FragmentManager;ZZZLjava/lang/String;ZILjava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public final p(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->i(Landroidx/fragment/app/Fragment;Lorg/xplatform/aggregator/api/model/Game;LAb1/a;)V

    .line 8
    .line 9
    .line 10
    sget-object p1, LKW0/b;->a:LKW0/b;

    .line 11
    .line 12
    iget-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->a:LTZ0/a;

    .line 13
    .line 14
    invoke-virtual {p1, v0, p2}, LKW0/b;->b(Landroidx/fragment/app/FragmentActivity;LTZ0/a;)V

    .line 15
    .line 16
    .line 17
    :cond_0
    return-void
.end method

.method public final q(Landroidx/fragment/app/Fragment;)V
    .locals 2

    .line 1
    sget-object v0, LKW0/b;->a:LKW0/b;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->a:LTZ0/a;

    .line 4
    .line 5
    invoke-virtual {v0, p1, v1}, LKW0/b;->f(Landroidx/fragment/app/Fragment;LTZ0/a;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
