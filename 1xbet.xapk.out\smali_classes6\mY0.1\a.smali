.class public abstract LmY0/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LmY0/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmY0/a$a;,
        LmY0/a$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<Position:",
        "LmY0/d;",
        ">",
        "Ljava/lang/Object;",
        "LmY0/e<",
        "TPosition;>;"
    }
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0007\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0010!\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\r\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0015\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0016\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\'\u0018\u0000*\u0008\u0008\u0000\u0010\u0002*\u00020\u00012\u0008\u0012\u0004\u0012\u00028\u00000\u0003:\u0002\u0015\nB\u0007\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\'\u0010\n\u001a\u00020\t2\u0016\u0010\u0008\u001a\u000c\u0012\u0008\u0008\u0001\u0012\u0004\u0018\u00010\u00070\u0006\"\u0004\u0018\u00010\u0007H\u0016\u00a2\u0006\u0004\u0008\n\u0010\u000bJ/\u0010\u0012\u001a\u00020\u00112\u0006\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000e\u001a\u00020\u000c2\u0006\u0010\u000f\u001a\u00020\u000c2\u0006\u0010\u0010\u001a\u00020\u000cH\u0004\u00a2\u0006\u0004\u0008\u0012\u0010\u0013R\u001a\u0010\u0017\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R*\u0010\u001e\u001a\u0012\u0012\u0004\u0012\u00020\u00190\u0018j\u0008\u0012\u0004\u0012\u00020\u0019`\u001a8\u0004X\u0084\u0004\u00a2\u0006\u000c\n\u0004\u0008\n\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001dR\u001a\u0010\u0008\u001a\u00020\u00078\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u001f\u0010 \u001a\u0004\u0008!\u0010\"R$\u0010*\u001a\u0004\u0018\u00010#8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'\"\u0004\u0008(\u0010)R$\u00102\u001a\u0004\u0018\u00010+8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/\"\u0004\u00080\u00101R$\u00106\u001a\u0004\u0018\u00010+8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00083\u0010-\u001a\u0004\u00084\u0010/\"\u0004\u00085\u00101R$\u0010:\u001a\u0004\u0018\u00010+8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00087\u0010-\u001a\u0004\u00088\u0010/\"\u0004\u00089\u00101R\"\u0010@\u001a\u00020\u000c8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008;\u00109\u001a\u0004\u0008<\u0010=\"\u0004\u0008>\u0010?R\"\u0010H\u001a\u00020A8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008B\u0010C\u001a\u0004\u0008D\u0010E\"\u0004\u0008F\u0010GR(\u0010P\u001a\u0008\u0012\u0004\u0012\u00028\u00000I8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008J\u0010K\u001a\u0004\u0008L\u0010M\"\u0004\u0008N\u0010OR\"\u0010T\u001a\u00020\u000c8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008Q\u00109\u001a\u0004\u0008R\u0010=\"\u0004\u0008S\u0010?R$\u0010X\u001a\u0004\u0018\u00010#8\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008U\u0010%\u001a\u0004\u0008V\u0010\'\"\u0004\u0008W\u0010)R$\u0010_\u001a\u0004\u0018\u00010\u00198\u0006@\u0006X\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008Y\u0010Z\u001a\u0004\u0008[\u0010\\\"\u0004\u0008]\u0010^R\u0018\u0010c\u001a\u00020\u000c*\u00020`8DX\u0084\u0004\u00a2\u0006\u0006\u001a\u0004\u0008a\u0010bR\u0018\u0010e\u001a\u00020\u000c*\u00020`8DX\u0084\u0004\u00a2\u0006\u0006\u001a\u0004\u0008d\u0010bR\u0018\u0010g\u001a\u00020\u000c*\u00020`8DX\u0084\u0004\u00a2\u0006\u0006\u001a\u0004\u0008f\u0010bR\u0018\u0010i\u001a\u00020\u000c*\u00020`8DX\u0084\u0004\u00a2\u0006\u0006\u001a\u0004\u0008h\u0010b\u00a8\u0006j"
    }
    d2 = {
        "LmY0/a;",
        "LmY0/d;",
        "Position",
        "LmY0/e;",
        "<init>",
        "()V",
        "",
        "Landroid/graphics/RectF;",
        "bounds",
        "",
        "b",
        "([Landroid/graphics/RectF;)V",
        "",
        "left",
        "top",
        "right",
        "bottom",
        "",
        "D",
        "(FFFF)Z",
        "",
        "a",
        "Ljava/util/List;",
        "restrictedBounds",
        "Ljava/util/ArrayList;",
        "",
        "Lkotlin/collections/ArrayList;",
        "Ljava/util/ArrayList;",
        "v",
        "()Ljava/util/ArrayList;",
        "labels",
        "c",
        "Landroid/graphics/RectF;",
        "getBounds",
        "()Landroid/graphics/RectF;",
        "Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "d",
        "Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "t",
        "()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;",
        "G",
        "(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V",
        "label",
        "LDY0/a;",
        "e",
        "LDY0/a;",
        "p",
        "()LDY0/a;",
        "E",
        "(LDY0/a;)V",
        "axisLine",
        "f",
        "x",
        "J",
        "tick",
        "g",
        "r",
        "F",
        "guideline",
        "h",
        "getTickLengthDp",
        "()F",
        "K",
        "(F)V",
        "tickLengthDp",
        "LmY0/a$b;",
        "i",
        "LmY0/a$b;",
        "w",
        "()LmY0/a$b;",
        "I",
        "(LmY0/a$b;)V",
        "sizeConstraint",
        "LnY0/a;",
        "j",
        "LnY0/a;",
        "C",
        "()LnY0/a;",
        "N",
        "(LnY0/a;)V",
        "valueFormatter",
        "k",
        "u",
        "H",
        "labelRotationDegrees",
        "l",
        "B",
        "M",
        "titleComponent",
        "m",
        "Ljava/lang/CharSequence;",
        "A",
        "()Ljava/lang/CharSequence;",
        "L",
        "(Ljava/lang/CharSequence;)V",
        "title",
        "LIY0/d;",
        "q",
        "(LIY0/d;)F",
        "axisThickness",
        "z",
        "tickThickness",
        "s",
        "guidelineThickness",
        "y",
        "tickLength",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/graphics/RectF;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/CharSequence;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/graphics/RectF;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

.field public e:LDY0/a;

.field public f:LDY0/a;

.field public g:LDY0/a;

.field public h:F

.field public i:LmY0/a$b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j:LnY0/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LnY0/a<",
            "TPosition;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k:F

.field public l:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

.field public m:Ljava/lang/CharSequence;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>()V
    .locals 4

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Ljava/util/ArrayList;

    .line 5
    .line 6
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, LmY0/a;->a:Ljava/util/List;

    .line 10
    .line 11
    new-instance v0, Ljava/util/ArrayList;

    .line 12
    .line 13
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, LmY0/a;->b:Ljava/util/ArrayList;

    .line 17
    .line 18
    new-instance v0, Landroid/graphics/RectF;

    .line 19
    .line 20
    invoke-direct {v0}, Landroid/graphics/RectF;-><init>()V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, LmY0/a;->c:Landroid/graphics/RectF;

    .line 24
    .line 25
    new-instance v0, LmY0/a$b$a;

    .line 26
    .line 27
    const/4 v1, 0x3

    .line 28
    const/4 v2, 0x0

    .line 29
    const/4 v3, 0x0

    .line 30
    invoke-direct {v0, v3, v3, v1, v2}, LmY0/a$b$a;-><init>(FFILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 31
    .line 32
    .line 33
    iput-object v0, p0, LmY0/a;->i:LmY0/a$b;

    .line 34
    .line 35
    new-instance v0, LnY0/b;

    .line 36
    .line 37
    invoke-direct {v0}, LnY0/b;-><init>()V

    .line 38
    .line 39
    .line 40
    iput-object v0, p0, LmY0/a;->j:LnY0/a;

    .line 41
    .line 42
    return-void
.end method


# virtual methods
.method public final A()Ljava/lang/CharSequence;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a;->m:Ljava/lang/CharSequence;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a;->l:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-object v0
.end method

.method public final C()LnY0/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LnY0/a<",
            "TPosition;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/a;->j:LnY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final D(FFFF)Z
    .locals 4

    .line 1
    iget-object v0, p0, LmY0/a;->a:Ljava/util/List;

    .line 2
    .line 3
    invoke-static {v0}, Landroidx/activity/v;->a(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 11
    .line 12
    .line 13
    move-result v1

    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    return v2

    .line 17
    :cond_0
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 22
    .line 23
    .line 24
    move-result v1

    .line 25
    if-eqz v1, :cond_3

    .line 26
    .line 27
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    check-cast v1, Landroid/graphics/RectF;

    .line 32
    .line 33
    invoke-virtual {v1, p1, p2, p3, p4}, Landroid/graphics/RectF;->contains(FFFF)Z

    .line 34
    .line 35
    .line 36
    move-result v3

    .line 37
    if-nez v3, :cond_2

    .line 38
    .line 39
    invoke-virtual {v1, p1, p2, p3, p4}, Landroid/graphics/RectF;->intersects(FFFF)Z

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    if-eqz v1, :cond_1

    .line 44
    .line 45
    :cond_2
    const/4 p1, 0x0

    .line 46
    return p1

    .line 47
    :cond_3
    return v2
.end method

.method public final E(LDY0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a;->e:LDY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public final F(LDY0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a;->g:LDY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public final G(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a;->d:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-void
.end method

.method public final H(F)V
    .locals 0

    .line 1
    iput p1, p0, LmY0/a;->k:F

    .line 2
    .line 3
    return-void
.end method

.method public final I(LmY0/a$b;)V
    .locals 0
    .param p1    # LmY0/a$b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, LmY0/a;->i:LmY0/a$b;

    .line 2
    .line 3
    return-void
.end method

.method public final J(LDY0/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a;->f:LDY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public final K(F)V
    .locals 0

    .line 1
    iput p1, p0, LmY0/a;->h:F

    .line 2
    .line 3
    return-void
.end method

.method public final L(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a;->m:Ljava/lang/CharSequence;

    .line 2
    .line 3
    return-void
.end method

.method public final M(Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;)V
    .locals 0

    .line 1
    iput-object p1, p0, LmY0/a;->l:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-void
.end method

.method public final N(LnY0/a;)V
    .locals 0
    .param p1    # LnY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LnY0/a<",
            "TPosition;>;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, LmY0/a;->j:LnY0/a;

    .line 2
    .line 3
    return-void
.end method

.method public varargs b([Landroid/graphics/RectF;)V
    .locals 1
    .param p1    # [Landroid/graphics/RectF;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/a;->a:Ljava/util/List;

    .line 2
    .line 3
    invoke-static {p1}, Lkotlin/collections/r;->f0([Ljava/lang/Object;)Ljava/util/List;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {v0, p1}, LMY0/c;->e(Ljava/util/List;Ljava/util/Collection;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public c(Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V
    .locals 0
    .param p1    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/Number;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, LmY0/e$a;->c(LmY0/e;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;Ljava/lang/Number;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public f(LIY0/d;LwY0/c;LtY0/a;)V
    .locals 0
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LwY0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LtY0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1, p2, p3}, LmY0/e$a;->b(LmY0/e;LIY0/d;LwY0/c;LtY0/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public getBounds()Landroid/graphics/RectF;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/a;->c:Landroid/graphics/RectF;

    .line 2
    .line 3
    return-object v0
.end method

.method public m(LIY0/d;FLwY0/b;)V
    .locals 0
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # LwY0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1, p2, p3}, LmY0/e$a;->a(LmY0/e;LIY0/d;FLwY0/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final p()LDY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a;->e:LDY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final q(LIY0/d;)F
    .locals 1
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/a;->e:LDY0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, LDY0/a;->t()F

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    :goto_0
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    const/4 v0, 0x0

    .line 23
    :goto_1
    invoke-interface {p1, v0}, LIY0/d;->N(F)F

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    return p1
.end method

.method public final r()LDY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a;->g:LDY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s(LIY0/d;)F
    .locals 1
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/a;->g:LDY0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, LDY0/a;->t()F

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    :goto_0
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    const/4 v0, 0x0

    .line 23
    :goto_1
    invoke-interface {p1, v0}, LIY0/d;->N(F)F

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    return p1
.end method

.method public final t()Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a;->d:Lorg/xbet/ui_common/viewcomponents/views/chartview/core/component/text/TextComponent;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u()F
    .locals 1

    .line 1
    iget v0, p0, LmY0/a;->k:F

    .line 2
    .line 3
    return v0
.end method

.method public final v()Ljava/util/ArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/ArrayList<",
            "Ljava/lang/CharSequence;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/a;->b:Ljava/util/ArrayList;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w()LmY0/a$b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LmY0/a;->i:LmY0/a$b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final x()LDY0/a;
    .locals 1

    .line 1
    iget-object v0, p0, LmY0/a;->f:LDY0/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y(LIY0/d;)F
    .locals 1
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/a;->f:LDY0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget v0, p0, LmY0/a;->h:F

    .line 6
    .line 7
    invoke-interface {p1, v0}, LIY0/d;->N(F)F

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    return p1

    .line 12
    :cond_0
    const/4 p1, 0x0

    .line 13
    return p1
.end method

.method public final z(LIY0/d;)F
    .locals 1
    .param p1    # LIY0/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, LmY0/a;->f:LDY0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, LDY0/a;->t()F

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    :goto_0
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    const/4 v0, 0x0

    .line 23
    :goto_1
    invoke-interface {p1, v0}, LIY0/d;->N(F)F

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    return p1
.end method
