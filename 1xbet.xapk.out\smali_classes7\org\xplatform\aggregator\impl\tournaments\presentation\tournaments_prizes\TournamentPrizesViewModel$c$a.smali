.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0011\u0008\u0086\u0008\u0018\u00002\u00020\u0001B;\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u000c\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u000c\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u000eH\u00d6\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u0010\u0010\u0012\u001a\u00020\u0011H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0012\u0010\u0013J\u001a\u0010\u0016\u001a\u00020\n2\u0008\u0010\u0015\u001a\u0004\u0018\u00010\u0014H\u00d6\u0003\u00a2\u0006\u0004\u0008\u0016\u0010\u0017R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0018\u0010\u0019\u001a\u0004\u0008\u001a\u0010\u001bR\u0017\u0010\u0005\u001a\u00020\u00048\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001c\u0010\u001d\u001a\u0004\u0008\u001e\u0010\u001fR\u001d\u0010\u0008\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008 \u0010!\u001a\u0004\u0008\u0018\u0010\"R\u001d\u0010\t\u001a\u0008\u0012\u0004\u0012\u00020\u00070\u00068\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010!\u001a\u0004\u0008 \u0010\"R\u0017\u0010\u000b\u001a\u00020\n8\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001e\u0010#\u001a\u0004\u0008\u001c\u0010$\u00a8\u0006%"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "tournamentKind",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "userActionButton",
        "",
        "Lkb1/z;",
        "prizes",
        "stagePrize",
        "",
        "showTabs",
        "<init>",
        "(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Ljava/util/List;Ljava/util/List;Z)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "d",
        "()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "b",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "e",
        "()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
        "c",
        "Ljava/util/List;",
        "()Ljava/util/List;",
        "Z",
        "()Z",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkb1/z;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkb1/z;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Z


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Ljava/util/List;Ljava/util/List;Z)V
    .locals 0
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
            "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;",
            "Ljava/util/List<",
            "+",
            "Lkb1/z;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Lkb1/z;",
            ">;Z)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c:Ljava/util/List;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->d:Ljava/util/List;

    .line 11
    .line 12
    iput-boolean p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e:Z

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final a()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lkb1/z;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e:Z

    .line 2
    .line 3
    return v0
.end method

.method public final c()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lkb1/z;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->d:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 2
    .line 3
    return-object v0
.end method

.method public final e()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    if-eq v1, v3, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c:Ljava/util/List;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c:Ljava/util/List;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_4

    return v2

    :cond_4
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->d:Ljava/util/List;

    iget-object v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->d:Ljava/util/List;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_5

    return v2

    :cond_5
    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e:Z

    iget-boolean p1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e:Z

    if-eq v1, p1, :cond_6

    return v2

    :cond_6
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c:Ljava/util/List;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->d:Ljava/util/List;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e:Z

    invoke-static {v1}, Landroidx/compose/animation/j;->a(Z)I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 7
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->a:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->b:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    iget-object v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->c:Ljava/util/List;

    iget-object v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->d:Ljava/util/List;

    iget-boolean v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;->e:Z

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Content(tournamentKind="

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", userActionButton="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", prizes="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", stagePrize="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", showTabs="

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
