.class public Lorg/xbet/uikit_sport/sport_cell/DsSportCell;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0015\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0017\u0018\u00002\u00020\u0001:\u0001?B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0002\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u001f\u0010\r\u001a\u00020\u000c2\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u0006H\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0015\u0010\u0015\u001a\u00020\u000c2\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\u0015\u0010\u0018\u001a\u00020\u000c2\u0006\u0010\u0017\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0018\u0010\u0016J\u000f\u0010\u0019\u001a\u00020\u000cH\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u001aR\u0016\u0010\u0014\u001a\u00020\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u0016\u0010\u0017\u001a\u00020\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010\u001cR\u0016\u0010\u001f\u001a\u00020\u00138\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001cR\u0016\u0010#\u001a\u00020 8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008!\u0010\"R\u0016\u0010&\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008$\u0010%R\u0016\u0010(\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\'\u0010%R\u0016\u0010*\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008)\u0010%R\u001d\u00100\u001a\u0004\u0018\u00010+8DX\u0084\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008,\u0010-\u001a\u0004\u0008.\u0010/R\u001d\u00104\u001a\u0004\u0018\u0001018DX\u0084\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008\u0019\u0010-\u001a\u0004\u00082\u00103R\u001d\u00109\u001a\u0004\u0018\u0001058DX\u0084\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00086\u0010-\u001a\u0004\u00087\u00108R\u001b\u0010>\u001a\u00020:8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008;\u0010-\u001a\u0004\u0008<\u0010=\u00a8\u0006@"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "extraSpace",
        "",
        "onCreateDrawableState",
        "(I)[I",
        "",
        "roundedTop",
        "setRoundedTop",
        "(Z)V",
        "roundedBottom",
        "setRoundedBottom",
        "i",
        "()V",
        "a",
        "Z",
        "b",
        "c",
        "separatorAvailable",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;",
        "d",
        "Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;",
        "roundCornersType",
        "e",
        "I",
        "cellMaxWidth",
        "f",
        "separatorStartPadding",
        "g",
        "separatorEndPadding",
        "Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;",
        "h",
        "Lkotlin/j;",
        "getCellLeftView",
        "()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;",
        "cellLeftView",
        "Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;",
        "getCellMiddleView",
        "()Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;",
        "cellMiddleView",
        "Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;",
        "j",
        "getCellRightView",
        "()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;",
        "cellRightView",
        "Lorg/xbet/uikit/components/separator/Separator;",
        "k",
        "getSeparator",
        "()Lorg/xbet/uikit/components/separator/Separator;",
        "separator",
        "RoundCornersType",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public a:Z

.field public b:Z

.field public c:Z

.field public d:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public e:I

.field public f:I

.field public g:I

.field public final h:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final i:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final j:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final k:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    const/4 v0, 0x1

    .line 6
    iput-boolean v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->c:Z

    .line 7
    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_16:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->d:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    const v1, 0x7fffffff

    .line 8
    iput v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->e:I

    .line 9
    new-instance v1, LJ31/a;

    invoke-direct {v1, p0}, LJ31/a;-><init>(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)V

    .line 10
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    invoke-static {v2, v1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v1

    .line 11
    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->h:Lkotlin/j;

    .line 12
    new-instance v1, LJ31/b;

    invoke-direct {v1, p0}, LJ31/b;-><init>(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)V

    .line 13
    invoke-static {v2, v1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v1

    .line 14
    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->i:Lkotlin/j;

    .line 15
    new-instance v1, LJ31/c;

    invoke-direct {v1, p0}, LJ31/c;-><init>(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)V

    .line 16
    invoke-static {v2, v1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v1

    .line 17
    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->j:Lkotlin/j;

    .line 18
    new-instance v1, LJ31/d;

    invoke-direct {v1, p1}, LJ31/d;-><init>(Landroid/content/Context;)V

    .line 19
    invoke-static {v2, v1}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v1

    .line 20
    iput-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->k:Lkotlin/j;

    .line 21
    sget-object v1, Lm31/g;->DsSportCell:[I

    const/4 v2, 0x0

    .line 22
    invoke-virtual {p1, p2, v1, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object p2

    .line 23
    sget p3, Lm31/g;->DsSportCell_sportCellBackgroundTint:I

    invoke-static {p2, p1, p3}, Lorg/xbet/uikit/utils/I;->c(Landroid/content/res/TypedArray;Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    move-result-object p1

    invoke-static {p0, p1}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 24
    sget p1, Lm31/g;->DsSportCell_sportCellRoundedTop:I

    iget-boolean p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->a:Z

    invoke-virtual {p2, p1, p3}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result p1

    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->a:Z

    .line 25
    sget p1, Lm31/g;->DsSportCell_sportCellRoundedBottom:I

    iget-boolean p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    invoke-virtual {p2, p1, p3}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result p1

    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    .line 26
    sget p1, Lm31/g;->DsSportCell_sportCellSeparatorAvailable:I

    iget-boolean p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->c:Z

    invoke-virtual {p2, p1, p3}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result p1

    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->c:Z

    .line 27
    sget p1, Lm31/g;->DsSportCell_sportCellSeparatorStartPadding:I

    iget p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->f:I

    invoke-virtual {p2, p1, p3}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->f:I

    .line 28
    sget p1, Lm31/g;->DsSportCell_sportCellSeparatorEndPadding:I

    iget p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->g:I

    invoke-virtual {p2, p1, p3}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->g:I

    .line 29
    sget-object p1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->Companion:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;

    sget p3, Lm31/g;->DsSportCell_sportCellRoundCorners:I

    sget-object v1, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_ZERO:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    invoke-virtual {v1}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->getValue()I

    move-result v1

    invoke-virtual {p2, p3, v1}, Landroid/content/res/TypedArray;->getInt(II)I

    move-result p3

    invoke-virtual {p1, p3}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType$a;->a(I)Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->d:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 30
    sget p1, Lm31/g;->DsSportCell_sportCellMaxWidth:I

    iget p3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->e:I

    invoke-virtual {p2, p1, p3}, Landroid/content/res/TypedArray;->getDimensionPixelSize(II)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->e:I

    .line 31
    invoke-virtual {p2}, Landroid/content/res/TypedArray;->recycle()V

    .line 32
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getSeparator()Lorg/xbet/uikit/components/separator/Separator;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 33
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getSeparator()Lorg/xbet/uikit/components/separator/Separator;

    move-result-object p1

    iget-boolean p2, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->c:Z

    if-eqz p2, :cond_0

    iget-boolean p2, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    goto :goto_1

    :cond_1
    const/16 v2, 0x8

    .line 34
    :goto_1
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCellStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->g(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->e(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Landroid/content/Context;)Lorg/xbet/uikit/components/separator/Separator;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->h(Landroid/content/Context;)Lorg/xbet/uikit/components/separator/Separator;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->f(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
    .locals 3

    .line 1
    invoke-static {p0}, Landroidx/core/view/ViewGroupKt;->c(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v1, 0x0

    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    move-object v2, v0

    .line 21
    check-cast v2, Landroid/view/View;

    .line 22
    .line 23
    instance-of v2, v2, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 24
    .line 25
    if-eqz v2, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    move-object v0, v1

    .line 29
    :goto_0
    instance-of p0, v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 30
    .line 31
    if-nez p0, :cond_2

    .line 32
    .line 33
    move-object v0, v1

    .line 34
    :cond_2
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 35
    .line 36
    if-eqz v0, :cond_4

    .line 37
    .line 38
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 39
    .line 40
    .line 41
    move-result p0

    .line 42
    const/4 v1, -0x1

    .line 43
    if-ne p0, v1, :cond_3

    .line 44
    .line 45
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 46
    .line 47
    .line 48
    move-result p0

    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 50
    .line 51
    .line 52
    :cond_3
    return-object v0

    .line 53
    :cond_4
    return-object v1
.end method

.method public static final f(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
    .locals 3

    .line 1
    invoke-static {p0}, Landroidx/core/view/ViewGroupKt;->c(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v1, 0x0

    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    move-object v2, v0

    .line 21
    check-cast v2, Landroid/view/View;

    .line 22
    .line 23
    instance-of v2, v2, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 24
    .line 25
    if-eqz v2, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    move-object v0, v1

    .line 29
    :goto_0
    instance-of p0, v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 30
    .line 31
    if-nez p0, :cond_2

    .line 32
    .line 33
    move-object v0, v1

    .line 34
    :cond_2
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 35
    .line 36
    if-eqz v0, :cond_4

    .line 37
    .line 38
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 39
    .line 40
    .line 41
    move-result p0

    .line 42
    const/4 v1, -0x1

    .line 43
    if-ne p0, v1, :cond_3

    .line 44
    .line 45
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 46
    .line 47
    .line 48
    move-result p0

    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 50
    .line 51
    .line 52
    :cond_3
    return-object v0

    .line 53
    :cond_4
    return-object v1
.end method

.method public static final g(Lorg/xbet/uikit_sport/sport_cell/DsSportCell;)Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
    .locals 3

    .line 1
    invoke-static {p0}, Landroidx/core/view/ViewGroupKt;->c(Landroid/view/ViewGroup;)Lkotlin/sequences/Sequence;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-interface {p0}, Lkotlin/sequences/Sequence;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object p0

    .line 9
    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    const/4 v1, 0x0

    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    move-object v2, v0

    .line 21
    check-cast v2, Landroid/view/View;

    .line 22
    .line 23
    instance-of v2, v2, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 24
    .line 25
    if-eqz v2, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_1
    move-object v0, v1

    .line 29
    :goto_0
    instance-of p0, v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 30
    .line 31
    if-nez p0, :cond_2

    .line 32
    .line 33
    move-object v0, v1

    .line 34
    :cond_2
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 35
    .line 36
    if-eqz v0, :cond_4

    .line 37
    .line 38
    invoke-virtual {v0}, Landroid/view/View;->getId()I

    .line 39
    .line 40
    .line 41
    move-result p0

    .line 42
    const/4 v1, -0x1

    .line 43
    if-ne p0, v1, :cond_3

    .line 44
    .line 45
    invoke-static {}, Lorg/xbet/uikit/utils/S;->f()I

    .line 46
    .line 47
    .line 48
    move-result p0

    .line 49
    invoke-virtual {v0, p0}, Landroid/view/View;->setId(I)V

    .line 50
    .line 51
    .line 52
    :cond_3
    return-object v0

    .line 53
    :cond_4
    return-object v1
.end method

.method private final getSeparator()Lorg/xbet/uikit/components/separator/Separator;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->k:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/separator/Separator;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final h(Landroid/content/Context;)Lorg/xbet/uikit/components/separator/Separator;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/separator/Separator;

    .line 2
    .line 3
    new-instance v1, Landroid/view/ContextThemeWrapper;

    .line 4
    .line 5
    sget v2, LlZ0/n;->Widget_Separator_Separator60:I

    .line 6
    .line 7
    invoke-direct {v1, p0, v2}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    .line 8
    .line 9
    .line 10
    const/4 v4, 0x6

    .line 11
    const/4 v5, 0x0

    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/separator/Separator;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 15
    .line 16
    .line 17
    const/16 p0, 0x8

    .line 18
    .line 19
    invoke-virtual {v0, p0}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    return-object v0
.end method


# virtual methods
.method public final getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->h:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final getCellMiddleView()Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->i:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->j:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 8
    .line 9
    return-object v0
.end method

.method public final i()V
    .locals 3

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getSeparator()Lorg/xbet/uikit/components/separator/Separator;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->c:Z

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    .line 11
    .line 12
    if-nez v1, :cond_0

    .line 13
    .line 14
    const/4 v1, 0x1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 v1, 0x0

    .line 17
    :goto_0
    if-eqz v1, :cond_1

    .line 18
    .line 19
    goto :goto_1

    .line 20
    :cond_1
    const/16 v2, 0x8

    .line 21
    .line 22
    :goto_1
    invoke-virtual {v0, v2}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->refreshDrawableState()V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public onCreateDrawableState(I)[I
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/4 v0, 0x2

    .line 2
    add-int/2addr p1, v0

    .line 3
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onCreateDrawableState(I)[I

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->a:Z

    .line 8
    .line 9
    if-nez v1, :cond_4

    .line 10
    .line 11
    iget-boolean v2, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    .line 12
    .line 13
    if-eqz v2, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->d:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 17
    .line 18
    sget-object v2, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_8:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 19
    .line 20
    const/4 v3, 0x0

    .line 21
    const/4 v4, 0x1

    .line 22
    if-ne v1, v2, :cond_1

    .line 23
    .line 24
    new-array v0, v4, [I

    .line 25
    .line 26
    sget v1, LlZ0/d;->round_8:I

    .line 27
    .line 28
    aput v1, v0, v3

    .line 29
    .line 30
    goto :goto_3

    .line 31
    :cond_1
    sget-object v2, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_16:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 32
    .line 33
    if-ne v1, v2, :cond_2

    .line 34
    .line 35
    new-array v0, v4, [I

    .line 36
    .line 37
    sget v1, LlZ0/d;->round_16:I

    .line 38
    .line 39
    aput v1, v0, v3

    .line 40
    .line 41
    goto :goto_3

    .line 42
    :cond_2
    sget-object v2, Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;->ROUND_360:Lorg/xbet/uikit_sport/sport_cell/DsSportCell$RoundCornersType;

    .line 43
    .line 44
    if-ne v1, v2, :cond_3

    .line 45
    .line 46
    new-array v0, v4, [I

    .line 47
    .line 48
    sget v1, LlZ0/d;->round_full:I

    .line 49
    .line 50
    aput v1, v0, v3

    .line 51
    .line 52
    goto :goto_3

    .line 53
    :cond_3
    new-array v0, v0, [I

    .line 54
    .line 55
    sget v1, LlZ0/d;->order_first:I

    .line 56
    .line 57
    neg-int v1, v1

    .line 58
    aput v1, v0, v3

    .line 59
    .line 60
    sget v1, LlZ0/d;->order_last:I

    .line 61
    .line 62
    neg-int v1, v1

    .line 63
    aput v1, v0, v4

    .line 64
    .line 65
    goto :goto_3

    .line 66
    :cond_4
    :goto_0
    sget v0, LlZ0/d;->order_first:I

    .line 67
    .line 68
    if-eqz v1, :cond_5

    .line 69
    .line 70
    goto :goto_1

    .line 71
    :cond_5
    neg-int v0, v0

    .line 72
    :goto_1
    iget-boolean v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    .line 73
    .line 74
    if-eqz v1, :cond_6

    .line 75
    .line 76
    sget v1, LlZ0/d;->order_last:I

    .line 77
    .line 78
    goto :goto_2

    .line 79
    :cond_6
    sget v1, LlZ0/d;->order_last:I

    .line 80
    .line 81
    neg-int v1, v1

    .line 82
    :goto_2
    filled-new-array {v0, v1}, [I

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    :goto_3
    invoke-static {p1, v0}, Landroid/view/View;->mergeDrawableStates([I[I)[I

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    return-object p1
.end method

.method public onMeasure(II)V
    .locals 9

    .line 1
    const/4 p2, 0x1

    .line 2
    const/4 v0, 0x0

    .line 3
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellMiddleView()Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    invoke-static {v0, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result v2

    .line 14
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 15
    .line 16
    .line 17
    move-result p1

    .line 18
    iget v3, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->e:I

    .line 19
    .line 20
    invoke-static {p1, v3}, Ljava/lang/Math;->min(II)I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 25
    .line 26
    .line 27
    move-result-object v3

    .line 28
    iget v3, v3, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 29
    .line 30
    const/high16 v4, 0x40000000    # 2.0f

    .line 31
    .line 32
    if-lez v3, :cond_1

    .line 33
    .line 34
    invoke-static {v3, v4}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 35
    .line 36
    .line 37
    move-result v3

    .line 38
    goto :goto_0

    .line 39
    :cond_1
    move v3, v2

    .line 40
    :goto_0
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 41
    .line 42
    .line 43
    move-result-object v5

    .line 44
    const/16 v6, 0x8

    .line 45
    .line 46
    if-eqz v5, :cond_2

    .line 47
    .line 48
    invoke-virtual {v5}, Landroid/view/View;->getVisibility()I

    .line 49
    .line 50
    .line 51
    move-result v5

    .line 52
    if-ne v5, v6, :cond_2

    .line 53
    .line 54
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 55
    .line 56
    .line 57
    move-result-object v5

    .line 58
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 59
    .line 60
    .line 61
    move-result-object v7

    .line 62
    invoke-static {v5, v7}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 63
    .line 64
    .line 65
    move-result-object v5

    .line 66
    goto :goto_3

    .line 67
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 68
    .line 69
    .line 70
    move-result-object v5

    .line 71
    if-eqz v5, :cond_3

    .line 72
    .line 73
    invoke-virtual {v5, v2, v2}, Landroid/view/View;->measure(II)V

    .line 74
    .line 75
    .line 76
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 77
    .line 78
    .line 79
    move-result-object v5

    .line 80
    if-eqz v5, :cond_4

    .line 81
    .line 82
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredWidth()I

    .line 83
    .line 84
    .line 85
    move-result v5

    .line 86
    goto :goto_1

    .line 87
    :cond_4
    const/4 v5, 0x0

    .line 88
    :goto_1
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 89
    .line 90
    .line 91
    move-result-object v5

    .line 92
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellRightView()Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 93
    .line 94
    .line 95
    move-result-object v7

    .line 96
    if-eqz v7, :cond_5

    .line 97
    .line 98
    invoke-virtual {v7}, Landroid/view/View;->getMeasuredHeight()I

    .line 99
    .line 100
    .line 101
    move-result v7

    .line 102
    goto :goto_2

    .line 103
    :cond_5
    const/4 v7, 0x0

    .line 104
    :goto_2
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 105
    .line 106
    .line 107
    move-result-object v7

    .line 108
    invoke-static {v5, v7}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 109
    .line 110
    .line 111
    move-result-object v5

    .line 112
    :goto_3
    invoke-virtual {v5}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 113
    .line 114
    .line 115
    move-result-object v7

    .line 116
    check-cast v7, Ljava/lang/Number;

    .line 117
    .line 118
    invoke-virtual {v7}, Ljava/lang/Number;->intValue()I

    .line 119
    .line 120
    .line 121
    move-result v7

    .line 122
    invoke-virtual {v5}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 123
    .line 124
    .line 125
    move-result-object v5

    .line 126
    check-cast v5, Ljava/lang/Number;

    .line 127
    .line 128
    invoke-virtual {v5}, Ljava/lang/Number;->intValue()I

    .line 129
    .line 130
    .line 131
    move-result v5

    .line 132
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 133
    .line 134
    .line 135
    move-result-object v8

    .line 136
    if-eqz v8, :cond_6

    .line 137
    .line 138
    invoke-virtual {v8}, Landroid/view/View;->getVisibility()I

    .line 139
    .line 140
    .line 141
    move-result v8

    .line 142
    if-ne v8, v6, :cond_6

    .line 143
    .line 144
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 145
    .line 146
    .line 147
    move-result-object v2

    .line 148
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 149
    .line 150
    .line 151
    move-result-object v6

    .line 152
    invoke-static {v2, v6}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 153
    .line 154
    .line 155
    move-result-object v2

    .line 156
    goto :goto_7

    .line 157
    :cond_6
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 158
    .line 159
    .line 160
    move-result-object v6

    .line 161
    if-eqz v6, :cond_7

    .line 162
    .line 163
    invoke-virtual {v6}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 164
    .line 165
    .line 166
    move-result-object v6

    .line 167
    if-eqz v6, :cond_7

    .line 168
    .line 169
    iget v6, v6, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 170
    .line 171
    goto :goto_4

    .line 172
    :cond_7
    const/4 v6, 0x0

    .line 173
    :goto_4
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 174
    .line 175
    .line 176
    move-result-object v8

    .line 177
    if-eqz v8, :cond_8

    .line 178
    .line 179
    invoke-static {v6, v4}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 180
    .line 181
    .line 182
    move-result v6

    .line 183
    invoke-virtual {v8, v2, v6}, Landroid/view/View;->measure(II)V

    .line 184
    .line 185
    .line 186
    :cond_8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 187
    .line 188
    .line 189
    move-result-object v2

    .line 190
    if-eqz v2, :cond_9

    .line 191
    .line 192
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 193
    .line 194
    .line 195
    move-result v2

    .line 196
    goto :goto_5

    .line 197
    :cond_9
    const/4 v2, 0x0

    .line 198
    :goto_5
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 199
    .line 200
    .line 201
    move-result-object v2

    .line 202
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getCellLeftView()Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 203
    .line 204
    .line 205
    move-result-object v6

    .line 206
    if-eqz v6, :cond_a

    .line 207
    .line 208
    invoke-virtual {v6}, Landroid/view/View;->getMeasuredHeight()I

    .line 209
    .line 210
    .line 211
    move-result v6

    .line 212
    goto :goto_6

    .line 213
    :cond_a
    const/4 v6, 0x0

    .line 214
    :goto_6
    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 215
    .line 216
    .line 217
    move-result-object v6

    .line 218
    invoke-static {v2, v6}, Lkotlin/o;->a(Ljava/lang/Object;Ljava/lang/Object;)Lkotlin/Pair;

    .line 219
    .line 220
    .line 221
    move-result-object v2

    .line 222
    :goto_7
    invoke-virtual {v2}, Lkotlin/Pair;->component1()Ljava/lang/Object;

    .line 223
    .line 224
    .line 225
    move-result-object v6

    .line 226
    check-cast v6, Ljava/lang/Number;

    .line 227
    .line 228
    invoke-virtual {v6}, Ljava/lang/Number;->intValue()I

    .line 229
    .line 230
    .line 231
    move-result v6

    .line 232
    invoke-virtual {v2}, Lkotlin/Pair;->component2()Ljava/lang/Object;

    .line 233
    .line 234
    .line 235
    move-result-object v2

    .line 236
    check-cast v2, Ljava/lang/Number;

    .line 237
    .line 238
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 239
    .line 240
    .line 241
    move-result v2

    .line 242
    sub-int v7, p1, v7

    .line 243
    .line 244
    sub-int/2addr v7, v6

    .line 245
    invoke-virtual {p0}, Landroid/view/View;->getPaddingStart()I

    .line 246
    .line 247
    .line 248
    move-result v8

    .line 249
    sub-int/2addr v7, v8

    .line 250
    invoke-virtual {p0}, Landroid/view/View;->getPaddingEnd()I

    .line 251
    .line 252
    .line 253
    move-result v8

    .line 254
    sub-int/2addr v7, v8

    .line 255
    const/high16 v8, -0x80000000

    .line 256
    .line 257
    invoke-static {v7, v8}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 258
    .line 259
    .line 260
    move-result v7

    .line 261
    invoke-virtual {v1, v7, v3}, Landroid/view/View;->measure(II)V

    .line 262
    .line 263
    .line 264
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 265
    .line 266
    .line 267
    move-result-object v2

    .line 268
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 269
    .line 270
    .line 271
    move-result v3

    .line 272
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 273
    .line 274
    .line 275
    move-result-object v3

    .line 276
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 277
    .line 278
    .line 279
    move-result-object v5

    .line 280
    const/4 v7, 0x3

    .line 281
    new-array v7, v7, [Ljava/lang/Integer;

    .line 282
    .line 283
    aput-object v2, v7, v0

    .line 284
    .line 285
    aput-object v3, v7, p2

    .line 286
    .line 287
    const/4 v0, 0x2

    .line 288
    aput-object v5, v7, v0

    .line 289
    .line 290
    invoke-static {v7}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    .line 291
    .line 292
    .line 293
    move-result-object v0

    .line 294
    invoke-static {v0}, Lkotlin/collections/CollectionsKt;->Q0(Ljava/lang/Iterable;)Ljava/lang/Comparable;

    .line 295
    .line 296
    .line 297
    move-result-object v0

    .line 298
    check-cast v0, Ljava/lang/Number;

    .line 299
    .line 300
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 301
    .line 302
    .line 303
    move-result v0

    .line 304
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 305
    .line 306
    .line 307
    move-result-object v2

    .line 308
    const-string v3, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 309
    .line 310
    if-eqz v2, :cond_c

    .line 311
    .line 312
    check-cast v2, Landroid/widget/FrameLayout$LayoutParams;

    .line 313
    .line 314
    invoke-virtual {v2, v6}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 315
    .line 316
    .line 317
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 318
    .line 319
    .line 320
    iget v1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->f:I

    .line 321
    .line 322
    sub-int v1, p1, v1

    .line 323
    .line 324
    iget v2, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->g:I

    .line 325
    .line 326
    sub-int/2addr v1, v2

    .line 327
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getSeparator()Lorg/xbet/uikit/components/separator/Separator;

    .line 328
    .line 329
    .line 330
    move-result-object v2

    .line 331
    invoke-static {v1, v4}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 332
    .line 333
    .line 334
    move-result v1

    .line 335
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 336
    .line 337
    .line 338
    move-result-object v5

    .line 339
    invoke-static {p2, v5}, Lorg/xbet/uikit/utils/S;->d(ILandroid/content/Context;)I

    .line 340
    .line 341
    .line 342
    move-result p2

    .line 343
    invoke-static {p2, v4}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 344
    .line 345
    .line 346
    move-result p2

    .line 347
    invoke-virtual {v2, v1, p2}, Landroid/view/View;->measure(II)V

    .line 348
    .line 349
    .line 350
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->getSeparator()Lorg/xbet/uikit/components/separator/Separator;

    .line 351
    .line 352
    .line 353
    move-result-object p2

    .line 354
    invoke-virtual {p2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 355
    .line 356
    .line 357
    move-result-object v1

    .line 358
    if-eqz v1, :cond_b

    .line 359
    .line 360
    check-cast v1, Landroid/widget/FrameLayout$LayoutParams;

    .line 361
    .line 362
    const v2, 0x800055

    .line 363
    .line 364
    .line 365
    iput v2, v1, Landroid/widget/FrameLayout$LayoutParams;->gravity:I

    .line 366
    .line 367
    iget v2, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->g:I

    .line 368
    .line 369
    invoke-virtual {v1, v2}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 370
    .line 371
    .line 372
    invoke-virtual {p2, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 373
    .line 374
    .line 375
    invoke-virtual {p0, p1, v0}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 376
    .line 377
    .line 378
    return-void

    .line 379
    :cond_b
    new-instance p1, Ljava/lang/NullPointerException;

    .line 380
    .line 381
    invoke-direct {p1, v3}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 382
    .line 383
    .line 384
    throw p1

    .line 385
    :cond_c
    new-instance p1, Ljava/lang/NullPointerException;

    .line 386
    .line 387
    invoke-direct {p1, v3}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 388
    .line 389
    .line 390
    throw p1
.end method

.method public final setRoundedBottom(Z)V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->b:Z

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->i()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final setRoundedTop(Z)V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->a:Z

    .line 2
    .line 3
    if-ne v0, p1, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iput-boolean p1, p0, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->a:Z

    .line 7
    .line 8
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_cell/DsSportCell;->i()V

    .line 9
    .line 10
    .line 11
    return-void
.end method
