.class public final synthetic LNZ0/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/chips/DsChipGroup;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/chips/DsChipGroup;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/k;->a:Lorg/xbet/uikit/components/chips/DsChipGroup;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LNZ0/k;->a:Lorg/xbet/uikit/components/chips/DsChipGroup;

    check-cast p1, Lorg/xbet/uikit/components/chips/DsChip;

    check-cast p2, <PERSON>ja<PERSON>/lang/<PERSON>;

    invoke-virtual {p2}, <PERSON>ja<PERSON>/lang/<PERSON>;->booleanValue()Z

    move-result p2

    invoke-static {v0, p1, p2}, Lorg/xbet/uikit/components/chips/DsChipGroup;->d(Lorg/xbet/uikit/components/chips/DsChipGroup;Lorg/xbet/uikit/components/chips/DsChip;Z)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
