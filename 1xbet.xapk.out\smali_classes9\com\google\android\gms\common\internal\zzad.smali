.class public interface abstract Lcom/google/android/gms/common/internal/zzad;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/os/IInterface;


# virtual methods
.method public abstract V(Lcom/google/android/gms/common/zzv;Lcom/google/android/gms/dynamic/IObjectWrapper;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method

.method public abstract c0(Lcom/google/android/gms/common/zzr;)Lcom/google/android/gms/common/zzt;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method

.method public abstract i(Lcom/google/android/gms/common/zzr;)Lcom/google/android/gms/common/zzt;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method

.method public abstract zzg()Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method
