.class public final synthetic Ln41/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Ln41/c;

.field public final synthetic b:Ln41/m;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Ln41/c;Ln41/m;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln41/a;->a:Ln41/c;

    iput-object p2, p0, Ln41/a;->b:Ln41/m;

    iput p3, p0, Ln41/a;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ln41/a;->a:Ln41/c;

    iget-object v1, p0, Ln41/a;->b:Ln41/m;

    iget v2, p0, Ln41/a;->c:I

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, v2, p1}, Ln41/c;->t(Ln41/c;Ln41/m;ILandroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
