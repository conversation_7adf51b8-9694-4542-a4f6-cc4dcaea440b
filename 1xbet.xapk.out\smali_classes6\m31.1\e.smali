.class public final Lm31/e;
.super Ljava/lang/Object;


# static fields
.field public static banner_championship_layout:I = 0x7f0d008b

.field public static bet_constructor_header_view:I = 0x7f0d0093

.field public static bottom_market_line_view:I = 0x7f0d00ae

.field public static championship_card_collection_item:I = 0x7f0d00d5

.field public static coupon_card_view:I = 0x7f0d00f8

.field public static ds_bottom_sheet_preset_sport_cell_long:I = 0x7f0d02ac

.field public static ds_bottom_sheet_preset_sport_cell_short:I = 0x7f0d02ad

.field public static event_card_bottom_info_cell_view:I = 0x7f0d02c8

.field public static event_card_bottom_info_view:I = 0x7f0d02c9

.field public static event_card_bottom_market_blocked_view:I = 0x7f0d02ca

.field public static event_card_bottom_market_expandable_view:I = 0x7f0d02cb

.field public static event_card_bottom_market_group_view:I = 0x7f0d02cc

.field public static event_card_bottom_market_line_view:I = 0x7f0d02cd

.field public static event_card_championship_header_view:I = 0x7f0d02ce

.field public static event_card_compact_live_info_view:I = 0x7f0d02cf

.field public static event_card_empty_middle_two_teams_view:I = 0x7f0d02d0

.field public static event_card_header_view:I = 0x7f0d02d1

.field public static event_card_history_view:I = 0x7f0d02d2

.field public static event_card_indicator:I = 0x7f0d02d3

.field public static event_card_info_championship_view:I = 0x7f0d02d4

.field public static event_card_info_favourites_view:I = 0x7f0d02d5

.field public static event_card_line_view:I = 0x7f0d02d6

.field public static event_card_live_view:I = 0x7f0d02d7

.field public static event_card_middle_baccarat_view:I = 0x7f0d02d8

.field public static event_card_middle_championship_view:I = 0x7f0d02d9

.field public static event_card_middle_cricket_view:I = 0x7f0d02da

.field public static event_card_middle_cyber_poker_view:I = 0x7f0d02db

.field public static event_card_middle_cyber_view:I = 0x7f0d02dc

.field public static event_card_middle_dice_view:I = 0x7f0d02dd

.field public static event_card_middle_fighting_view:I = 0x7f0d02de

.field public static event_card_middle_multi_teams_view:I = 0x7f0d02df

.field public static event_card_middle_score_view:I = 0x7f0d02e0

.field public static event_card_middle_sette_view:I = 0x7f0d02e1

.field public static event_card_middle_single_team_view:I = 0x7f0d02e2

.field public static event_card_middle_two_teams_view:I = 0x7f0d02e3

.field public static event_card_middle_winning_formula_view:I = 0x7f0d02e4

.field public static event_card_promotion_view:I = 0x7f0d02e5

.field public static event_card_seeding_view:I = 0x7f0d02e6

.field public static item_sport_feeds_cell_championship_large:I = 0x7f0d06c4

.field public static item_sport_feeds_cell_championship_medium:I = 0x7f0d06ca

.field public static item_sport_feeds_cell_championship_medium_clear:I = 0x7f0d06cb

.field public static item_sport_feeds_cell_championship_small:I = 0x7f0d06da

.field public static item_sport_feeds_cell_sport_large:I = 0x7f0d06e0

.field public static item_sport_feeds_cell_sport_medium:I = 0x7f0d06e1

.field public static item_sport_feeds_cell_sport_medium_clear:I = 0x7f0d06e2

.field public static item_sport_feeds_cell_sport_small:I = 0x7f0d06e7

.field public static score_item:I = 0x7f0d0894

.field public static sport_collection_item_layout:I = 0x7f0d0961

.field public static sport_collection_item_shimmer:I = 0x7f0d0962

.field public static sport_collection_item_view:I = 0x7f0d0963

.field public static statistics_indicator_layout:I = 0x7f0d0985


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
