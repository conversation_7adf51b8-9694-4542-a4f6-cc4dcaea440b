.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;

.field public final synthetic b:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;->b:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/c;->b:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;->A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$a;Landroid/view/View;)V

    return-void
.end method
