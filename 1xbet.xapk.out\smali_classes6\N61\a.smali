.class public final synthetic LN61/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnKeyListener;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView$SafeWebView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView$SafeWebView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN61/a;->a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView$SafeWebView;

    return-void
.end method


# virtual methods
.method public final onKey(Landroid/view/View;ILandroid/view/KeyEvent;)Z
    .locals 1

    .line 1
    iget-object v0, p0, LN61/a;->a:Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView$SafeWebView;

    invoke-static {v0, p1, p2, p3}, Lorg/xbet/web/presentation/views/BaseWebView;->y(Lorg/xbet/ui_common/viewcomponents/webview/FixedWebView$SafeWebView;Landroid/view/View;ILandroid/view/KeyEvent;)Z

    move-result p1

    return p1
.end method
