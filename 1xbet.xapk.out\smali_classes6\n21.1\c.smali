.class public final Ln21/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ln21/b;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0006\u0008\u0087\u0008\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0010\u0010\n\u001a\u00020\tH\u00d6\u0001\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u001a\u0010\u000f\u001a\u00020\u000e2\u0008\u0010\r\u001a\u0004\u0018\u00010\u000cH\u00d6\u0003\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u001a\u0010\u0003\u001a\u00020\u00028\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008\u0011\u0010\u0012\u001a\u0004\u0008\u0011\u0010\u0013\u00a8\u0006\u0014"
    }
    d2 = {
        "Ln21/c;",
        "Ln21/b;",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "styleType",
        "<init>",
        "(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "",
        "other",
        "",
        "equals",
        "(Ljava/lang/Object;)Z",
        "a",
        "Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "()Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    .line 1
    return-void
.end method

.method public constructor <init>(Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Ln21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public a()Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Ln21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 2
    .line 3
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    if-ne p0, p1, :cond_0

    .line 3
    .line 4
    return v0

    .line 5
    :cond_0
    instance-of v1, p1, Ln21/c;

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    if-nez v1, :cond_1

    .line 9
    .line 10
    return v2

    .line 11
    :cond_1
    check-cast p1, Ln21/c;

    .line 12
    .line 13
    iget-object v1, p0, Ln21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 14
    .line 15
    iget-object p1, p1, Ln21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 16
    .line 17
    if-eq v1, p1, :cond_2

    .line 18
    .line 19
    return v2

    .line 20
    :cond_2
    return v0
.end method

.method public hashCode()I
    .locals 1

    .line 1
    iget-object v0, p0, Ln21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Ln21/c;->a:Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 2
    .line 3
    new-instance v1, Ljava/lang/StringBuilder;

    .line 4
    .line 5
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 6
    .line 7
    .line 8
    const-string v2, "AggregatorTournamentPrizePoolShimmerDsModel(styleType="

    .line 9
    .line 10
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 11
    .line 12
    .line 13
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 14
    .line 15
    .line 16
    const-string v0, ")"

    .line 17
    .line 18
    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 19
    .line 20
    .line 21
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    return-object v0
.end method
