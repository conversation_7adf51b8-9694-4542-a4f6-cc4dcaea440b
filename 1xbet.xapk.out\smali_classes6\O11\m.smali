.class public final LO11/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LRc/c;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "LL2/a;",
        ">",
        "Ljava/lang/Object;",
        "LRc/c<",
        "Landroidx/fragment/app/Fragment;",
        "TT;>;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0002\u0018\u0000*\u0008\u0008\u0000\u0010\u0002*\u00020\u00012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00028\u00000\u0003B#\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00028\u00000\u0007\u00a2\u0006\u0004\u0008\n\u0010\u000bJ\u0017\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u000cH\u0003\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J$\u0010\u0014\u001a\u00028\u00002\u0006\u0010\u0011\u001a\u00020\u00042\n\u0010\u0013\u001a\u0006\u0012\u0002\u0008\u00030\u0012H\u0097\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015R\u0017\u0010\u0006\u001a\u00020\u00058\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0016\u0010\u0017\u001a\u0004\u0008\u0018\u0010\u0019R#\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00028\u00000\u00078\u0006\u00a2\u0006\u000c\n\u0004\u0008\u001a\u0010\u001b\u001a\u0004\u0008\u001c\u0010\u001dR\u0018\u0010 \u001a\u0004\u0018\u00018\u00008\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010\u001fR\u0014\u0010#\u001a\u00020!8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000f\u0010\"\u00a8\u0006$"
    }
    d2 = {
        "LO11/m;",
        "LL2/a;",
        "T",
        "LRc/c;",
        "Landroidx/fragment/app/Fragment;",
        "Landroidx/fragment/app/l;",
        "dialog",
        "Lkotlin/Function1;",
        "Landroid/view/LayoutInflater;",
        "viewBindingFactory",
        "<init>",
        "(Landroidx/fragment/app/l;Lkotlin/jvm/functions/Function1;)V",
        "Landroidx/lifecycle/Lifecycle;",
        "lifecycle",
        "",
        "d",
        "(Landroidx/lifecycle/Lifecycle;)V",
        "thisRef",
        "Lkotlin/reflect/m;",
        "property",
        "g",
        "(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)LL2/a;",
        "a",
        "Landroidx/fragment/app/l;",
        "getDialog",
        "()Landroidx/fragment/app/l;",
        "b",
        "Lkotlin/jvm/functions/Function1;",
        "getViewBindingFactory",
        "()Lkotlin/jvm/functions/Function1;",
        "c",
        "LL2/a;",
        "binding",
        "Landroid/os/Handler;",
        "Landroid/os/Handler;",
        "mainHandler",
        "uikit_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroidx/fragment/app/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "Landroid/view/LayoutInflater;",
            "TT;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public c:LL2/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "TT;"
        }
    .end annotation
.end field

.field public final d:Landroid/os/Handler;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/fragment/app/l;Lkotlin/jvm/functions/Function1;)V
    .locals 0
    .param p1    # Landroidx/fragment/app/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/fragment/app/l;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/LayoutInflater;",
            "+TT;>;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LO11/m;->a:Landroidx/fragment/app/l;

    .line 5
    .line 6
    iput-object p2, p0, LO11/m;->b:Lkotlin/jvm/functions/Function1;

    .line 7
    .line 8
    new-instance p1, Landroid/os/Handler;

    .line 9
    .line 10
    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    .line 11
    .line 12
    .line 13
    move-result-object p2

    .line 14
    invoke-direct {p1, p2}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    .line 15
    .line 16
    .line 17
    iput-object p1, p0, LO11/m;->d:Landroid/os/Handler;

    .line 18
    .line 19
    return-void
.end method

.method public static synthetic b(Landroidx/lifecycle/Lifecycle;LO11/m;Landroidx/lifecycle/w;Landroidx/lifecycle/v;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LO11/m;->e(Landroidx/lifecycle/Lifecycle;LO11/m;Landroidx/lifecycle/w;Landroidx/lifecycle/v;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LO11/m;)V
    .locals 0

    .line 1
    invoke-static {p0}, LO11/m;->f(LO11/m;)V

    return-void
.end method

.method private final d(Landroidx/lifecycle/Lifecycle;)V
    .locals 9

    .line 1
    new-instance v0, LO11/j;

    .line 2
    .line 3
    new-instance v6, LO11/k;

    .line 4
    .line 5
    invoke-direct {v6, p1, p0}, LO11/k;-><init>(Landroidx/lifecycle/Lifecycle;LO11/m;)V

    .line 6
    .line 7
    .line 8
    const/16 v7, 0x1f

    .line 9
    .line 10
    const/4 v8, 0x0

    .line 11
    const/4 v1, 0x0

    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v3, 0x0

    .line 14
    const/4 v4, 0x0

    .line 15
    const/4 v5, 0x0

    .line 16
    invoke-direct/range {v0 .. v8}, LO11/j;-><init>(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 17
    .line 18
    .line 19
    invoke-virtual {p1, v0}, Landroidx/lifecycle/Lifecycle;->a(Landroidx/lifecycle/v;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public static final e(Landroidx/lifecycle/Lifecycle;LO11/m;Landroidx/lifecycle/w;Landroidx/lifecycle/v;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0, p3}, Landroidx/lifecycle/Lifecycle;->d(Landroidx/lifecycle/v;)V

    .line 2
    .line 3
    .line 4
    iget-object p0, p1, LO11/m;->d:Landroid/os/Handler;

    .line 5
    .line 6
    new-instance p2, LO11/l;

    .line 7
    .line 8
    invoke-direct {p2, p1}, LO11/l;-><init>(LO11/m;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final f(LO11/m;)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, LO11/m;->c:LL2/a;

    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public g(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)LL2/a;
    .locals 0
    .param p1    # Landroidx/fragment/app/Fragment;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/reflect/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/fragment/app/Fragment;",
            "Lkotlin/reflect/m<",
            "*>;)TT;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object p1, p0, LO11/m;->c:LL2/a;

    .line 2
    .line 3
    if-eqz p1, :cond_0

    .line 4
    .line 5
    return-object p1

    .line 6
    :cond_0
    iget-object p1, p0, LO11/m;->b:Lkotlin/jvm/functions/Function1;

    .line 7
    .line 8
    iget-object p2, p0, LO11/m;->a:Landroidx/fragment/app/l;

    .line 9
    .line 10
    invoke-virtual {p2}, Landroidx/fragment/app/Fragment;->requireContext()Landroid/content/Context;

    .line 11
    .line 12
    .line 13
    move-result-object p2

    .line 14
    invoke-static {p2}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 15
    .line 16
    .line 17
    move-result-object p2

    .line 18
    invoke-interface {p1, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    check-cast p1, LL2/a;

    .line 23
    .line 24
    iput-object p1, p0, LO11/m;->c:LL2/a;

    .line 25
    .line 26
    iget-object p2, p0, LO11/m;->a:Landroidx/fragment/app/l;

    .line 27
    .line 28
    invoke-virtual {p2}, Landroidx/fragment/app/Fragment;->getLifecycle()Landroidx/lifecycle/Lifecycle;

    .line 29
    .line 30
    .line 31
    move-result-object p2

    .line 32
    invoke-direct {p0, p2}, LO11/m;->d(Landroidx/lifecycle/Lifecycle;)V

    .line 33
    .line 34
    .line 35
    return-object p1
.end method

.method public bridge synthetic getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    invoke-virtual {p0, p1, p2}, LO11/m;->g(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)LL2/a;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
