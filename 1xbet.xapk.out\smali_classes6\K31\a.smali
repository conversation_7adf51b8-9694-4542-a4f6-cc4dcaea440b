.class public final synthetic LK31/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Landroid/view/View$OnClickListener;

.field public final synthetic b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;


# direct methods
.method public synthetic constructor <init>(Landroid/view/View$OnClickListener;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LK31/a;->a:Landroid/view/View$OnClickListener;

    iput-object p2, p0, LK31/a;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LK31/a;->a:Landroid/view/View$OnClickListener;

    iget-object v1, p0, LK31/a;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->g(Landroid/view/View$OnClickListener;Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;Landroid/view/View;)V

    return-void
.end method
