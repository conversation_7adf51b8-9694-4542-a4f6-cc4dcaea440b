.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0005\n\u0002\u0010\r\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0082\u0008\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0015\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ+\u0010\u0013\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\r2\u0014\u0010\u0012\u001a\u0010\u0012\u0004\u0012\u00020\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u000f\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0010\u0010\u0016\u001a\u00020\u0015H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0010\u0010\u0019\u001a\u00020\u0018H\u00d6\u0001\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u001a\u0010\u001c\u001a\u00020\u00082\u0008\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ!\u0010 \u001a\u00020\n2\u0006\u0010\u0003\u001a\u00020\u00022\u0008\u0010\u001f\u001a\u0004\u0018\u00010\u001eH\u0002\u00a2\u0006\u0004\u0008 \u0010!J5\u0010%\u001a\u00020\n2\u0006\u0010\"\u001a\u00020\u00022\u0006\u0010$\u001a\u00020#2\u0014\u0010\u0012\u001a\u0010\u0012\u0004\u0012\u00020\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u00110\u000fH\u0002\u00a2\u0006\u0004\u0008%\u0010&R\u0017\u0010\u0003\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008%\u0010\'\u001a\u0004\u0008(\u0010)R\u0017\u0010\u0004\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0013\u0010\'\u001a\u0004\u0008*\u0010)R\u0017\u0010\u0005\u001a\u00020\u00028\u0006\u00a2\u0006\u000c\n\u0004\u0008 \u0010\'\u001a\u0004\u0008+\u0010)\u00a8\u0006,"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;",
        "",
        "Landroid/widget/TextView;",
        "title",
        "topCell",
        "botCell",
        "<init>",
        "(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V",
        "",
        "visible",
        "",
        "d",
        "(Z)V",
        "LI11/b;",
        "fightResult",
        "Lkotlin/Function1;",
        "Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;",
        "Landroid/graphics/drawable/Drawable;",
        "getTintedDrawable",
        "b",
        "(LI11/b;Lkotlin/jvm/functions/Function1;)V",
        "",
        "toString",
        "()Ljava/lang/String;",
        "",
        "hashCode",
        "()I",
        "other",
        "equals",
        "(Ljava/lang/Object;)Z",
        "",
        "text",
        "c",
        "(Landroid/widget/TextView;Ljava/lang/CharSequence;)V",
        "cell",
        "LI11/a;",
        "content",
        "a",
        "(Landroid/widget/TextView;LI11/a;Lkotlin/jvm/functions/Function1;)V",
        "Landroid/widget/TextView;",
        "getTitle",
        "()Landroid/widget/TextView;",
        "getTopCell",
        "getBotCell",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Landroid/widget/TextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/widget/TextView;Landroid/widget/TextView;Landroid/widget/TextView;)V
    .locals 0
    .param p1    # Landroid/widget/TextView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Landroid/widget/TextView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Landroid/widget/TextView;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final a(Landroid/widget/TextView;LI11/a;Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/widget/TextView;",
            "LI11/a;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;",
            "+",
            "Landroid/graphics/drawable/Drawable;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-virtual {p2}, LI11/a;->a()Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    if-eqz v0, :cond_1

    .line 6
    .line 7
    invoke-static {}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->x()Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p2}, LI11/a;->a()Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;

    .line 12
    .line 13
    .line 14
    move-result-object p2

    .line 15
    if-eqz p2, :cond_0

    .line 16
    .line 17
    invoke-interface {p3, p2}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    check-cast p2, Landroid/graphics/drawable/Drawable;

    .line 22
    .line 23
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->b(Landroid/widget/TextView;Landroid/graphics/drawable/Drawable;)V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 28
    .line 29
    const-string p2, "Required value was null."

    .line 30
    .line 31
    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_1
    invoke-static {}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->x()Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 36
    .line 37
    .line 38
    move-result-object p3

    .line 39
    invoke-virtual {p2}, LI11/a;->c()Ljava/lang/CharSequence;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-virtual {p2}, LI11/a;->b()Lorg/xbet/uikit/core/eventcard/ScoreState;

    .line 44
    .line 45
    .line 46
    move-result-object p2

    .line 47
    invoke-virtual {p3, p1, v0, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->c(Landroid/widget/TextView;Ljava/lang/CharSequence;Lorg/xbet/uikit/core/eventcard/ScoreState;)V

    .line 48
    .line 49
    .line 50
    return-void
.end method

.method public final b(LI11/b;Lkotlin/jvm/functions/Function1;)V
    .locals 2
    .param p1    # LI11/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lkotlin/jvm/functions/Function1;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LI11/b;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Lorg/xbet/uikit/core/eventcard/middle/FightCellDrawable;",
            "+",
            "Landroid/graphics/drawable/Drawable;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    .line 2
    .line 3
    invoke-virtual {p1}, LI11/b;->c()Ljava/lang/CharSequence;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0, v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c(Landroid/widget/TextView;Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    .line 11
    .line 12
    invoke-virtual {p1}, LI11/b;->a()LI11/a;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {p0, v0, v1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a(Landroid/widget/TextView;LI11/a;Lkotlin/jvm/functions/Function1;)V

    .line 17
    .line 18
    .line 19
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    .line 20
    .line 21
    invoke-virtual {p1}, LI11/b;->b()LI11/a;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-virtual {p0, v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a(Landroid/widget/TextView;LI11/a;Lkotlin/jvm/functions/Function1;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public final c(Landroid/widget/TextView;Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-static {}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting;->x()Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$a;->d(Landroid/widget/TextView;Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final d(Z)V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    .line 2
    .line 3
    const/16 v1, 0x8

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    if-eqz p1, :cond_0

    .line 7
    .line 8
    const/4 v3, 0x0

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/16 v3, 0x8

    .line 11
    .line 12
    :goto_0
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    .line 16
    .line 17
    if-eqz p1, :cond_1

    .line 18
    .line 19
    const/4 v3, 0x0

    .line 20
    goto :goto_1

    .line 21
    :cond_1
    const/16 v3, 0x8

    .line 22
    .line 23
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 24
    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    .line 27
    .line 28
    if-eqz p1, :cond_2

    .line 29
    .line 30
    const/4 v1, 0x0

    .line 31
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_2

    return v2

    :cond_2
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    iget-object v3, p1, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    invoke-static {v1, v3}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_3

    return v2

    :cond_3
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    iget-object p1, p1, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_4

    return v2

    :cond_4
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    invoke-virtual {v1}, Ljava/lang/Object;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 5
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->a:Landroid/widget/TextView;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->b:Landroid/widget/TextView;

    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleFighting$b;->c:Landroid/widget/TextView;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "FightResultView(title="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", topCell="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ", botCell="

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v0, ")"

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
