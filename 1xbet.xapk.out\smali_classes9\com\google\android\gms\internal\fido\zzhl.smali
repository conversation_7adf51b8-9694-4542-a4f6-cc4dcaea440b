.class public final Lcom/google/android/gms/internal/fido/zzhl;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field private final zza:Lcom/google/android/gms/internal/fido/zzhp;

.field private final zzb:Lcom/google/android/gms/internal/fido/zzhp;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/fido/zzhp;Lcom/google/android/gms/internal/fido/zzhp;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/google/android/gms/internal/fido/zzhl;->zza:Lcom/google/android/gms/internal/fido/zzhp;

    iput-object p2, p0, Lcom/google/android/gms/internal/fido/zzhl;->zzb:Lcom/google/android/gms/internal/fido/zzhp;

    return-void
.end method


# virtual methods
.method public final zza()Lcom/google/android/gms/internal/fido/zzhp;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/fido/zzhl;->zza:Lcom/google/android/gms/internal/fido/zzhp;

    return-object v0
.end method

.method public final zzb()Lcom/google/android/gms/internal/fido/zzhp;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/fido/zzhl;->zzb:Lcom/google/android/gms/internal/fido/zzhp;

    return-object v0
.end method
