.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;",
        ">;"
    }
.end annotation


# instance fields
.field public final A:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Luf0/a;",
            ">;"
        }
    .end annotation
.end field

.field public final B:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lp9/c;",
            ">;"
        }
    .end annotation
.end field

.field public final C:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LXa0/i;",
            ">;"
        }
    .end annotation
.end field

.field public final D:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/j;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/g;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Li8/l;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/k;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LDg/a;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lfk/m;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lv81/p;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lek/f;",
            ">;"
        }
    .end annotation
.end field

.field public final w:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final x:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;"
        }
    .end annotation
.end field

.field public final y:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/d;",
            ">;"
        }
    .end annotation
.end field

.field public final z:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ldu/e;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
            ">;",
            "LBc/a<",
            "Lv81/g;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;",
            ">;",
            "LBc/a<",
            "Li8/l;",
            ">;",
            "LBc/a<",
            "Lfk/k;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LDg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "Lfk/m;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
            ">;",
            "LBc/a<",
            "LSR/a;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lv81/p;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lek/f;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;",
            "LBc/a<",
            "LnR/d;",
            ">;",
            "LBc/a<",
            "Ldu/e;",
            ">;",
            "LBc/a<",
            "Luf0/a;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "LXa0/i;",
            ">;",
            "LBc/a<",
            "Lv81/j;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->v:LBc/a;

    .line 61
    .line 62
    move-object/from16 p1, p23

    .line 63
    .line 64
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->w:LBc/a;

    .line 65
    .line 66
    move-object/from16 p1, p24

    .line 67
    .line 68
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->x:LBc/a;

    .line 69
    .line 70
    move-object/from16 p1, p25

    .line 71
    .line 72
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->y:LBc/a;

    .line 73
    .line 74
    move-object/from16 p1, p26

    .line 75
    .line 76
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->z:LBc/a;

    .line 77
    .line 78
    move-object/from16 p1, p27

    .line 79
    .line 80
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->A:LBc/a;

    .line 81
    .line 82
    move-object/from16 p1, p28

    .line 83
    .line 84
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->B:LBc/a;

    .line 85
    .line 86
    move-object/from16 p1, p29

    .line 87
    .line 88
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->C:LBc/a;

    .line 89
    .line 90
    move-object/from16 p1, p30

    .line 91
    .line 92
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->D:LBc/a;

    .line 93
    .line 94
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;
    .locals 31
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Ljava/lang/Boolean;",
            ">;",
            "LBc/a<",
            "LwX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
            ">;",
            "LBc/a<",
            "Lv81/g;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;",
            ">;",
            "LBc/a<",
            "Li8/l;",
            ">;",
            "LBc/a<",
            "Lfk/k;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/a;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "LDg/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "Lfk/m;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
            ">;",
            "LBc/a<",
            "LSR/a;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "Lv81/p;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;",
            "LBc/a<",
            "Lek/f;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
            ">;",
            "LBc/a<",
            "LnR/d;",
            ">;",
            "LBc/a<",
            "Ldu/e;",
            ">;",
            "LBc/a<",
            "Luf0/a;",
            ">;",
            "LBc/a<",
            "Lp9/c;",
            ">;",
            "LBc/a<",
            "LXa0/i;",
            ">;",
            "LBc/a<",
            "Lv81/j;",
            ">;)",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    invoke-direct/range {v0 .. v30}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 64
    .line 65
    .line 66
    return-object v0
.end method

.method public static c(ZLwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;Lv81/g;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;Li8/l;Lfk/k;LHX0/e;Lorg/xplatform/aggregator/api/navigation/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;LSR/a;LnR/a;Lv81/p;Lorg/xbet/remoteconfig/domain/usecases/i;Lek/f;Lm8/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LnR/d;Ldu/e;Luf0/a;Lp9/c;LXa0/i;Lv81/j;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;
    .locals 31

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 2
    .line 3
    move/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    move-object/from16 v23, p22

    .line 48
    .line 49
    move-object/from16 v24, p23

    .line 50
    .line 51
    move-object/from16 v25, p24

    .line 52
    .line 53
    move-object/from16 v26, p25

    .line 54
    .line 55
    move-object/from16 v27, p26

    .line 56
    .line 57
    move-object/from16 v28, p27

    .line 58
    .line 59
    move-object/from16 v29, p28

    .line 60
    .line 61
    move-object/from16 v30, p29

    .line 62
    .line 63
    invoke-direct/range {v0 .. v30}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;-><init>(ZLwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;Lv81/g;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;Li8/l;Lfk/k;LHX0/e;Lorg/xplatform/aggregator/api/navigation/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;LSR/a;LnR/a;Lv81/p;Lorg/xbet/remoteconfig/domain/usecases/i;Lek/f;Lm8/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LnR/d;Ldu/e;Luf0/a;Lp9/c;LXa0/i;Lv81/j;)V

    .line 64
    .line 65
    .line 66
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;
    .locals 32

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    check-cast v1, Ljava/lang/Boolean;

    .line 10
    .line 11
    invoke-virtual {v1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->b:LBc/a;

    .line 16
    .line 17
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    move-object v3, v1

    .line 22
    check-cast v3, LwX0/c;

    .line 23
    .line 24
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->c:LBc/a;

    .line 25
    .line 26
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    move-object v4, v1

    .line 31
    check-cast v4, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 32
    .line 33
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->d:LBc/a;

    .line 34
    .line 35
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    move-object v5, v1

    .line 40
    check-cast v5, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;

    .line 41
    .line 42
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->e:LBc/a;

    .line 43
    .line 44
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    move-object v6, v1

    .line 49
    check-cast v6, Lv81/g;

    .line 50
    .line 51
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->f:LBc/a;

    .line 52
    .line 53
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    move-object v7, v1

    .line 58
    check-cast v7, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;

    .line 59
    .line 60
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->g:LBc/a;

    .line 61
    .line 62
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    move-object v8, v1

    .line 67
    check-cast v8, Li8/l;

    .line 68
    .line 69
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->h:LBc/a;

    .line 70
    .line 71
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    move-object v9, v1

    .line 76
    check-cast v9, Lfk/k;

    .line 77
    .line 78
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->i:LBc/a;

    .line 79
    .line 80
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 81
    .line 82
    .line 83
    move-result-object v1

    .line 84
    move-object v10, v1

    .line 85
    check-cast v10, LHX0/e;

    .line 86
    .line 87
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->j:LBc/a;

    .line 88
    .line 89
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v1

    .line 93
    move-object v11, v1

    .line 94
    check-cast v11, Lorg/xplatform/aggregator/api/navigation/a;

    .line 95
    .line 96
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->k:LBc/a;

    .line 97
    .line 98
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 99
    .line 100
    .line 101
    move-result-object v1

    .line 102
    move-object v12, v1

    .line 103
    check-cast v12, LSX0/c;

    .line 104
    .line 105
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->l:LBc/a;

    .line 106
    .line 107
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    move-object v13, v1

    .line 112
    check-cast v13, Lorg/xbet/ui_common/utils/internet/a;

    .line 113
    .line 114
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->m:LBc/a;

    .line 115
    .line 116
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    move-object v14, v1

    .line 121
    check-cast v14, Lorg/xbet/ui_common/utils/M;

    .line 122
    .line 123
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->n:LBc/a;

    .line 124
    .line 125
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    move-object v15, v1

    .line 130
    check-cast v15, LDg/a;

    .line 131
    .line 132
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->o:LBc/a;

    .line 133
    .line 134
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object v1

    .line 138
    move-object/from16 v16, v1

    .line 139
    .line 140
    check-cast v16, Lorg/xbet/analytics/domain/scope/g0;

    .line 141
    .line 142
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->p:LBc/a;

    .line 143
    .line 144
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    move-object/from16 v17, v1

    .line 149
    .line 150
    check-cast v17, Lfk/m;

    .line 151
    .line 152
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->q:LBc/a;

    .line 153
    .line 154
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 155
    .line 156
    .line 157
    move-result-object v1

    .line 158
    move-object/from16 v18, v1

    .line 159
    .line 160
    check-cast v18, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 161
    .line 162
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->r:LBc/a;

    .line 163
    .line 164
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 165
    .line 166
    .line 167
    move-result-object v1

    .line 168
    move-object/from16 v19, v1

    .line 169
    .line 170
    check-cast v19, LSR/a;

    .line 171
    .line 172
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->s:LBc/a;

    .line 173
    .line 174
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 175
    .line 176
    .line 177
    move-result-object v1

    .line 178
    move-object/from16 v20, v1

    .line 179
    .line 180
    check-cast v20, LnR/a;

    .line 181
    .line 182
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->t:LBc/a;

    .line 183
    .line 184
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 185
    .line 186
    .line 187
    move-result-object v1

    .line 188
    move-object/from16 v21, v1

    .line 189
    .line 190
    check-cast v21, Lv81/p;

    .line 191
    .line 192
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->u:LBc/a;

    .line 193
    .line 194
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 195
    .line 196
    .line 197
    move-result-object v1

    .line 198
    move-object/from16 v22, v1

    .line 199
    .line 200
    check-cast v22, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 201
    .line 202
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->v:LBc/a;

    .line 203
    .line 204
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 205
    .line 206
    .line 207
    move-result-object v1

    .line 208
    move-object/from16 v23, v1

    .line 209
    .line 210
    check-cast v23, Lek/f;

    .line 211
    .line 212
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->w:LBc/a;

    .line 213
    .line 214
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 215
    .line 216
    .line 217
    move-result-object v1

    .line 218
    move-object/from16 v24, v1

    .line 219
    .line 220
    check-cast v24, Lm8/a;

    .line 221
    .line 222
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->x:LBc/a;

    .line 223
    .line 224
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 225
    .line 226
    .line 227
    move-result-object v1

    .line 228
    move-object/from16 v25, v1

    .line 229
    .line 230
    check-cast v25, Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    .line 231
    .line 232
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->y:LBc/a;

    .line 233
    .line 234
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 235
    .line 236
    .line 237
    move-result-object v1

    .line 238
    move-object/from16 v26, v1

    .line 239
    .line 240
    check-cast v26, LnR/d;

    .line 241
    .line 242
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->z:LBc/a;

    .line 243
    .line 244
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 245
    .line 246
    .line 247
    move-result-object v1

    .line 248
    move-object/from16 v27, v1

    .line 249
    .line 250
    check-cast v27, Ldu/e;

    .line 251
    .line 252
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->A:LBc/a;

    .line 253
    .line 254
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 255
    .line 256
    .line 257
    move-result-object v1

    .line 258
    move-object/from16 v28, v1

    .line 259
    .line 260
    check-cast v28, Luf0/a;

    .line 261
    .line 262
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->B:LBc/a;

    .line 263
    .line 264
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 265
    .line 266
    .line 267
    move-result-object v1

    .line 268
    move-object/from16 v29, v1

    .line 269
    .line 270
    check-cast v29, Lp9/c;

    .line 271
    .line 272
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->C:LBc/a;

    .line 273
    .line 274
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 275
    .line 276
    .line 277
    move-result-object v1

    .line 278
    move-object/from16 v30, v1

    .line 279
    .line 280
    check-cast v30, LXa0/i;

    .line 281
    .line 282
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->D:LBc/a;

    .line 283
    .line 284
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 285
    .line 286
    .line 287
    move-result-object v1

    .line 288
    move-object/from16 v31, v1

    .line 289
    .line 290
    check-cast v31, Lv81/j;

    .line 291
    .line 292
    invoke-static/range {v2 .. v31}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->c(ZLwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;Lv81/g;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;Li8/l;Lfk/k;LHX0/e;Lorg/xplatform/aggregator/api/navigation/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;LSR/a;LnR/a;Lv81/p;Lorg/xbet/remoteconfig/domain/usecases/i;Lek/f;Lm8/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LnR/d;Ldu/e;Luf0/a;Lp9/c;LXa0/i;Lv81/j;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 293
    .line 294
    .line 295
    move-result-object v1

    .line 296
    return-object v1
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/v;->b()Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
