.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$a;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$b;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c0\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u001d\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0000\u0018\u0000 n2\u00020\u0001:\u0003opqBq\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ/\u0010\"\u001a\u00020!2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010 \u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\'\u0010&\u001a\u00020!2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010%\u001a\u00020$2\u0006\u0010 \u001a\u00020\u0014H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010)\u001a\u00020(2\u0006\u0010%\u001a\u00020$H\u0002\u00a2\u0006\u0004\u0008)\u0010*J\u0013\u0010-\u001a\u0008\u0012\u0004\u0012\u00020,0+\u00a2\u0006\u0004\u0008-\u0010.J\u0013\u00101\u001a\u0008\u0012\u0004\u0012\u0002000/\u00a2\u0006\u0004\u00081\u00102J%\u00105\u001a\u00020!2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u00103\u001a\u00020\u00122\u0006\u00104\u001a\u00020(\u00a2\u0006\u0004\u00085\u00106J\r\u00107\u001a\u00020!\u00a2\u0006\u0004\u00087\u00108J%\u0010<\u001a\u00020!2\u0006\u0010:\u001a\u0002092\u0006\u0010;\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\u0014\u00a2\u0006\u0004\u0008<\u0010=J\u001d\u0010@\u001a\u00020!2\u0006\u0010?\u001a\u00020>2\u0006\u0010 \u001a\u00020\u0014\u00a2\u0006\u0004\u0008@\u0010AR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u001c\u0010_\u001a\u0008\u0012\u0004\u0012\u00020!0\\8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0018\u0010c\u001a\u0004\u0018\u00010`8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008a\u0010bR\u001a\u0010g\u001a\u0008\u0012\u0004\u0012\u00020,0d8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008e\u0010fR\u001a\u0010k\u001a\u0008\u0012\u0004\u0012\u0002000h8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008i\u0010jR\u0016\u0010m\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008l\u0010S\u00a8\u0006r"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lw81/c;",
        "getTournamentFullInfoScenario",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lgk/b;",
        "getCurrencyByIdUseCase",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "Lm8/a;",
        "coroutineDispatchers",
        "LP91/b;",
        "aggregatorNavigator",
        "Lw81/g;",
        "takePartTournamentsScenario",
        "LwX0/C;",
        "routerHolder",
        "",
        "tournamentId",
        "",
        "tournamentTitle",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xbet/analytics/domain/scope/g;",
        "aggregatorTournamentsAnalytics",
        "LnR/d;",
        "aggregatorTournamentFatmanLogger",
        "<init>",
        "(Lw81/c;LSX0/c;Lgk/b;Lorg/xbet/ui_common/utils/M;Lm8/a;LP91/b;Lw81/g;LwX0/C;JLjava/lang/String;LHX0/e;Lorg/xbet/analytics/domain/scope/g;LnR/d;)V",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "kind",
        "screenName",
        "",
        "K3",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V",
        "Lh81/b;",
        "result",
        "H3",
        "(JLh81/b;Ljava/lang/String;)V",
        "",
        "M3",
        "(Lh81/b;)Z",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;",
        "G3",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$b;",
        "F3",
        "()Lkotlinx/coroutines/flow/e;",
        "stageTournamentID",
        "fromCache",
        "L3",
        "(JJZ)V",
        "onBackPressed",
        "()V",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "buttonAction",
        "tournamentKind",
        "I3",
        "(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;)V",
        "Lkb1/z$c;",
        "model",
        "J3",
        "(Lkb1/z$c;Ljava/lang/String;)V",
        "v1",
        "Lw81/c;",
        "x1",
        "LSX0/c;",
        "y1",
        "Lgk/b;",
        "F1",
        "Lorg/xbet/ui_common/utils/M;",
        "H1",
        "Lm8/a;",
        "I1",
        "LP91/b;",
        "P1",
        "Lw81/g;",
        "S1",
        "LwX0/C;",
        "V1",
        "J",
        "b2",
        "Ljava/lang/String;",
        "v2",
        "LHX0/e;",
        "x2",
        "Lorg/xbet/analytics/domain/scope/g;",
        "y2",
        "LnR/d;",
        "Lkotlin/Function0;",
        "F2",
        "Lkotlin/jvm/functions/Function0;",
        "backAction",
        "Lkotlinx/coroutines/x0;",
        "H2",
        "Lkotlinx/coroutines/x0;",
        "tournamentFullInfoJob",
        "Lkotlinx/coroutines/flow/V;",
        "I2",
        "Lkotlinx/coroutines/flow/V;",
        "mutableState",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "P2",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "eventFlow",
        "S2",
        "currentStageTournamentId",
        "V2",
        "c",
        "b",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final V2:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F2:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H2:Lkotlinx/coroutines/x0;

.field public final I1:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Lw81/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public S2:J

.field public final V1:J

.field public final b2:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lw81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/analytics/domain/scope/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lgk/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LnR/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->V2:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$a;

    return-void
.end method

.method public constructor <init>(Lw81/c;LSX0/c;Lgk/b;Lorg/xbet/ui_common/utils/M;Lm8/a;LP91/b;Lw81/g;LwX0/C;JLjava/lang/String;LHX0/e;Lorg/xbet/analytics/domain/scope/g;LnR/d;)V
    .locals 0
    .param p1    # Lw81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lgk/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Lw81/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/analytics/domain/scope/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LnR/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->v1:Lw81/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->x1:LSX0/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->y1:Lgk/b;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->F1:Lorg/xbet/ui_common/utils/M;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->H1:Lm8/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I1:LP91/b;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->P1:Lw81/g;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->S1:LwX0/C;

    .line 19
    .line 20
    iput-wide p9, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->V1:J

    .line 21
    .line 22
    iput-object p11, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->b2:Ljava/lang/String;

    .line 23
    .line 24
    iput-object p12, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->v2:LHX0/e;

    .line 25
    .line 26
    iput-object p13, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 27
    .line 28
    iput-object p14, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->y2:LnR/d;

    .line 29
    .line 30
    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/l;

    .line 31
    .line 32
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/l;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)V

    .line 33
    .line 34
    .line 35
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->F2:Lkotlin/jvm/functions/Function0;

    .line 36
    .line 37
    sget-object p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$c;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$c;

    .line 38
    .line 39
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 44
    .line 45
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 46
    .line 47
    const/4 p2, 0x1

    .line 48
    sget-object p3, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 49
    .line 50
    invoke-direct {p1, p2, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 51
    .line 52
    .line 53
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->P2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 54
    .line 55
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->b2:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;JLh81/b;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->H3(JLh81/b;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->K3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;Lh81/b;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->M3(Lh81/b;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I1:LP91/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LP91/b;->a()V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method

.method private final H3(JLh81/b;Ljava/lang/String;)V
    .locals 7

    .line 1
    instance-of v3, p3, Lh81/b$a;

    .line 2
    .line 3
    instance-of v0, p3, Lh81/b$b;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    check-cast p3, Lh81/b$b;

    .line 9
    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object p3, v1

    .line 12
    :goto_0
    if-eqz p3, :cond_1

    .line 13
    .line 14
    invoke-virtual {p3}, Lh81/b$b;->a()I

    .line 15
    .line 16
    .line 17
    move-result p3

    .line 18
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :cond_1
    move-object v4, v1

    .line 23
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 24
    .line 25
    const-string v5, "prizes_tournament"

    .line 26
    .line 27
    move-wide v1, p1

    .line 28
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/analytics/domain/scope/g;->c(JZLjava/lang/Integer;Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->y2:LnR/d;

    .line 32
    .line 33
    const-string v5, "prizes_tournament"

    .line 34
    .line 35
    move-object v6, v4

    .line 36
    move v4, v3

    .line 37
    move-wide v2, v1

    .line 38
    move-object v1, p4

    .line 39
    invoke-interface/range {v0 .. v6}, LnR/d;->i(Ljava/lang/String;JZLjava/lang/String;Ljava/lang/Integer;)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method private final K3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 11

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v8

    .line 5
    new-instance v9, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$onParticipateClick$1;

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->F1:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v9, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$onParticipateClick$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->H1:Lm8/a;

    .line 13
    .line 14
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 15
    .line 16
    .line 17
    move-result-object v10

    .line 18
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$onParticipateClick$2;

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    move-object v1, p0

    .line 22
    move-wide v2, p1

    .line 23
    move-object v4, p3

    .line 24
    move-object v6, p4

    .line 25
    move-object/from16 v5, p5

    .line 26
    .line 27
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$onParticipateClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/16 v6, 0xa

    .line 31
    .line 32
    const/4 v2, 0x0

    .line 33
    const/4 v4, 0x0

    .line 34
    move-object v5, v0

    .line 35
    move-object v0, v8

    .line 36
    move-object v1, v9

    .line 37
    move-object v3, v10

    .line 38
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method private final M3(Lh81/b;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lh81/b$c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    instance-of p1, p1, Lh81/b$g;

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    return p1

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    return p1
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)LP91/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I1:LP91/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->S2:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static final synthetic s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->P2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lgk/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->y1:Lgk/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)LSX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->x1:LSX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->v2:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->S1:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lw81/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->P1:Lw81/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->V1:J

    .line 2
    .line 3
    return-wide v0
.end method


# virtual methods
.method public final F3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->P2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final G3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final I3(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;)V
    .locals 7
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$onButtonClick$1;

    .line 6
    .line 7
    const/4 v6, 0x0

    .line 8
    move-object v3, p0

    .line 9
    move-object v2, p1

    .line 10
    move-object v4, p2

    .line 11
    move-object v5, p3

    .line 12
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$onButtonClick$1;-><init>(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x3

    .line 16
    const/4 v5, 0x0

    .line 17
    move-object v3, v1

    .line 18
    const/4 v1, 0x0

    .line 19
    const/4 v2, 0x0

    .line 20
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final J3(Lkb1/z$c;Ljava/lang/String;)V
    .locals 16
    .param p1    # Lkb1/z$c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual/range {p1 .. p1}, Lkb1/z$c;->f()Z

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    if-nez v1, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 11
    .line 12
    iget-wide v2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->V1:J

    .line 13
    .line 14
    invoke-virtual/range {p1 .. p1}, Lkb1/z$c;->getId()J

    .line 15
    .line 16
    .line 17
    move-result-wide v4

    .line 18
    invoke-virtual {v1, v2, v3, v4, v5}, Lorg/xbet/analytics/domain/scope/g;->m(JJ)V

    .line 19
    .line 20
    .line 21
    iget-object v6, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->y2:LnR/d;

    .line 22
    .line 23
    iget-wide v8, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->V1:J

    .line 24
    .line 25
    invoke-virtual/range {p1 .. p1}, Lkb1/z$c;->getId()J

    .line 26
    .line 27
    .line 28
    move-result-wide v10

    .line 29
    move-object/from16 v7, p2

    .line 30
    .line 31
    invoke-interface/range {v6 .. v11}, LnR/d;->c(Ljava/lang/String;JJ)V

    .line 32
    .line 33
    .line 34
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->I1:LP91/b;

    .line 35
    .line 36
    new-instance v2, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;

    .line 37
    .line 38
    new-instance v3, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;

    .line 39
    .line 40
    iget-wide v4, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->V1:J

    .line 41
    .line 42
    invoke-virtual/range {p1 .. p1}, Lkb1/z$c;->getTitle()Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object v6

    .line 46
    invoke-virtual/range {p1 .. p1}, Lkb1/z$c;->getId()J

    .line 47
    .line 48
    .line 49
    move-result-wide v7

    .line 50
    invoke-direct/range {v3 .. v8}, Lorg/xplatform/aggregator/api/navigation/AggregatorScreenType$TournamentPrizesScreen;-><init>(JLjava/lang/String;J)V

    .line 51
    .line 52
    .line 53
    const/16 v14, 0xf7

    .line 54
    .line 55
    const/4 v15, 0x0

    .line 56
    move-object v7, v3

    .line 57
    const/4 v3, 0x0

    .line 58
    const/4 v4, 0x0

    .line 59
    const-wide/16 v5, 0x0

    .line 60
    .line 61
    const/4 v8, 0x0

    .line 62
    const-wide/16 v9, 0x0

    .line 63
    .line 64
    const-wide/16 v11, 0x0

    .line 65
    .line 66
    const/4 v13, 0x0

    .line 67
    invoke-direct/range {v2 .. v15}, Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;-><init>(Ljava/lang/String;Ljava/lang/String;JLorg/xplatform/aggregator/api/navigation/AggregatorScreenType;Lorg/xbet/analytics/domain/scope/search/SearchScreenType;JJLjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {v1, v2}, LP91/b;->f(Lorg/xplatform/aggregator/impl/core/navigation/AggregatorScreenModel;)V

    .line 71
    .line 72
    .line 73
    return-void
.end method

.method public final L3(JJZ)V
    .locals 3

    .line 1
    iput-wide p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->S2:J

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->H2:Lkotlinx/coroutines/x0;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    const/4 v2, 0x1

    .line 9
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->v1:Lw81/c;

    .line 13
    .line 14
    invoke-interface {v0, p1, p2, p5}, Lw81/c;->a(JZ)Lkotlinx/coroutines/flow/e;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;

    .line 19
    .line 20
    invoke-direct {p2, p0, p3, p4, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;JLkotlin/coroutines/e;)V

    .line 21
    .line 22
    .line 23
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 28
    .line 29
    .line 30
    move-result-object p2

    .line 31
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->H1:Lm8/a;

    .line 32
    .line 33
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 34
    .line 35
    .line 36
    move-result-object p3

    .line 37
    invoke-static {p2, p3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 38
    .line 39
    .line 40
    move-result-object p2

    .line 41
    new-instance p3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$2;

    .line 42
    .line 43
    invoke-direct {p3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;Lkotlin/coroutines/e;)V

    .line 44
    .line 45
    .line 46
    invoke-static {p1, p2, p3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 47
    .line 48
    .line 49
    move-result-object p1

    .line 50
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->H2:Lkotlinx/coroutines/x0;

    .line 51
    .line 52
    return-void
.end method

.method public final onBackPressed()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->F2:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method
