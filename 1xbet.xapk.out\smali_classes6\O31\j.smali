.class public final synthetic LO31/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LB4/a;


# direct methods
.method public synthetic constructor <init>(LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO31/j;->a:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LO31/j;->a:LB4/a;

    check-cast p1, Ljava/util/List;

    invoke-static {v0, p1}, Lorg/xbet/uikit_sport/sport_collection/adapter/SportCollectionViewHolderKt;->b(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
