.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;
.super Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0002\u0008 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\"\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0001\u0018\u0000 \u0082\u00012\u00020\u0001:\u0001CB\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\u000e\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0010\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0017\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J\u0017\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0014J\u0017\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0014J\u0017\u0010\u0017\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u0014J\u0017\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0014J\u0017\u0010\u0019\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u0014J\u000f\u0010\u001a\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u0011J\u000f\u0010\u001b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u0011J\u000f\u0010\u001c\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u0011J\u000f\u0010\u001d\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u0011J\u000f\u0010\u001e\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u0011J\u000f\u0010\u001f\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u0011J\u000f\u0010 \u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008 \u0010\u0011J\u000f\u0010!\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008!\u0010\u0011J\u000f\u0010\"\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\"\u0010\u0011J\u001f\u0010%\u001a\u00020\n2\u0006\u0010#\u001a\u00020\r2\u0006\u0010$\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008%\u0010&J7\u0010,\u001a\u00020\n2\u0006\u0010\'\u001a\u00020\u00082\u0006\u0010(\u001a\u00020\r2\u0006\u0010)\u001a\u00020\r2\u0006\u0010*\u001a\u00020\r2\u0006\u0010+\u001a\u00020\rH\u0014\u00a2\u0006\u0004\u0008,\u0010-J\u0017\u00100\u001a\u00020\n2\u0006\u0010/\u001a\u00020.H\u0016\u00a2\u0006\u0004\u00080\u00101J\u0019\u00104\u001a\u00020\n2\u0008\u00103\u001a\u0004\u0018\u000102H\u0016\u00a2\u0006\u0004\u00084\u00105J\u0017\u00108\u001a\u00020\n2\u0006\u00107\u001a\u000206H\u0016\u00a2\u0006\u0004\u00088\u00109J\u0017\u0010:\u001a\u00020\n2\u0006\u00107\u001a\u000206H\u0016\u00a2\u0006\u0004\u0008:\u00109J\u0017\u0010;\u001a\u00020\n2\u0006\u00107\u001a\u000206H\u0016\u00a2\u0006\u0004\u0008;\u00109J\u0017\u0010<\u001a\u00020\n2\u0006\u00107\u001a\u000206H\u0016\u00a2\u0006\u0004\u0008<\u00109J\u0017\u0010=\u001a\u00020\n2\u0006\u00107\u001a\u000206H\u0016\u00a2\u0006\u0004\u0008=\u00109J\u0017\u0010>\u001a\u00020\n2\u0006\u00107\u001a\u000206H\u0016\u00a2\u0006\u0004\u0008>\u00109J\u0017\u0010?\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u0008?\u0010\u000cJ\u001b\u0010A\u001a\u00020\n2\n\u0008\u0001\u0010@\u001a\u0004\u0018\u00010\rH\u0016\u00a2\u0006\u0004\u0008A\u0010BJ\u000f\u0010C\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008C\u0010\u0011J\u000f\u0010D\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008D\u0010\u0011R\u0014\u0010F\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010H\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010ER\u0014\u0010I\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010ER\u0014\u0010J\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\"\u0010ER\u0014\u0010K\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001d\u0010ER\u0014\u0010L\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008 \u0010ER\u0014\u0010M\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008!\u0010ER\u0014\u0010N\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010ER\u0014\u0010O\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001f\u0010ER\u0014\u0010P\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010ER\u0014\u0010Q\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0010\u0010ER\u0014\u0010R\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010ER\u0014\u0010S\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010ER\u0014\u0010T\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010ER\u0014\u0010U\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010ER\u0014\u0010V\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010ER\u0014\u0010W\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010ER\u0014\u0010X\u001a\u00020\r8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010ER\u0014\u0010[\u001a\u00020Y8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010ZR\u0016\u0010\t\u001a\u00020\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010a\u001a\u00020^8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008_\u0010`R\u0014\u0010e\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008c\u0010dR\u0014\u0010i\u001a\u00020f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008g\u0010hR\u0014\u0010k\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010dR\u0014\u0010m\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010dR\u0014\u0010o\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008n\u0010dR\u0014\u0010q\u001a\u00020b8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008p\u0010dR\u001a\u0010w\u001a\u00020r8\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008s\u0010t\u001a\u0004\u0008u\u0010vR \u0010~\u001a\u0008\u0012\u0004\u0012\u00020y0x8\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008z\u0010{\u001a\u0004\u0008|\u0010}R\u0016\u0010\u0081\u0001\u001a\u00020y8VX\u0096\u0004\u00a2\u0006\u0007\u001a\u0005\u0008\u007f\u0010\u0080\u0001\u00a8\u0006\u0083\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "isActive",
        "",
        "t",
        "(Z)V",
        "",
        "getStartTextContentOffset",
        "()I",
        "l",
        "()V",
        "parentMeasuredWidth",
        "s",
        "(I)V",
        "n",
        "q",
        "r",
        "o",
        "p",
        "m",
        "d",
        "k",
        "f",
        "i",
        "j",
        "g",
        "h",
        "e",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Lj31/a;",
        "data",
        "setData",
        "(Lj31/a;)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "setStatusDrawable",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "text",
        "setStatusText",
        "(Ljava/lang/String;)V",
        "setCashbackText",
        "setExperienceLabelText",
        "setExperienceText",
        "setCoefLabelText",
        "setCoefText",
        "setIsActive",
        "resId",
        "setStatusDrawableRes",
        "(Ljava/lang/Integer;)V",
        "a",
        "b",
        "I",
        "size52",
        "c",
        "size80",
        "space1",
        "space2",
        "space4",
        "space8",
        "space6",
        "space12",
        "space14",
        "textSize12",
        "textSize14",
        "lineHeight14",
        "tvExperienceLineHeight",
        "tvCoefLineHeight",
        "colorBackgroundLight60",
        "colorBackgroundContent",
        "colorBackground",
        "cardHeight",
        "Landroid/graphics/Rect;",
        "Landroid/graphics/Rect;",
        "helperRect",
        "u",
        "Z",
        "Landroid/widget/ImageView;",
        "v",
        "Landroid/widget/ImageView;",
        "ivStatus",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "w",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvStatus",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "x",
        "Lorg/xbet/uikit/components/tag/Tag;",
        "tagCashback",
        "y",
        "tvExperienceLabel",
        "z",
        "tvExperienceValue",
        "A",
        "tvCoefLabel",
        "B",
        "tvCoefValue",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "C",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "getShimmerView",
        "()Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerView",
        "",
        "Landroid/view/View;",
        "D",
        "Ljava/util/List;",
        "getContentViews",
        "()Ljava/util/List;",
        "contentViews",
        "getView",
        "()Landroid/view/View;",
        "view",
        "E",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final E:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final F:I


# instance fields
.field public final A:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final B:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final C:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final D:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:I

.field public final s:I

.field public final t:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public u:Z

.field public final v:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Lorg/xbet/uikit/components/tag/Tag;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->E:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->F:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 19
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move-object/from16 v0, p0

    move-object/from16 v2, p1

    .line 2
    invoke-direct/range {p0 .. p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->size_52:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->b:I

    .line 4
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_80:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v7

    iput v7, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->c:I

    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_1:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->d:I

    .line 6
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_2:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->e:I

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_4:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 8
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_8:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->g:I

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->space_6:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->h:I

    .line 10
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_12:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->space_14:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j:I

    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->text_12:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v4

    float-to-int v4, v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->k:I

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->text_14:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v4

    float-to-int v4, v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->l:I

    .line 14
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->line_height_14:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->m:I

    .line 15
    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->n:I

    .line 16
    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->o:I

    .line 17
    sget v4, LlZ0/d;->uikitBackgroundLight60:I

    const/4 v8, 0x0

    const/4 v9, 0x2

    invoke-static {v2, v4, v8, v9, v8}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->p:I

    .line 18
    sget v4, LlZ0/d;->uikitBackgroundContent:I

    invoke-static {v2, v4, v8, v9, v8}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->q:I

    .line 19
    sget v4, LlZ0/d;->uikitBackground:I

    invoke-static {v2, v4, v8, v9, v8}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->r:I

    .line 20
    iput v7, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->s:I

    .line 21
    new-instance v4, Landroid/graphics/Rect;

    invoke-direct {v4}, Landroid/graphics/Rect;-><init>()V

    iput-object v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 22
    new-instance v10, Landroid/widget/ImageView;

    invoke-direct {v10, v2}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 23
    sget v4, LS11/d;->aggregatorVipCashbackStatusesIvStatus:I

    invoke-virtual {v10, v4}, Landroid/view/View;->setId(I)V

    .line 24
    new-instance v4, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v4, v1, v1}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v10, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 25
    sget v1, LlZ0/h;->circle_background:I

    invoke-static {v2, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v1

    invoke-virtual {v10, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 26
    invoke-virtual {v10, v3, v3, v3, v3}, Landroid/view/View;->setPadding(IIII)V

    .line 27
    iput-object v10, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 28
    new-instance v11, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v11, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 29
    sget v1, LS11/d;->aggregatorVipCashbackStatusesTvStatus:I

    invoke-virtual {v11, v1}, Landroid/view/View;->setId(I)V

    .line 30
    sget v1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {v11, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 31
    new-instance v1, Landroid/view/ViewGroup$LayoutParams;

    const/4 v12, -0x2

    invoke-direct {v1, v12, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v11, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    const/16 v13, 0x10

    .line 32
    invoke-virtual {v11, v13}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v14, 0x1

    .line 33
    invoke-virtual {v11, v14}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 34
    sget-object v15, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v11, v15}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v1, 0x3

    .line 35
    invoke-virtual {v11, v1}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 v3, 0x0

    .line 36
    invoke-virtual {v11, v3}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 37
    iput-object v11, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    const/4 v4, 0x3

    .line 38
    new-instance v1, Lorg/xbet/uikit/components/tag/Tag;

    const/4 v5, 0x6

    const/4 v6, 0x0

    const/16 v16, 0x0

    const/4 v3, 0x0

    const/16 v17, 0x3

    const/4 v4, 0x0

    const/4 v8, 0x3

    const/4 v9, 0x0

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/tag/Tag;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 39
    sget v3, LS11/d;->aggregatorVipCashbackStatusesTvCashback:I

    invoke-virtual {v1, v3}, Landroid/view/View;->setId(I)V

    .line 40
    new-instance v3, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v3, v12, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 41
    invoke-virtual {v1, v14}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 42
    invoke-virtual {v1, v15}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 43
    invoke-virtual {v1, v8}, Landroid/view/View;->setLayoutDirection(I)V

    const/16 v3, 0x11

    .line 44
    invoke-virtual {v1, v3}, Landroid/widget/TextView;->setGravity(I)V

    .line 45
    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 46
    new-instance v3, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v3, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 47
    sget v4, LS11/d;->aggregatorVipCashbackStatusesTvExperienceLabel:I

    invoke-virtual {v3, v4}, Landroid/view/View;->setId(I)V

    .line 48
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v3, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 49
    new-instance v4, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v4, v12, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v3, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 50
    invoke-virtual {v3, v13}, Landroid/widget/TextView;->setGravity(I)V

    .line 51
    invoke-virtual {v3, v14}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 52
    invoke-virtual {v3, v15}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 53
    invoke-virtual {v3, v8}, Landroid/view/View;->setLayoutDirection(I)V

    .line 54
    invoke-virtual {v3, v9}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 55
    iput-object v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 56
    new-instance v4, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v4, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 57
    sget v5, LS11/d;->aggregatorVipCashbackStatusesTvExperience:I

    invoke-virtual {v4, v5}, Landroid/view/View;->setId(I)V

    .line 58
    sget v5, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    invoke-static {v4, v5}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 59
    new-instance v5, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v5, v12, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v4, v5}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 60
    invoke-virtual {v4, v13}, Landroid/widget/TextView;->setGravity(I)V

    .line 61
    invoke-virtual {v4, v14}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 62
    invoke-virtual {v4, v15}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 63
    invoke-virtual {v4, v8}, Landroid/view/View;->setLayoutDirection(I)V

    .line 64
    invoke-virtual {v4, v9}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 65
    iput-object v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 66
    new-instance v5, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v5, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 67
    sget v6, LS11/d;->aggregatorVipCashbackStatusesTvCoefLabel:I

    invoke-virtual {v5, v6}, Landroid/view/View;->setId(I)V

    .line 68
    sget v6, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    invoke-static {v5, v6}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 69
    new-instance v6, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v6, v12, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v5, v6}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 70
    invoke-virtual {v5, v13}, Landroid/widget/TextView;->setGravity(I)V

    .line 71
    invoke-virtual {v5, v14}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 72
    invoke-virtual {v5, v15}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 73
    invoke-virtual {v5, v8}, Landroid/view/View;->setLayoutDirection(I)V

    .line 74
    invoke-virtual {v5, v9}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 75
    iput-object v5, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 76
    new-instance v6, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v6, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 77
    sget v9, LS11/d;->aggregatorVipCashbackStatusesTvCoef:I

    invoke-virtual {v6, v9}, Landroid/view/View;->setId(I)V

    .line 78
    sget v9, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    invoke-static {v6, v9}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 79
    new-instance v9, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v9, v12, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v6, v9}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 80
    invoke-virtual {v6, v13}, Landroid/widget/TextView;->setGravity(I)V

    .line 81
    invoke-virtual {v6, v14}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 82
    invoke-virtual {v6, v15}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 83
    invoke-virtual {v6, v8}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 v9, 0x0

    .line 84
    invoke-virtual {v6, v9}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 85
    iput-object v6, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->B:Landroidx/appcompat/widget/AppCompatTextView;

    move-object v9, v1

    .line 86
    new-instance v1, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-object v12, v5

    const/4 v5, 0x6

    move-object v13, v6

    const/4 v6, 0x0

    move-object v15, v3

    const/4 v3, 0x0

    move-object/from16 v18, v4

    const/4 v4, 0x0

    move-object v8, v13

    move-object v13, v12

    move-object/from16 v12, v18

    const/16 v18, 0x3

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 87
    sget v3, LS11/d;->aggregatorVipCashbackStatusesShimmer:I

    invoke-virtual {v1, v3}, Landroid/view/View;->setId(I)V

    .line 88
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v4, -0x1

    invoke-direct {v3, v4, v7}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 89
    new-instance v3, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v3}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 90
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->radius_16:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v4

    invoke-virtual {v3, v4}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 91
    sget v4, LlZ0/d;->uikitSecondary20:I

    const/4 v5, 0x0

    const/4 v6, 0x2

    invoke-static {v2, v4, v5, v6, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-static {v2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v2

    invoke-virtual {v3, v2}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 92
    invoke-virtual {v1, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 93
    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->C:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    const/4 v1, 0x7

    .line 94
    new-array v1, v1, [Landroid/view/View;

    const/16 v17, 0x0

    aput-object v10, v1, v17

    aput-object v11, v1, v14

    aput-object v9, v1, v6

    aput-object v15, v1, v18

    const/4 v2, 0x4

    aput-object v12, v1, v2

    const/4 v2, 0x5

    aput-object v13, v1, v2

    const/4 v2, 0x6

    aput-object v8, v1, v2

    .line 95
    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->D:Ljava/util/List;

    .line 96
    sget v1, LlZ0/h;->rounded_background_16:I

    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 97
    invoke-virtual {v0, v10}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 98
    invoke-virtual {v0, v11}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 99
    invoke-virtual {v0, v9}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 100
    invoke-virtual {v0, v15}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 101
    invoke-virtual {v0, v12}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 102
    invoke-virtual {v0, v13}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 103
    invoke-virtual {v0, v8}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 104
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method private final d()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 2
    .line 3
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 4
    .line 5
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j:I

    .line 6
    .line 7
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->b:I

    .line 8
    .line 9
    add-int v4, v0, v2

    .line 10
    .line 11
    add-int v5, v0, v3

    .line 12
    .line 13
    move-object v0, p0

    .line 14
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 15
    .line 16
    .line 17
    return-void
.end method

.method private final e()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 6
    .line 7
    .line 8
    move-result v4

    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 14
    .line 15
    .line 16
    move-result v5

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    move-object v0, p0

    .line 20
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method private final f()V
    .locals 8

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 6
    .line 7
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    add-int/2addr v0, v1

    .line 12
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->g:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 20
    .line 21
    sub-int/2addr v1, v2

    .line 22
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 23
    .line 24
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    sub-int/2addr v1, v2

    .line 29
    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    .line 30
    .line 31
    .line 32
    move-result v4

    .line 33
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 34
    .line 35
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j:I

    .line 36
    .line 37
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    add-int v6, v4, v0

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 44
    .line 45
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j:I

    .line 50
    .line 51
    add-int v7, v0, v1

    .line 52
    .line 53
    move-object v2, p0

    .line 54
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 55
    .line 56
    .line 57
    return-void
.end method

.method private final g()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 9
    .line 10
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->e:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->o:I

    .line 16
    .line 17
    add-int v7, v0, v1

    .line 18
    .line 19
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 28
    .line 29
    mul-int/lit8 v1, v1, 0x2

    .line 30
    .line 31
    add-int v4, v0, v1

    .line 32
    .line 33
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 34
    .line 35
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    sub-int v5, v7, v0

    .line 40
    .line 41
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 42
    .line 43
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 48
    .line 49
    mul-int/lit8 v1, v1, 0x2

    .line 50
    .line 51
    add-int/2addr v0, v1

    .line 52
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 53
    .line 54
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    add-int v6, v0, v1

    .line 59
    .line 60
    move-object v2, p0

    .line 61
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method private final getStartTextContentOffset()I
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 8
    .line 9
    mul-int/lit8 v1, v1, 0x2

    .line 10
    .line 11
    add-int/2addr v0, v1

    .line 12
    return v0
.end method

.method private final h()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 9
    .line 10
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->e:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->o:I

    .line 16
    .line 17
    add-int v7, v0, v1

    .line 18
    .line 19
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->B:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 28
    .line 29
    mul-int/lit8 v1, v1, 0x2

    .line 30
    .line 31
    add-int/2addr v0, v1

    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    add-int/2addr v0, v1

    .line 39
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 40
    .line 41
    add-int v4, v0, v1

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->B:Landroidx/appcompat/widget/AppCompatTextView;

    .line 44
    .line 45
    invoke-virtual {v0}, Landroid/view/View;->getHeight()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    sub-int v5, v7, v0

    .line 50
    .line 51
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 52
    .line 53
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 58
    .line 59
    mul-int/lit8 v1, v1, 0x2

    .line 60
    .line 61
    add-int/2addr v0, v1

    .line 62
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 63
    .line 64
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 65
    .line 66
    .line 67
    move-result v1

    .line 68
    add-int/2addr v0, v1

    .line 69
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 70
    .line 71
    add-int/2addr v0, v1

    .line 72
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->B:Landroidx/appcompat/widget/AppCompatTextView;

    .line 73
    .line 74
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    add-int v6, v0, v1

    .line 79
    .line 80
    move-object v2, p0

    .line 81
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method private final i()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 9
    .line 10
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->h:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->n:I

    .line 16
    .line 17
    add-int v7, v0, v1

    .line 18
    .line 19
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 22
    .line 23
    .line 24
    move-result v4

    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 26
    .line 27
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    sub-int v5, v7, v0

    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 34
    .line 35
    .line 36
    move-result v0

    .line 37
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 38
    .line 39
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 40
    .line 41
    .line 42
    move-result v1

    .line 43
    add-int v6, v0, v1

    .line 44
    .line 45
    move-object v2, p0

    .line 46
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method private final j()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t:Landroid/graphics/Rect;

    .line 9
    .line 10
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->h:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->n:I

    .line 16
    .line 17
    add-int v7, v0, v1

    .line 18
    .line 19
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 22
    .line 23
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 28
    .line 29
    mul-int/lit8 v1, v1, 0x2

    .line 30
    .line 31
    add-int/2addr v0, v1

    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    add-int/2addr v0, v1

    .line 39
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 40
    .line 41
    add-int v4, v0, v1

    .line 42
    .line 43
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 44
    .line 45
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 46
    .line 47
    .line 48
    move-result v0

    .line 49
    sub-int v5, v7, v0

    .line 50
    .line 51
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 52
    .line 53
    invoke-virtual {v0}, Landroid/view/View;->getWidth()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 58
    .line 59
    mul-int/lit8 v1, v1, 0x2

    .line 60
    .line 61
    add-int/2addr v0, v1

    .line 62
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 63
    .line 64
    invoke-virtual {v1}, Landroid/view/View;->getWidth()I

    .line 65
    .line 66
    .line 67
    move-result v1

    .line 68
    add-int/2addr v0, v1

    .line 69
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 70
    .line 71
    add-int/2addr v0, v1

    .line 72
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 73
    .line 74
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 75
    .line 76
    .line 77
    move-result v1

    .line 78
    add-int v6, v0, v1

    .line 79
    .line 80
    move-object v2, p0

    .line 81
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method private final k()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 4
    .line 5
    .line 6
    move-result v2

    .line 7
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j:I

    .line 8
    .line 9
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->d:I

    .line 10
    .line 11
    sub-int v3, v0, v3

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 14
    .line 15
    .line 16
    move-result v0

    .line 17
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 18
    .line 19
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 20
    .line 21
    .line 22
    move-result v4

    .line 23
    add-int/2addr v4, v0

    .line 24
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 25
    .line 26
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 27
    .line 28
    .line 29
    move-result v0

    .line 30
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j:I

    .line 31
    .line 32
    add-int/2addr v0, v5

    .line 33
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->d:I

    .line 34
    .line 35
    sub-int v5, v0, v5

    .line 36
    .line 37
    move-object v0, p0

    .line 38
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method private final l()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->b:I

    .line 4
    .line 5
    const/high16 v2, 0x40000000    # 2.0f

    .line 6
    .line 7
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->b:I

    .line 12
    .line 13
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method private final m()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->c:I

    .line 16
    .line 17
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private final n(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    const/high16 v1, -0x80000000

    .line 12
    .line 13
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    const/4 v1, 0x0

    .line 18
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method private final o(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 4
    .line 5
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    sub-int/2addr p1, v1

    .line 10
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 11
    .line 12
    mul-int/lit8 v1, v1, 0x3

    .line 13
    .line 14
    sub-int/2addr p1, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 16
    .line 17
    sub-int/2addr p1, v1

    .line 18
    div-int/lit8 p1, p1, 0x2

    .line 19
    .line 20
    const/high16 v1, -0x80000000

    .line 21
    .line 22
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    const/4 v1, 0x0

    .line 27
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method private final p(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->B:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 12
    .line 13
    sub-int/2addr p1, v1

    .line 14
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 15
    .line 16
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    sub-int/2addr p1, v1

    .line 21
    const/high16 v1, 0x40000000    # 2.0f

    .line 22
    .line 23
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    const/4 v1, 0x0

    .line 28
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method private final q(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 12
    .line 13
    sub-int/2addr p1, v1

    .line 14
    div-int/lit8 p1, p1, 0x2

    .line 15
    .line 16
    const/high16 v1, -0x80000000

    .line 17
    .line 18
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    const/4 v1, 0x0

    .line 23
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method private final r(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f:I

    .line 12
    .line 13
    sub-int/2addr p1, v1

    .line 14
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 15
    .line 16
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    sub-int/2addr p1, v1

    .line 21
    const/high16 v1, 0x40000000    # 2.0f

    .line 22
    .line 23
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    const/4 v1, 0x0

    .line 28
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method private final s(I)V
    .locals 7

    .line 1
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i:I

    .line 2
    .line 3
    sub-int/2addr p1, v0

    .line 4
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->g:I

    .line 5
    .line 6
    sub-int/2addr p1, v0

    .line 7
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredWidth()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    sub-int/2addr p1, v0

    .line 14
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->getStartTextContentOffset()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    sub-int/2addr p1, v0

    .line 19
    invoke-static {p1}, LlZ0/b;->c(I)I

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->l:I

    .line 24
    .line 25
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->k:I

    .line 26
    .line 27
    sub-int/2addr v0, v1

    .line 28
    int-to-float v0, v0

    .line 29
    const/high16 v1, 0x40000000    # 2.0f

    .line 30
    .line 31
    div-float/2addr v0, v1

    .line 32
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 33
    .line 34
    invoke-virtual {v1}, Landroidx/appcompat/widget/AppCompatTextView;->getText()Ljava/lang/CharSequence;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    const/4 v2, 0x1

    .line 43
    move v3, p1

    .line 44
    :goto_0
    const/4 v4, 0x3

    .line 45
    if-ge v2, v4, :cond_1

    .line 46
    .line 47
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->k:I

    .line 48
    .line 49
    int-to-float v4, v4

    .line 50
    int-to-float v5, v2

    .line 51
    mul-float v5, v5, v0

    .line 52
    .line 53
    add-float/2addr v4, v5

    .line 54
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 55
    .line 56
    invoke-virtual {v5}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    .line 57
    .line 58
    .line 59
    move-result-object v5

    .line 60
    invoke-virtual {v5, v4}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 61
    .line 62
    .line 63
    iget-object v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 64
    .line 65
    invoke-virtual {v5}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    .line 66
    .line 67
    .line 68
    move-result-object v5

    .line 69
    invoke-virtual {v5, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 70
    .line 71
    .line 72
    move-result v5

    .line 73
    int-to-float v6, p1

    .line 74
    cmpl-float v6, v5, v6

    .line 75
    .line 76
    if-lez v6, :cond_0

    .line 77
    .line 78
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 79
    .line 80
    invoke-virtual {p1}, Landroid/widget/TextView;->getPaint()Landroid/text/TextPaint;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    sub-float/2addr v4, v0

    .line 85
    invoke-virtual {p1, v4}, Landroid/graphics/Paint;->setTextSize(F)V

    .line 86
    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_0
    float-to-int v3, v5

    .line 90
    add-int/lit8 v2, v2, 0x1

    .line 91
    .line 92
    goto :goto_0

    .line 93
    :cond_1
    :goto_1
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 94
    .line 95
    const/high16 v0, 0x40000000    # 2.0f

    .line 96
    .line 97
    invoke-static {v3, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 98
    .line 99
    .line 100
    move-result v0

    .line 101
    const/4 v1, 0x0

    .line 102
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 103
    .line 104
    .line 105
    move-result v1

    .line 106
    invoke-virtual {p1, v0, v1}, Landroid/view/View;->measure(II)V

    .line 107
    .line 108
    .line 109
    return-void
.end method

.method private final t(Z)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->p:I

    .line 4
    .line 5
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->q:I

    .line 11
    .line 12
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    :goto_0
    invoke-static {p0, p1}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method


# virtual methods
.method public a()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 3
    .line 4
    .line 5
    invoke-super {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->a()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public b()V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->u:Z

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t(Z)V

    .line 4
    .line 5
    .line 6
    invoke-super {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->b()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public getContentViews()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->D:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->C:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    return-object v0
.end method

.method public getView()Landroid/view/View;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    return-object p0
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->d()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->k()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->f()V

    .line 11
    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->i()V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->j()V

    .line 17
    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->g()V

    .line 20
    .line 21
    .line 22
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->h()V

    .line 23
    .line 24
    .line 25
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->e()V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->l()V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->n(I)V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->s(I)V

    .line 12
    .line 13
    .line 14
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->q(I)V

    .line 15
    .line 16
    .line 17
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->r(I)V

    .line 18
    .line 19
    .line 20
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->o(I)V

    .line 21
    .line 22
    .line 23
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->p(I)V

    .line 24
    .line 25
    .line 26
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->m()V

    .line 27
    .line 28
    .line 29
    const/high16 p2, 0x40000000    # 2.0f

    .line 30
    .line 31
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->s:I

    .line 36
    .line 37
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 38
    .line 39
    .line 40
    move-result p2

    .line 41
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public setCashbackText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setCoefLabelText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->A:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setCoefText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->B:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setData(Lj31/a;)V
    .locals 2
    .param p1    # Lj31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setData(Lj31/a;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, Lj31/c;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    sget-object v0, Li31/i;->a:Li31/i;

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 11
    .line 12
    check-cast p1, Lj31/c;

    .line 13
    .line 14
    invoke-virtual {p1}, Lj31/c;->g()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {v0, v1, p1}, Li31/i;->a(Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public setExperienceLabelText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setExperienceText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->z:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setIsActive(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->u:Z

    .line 2
    .line 3
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->t(Z)V

    .line 4
    .line 5
    .line 6
    if-eqz p1, :cond_0

    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 9
    .line 10
    sget v0, LlZ0/n;->Widget_Tag_RectangularS_Green:I

    .line 11
    .line 12
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/tag/Tag;->setStyle(I)V

    .line 13
    .line 14
    .line 15
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 16
    .line 17
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->q:I

    .line 18
    .line 19
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 24
    .line 25
    .line 26
    return-void

    .line 27
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->x:Lorg/xbet/uikit/components/tag/Tag;

    .line 28
    .line 29
    sget v0, LlZ0/n;->Widget_Tag_RectangularS_Light:I

    .line 30
    .line 31
    invoke-virtual {p1, v0}, Lorg/xbet/uikit/components/tag/Tag;->setStyle(I)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 35
    .line 36
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->r:I

    .line 37
    .line 38
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public setStatusDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setStatusDrawableRes(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 7
    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->v:Landroid/widget/ImageView;

    .line 11
    .line 12
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public setStatusText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemLargeIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
