.class public final synthetic Lorg/xbet/uikit_sport/eventcard/bottom/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/e;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/e;->a:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;->t(Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomInfo;Landroid/view/View;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
