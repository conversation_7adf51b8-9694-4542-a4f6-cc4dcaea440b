.class public final synthetic LO11/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:LO11/p;


# direct methods
.method public synthetic constructor <init>(LO11/p;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LO11/o;->a:LO11/p;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    .line 1
    iget-object v0, p0, LO11/o;->a:LO11/p;

    invoke-static {v0}, LO11/p;->c(LO11/p;)V

    return-void
.end method
