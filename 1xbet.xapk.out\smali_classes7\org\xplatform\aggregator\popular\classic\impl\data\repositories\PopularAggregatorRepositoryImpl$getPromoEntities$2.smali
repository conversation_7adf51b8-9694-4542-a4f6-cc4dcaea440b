.class final Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.data.repositories.PopularAggregatorRepositoryImpl$getPromoEntities$2"
    f = "PopularAggregatorRepositoryImpl.kt"
    l = {
        0x22
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->a(ZZZZZLkotlin/coroutines/e;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "LBb1/a;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "LBb1/a;",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)LBb1/a;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $brandsApi:Z

.field final synthetic $fromCache:Z

.field final synthetic $hasAggregatorBrandsFullInfo:Z

.field final synthetic $hasProvidersAggregator:Z

.field final synthetic $test:Z

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;


# direct methods
.method public constructor <init>(ZLorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;ZZZZLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;",
            "ZZZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;",
            ">;)V"
        }
    .end annotation

    iput-boolean p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$fromCache:Z

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$test:Z

    iput-boolean p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$brandsApi:Z

    iput-boolean p5, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$hasProvidersAggregator:Z

    iput-boolean p6, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$hasAggregatorBrandsFullInfo:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1, p7}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;

    iget-boolean v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$fromCache:Z

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    iget-boolean v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$test:Z

    iget-boolean v4, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$brandsApi:Z

    iget-boolean v5, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$hasProvidersAggregator:Z

    iget-boolean v6, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$hasAggregatorBrandsFullInfo:Z

    move-object v7, p2

    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;-><init>(ZLorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;ZZZZLkotlin/coroutines/e;)V

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "LBb1/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 12

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    move-object v11, p0

    .line 16
    goto/16 :goto_2

    .line 17
    .line 18
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 19
    .line 20
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 21
    .line 22
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw p1

    .line 26
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 27
    .line 28
    .line 29
    iget-boolean p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$fromCache:Z

    .line 30
    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 34
    .line 35
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->b(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LEb1/a;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-virtual {p1}, LEb1/a;->a()LBb1/a;

    .line 40
    .line 41
    .line 42
    move-result-object p1

    .line 43
    if-eqz p1, :cond_2

    .line 44
    .line 45
    return-object p1

    .line 46
    :cond_2
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->d(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LEb1/d;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 53
    .line 54
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)Lc8/h;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-interface {p1}, Lc8/h;->c()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v4

    .line 62
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 63
    .line 64
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)Lc8/h;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    invoke-interface {p1}, Lc8/h;->d()I

    .line 69
    .line 70
    .line 71
    move-result v5

    .line 72
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 73
    .line 74
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)Lc8/h;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-interface {p1}, Lc8/h;->b()I

    .line 79
    .line 80
    .line 81
    move-result v9

    .line 82
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 83
    .line 84
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)Lc8/h;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    invoke-interface {p1}, Lc8/h;->f()I

    .line 89
    .line 90
    .line 91
    move-result v6

    .line 92
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 93
    .line 94
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->c(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LS8/a;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    invoke-virtual {p1}, LS8/a;->b()Le9/a;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    if-eqz p1, :cond_3

    .line 103
    .line 104
    invoke-virtual {p1}, Le9/a;->w()Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object p1

    .line 108
    if-eqz p1, :cond_3

    .line 109
    .line 110
    invoke-static {p1}, Lkotlin/text/StringsKt;->toIntOrNull(Ljava/lang/String;)Ljava/lang/Integer;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    :goto_0
    move-object v7, p1

    .line 115
    goto :goto_1

    .line 116
    :cond_3
    const/4 p1, 0x0

    .line 117
    goto :goto_0

    .line 118
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 119
    .line 120
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->e(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)Lc8/h;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    invoke-interface {p1}, Lc8/h;->getGroupId()I

    .line 125
    .line 126
    .line 127
    move-result v8

    .line 128
    iget-boolean v10, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$test:Z

    .line 129
    .line 130
    iput v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->label:I

    .line 131
    .line 132
    move-object v11, p0

    .line 133
    invoke-virtual/range {v3 .. v11}, LEb1/d;->b(Ljava/lang/String;IILjava/lang/Integer;IIZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 134
    .line 135
    .line 136
    move-result-object p1

    .line 137
    if-ne p1, v0, :cond_4

    .line 138
    .line 139
    return-object v0

    .line 140
    :cond_4
    :goto_2
    check-cast p1, Le8/b;

    .line 141
    .line 142
    invoke-virtual {p1}, Le8/b;->a()Ljava/lang/Object;

    .line 143
    .line 144
    .line 145
    move-result-object p1

    .line 146
    check-cast p1, LGb1/c;

    .line 147
    .line 148
    iget-boolean v0, v11, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$brandsApi:Z

    .line 149
    .line 150
    iget-boolean v1, v11, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$hasProvidersAggregator:Z

    .line 151
    .line 152
    iget-boolean v2, v11, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->$hasAggregatorBrandsFullInfo:Z

    .line 153
    .line 154
    invoke-static {p1, v0, v1, v2}, LFb1/a;->d(LGb1/c;ZZZ)LBb1/a;

    .line 155
    .line 156
    .line 157
    move-result-object p1

    .line 158
    iget-object v0, v11, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;

    .line 159
    .line 160
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;->b(Lorg/xplatform/aggregator/popular/classic/impl/data/repositories/PopularAggregatorRepositoryImpl;)LEb1/a;

    .line 161
    .line 162
    .line 163
    move-result-object v0

    .line 164
    invoke-virtual {v0, p1}, LEb1/a;->b(LBb1/a;)V

    .line 165
    .line 166
    .line 167
    return-object p1
.end method
