.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic d:I


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->b:Ljava/lang/String;

    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->c:Lorg/xplatform/aggregator/api/model/Game;

    iput p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->d:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->c:Lorg/xplatform/aggregator/api/model/Game;

    iget v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;->d:I

    check-cast p1, Ljava/lang/Throwable;

    invoke-static {v0, v1, v2, v3, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->p3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
