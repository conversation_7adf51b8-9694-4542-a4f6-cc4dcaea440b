.class public final synthetic Lm11/C;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lm11/H$h;


# direct methods
.method public synthetic constructor <init>(Lm11/H$h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm11/C;->a:Lm11/H$h;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lm11/C;->a:Lm11/H$h;

    check-cast p1, Lb0/f;

    invoke-static {v0, p1}, Lm11/G$a;->c(Lm11/H$h;Lb0/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
