.class public final LN81/c$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LN81/l;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LN81/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:LRf0/l;

.field public final b:Lf8/g;

.field public final c:Lc8/h;

.field public final d:LJ81/a;

.field public final e:Lm8/a;

.field public final f:LwX0/c;

.field public final g:Lp9/c;

.field public final h:LHX0/e;

.field public final i:LwX0/a;

.field public final j:Lorg/xbet/remoteconfig/domain/usecases/i;

.field public final k:LwX0/C;

.field public final l:Lorg/xplatform/aggregator/api/navigation/a;

.field public final m:Lorg/xbet/analytics/domain/scope/E;

.field public final n:Lp9/g;

.field public final o:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

.field public final p:Lak/a;

.field public final q:Lorg/xbet/ui_common/utils/M;

.field public final r:LWb0/a;

.field public final s:Lorg/xbet/ui_common/utils/internet/a;

.field public final t:LN81/c$a;


# direct methods
.method public constructor <init>(LWb0/a;Lak/a;LwX0/c;LRf0/l;LHX0/e;Lf8/g;Lcom/xbet/onexuser/domain/user/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwX0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lp9/g;Lp9/c;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LN81/c$a;->t:LN81/c$a;

    .line 4
    iput-object p4, p0, LN81/c$a;->a:LRf0/l;

    .line 5
    iput-object p6, p0, LN81/c$a;->b:Lf8/g;

    move-object/from16 p4, p18

    .line 6
    iput-object p4, p0, LN81/c$a;->c:Lc8/h;

    .line 7
    iput-object p14, p0, LN81/c$a;->d:LJ81/a;

    .line 8
    iput-object p8, p0, LN81/c$a;->e:Lm8/a;

    .line 9
    iput-object p3, p0, LN81/c$a;->f:LwX0/c;

    move-object/from16 p3, p16

    .line 10
    iput-object p3, p0, LN81/c$a;->g:Lp9/c;

    .line 11
    iput-object p5, p0, LN81/c$a;->h:LHX0/e;

    .line 12
    iput-object p12, p0, LN81/c$a;->i:LwX0/a;

    .line 13
    iput-object p11, p0, LN81/c$a;->j:Lorg/xbet/remoteconfig/domain/usecases/i;

    move-object/from16 p3, p19

    .line 14
    iput-object p3, p0, LN81/c$a;->k:LwX0/C;

    move-object/from16 p3, p20

    .line 15
    iput-object p3, p0, LN81/c$a;->l:Lorg/xplatform/aggregator/api/navigation/a;

    move-object/from16 p3, p21

    .line 16
    iput-object p3, p0, LN81/c$a;->m:Lorg/xbet/analytics/domain/scope/E;

    .line 17
    iput-object p15, p0, LN81/c$a;->n:Lp9/g;

    .line 18
    iput-object p13, p0, LN81/c$a;->o:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 19
    iput-object p2, p0, LN81/c$a;->p:Lak/a;

    .line 20
    iput-object p9, p0, LN81/c$a;->q:Lorg/xbet/ui_common/utils/M;

    .line 21
    iput-object p1, p0, LN81/c$a;->r:LWb0/a;

    move-object/from16 p1, p17

    .line 22
    iput-object p1, p0, LN81/c$a;->s:Lorg/xbet/ui_common/utils/internet/a;

    return-void
.end method

.method public synthetic constructor <init>(LWb0/a;Lak/a;LwX0/c;LRf0/l;LHX0/e;Lf8/g;Lcom/xbet/onexuser/domain/user/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwX0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lp9/g;Lp9/c;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;LN81/d;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p21}, LN81/c$a;-><init>(LWb0/a;Lak/a;LwX0/c;LRf0/l;LHX0/e;Lf8/g;Lcom/xbet/onexuser/domain/user/c;Lm8/a;Lorg/xbet/ui_common/utils/M;LxX0/a;Lorg/xbet/remoteconfig/domain/usecases/i;LwX0/a;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LJ81/a;Lp9/g;Lp9/c;Lorg/xbet/ui_common/utils/internet/a;Lc8/h;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;)V

    return-void
.end method


# virtual methods
.method public final A()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTaskOnboardingStreamScenarioImpl;
    .locals 6

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTaskOnboardingStreamScenarioImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->w()LR81/h;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, p0, LN81/c$a;->n:Lp9/g;

    .line 8
    .line 9
    invoke-virtual {p0}, LN81/c$a;->v()LR81/f;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    iget-object v4, p0, LN81/c$a;->j:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 14
    .line 15
    invoke-virtual {p0}, LN81/c$a;->B()LR81/l;

    .line 16
    .line 17
    .line 18
    move-result-object v5

    .line 19
    invoke-direct/range {v0 .. v5}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTaskOnboardingStreamScenarioImpl;-><init>(LR81/h;Lp9/g;LR81/f;Lorg/xbet/remoteconfig/domain/usecases/i;LR81/l;)V

    .line 20
    .line 21
    .line 22
    return-object v0
.end method

.method public final B()LR81/l;
    .locals 2

    .line 1
    new-instance v0, LR81/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/l;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final C()LR81/n;
    .locals 2

    .line 1
    new-instance v0, LR81/n;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/n;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final D()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;
    .locals 4

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->t()LR81/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, LN81/c$a;->v()LR81/f;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, LN81/c$a;->E()LR81/p;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-direct {v0, v1, v2, v3}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;-><init>(LR81/c;LR81/f;LR81/p;)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method

.method public final E()LR81/p;
    .locals 2

    .line 1
    new-instance v0, LR81/p;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/p;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final F()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/RefreshDailyTaskScenarioImpl;
    .locals 4

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/RefreshDailyTaskScenarioImpl;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->o:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 4
    .line 5
    iget-object v2, p0, LN81/c$a;->p:Lak/a;

    .line 6
    .line 7
    invoke-interface {v2}, Lak/a;->F()Lek/d;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-static {v2}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    check-cast v2, Lek/d;

    .line 16
    .line 17
    invoke-virtual {p0}, LN81/c$a;->u()LR81/e;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-direct {v0, v1, v2, v3}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/RefreshDailyTaskScenarioImpl;-><init>(Lcom/xbet/onexuser/domain/managers/TokenRefresher;Lek/d;LR81/e;)V

    .line 22
    .line 23
    .line 24
    return-object v0
.end method

.method public final G()LR81/t;
    .locals 2

    .line 1
    new-instance v0, LR81/t;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/t;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final H()LR81/w;
    .locals 2

    .line 1
    new-instance v0, LR81/w;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/w;-><init>(LQ81/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final I()LR81/x;
    .locals 2

    .line 1
    new-instance v0, LR81/x;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/x;-><init>(LQ81/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final J()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/c;
    .locals 3

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/c;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->j:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 4
    .line 5
    invoke-virtual {p0}, LN81/c$a;->K()LR81/y;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-direct {v0, v1, v2}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/c;-><init>(Lorg/xbet/remoteconfig/domain/usecases/i;LR81/y;)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method

.method public final K()LR81/y;
    .locals 2

    .line 1
    new-instance v0, LR81/y;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/y;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final L()LR81/z;
    .locals 2

    .line 1
    new-instance v0, LR81/z;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/z;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;
    .locals 2

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->a:LRf0/l;

    .line 4
    .line 5
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;-><init>(LRf0/l;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public a()LC81/f;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->J()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public b()LC81/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->j()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/CheckAvailableTaskScenarioImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public c()LG81/b;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->l()Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskRefreshViewModelDelegateImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public d()LF81/a;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetAdapterDelegatesImpl;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetAdapterDelegatesImpl;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public e()LG81/d;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->p()Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetPromoViewModelDelegateImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public f()LG81/c;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->o()Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetMyAggregatorViewModelDelegateImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public g()LE81/a;
    .locals 1

    .line 1
    new-instance v0, LN81/w;

    .line 2
    .line 3
    invoke-direct {v0}, LN81/w;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public h()LC81/c;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->s()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetActiveDailyTaskScenarioImpl;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public i()LD81/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, LN81/c$a;->k()LR81/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final j()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/CheckAvailableTaskScenarioImpl;
    .locals 4

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/CheckAvailableTaskScenarioImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->v()LR81/f;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, LN81/c$a;->z()LR81/k;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, LN81/c$a;->I()LR81/x;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-direct {v0, v1, v2, v3}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/CheckAvailableTaskScenarioImpl;-><init>(LR81/f;LD81/b;LD81/c;)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method

.method public final k()LR81/a;
    .locals 3

    .line 1
    new-instance v0, LR81/a;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, LN81/c$a;->M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-direct {v0, v1, v2}, LR81/a;-><init>(LQ81/a;LQ81/b;)V

    .line 12
    .line 13
    .line 14
    return-object v0
.end method

.method public final l()Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskRefreshViewModelDelegateImpl;
    .locals 14

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskRefreshViewModelDelegateImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->m()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/DailyTaskUpdateStatusStreamScenarioImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, p0, LN81/c$a;->e:Lm8/a;

    .line 8
    .line 9
    invoke-virtual {p0}, LN81/c$a;->A()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTaskOnboardingStreamScenarioImpl;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    iget-object v4, p0, LN81/c$a;->q:Lorg/xbet/ui_common/utils/M;

    .line 14
    .line 15
    iget-object v5, p0, LN81/c$a;->r:LWb0/a;

    .line 16
    .line 17
    invoke-interface {v5}, LWb0/a;->d()LYb0/a;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    invoke-static {v5}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v5

    .line 25
    check-cast v5, LYb0/a;

    .line 26
    .line 27
    iget-object v6, p0, LN81/c$a;->n:Lp9/g;

    .line 28
    .line 29
    invoke-virtual {p0}, LN81/c$a;->B()LR81/l;

    .line 30
    .line 31
    .line 32
    move-result-object v7

    .line 33
    invoke-virtual {p0}, LN81/c$a;->G()LR81/t;

    .line 34
    .line 35
    .line 36
    move-result-object v8

    .line 37
    invoke-virtual {p0}, LN81/c$a;->F()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/RefreshDailyTaskScenarioImpl;

    .line 38
    .line 39
    .line 40
    move-result-object v9

    .line 41
    iget-object v10, p0, LN81/c$a;->s:Lorg/xbet/ui_common/utils/internet/a;

    .line 42
    .line 43
    iget-object v11, p0, LN81/c$a;->m:Lorg/xbet/analytics/domain/scope/E;

    .line 44
    .line 45
    invoke-virtual {p0}, LN81/c$a;->y()LR81/j;

    .line 46
    .line 47
    .line 48
    move-result-object v12

    .line 49
    iget-object v13, p0, LN81/c$a;->j:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 50
    .line 51
    invoke-direct/range {v0 .. v13}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskRefreshViewModelDelegateImpl;-><init>(LC81/b;Lm8/a;LC81/d;Lorg/xbet/ui_common/utils/M;LYb0/a;Lp9/g;LR81/l;LR81/t;LC81/e;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/analytics/domain/scope/E;LR81/j;Lorg/xbet/remoteconfig/domain/usecases/i;)V

    .line 52
    .line 53
    .line 54
    return-object v0
.end method

.method public final m()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/DailyTaskUpdateStatusStreamScenarioImpl;
    .locals 5

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/DailyTaskUpdateStatusStreamScenarioImpl;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->o:Lcom/xbet/onexuser/domain/managers/TokenRefresher;

    .line 4
    .line 5
    invoke-virtual {p0}, LN81/c$a;->C()LR81/n;

    .line 6
    .line 7
    .line 8
    move-result-object v2

    .line 9
    invoke-virtual {p0}, LN81/c$a;->n()LR81/b;

    .line 10
    .line 11
    .line 12
    move-result-object v3

    .line 13
    iget-object v4, p0, LN81/c$a;->p:Lak/a;

    .line 14
    .line 15
    invoke-interface {v4}, Lak/a;->F()Lek/d;

    .line 16
    .line 17
    .line 18
    move-result-object v4

    .line 19
    invoke-static {v4}, Ldagger/internal/g;->d(Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    check-cast v4, Lek/d;

    .line 24
    .line 25
    invoke-direct {v0, v1, v2, v3, v4}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/DailyTaskUpdateStatusStreamScenarioImpl;-><init>(Lcom/xbet/onexuser/domain/managers/TokenRefresher;LR81/n;LR81/b;Lek/d;)V

    .line 26
    .line 27
    .line 28
    return-object v0
.end method

.method public final n()LR81/b;
    .locals 2

    .line 1
    new-instance v0, LR81/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/b;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final o()Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetMyAggregatorViewModelDelegateImpl;
    .locals 15

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetMyAggregatorViewModelDelegateImpl;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->f:LwX0/c;

    .line 4
    .line 5
    iget-object v2, p0, LN81/c$a;->g:Lp9/c;

    .line 6
    .line 7
    iget-object v3, p0, LN81/c$a;->e:Lm8/a;

    .line 8
    .line 9
    iget-object v4, p0, LN81/c$a;->h:LHX0/e;

    .line 10
    .line 11
    iget-object v5, p0, LN81/c$a;->i:LwX0/a;

    .line 12
    .line 13
    invoke-virtual {p0}, LN81/c$a;->J()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/c;

    .line 14
    .line 15
    .line 16
    move-result-object v6

    .line 17
    invoke-virtual {p0}, LN81/c$a;->L()LR81/z;

    .line 18
    .line 19
    .line 20
    move-result-object v7

    .line 21
    invoke-virtual {p0}, LN81/c$a;->C()LR81/n;

    .line 22
    .line 23
    .line 24
    move-result-object v8

    .line 25
    iget-object v9, p0, LN81/c$a;->k:LwX0/C;

    .line 26
    .line 27
    iget-object v10, p0, LN81/c$a;->l:Lorg/xplatform/aggregator/api/navigation/a;

    .line 28
    .line 29
    iget-object v11, p0, LN81/c$a;->m:Lorg/xbet/analytics/domain/scope/E;

    .line 30
    .line 31
    iget-object v12, p0, LN81/c$a;->j:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 32
    .line 33
    iget-object v13, p0, LN81/c$a;->n:Lp9/g;

    .line 34
    .line 35
    invoke-virtual {p0}, LN81/c$a;->D()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;

    .line 36
    .line 37
    .line 38
    move-result-object v14

    .line 39
    invoke-direct/range {v0 .. v14}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetMyAggregatorViewModelDelegateImpl;-><init>(LwX0/c;Lp9/c;Lm8/a;LHX0/e;LwX0/a;LC81/f;LR81/z;LR81/n;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;Lorg/xbet/remoteconfig/domain/usecases/i;Lp9/g;Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;)V

    .line 40
    .line 41
    .line 42
    return-object v0
.end method

.method public final p()Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetPromoViewModelDelegateImpl;
    .locals 15

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetPromoViewModelDelegateImpl;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->f:LwX0/c;

    .line 4
    .line 5
    iget-object v2, p0, LN81/c$a;->g:Lp9/c;

    .line 6
    .line 7
    iget-object v3, p0, LN81/c$a;->e:Lm8/a;

    .line 8
    .line 9
    iget-object v4, p0, LN81/c$a;->h:LHX0/e;

    .line 10
    .line 11
    iget-object v5, p0, LN81/c$a;->i:LwX0/a;

    .line 12
    .line 13
    invoke-virtual {p0}, LN81/c$a;->J()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/c;

    .line 14
    .line 15
    .line 16
    move-result-object v6

    .line 17
    invoke-virtual {p0}, LN81/c$a;->L()LR81/z;

    .line 18
    .line 19
    .line 20
    move-result-object v7

    .line 21
    invoke-virtual {p0}, LN81/c$a;->C()LR81/n;

    .line 22
    .line 23
    .line 24
    move-result-object v8

    .line 25
    iget-object v9, p0, LN81/c$a;->k:LwX0/C;

    .line 26
    .line 27
    iget-object v10, p0, LN81/c$a;->l:Lorg/xplatform/aggregator/api/navigation/a;

    .line 28
    .line 29
    iget-object v11, p0, LN81/c$a;->m:Lorg/xbet/analytics/domain/scope/E;

    .line 30
    .line 31
    iget-object v12, p0, LN81/c$a;->j:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 32
    .line 33
    iget-object v13, p0, LN81/c$a;->n:Lp9/g;

    .line 34
    .line 35
    invoke-virtual {p0}, LN81/c$a;->D()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;

    .line 36
    .line 37
    .line 38
    move-result-object v14

    .line 39
    invoke-direct/range {v0 .. v14}, Lorg/xplatform/aggregator/daily_tasks/impl/presentation/delegates/DailyTaskWidgetPromoViewModelDelegateImpl;-><init>(LwX0/c;Lp9/c;Lm8/a;LHX0/e;LwX0/a;LC81/f;LR81/z;LR81/n;LwX0/C;Lorg/xplatform/aggregator/api/navigation/a;Lorg/xbet/analytics/domain/scope/E;Lorg/xbet/remoteconfig/domain/usecases/i;Lp9/g;Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetDailyTasksCurrentWrapperScenario;)V

    .line 40
    .line 41
    .line 42
    return-object v0
.end method

.method public final q()LJ81/c;
    .locals 2

    .line 1
    new-instance v0, LJ81/c;

    .line 2
    .line 3
    iget-object v1, p0, LN81/c$a;->b:Lf8/g;

    .line 4
    .line 5
    invoke-direct {v0, v1}, LJ81/c;-><init>(Lf8/g;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public final r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;
    .locals 5

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->q()LJ81/c;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    iget-object v2, p0, LN81/c$a;->c:Lc8/h;

    .line 8
    .line 9
    iget-object v3, p0, LN81/c$a;->d:LJ81/a;

    .line 10
    .line 11
    iget-object v4, p0, LN81/c$a;->e:Lm8/a;

    .line 12
    .line 13
    invoke-direct {v0, v1, v2, v3, v4}, Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;-><init>(LJ81/c;Lc8/h;LJ81/a;Lm8/a;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method

.method public final s()Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetActiveDailyTaskScenarioImpl;
    .locals 4

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetActiveDailyTaskScenarioImpl;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->H()LR81/w;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {p0}, LN81/c$a;->x()LR81/i;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {p0}, LN81/c$a;->t()LR81/c;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-direct {v0, v1, v2, v3}, Lorg/xplatform/aggregator/daily_tasks/impl/domain/scenario/GetActiveDailyTaskScenarioImpl;-><init>(LR81/w;LR81/i;LR81/c;)V

    .line 16
    .line 17
    .line 18
    return-object v0
.end method

.method public final t()LR81/c;
    .locals 2

    .line 1
    new-instance v0, LR81/c;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/c;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final u()LR81/e;
    .locals 2

    .line 1
    new-instance v0, LR81/e;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/e;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final v()LR81/f;
    .locals 2

    .line 1
    new-instance v0, LR81/f;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/f;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final w()LR81/h;
    .locals 2

    .line 1
    new-instance v0, LR81/h;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/h;-><init>(LQ81/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final x()LR81/i;
    .locals 2

    .line 1
    new-instance v0, LR81/i;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/i;-><init>(LQ81/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final y()LR81/j;
    .locals 2

    .line 1
    new-instance v0, LR81/j;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->r()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/DailyTasksRepositoryImpl;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/j;-><init>(LQ81/a;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method

.method public final z()LR81/k;
    .locals 2

    .line 1
    new-instance v0, LR81/k;

    .line 2
    .line 3
    invoke-virtual {p0}, LN81/c$a;->M()Lorg/xplatform/aggregator/daily_tasks/impl/data/repository/b;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, LR81/k;-><init>(LQ81/b;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
