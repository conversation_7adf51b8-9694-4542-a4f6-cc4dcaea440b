.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\u001a\u001b\u0010\u0003\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0003\u0010\u0004\u001a\u001b\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00020\u00010\u0000H\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0004\u00a8\u0006\u0006"
    }
    d2 = {
        "LA4/c;",
        "",
        "LVX0/i;",
        "n",
        "()LA4/c;",
        "i",
        "impl_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->p(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/f;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->j(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->r(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->q(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->m(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/g;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->o(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/g;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->l(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic h(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt;->k(LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final i()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/p;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/p;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/q;

    .line 7
    .line 8
    invoke-direct {v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/q;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerPromoDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerPromoDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerPromoDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerPromoDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final j(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/f;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/f;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/f;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final k(LB4/a;)Lkotlin/Unit;
    .locals 4

    .line 1
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    sget v1, Lpb/d;->isTablet:I

    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getBoolean(I)Z

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    if-eqz v0, :cond_0

    .line 16
    .line 17
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    sget v1, Lpb/f;->shimmer_size_190:I

    .line 26
    .line 27
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    goto :goto_0

    .line 32
    :cond_0
    sget-object v0, Lorg/xbet/ui_common/utils/g;->a:Lorg/xbet/ui_common/utils/g;

    .line 33
    .line 34
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/utils/g;->y(Landroid/content/Context;)Z

    .line 39
    .line 40
    .line 41
    move-result v0

    .line 42
    if-eqz v0, :cond_1

    .line 43
    .line 44
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    sget v1, Lpb/f;->shimmer_size_216:I

    .line 53
    .line 54
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 55
    .line 56
    .line 57
    move-result v0

    .line 58
    goto :goto_0

    .line 59
    :cond_1
    invoke-virtual {p0}, LB4/a;->g()Landroid/content/Context;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    sget v1, Lpb/f;->shimmer_size_250:I

    .line 68
    .line 69
    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 70
    .line 71
    .line 72
    move-result v0

    .line 73
    :goto_0
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 74
    .line 75
    .line 76
    move-result-object v1

    .line 77
    check-cast v1, LIb1/f;

    .line 78
    .line 79
    iget-object v1, v1, LIb1/f;->b:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 80
    .line 81
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 82
    .line 83
    .line 84
    move-result-object v2

    .line 85
    const-string v3, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 86
    .line 87
    if-eqz v2, :cond_3

    .line 88
    .line 89
    iput v0, v2, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 90
    .line 91
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 92
    .line 93
    .line 94
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 95
    .line 96
    .line 97
    move-result-object v1

    .line 98
    check-cast v1, LIb1/f;

    .line 99
    .line 100
    iget-object v1, v1, LIb1/f;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 101
    .line 102
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 103
    .line 104
    .line 105
    move-result-object v2

    .line 106
    if-eqz v2, :cond_2

    .line 107
    .line 108
    iput v0, v2, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 109
    .line 110
    invoke-virtual {v1, v2}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 111
    .line 112
    .line 113
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/v;

    .line 114
    .line 115
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/v;-><init>(LB4/a;)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {p0, v0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 119
    .line 120
    .line 121
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/w;

    .line 122
    .line 123
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/w;-><init>(LB4/a;)V

    .line 124
    .line 125
    .line 126
    invoke-virtual {p0, v0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 127
    .line 128
    .line 129
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 130
    .line 131
    return-object p0

    .line 132
    :cond_2
    new-instance p0, Ljava/lang/NullPointerException;

    .line 133
    .line 134
    invoke-direct {p0, v3}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 135
    .line 136
    .line 137
    throw p0

    .line 138
    :cond_3
    new-instance p0, Ljava/lang/NullPointerException;

    .line 139
    .line 140
    invoke-direct {p0, v3}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 141
    .line 142
    .line 143
    throw p0
.end method

.method public static final l(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LIb1/f;

    .line 6
    .line 7
    invoke-virtual {p0}, LIb1/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final m(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LIb1/f;

    .line 6
    .line 7
    invoke-virtual {p0}, LIb1/f;->b()Landroidx/constraintlayout/widget/ConstraintLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final n()LA4/c;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "LA4/c<",
            "Ljava/util/List<",
            "LVX0/i;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/r;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/r;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/s;

    .line 7
    .line 8
    invoke-direct {v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/s;-><init>()V

    .line 9
    .line 10
    .line 11
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerProviderDelegate$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerProviderDelegate$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v3, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerProviderDelegate$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicShimmersDelegateKt$getPopularClassicShimmerProviderDelegate$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v4, LB4/b;

    .line 19
    .line 20
    invoke-direct {v4, v0, v2, v1, v3}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v4
.end method

.method public static final o(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LIb1/g;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LIb1/g;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LIb1/g;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final p(LB4/a;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/t;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/t;-><init>(LB4/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, v0}, LB4/a;->r(Lkotlin/jvm/functions/Function0;)V

    .line 7
    .line 8
    .line 9
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/u;

    .line 10
    .line 11
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/u;-><init>(LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, v0}, LB4/a;->s(Lkotlin/jvm/functions/Function0;)V

    .line 15
    .line 16
    .line 17
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 18
    .line 19
    return-object p0
.end method

.method public static final q(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LIb1/g;

    .line 6
    .line 7
    invoke-virtual {p0}, LIb1/g;->b()Landroid/widget/FrameLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final r(LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    check-cast p0, LIb1/g;

    .line 6
    .line 7
    invoke-virtual {p0}, LIb1/g;->b()Landroid/widget/FrameLayout;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method
