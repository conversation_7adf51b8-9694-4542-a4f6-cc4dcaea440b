.class Lcom/google/android/gms/internal/common/zzac;
.super Lcom/google/android/gms/internal/common/zzad;
.source "SourceFile"


# instance fields
.field zza:[Ljava/lang/Object;

.field zzb:I

.field zzc:Z


# direct methods
.method public constructor <init>(I)V
    .locals 0

    invoke-direct {p0}, Lcom/google/android/gms/internal/common/zzad;-><init>()V

    const/4 p1, 0x4

    new-array p1, p1, [Ljava/lang/Object;

    iput-object p1, p0, Lcom/google/android/gms/internal/common/zzac;->zza:[Ljava/lang/Object;

    const/4 p1, 0x0

    iput p1, p0, Lcom/google/android/gms/internal/common/zzac;->zzb:I

    return-void
.end method


# virtual methods
.method public final zza(Ljava/lang/Object;)Lcom/google/android/gms/internal/common/zzac;
    .locals 4

    .line 1
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lcom/google/android/gms/internal/common/zzac;->zza:[Ljava/lang/Object;

    .line 5
    .line 6
    array-length v0, v0

    .line 7
    iget v1, p0, Lcom/google/android/gms/internal/common/zzac;->zzb:I

    .line 8
    .line 9
    add-int/lit8 v2, v1, 0x1

    .line 10
    .line 11
    if-ltz v2, :cond_5

    .line 12
    .line 13
    if-gt v2, v0, :cond_0

    .line 14
    .line 15
    move v3, v0

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    shr-int/lit8 v3, v0, 0x1

    .line 18
    .line 19
    add-int/2addr v3, v0

    .line 20
    add-int/lit8 v3, v3, 0x1

    .line 21
    .line 22
    if-ge v3, v2, :cond_1

    .line 23
    .line 24
    invoke-static {v1}, Ljava/lang/Integer;->highestOneBit(I)I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    add-int v3, v1, v1

    .line 29
    .line 30
    :cond_1
    if-gez v3, :cond_2

    .line 31
    .line 32
    const v3, 0x7fffffff

    .line 33
    .line 34
    .line 35
    :cond_2
    :goto_0
    if-gt v3, v0, :cond_3

    .line 36
    .line 37
    iget-boolean v0, p0, Lcom/google/android/gms/internal/common/zzac;->zzc:Z

    .line 38
    .line 39
    if-eqz v0, :cond_4

    .line 40
    .line 41
    :cond_3
    iget-object v0, p0, Lcom/google/android/gms/internal/common/zzac;->zza:[Ljava/lang/Object;

    .line 42
    .line 43
    invoke-static {v0, v3}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    iput-object v0, p0, Lcom/google/android/gms/internal/common/zzac;->zza:[Ljava/lang/Object;

    .line 48
    .line 49
    const/4 v0, 0x0

    .line 50
    iput-boolean v0, p0, Lcom/google/android/gms/internal/common/zzac;->zzc:Z

    .line 51
    .line 52
    :cond_4
    iget-object v0, p0, Lcom/google/android/gms/internal/common/zzac;->zza:[Ljava/lang/Object;

    .line 53
    .line 54
    iget v1, p0, Lcom/google/android/gms/internal/common/zzac;->zzb:I

    .line 55
    .line 56
    add-int/lit8 v2, v1, 0x1

    .line 57
    .line 58
    iput v2, p0, Lcom/google/android/gms/internal/common/zzac;->zzb:I

    .line 59
    .line 60
    aput-object p1, v0, v1

    .line 61
    .line 62
    return-object p0

    .line 63
    :cond_5
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 64
    .line 65
    const-string v0, "cannot store more than Integer.MAX_VALUE elements"

    .line 66
    .line 67
    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 68
    .line 69
    .line 70
    throw p1
.end method
