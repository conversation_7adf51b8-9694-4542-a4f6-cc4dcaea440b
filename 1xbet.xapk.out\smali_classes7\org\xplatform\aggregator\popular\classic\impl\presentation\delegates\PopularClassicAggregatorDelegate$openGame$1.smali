.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.delegates.PopularClassicAggregatorDelegate$openGame$1"
    f = "PopularClassicAggregatorDelegate.kt"
    l = {
        0xd6,
        0xd9,
        0xde,
        0xfb
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $game:Lorg/xplatform/aggregator/api/model/Game;

.field final synthetic $subcategoryId:I

.field L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
            "Lorg/xplatform/aggregator/api/model/Game;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$subcategoryId:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;

    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$subcategoryId:I

    invoke-direct {p1, v0, v1, v2, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x4

    .line 8
    const/4 v3, 0x3

    .line 9
    const/4 v4, 0x2

    .line 10
    const/4 v5, 0x1

    .line 11
    if-eqz v1, :cond_4

    .line 12
    .line 13
    if-eq v1, v5, :cond_3

    .line 14
    .line 15
    if-eq v1, v4, :cond_2

    .line 16
    .line 17
    if-eq v1, v3, :cond_1

    .line 18
    .line 19
    if-ne v1, v2, :cond_0

    .line 20
    .line 21
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 22
    .line 23
    .line 24
    move-object v6, p0

    .line 25
    goto/16 :goto_6

    .line 26
    .line 27
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 28
    .line 29
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 30
    .line 31
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw p1

    .line 35
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->L$0:Ljava/lang/Object;

    .line 36
    .line 37
    check-cast v1, Ld81/a;

    .line 38
    .line 39
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 40
    .line 41
    .line 42
    goto :goto_3

    .line 43
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 44
    .line 45
    .line 46
    goto :goto_2

    .line 47
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 48
    .line 49
    .line 50
    goto :goto_1

    .line 51
    :cond_4
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 52
    .line 53
    .line 54
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 55
    .line 56
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->f(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/internet/a;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-interface {p1}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput v5, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->label:I

    .line 65
    .line 66
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->N(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    if-ne p1, v0, :cond_5

    .line 71
    .line 72
    :goto_0
    move-object v6, p0

    .line 73
    goto/16 :goto_5

    .line 74
    .line 75
    :cond_5
    :goto_1
    const/4 v1, 0x0

    .line 76
    invoke-static {v1}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 77
    .line 78
    .line 79
    move-result-object v1

    .line 80
    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 81
    .line 82
    .line 83
    move-result p1

    .line 84
    if-nez p1, :cond_e

    .line 85
    .line 86
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 87
    .line 88
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->g(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Le81/b;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 93
    .line 94
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 95
    .line 96
    .line 97
    move-result-wide v5

    .line 98
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 99
    .line 100
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/Game;->getNeedTransfer()Z

    .line 101
    .line 102
    .line 103
    move-result v1

    .line 104
    iput v4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->label:I

    .line 105
    .line 106
    invoke-interface {p1, v5, v6, v1, p0}, Le81/b;->a(JZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    if-ne p1, v0, :cond_6

    .line 111
    .line 112
    goto :goto_0

    .line 113
    :cond_6
    :goto_2
    move-object v1, p1

    .line 114
    check-cast v1, Ld81/a;

    .line 115
    .line 116
    sget-object p1, Ld81/a$d;->a:Ld81/a$d;

    .line 117
    .line 118
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 119
    .line 120
    .line 121
    move-result p1

    .line 122
    if-nez p1, :cond_7

    .line 123
    .line 124
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 125
    .line 126
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->a(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)LJT/a;

    .line 127
    .line 128
    .line 129
    move-result-object p1

    .line 130
    iget-object v4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 131
    .line 132
    invoke-virtual {v4}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 133
    .line 134
    .line 135
    move-result-wide v4

    .line 136
    iput-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->L$0:Ljava/lang/Object;

    .line 137
    .line 138
    iput v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->label:I

    .line 139
    .line 140
    invoke-interface {p1, v4, v5, p0}, LJT/a;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object p1

    .line 144
    if-ne p1, v0, :cond_7

    .line 145
    .line 146
    goto :goto_0

    .line 147
    :cond_7
    :goto_3
    sget-object p1, Ld81/a$a;->a:Ld81/a$a;

    .line 148
    .line 149
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 150
    .line 151
    .line 152
    move-result p1

    .line 153
    if-eqz p1, :cond_9

    .line 154
    .line 155
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 156
    .line 157
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->i(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 158
    .line 159
    .line 160
    move-result-object p1

    .line 161
    new-instance v0, Lq81/c$b;

    .line 162
    .line 163
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 164
    .line 165
    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$subcategoryId:I

    .line 166
    .line 167
    invoke-direct {v0, v1, v2}, Lq81/c$b;-><init>(Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 168
    .line 169
    .line 170
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 171
    .line 172
    .line 173
    :cond_8
    :goto_4
    move-object v6, p0

    .line 174
    goto/16 :goto_7

    .line 175
    .line 176
    :cond_9
    sget-object p1, Ld81/a$b;->a:Ld81/a$b;

    .line 177
    .line 178
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 179
    .line 180
    .line 181
    move-result p1

    .line 182
    if-eqz p1, :cond_a

    .line 183
    .line 184
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 185
    .line 186
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->i(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 187
    .line 188
    .line 189
    move-result-object p1

    .line 190
    sget-object v0, Lq81/c$a;->a:Lq81/c$a;

    .line 191
    .line 192
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 193
    .line 194
    .line 195
    goto :goto_4

    .line 196
    :cond_a
    sget-object p1, Ld81/a$c;->a:Ld81/a$c;

    .line 197
    .line 198
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 199
    .line 200
    .line 201
    move-result p1

    .line 202
    if-eqz p1, :cond_b

    .line 203
    .line 204
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 205
    .line 206
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->e(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lv81/d;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    invoke-interface {p1}, Lv81/d;->invoke()Z

    .line 211
    .line 212
    .line 213
    move-result p1

    .line 214
    if-eqz p1, :cond_8

    .line 215
    .line 216
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 217
    .line 218
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->l(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lv81/u;

    .line 219
    .line 220
    .line 221
    move-result-object p1

    .line 222
    invoke-interface {p1}, Lv81/u;->invoke()V

    .line 223
    .line 224
    .line 225
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 226
    .line 227
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->i(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 228
    .line 229
    .line 230
    move-result-object p1

    .line 231
    new-instance v0, Lq81/c$c;

    .line 232
    .line 233
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 234
    .line 235
    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$subcategoryId:I

    .line 236
    .line 237
    invoke-direct {v0, v1, v2}, Lq81/c$c;-><init>(Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 238
    .line 239
    .line 240
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 241
    .line 242
    .line 243
    goto :goto_4

    .line 244
    :cond_b
    sget-object p1, Ld81/a$e;->a:Ld81/a$e;

    .line 245
    .line 246
    invoke-static {v1, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 247
    .line 248
    .line 249
    move-result p1

    .line 250
    if-eqz p1, :cond_c

    .line 251
    .line 252
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 253
    .line 254
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->i(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 255
    .line 256
    .line 257
    move-result-object p1

    .line 258
    sget-object v0, Lq81/c$d;->a:Lq81/c$d;

    .line 259
    .line 260
    invoke-virtual {p1, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 261
    .line 262
    .line 263
    goto :goto_4

    .line 264
    :cond_c
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 265
    .line 266
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lak/a;

    .line 267
    .line 268
    .line 269
    move-result-object p1

    .line 270
    invoke-interface {p1}, Lak/a;->F()Lek/d;

    .line 271
    .line 272
    .line 273
    move-result-object v3

    .line 274
    sget-object v4, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 275
    .line 276
    const/4 p1, 0x0

    .line 277
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->L$0:Ljava/lang/Object;

    .line 278
    .line 279
    iput v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->label:I

    .line 280
    .line 281
    const/4 v5, 0x0

    .line 282
    const/4 v7, 0x2

    .line 283
    const/4 v8, 0x0

    .line 284
    move-object v6, p0

    .line 285
    invoke-static/range {v3 .. v8}, Lek/d$a;->a(Lek/d;Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 286
    .line 287
    .line 288
    move-result-object p1

    .line 289
    if-ne p1, v0, :cond_d

    .line 290
    .line 291
    :goto_5
    return-object v0

    .line 292
    :cond_d
    :goto_6
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 293
    .line 294
    iget-object v0, v6, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 295
    .line 296
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->c(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;)Lak/a;

    .line 297
    .line 298
    .line 299
    move-result-object v0

    .line 300
    invoke-interface {v0}, Lak/a;->m()Lek/f;

    .line 301
    .line 302
    .line 303
    move-result-object v0

    .line 304
    sget-object v1, Lorg/xbet/balance/model/BalanceScreenType;->AGGREGATOR:Lorg/xbet/balance/model/BalanceScreenType;

    .line 305
    .line 306
    invoke-interface {v0, v1, p1}, Lek/f;->a(Lorg/xbet/balance/model/BalanceScreenType;Lorg/xbet/balance/model/BalanceModel;)V

    .line 307
    .line 308
    .line 309
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 310
    .line 311
    .line 312
    move-result-wide v0

    .line 313
    invoke-static {v0, v1}, LHc/a;->f(J)Ljava/lang/Long;

    .line 314
    .line 315
    .line 316
    move-result-object p1

    .line 317
    iget-object v0, v6, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 318
    .line 319
    iget-object v1, v6, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$game:Lorg/xplatform/aggregator/api/model/Game;

    .line 320
    .line 321
    iget v2, v6, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate$openGame$1;->$subcategoryId:I

    .line 322
    .line 323
    invoke-virtual {p1}, Ljava/lang/Number;->longValue()J

    .line 324
    .line 325
    .line 326
    move-result-wide v3

    .line 327
    invoke-static {v0, v1, v3, v4, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->n(Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/api/model/Game;JI)V

    .line 328
    .line 329
    .line 330
    :goto_7
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 331
    .line 332
    return-object p1

    .line 333
    :cond_e
    move-object v6, p0

    .line 334
    new-instance p1, Ljava/net/UnknownHostException;

    .line 335
    .line 336
    invoke-direct {p1}, Ljava/net/UnknownHostException;-><init>()V

    .line 337
    .line 338
    .line 339
    throw p1
.end method
