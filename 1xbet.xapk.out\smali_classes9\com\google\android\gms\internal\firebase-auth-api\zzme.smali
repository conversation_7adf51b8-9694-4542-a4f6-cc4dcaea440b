.class public final Lcom/google/android/gms/internal/firebase-auth-api/zzme;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field private static final zza:[[B


# direct methods
.method static constructor <clinit>()V
    .locals 12

    .line 1
    const/4 v0, 0x0

    .line 2
    const/16 v1, 0x20

    .line 3
    .line 4
    new-array v2, v1, [B

    .line 5
    .line 6
    fill-array-data v2, :array_0

    .line 7
    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    new-array v4, v1, [B

    .line 11
    .line 12
    fill-array-data v4, :array_1

    .line 13
    .line 14
    .line 15
    const/4 v5, 0x5

    .line 16
    new-array v6, v1, [B

    .line 17
    .line 18
    fill-array-data v6, :array_2

    .line 19
    .line 20
    .line 21
    const/4 v7, 0x4

    .line 22
    new-array v8, v1, [B

    .line 23
    .line 24
    fill-array-data v8, :array_3

    .line 25
    .line 26
    .line 27
    new-array v9, v1, [B

    .line 28
    .line 29
    fill-array-data v9, :array_4

    .line 30
    .line 31
    .line 32
    new-array v10, v1, [B

    .line 33
    .line 34
    fill-array-data v10, :array_5

    .line 35
    .line 36
    .line 37
    new-array v1, v1, [B

    .line 38
    .line 39
    fill-array-data v1, :array_6

    .line 40
    .line 41
    .line 42
    const/4 v11, 0x7

    .line 43
    new-array v11, v11, [[B

    .line 44
    .line 45
    aput-object v2, v11, v0

    .line 46
    .line 47
    aput-object v4, v11, v3

    .line 48
    .line 49
    const/4 v0, 0x2

    .line 50
    aput-object v6, v11, v0

    .line 51
    .line 52
    const/4 v0, 0x3

    .line 53
    aput-object v8, v11, v0

    .line 54
    .line 55
    aput-object v9, v11, v7

    .line 56
    .line 57
    aput-object v10, v11, v5

    .line 58
    .line 59
    const/4 v0, 0x6

    .line 60
    aput-object v1, v11, v0

    .line 61
    .line 62
    sput-object v11, Lcom/google/android/gms/internal/firebase-auth-api/zzme;->zza:[[B

    .line 63
    .line 64
    return-void

    .line 65
    :array_0
    .array-data 1
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
    .end array-data

    .line 66
    .line 67
    .line 68
    .line 69
    .line 70
    .line 71
    .line 72
    .line 73
    :array_1
    .array-data 1
        0x1t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
        0x0t
    .end array-data

    :array_2
    .array-data 1
        -0x20t
        -0x15t
        0x7at
        0x7ct
        0x3bt
        0x41t
        -0x48t
        -0x52t
        0x16t
        0x56t
        -0x1dt
        -0x6t
        -0xft
        -0x61t
        -0x3ct
        0x6at
        -0x26t
        0x9t
        -0x73t
        -0x15t
        -0x64t
        0x32t
        -0x4ft
        -0x3t
        -0x7at
        0x62t
        0x5t
        0x16t
        0x5ft
        0x49t
        -0x48t
        0x0t
    .end array-data

    :array_3
    .array-data 1
        0x5ft
        -0x64t
        -0x6bt
        -0x44t
        -0x5dt
        0x50t
        -0x74t
        0x24t
        -0x4ft
        -0x30t
        -0x4ft
        0x55t
        -0x64t
        -0x7dt
        -0x11t
        0x5bt
        0x4t
        0x44t
        0x5ct
        -0x3ct
        0x58t
        0x1ct
        -0x72t
        -0x7at
        -0x28t
        0x22t
        0x4et
        -0x23t
        -0x30t
        -0x61t
        0x11t
        0x57t
    .end array-data

    :array_4
    .array-data 1
        -0x14t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        0x7ft
    .end array-data

    :array_5
    .array-data 1
        -0x13t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        0x7ft
    .end array-data

    :array_6
    .array-data 1
        -0x12t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        -0x1t
        0x7ft
    .end array-data
.end method

.method public static zza([J[B[B)V
    .locals 24
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/InvalidKeyException;
        }
    .end annotation

    move-object/from16 v1, p2

    .line 1
    array-length v2, v1

    const/16 v3, 0x20

    if-ne v2, v3, :cond_5

    .line 2
    array-length v2, v1

    invoke-static {v1, v2}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object v2

    const/16 v4, 0x1f

    .line 3
    aget-byte v5, v2, v4

    and-int/lit8 v5, v5, 0x7f

    int-to-byte v5, v5

    aput-byte v5, v2, v4

    const/4 v4, 0x0

    const/4 v5, 0x0

    .line 4
    :goto_0
    sget-object v6, Lcom/google/android/gms/internal/firebase-auth-api/zzme;->zza:[[B

    array-length v7, v6

    if-ge v5, v7, :cond_1

    .line 5
    aget-object v7, v6, v5

    .line 6
    invoke-static {v7, v2}, Ljava/security/MessageDigest;->isEqual([B[B)Z

    move-result v7

    if-nez v7, :cond_0

    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 7
    :cond_0
    new-instance v0, Ljava/security/InvalidKeyException;

    aget-object v1, v6, v5

    invoke-static {v1}, Lcom/google/android/gms/internal/firebase-auth-api/zzxh;->zza([B)Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "Banned public key: "

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/security/InvalidKeyException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 8
    :cond_1
    invoke-static {v2}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([B)[J

    move-result-object v2

    const/16 v5, 0x13

    .line 9
    new-array v6, v5, [J

    .line 10
    new-array v7, v5, [J

    const-wide/16 v8, 0x1

    .line 11
    aput-wide v8, v7, v4

    .line 12
    new-array v10, v5, [J

    .line 13
    aput-wide v8, v10, v4

    .line 14
    new-array v11, v5, [J

    .line 15
    new-array v12, v5, [J

    .line 16
    new-array v13, v5, [J

    .line 17
    aput-wide v8, v13, v4

    .line 18
    new-array v14, v5, [J

    .line 19
    new-array v15, v5, [J

    .line 20
    aput-wide v8, v15, v4

    const/16 v8, 0xa

    .line 21
    invoke-static {v2, v4, v6, v4, v8}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    const/4 v9, 0x0

    :goto_1
    if-ge v9, v3, :cond_3

    rsub-int/lit8 v16, v9, 0x1f

    .line 22
    aget-byte v3, p1, v16

    and-int/lit16 v3, v3, 0xff

    :goto_2
    const/16 v5, 0x8

    if-ge v4, v5, :cond_2

    rsub-int/lit8 v5, v4, 0x7

    shr-int v5, v3, v5

    and-int/lit8 v5, v5, 0x1

    .line 23
    invoke-static {v10, v6, v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzme;->zza([J[JI)V

    .line 24
    invoke-static {v11, v7, v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzme;->zza([J[JI)V

    .line 25
    invoke-static {v10, v8}, Ljava/util/Arrays;->copyOf([JI)[J

    move-result-object v1

    move/from16 v17, v3

    const/16 v8, 0x13

    .line 26
    new-array v3, v8, [J

    move/from16 v18, v4

    .line 27
    new-array v4, v8, [J

    move/from16 v19, v9

    .line 28
    new-array v9, v8, [J

    .line 29
    new-array v0, v8, [J

    move/from16 v20, v5

    .line 30
    new-array v5, v8, [J

    move-object/from16 v21, v15

    .line 31
    new-array v15, v8, [J

    move-object/from16 v22, v3

    .line 32
    new-array v3, v8, [J

    .line 33
    invoke-static {v10, v11}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J)V

    .line 34
    invoke-static {v11, v1}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J[J)V

    const/16 v1, 0xa

    .line 35
    invoke-static {v6, v1}, Ljava/util/Arrays;->copyOf([JI)[J

    move-result-object v8

    .line 36
    invoke-static {v6, v7}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J)V

    .line 37
    invoke-static {v7, v8}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J[J)V

    .line 38
    invoke-static {v0, v6, v11}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J[J)V

    .line 39
    invoke-static {v5, v10, v7}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J[J)V

    .line 40
    invoke-static {v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J)V

    .line 41
    invoke-static {v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    .line 42
    invoke-static {v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J)V

    .line 43
    invoke-static {v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    move-object/from16 v23, v6

    const/4 v6, 0x0

    .line 44
    invoke-static {v0, v6, v8, v6, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 45
    invoke-static {v0, v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J)V

    .line 46
    invoke-static {v5, v8}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J[J)V

    .line 47
    invoke-static {v3, v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J)V

    .line 48
    invoke-static {v15, v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J)V

    .line 49
    invoke-static {v5, v15, v2}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J[J)V

    .line 50
    invoke-static {v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J)V

    .line 51
    invoke-static {v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    .line 52
    invoke-static {v3, v6, v12, v6, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 53
    invoke-static {v5, v6, v13, v6, v1}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 54
    invoke-static {v4, v10}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J)V

    .line 55
    invoke-static {v9, v11}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J)V

    .line 56
    invoke-static {v14, v4, v9}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J[J)V

    .line 57
    invoke-static {v14}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J)V

    .line 58
    invoke-static {v14}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    .line 59
    invoke-static {v9, v4}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J[J)V

    const/16 v0, 0x12

    const-wide/16 v5, 0x0

    move-object/from16 v3, v22

    .line 60
    invoke-static {v3, v1, v0, v5, v6}, Ljava/util/Arrays;->fill([JIIJ)V

    const-wide/32 v0, 0x1db41

    .line 61
    invoke-static {v3, v9, v0, v1}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[JJ)V

    .line 62
    invoke-static {v3}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    .line 63
    invoke-static {v3, v4}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J)V

    move-object/from16 v15, v21

    .line 64
    invoke-static {v15, v9, v3}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J[J)V

    .line 65
    invoke-static {v15}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J)V

    .line 66
    invoke-static {v15}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    move/from16 v0, v20

    .line 67
    invoke-static {v14, v12, v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzme;->zza([J[JI)V

    .line 68
    invoke-static {v15, v13, v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzme;->zza([J[JI)V

    add-int/lit8 v4, v18, 0x1

    move-object v1, v13

    move-object v13, v7

    move-object v7, v1

    move-object v1, v14

    move-object v14, v10

    move-object v10, v1

    move-object v1, v15

    move-object v15, v11

    move-object v11, v1

    move-object/from16 v1, p2

    move-object v6, v12

    move/from16 v3, v17

    move/from16 v9, v19

    move-object/from16 v12, v23

    const/16 v8, 0xa

    goto/16 :goto_2

    :cond_2
    move-object/from16 v23, v6

    move/from16 v19, v9

    add-int/lit8 v9, v19, 0x1

    move-object/from16 v1, p2

    const/16 v3, 0x20

    const/4 v4, 0x0

    const/16 v5, 0x13

    const/16 v8, 0xa

    goto/16 :goto_1

    :cond_3
    const/16 v1, 0xa

    .line 69
    new-array v0, v1, [J

    .line 70
    invoke-static {v0, v11}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J)V

    move-object/from16 v3, p0

    .line 71
    invoke-static {v3, v10, v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    .line 72
    new-array v0, v1, [J

    .line 73
    new-array v4, v1, [J

    const/16 v5, 0xb

    .line 74
    new-array v8, v5, [J

    .line 75
    new-array v9, v5, [J

    .line 76
    new-array v5, v5, [J

    .line 77
    invoke-static {v0, v2, v3}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    .line 78
    invoke-static {v4, v2, v3}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J[J)V

    .line 79
    new-array v1, v1, [J

    const-wide/32 v2, 0x76d06

    const/16 v16, 0x0

    .line 80
    aput-wide v2, v1, v16

    .line 81
    invoke-static {v9, v4, v1}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J[J)V

    .line 82
    invoke-static {v9, v9, v7}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    .line 83
    invoke-static {v9, v6}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J)V

    .line 84
    invoke-static {v9, v9, v0}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    .line 85
    invoke-static {v9, v9, v6}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    const-wide/16 v1, 0x4

    .line 86
    invoke-static {v8, v9, v1, v2}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[JJ)V

    .line 87
    invoke-static {v8}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J)V

    .line 88
    invoke-static {v9, v0, v7}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    .line 89
    invoke-static {v9, v9, v7}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J[J[J)V

    .line 90
    invoke-static {v5, v4, v6}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zza([J[J[J)V

    .line 91
    invoke-static {v9, v9, v5}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzd([J[J[J)V

    .line 92
    invoke-static {v9, v9}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzb([J[J)V

    .line 93
    invoke-static {v8}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J)[B

    move-result-object v0

    invoke-static {v9}, Lcom/google/android/gms/internal/firebase-auth-api/zzmj;->zzc([J)[B

    move-result-object v1

    .line 94
    invoke-static {v0, v1}, Ljava/security/MessageDigest;->isEqual([B[B)Z

    move-result v0

    if-eqz v0, :cond_4

    return-void

    .line 95
    :cond_4
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 96
    invoke-static/range {p2 .. p2}, Lcom/google/android/gms/internal/firebase-auth-api/zzxh;->zza([B)Ljava/lang/String;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    const-string v3, "Arithmetic error in curve multiplication with the public key: "

    invoke-direct {v2, v3}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 97
    :cond_5
    new-instance v0, Ljava/security/InvalidKeyException;

    const-string v1, "Public key length is not 32-byte"

    invoke-direct {v0, v1}, Ljava/security/InvalidKeyException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private static zza([J[JI)V
    .locals 6

    neg-int p2, p2

    const/4 v0, 0x0

    :goto_0
    const/16 v1, 0xa

    if-ge v0, v1, :cond_0

    .line 98
    aget-wide v1, p0, v0

    long-to-int v3, v1

    aget-wide v4, p1, v0

    long-to-int v5, v4

    xor-int/2addr v3, v5

    and-int/2addr v3, p2

    long-to-int v2, v1

    xor-int v1, v2, v3

    int-to-long v1, v1

    .line 99
    aput-wide v1, p0, v0

    .line 100
    aget-wide v1, p1, v0

    long-to-int v2, v1

    xor-int v1, v2, v3

    int-to-long v1, v1

    aput-wide v1, p1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method
