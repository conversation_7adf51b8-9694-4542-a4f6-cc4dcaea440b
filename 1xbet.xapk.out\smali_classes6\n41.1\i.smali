.class public final synthetic Ln41/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Landroid/graphics/drawable/Drawable;

    check-cast p2, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;

    invoke-static {p1, p2}, Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;->b(Landroid/graphics/drawable/Drawable;Lorg/xbet/uikit_web_games/game_collection/GameCollectionItem;)Z

    move-result p1

    invoke-static {p1}, Ljava/lang/Bo<PERSON>an;->valueOf(Z)Ljava/lang/<PERSON>an;

    move-result-object p1

    return-object p1
.end method
