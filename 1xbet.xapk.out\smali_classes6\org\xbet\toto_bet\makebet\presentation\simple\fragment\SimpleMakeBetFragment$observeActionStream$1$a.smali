.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0008\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0010\u0003\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0001\u0010\u0002"
    }
    d2 = {
        "",
        "run",
        "()V",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field public final synthetic a:Landroid/view/View;

.field public final synthetic b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

.field public final synthetic c:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;


# direct methods
.method public constructor <init>(Landroid/view/View;Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;)V
    .locals 0

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;->a:Landroid/view/View;

    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;->c:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;->c:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a;

    .line 10
    .line 11
    check-cast v1, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$h;

    .line 12
    .line 13
    invoke-virtual {v1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel$a$h;->a()Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x2

    .line 19
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setText$default(Lorg/xbet/ui_common/viewcomponents/views/StepInputView;Ljava/lang/String;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment$observeActionStream$1$a;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;

    .line 23
    .line 24
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;->G2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetFragment;)LOU0/g;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    iget-object v0, v0, LOU0/g;->j:Lorg/xbet/ui_common/viewcomponents/views/StepInputView;

    .line 29
    .line 30
    const/4 v1, 0x1

    .line 31
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/StepInputView;->setActionsEnabled(Z)V

    .line 32
    .line 33
    .line 34
    return-void
.end method
