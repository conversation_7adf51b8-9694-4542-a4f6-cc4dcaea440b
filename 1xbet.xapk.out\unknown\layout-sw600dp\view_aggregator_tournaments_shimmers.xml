<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/space_72"
    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/space_8"
        android:theme="?attr/uikitTheme">

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/view1"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_420"
            android:layout_marginEnd="@dimen/space_4"
            android:radius="@dimen/radius_16"
            app:layout_constraintEnd_toStartOf="@id/view2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/view2"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_420"
            android:layout_marginStart="@dimen/space_4"
            android:radius="@dimen/radius_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/view1"
            app:layout_constraintTop_toTopOf="parent" />

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/view3"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_420"
            android:layout_marginTop="@dimen/space_8"
            android:layout_marginEnd="@dimen/space_4"
            android:radius="@dimen/radius_16"
            app:layout_constraintEnd_toStartOf="@id/view4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view1" />

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/view4"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_420"
            android:layout_marginStart="@dimen/space_4"
            android:radius="@dimen/radius_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/view3"
            app:layout_constraintTop_toTopOf="@id/view3" />

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/view5"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_420"
            android:layout_marginTop="@dimen/space_8"
            android:layout_marginEnd="@dimen/space_4"
            android:radius="@dimen/radius_16"
            app:layout_constraintEnd_toStartOf="@id/view6"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view3" />

        <org.xbet.uikit.components.shimmer.ShimmerView
            android:id="@+id/view6"
            style="@style/Widget.Shimmer.Primary"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_420"
            android:layout_marginStart="@dimen/space_4"
            android:radius="@dimen/radius_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/view5"
            app:layout_constraintTop_toTopOf="@id/view5" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>