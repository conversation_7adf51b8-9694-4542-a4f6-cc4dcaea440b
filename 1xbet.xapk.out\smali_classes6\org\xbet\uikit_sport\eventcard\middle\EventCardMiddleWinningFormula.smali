.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\n\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 &2\u00020\u00012\u00020\u0002:\u0001#B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u000e\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u000e\u0010\u0010J\u0017\u0010\u0011\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0011\u0010\u000fJ\u0017\u0010\u0011\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0011\u0010\u0010J\u0017\u0010\u0012\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0012\u0010\u000fJ\u0017\u0010\u0012\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0012\u0010\u0010J\u0017\u0010\u0013\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0013\u0010\u000fJ\u0017\u0010\u0013\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0013\u0010\u0010J\u0017\u0010\u0014\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0014\u0010\u000fJ\u0017\u0010\u0014\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0014\u0010\u0010J\u0017\u0010\u0015\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0015\u0010\u000fJ\u0017\u0010\u0015\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0015\u0010\u0010J\u0017\u0010\u0016\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0016\u0010\u000fJ\u0017\u0010\u0016\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0016\u0010\u0010J\u0015\u0010\u0019\u001a\u00020\r2\u0006\u0010\u0018\u001a\u00020\u0017\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ\u0017\u0010\u001b\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u001b\u0010\u000fJ\u0017\u0010\u001b\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001b\u0010\u0010J\u0017\u0010\u001c\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u001c\u0010\u000fJ\u0017\u0010\u001c\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001c\u0010\u0010J\u0017\u0010\u001d\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u001d\u0010\u000fJ\u0017\u0010\u001d\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001d\u0010\u0010J\u0017\u0010\u001e\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u001e\u0010\u000fJ\u0017\u0010\u001e\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001e\u0010\u0010J\u0017\u0010\u001f\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u001f\u0010\u000fJ\u0017\u0010\u001f\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u001f\u0010\u0010J\u0017\u0010 \u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008 \u0010\u000fJ\u0017\u0010 \u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008 \u0010\u0010J\u0017\u0010!\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008!\u0010\u000fJ\u0017\u0010!\u001a\u00020\r2\u0008\u0008\u0001\u0010\u000c\u001a\u00020\u0007\u00a2\u0006\u0004\u0008!\u0010\u0010R\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$\u00a8\u0006\'"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "text",
        "",
        "setFirstPlayerName",
        "(Ljava/lang/CharSequence;)V",
        "(I)V",
        "setSecondPlayerName",
        "setInformation",
        "setFirstFormulaTitle",
        "setFirstFormula",
        "setSecondFormulaTitle",
        "setSecondFormula",
        "Lorg/xbet/uikit_sport/score/a;",
        "scoreModel",
        "setScoreModel",
        "(Lorg/xbet/uikit_sport/score/a;)V",
        "setFirstPlayerFirstAnswer",
        "setFirstPlayerSecondAnswer",
        "setFirstPlayerThirdAnswer",
        "setSecondPlayerFirstAnswer",
        "setSecondPlayerSecondAnswer",
        "setSecondPlayerThirdAnswer",
        "setTimeLeft",
        "LC31/D;",
        "a",
        "LC31/D;",
        "binding",
        "b",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final b:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final c:I


# instance fields
.field public final a:LC31/D;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->b:Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->c:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/D;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/D;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    const/4 p2, 0x0

    .line 7
    invoke-virtual {p0, p2}, Landroid/view/View;->setLayoutDirection(I)V

    .line 8
    iget-object p2, p1, LC31/D;->e:Landroid/widget/TextView;

    const-string p3, "x1"

    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 9
    iget-object p2, p1, LC31/D;->h:Landroid/widget/TextView;

    const-string v0, "x2"

    invoke-virtual {p2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 10
    iget-object p2, p1, LC31/D;->j:Landroid/widget/TextView;

    const-string v1, "x3"

    invoke-virtual {p2, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 11
    iget-object p2, p1, LC31/D;->p:Landroid/widget/TextView;

    invoke-virtual {p2, p3}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 12
    iget-object p2, p1, LC31/D;->s:Landroid/widget/TextView;

    invoke-virtual {p2, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 13
    iget-object p1, p1, LC31/D;->u:Landroid/widget/TextView;

    invoke-virtual {p1, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleWinningFormulaStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final setFirstFormula(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setFirstFormula(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstFormula(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->b:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstFormulaTitle(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setFirstFormulaTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstFormulaTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->c:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerFirstAnswer(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setFirstPlayerFirstAnswer(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerFirstAnswer(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->d:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerName(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setFirstPlayerName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->f:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerSecondAnswer(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setFirstPlayerSecondAnswer(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerSecondAnswer(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->g:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerThirdAnswer(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setFirstPlayerThirdAnswer(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setFirstPlayerThirdAnswer(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->i:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInformation(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setInformation(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setInformation(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->k:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/16 v1, 0x8

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->k:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setScoreModel(Lorg/xbet/uikit_sport/score/a;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_sport/score/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    .line 2
    .line 3
    iget-object v0, v0, LC31/D;->l:Lorg/xbet/uikit_sport/score/SportScore;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/score/SportScore;->setScore(Lorg/xbet/uikit_sport/score/a;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setSecondFormula(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setSecondFormula(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondFormula(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->m:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondFormulaTitle(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setSecondFormulaTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondFormulaTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->n:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerFirstAnswer(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setSecondPlayerSecondAnswer(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerFirstAnswer(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->o:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerName(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setSecondPlayerName(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->q:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerSecondAnswer(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setSecondPlayerSecondAnswer(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerSecondAnswer(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->r:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerThirdAnswer(I)V
    .locals 1

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setSecondPlayerThirdAnswer(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setSecondPlayerThirdAnswer(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->t:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTimeLeft(I)V
    .locals 1

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->setTimeLeft(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTimeLeft(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->v:Landroid/widget/TextView;

    const/4 v1, 0x0

    if-eqz p1, :cond_1

    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-nez v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v2, 0x1

    :goto_1
    if-eqz v2, :cond_2

    const/4 v1, 0x4

    .line 2
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleWinningFormula;->a:LC31/D;

    iget-object v0, v0, LC31/D;->v:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
