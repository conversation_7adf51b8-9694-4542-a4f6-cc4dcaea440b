.class public final enum Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\n\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\u0008\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0004\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0006\u0010\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000c\u00a8\u0006\r"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;",
        "",
        "typeId",
        "",
        "<init>",
        "(Ljava/lang/String;II)V",
        "getTypeId",
        "()I",
        "RECTANGLE_S_WITH_HEADER",
        "CIRCLE_WITH_LABEL",
        "RECTANGLE_S",
        "RECTANGLE_M",
        "RECTANGLE_L",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

.field public static final enum CIRCLE_WITH_LABEL:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

.field public static final enum RECTANGLE_L:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

.field public static final enum RECTANGLE_M:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

.field public static final enum RECTANGLE_S:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

.field public static final enum RECTANGLE_S_WITH_HEADER:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;


# instance fields
.field private final typeId:I


# direct methods
.method static constructor <clinit>()V
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 2
    .line 3
    const-string v1, "RECTANGLE_S_WITH_HEADER"

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x1

    .line 7
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;-><init>(Ljava/lang/String;II)V

    .line 8
    .line 9
    .line 10
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_S_WITH_HEADER:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 11
    .line 12
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 13
    .line 14
    const-string v1, "CIRCLE_WITH_LABEL"

    .line 15
    .line 16
    const/4 v2, 0x2

    .line 17
    invoke-direct {v0, v1, v3, v2}, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;-><init>(Ljava/lang/String;II)V

    .line 18
    .line 19
    .line 20
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->CIRCLE_WITH_LABEL:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 21
    .line 22
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 23
    .line 24
    const-string v1, "RECTANGLE_S"

    .line 25
    .line 26
    const/4 v3, 0x3

    .line 27
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;-><init>(Ljava/lang/String;II)V

    .line 28
    .line 29
    .line 30
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_S:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 31
    .line 32
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 33
    .line 34
    const-string v1, "RECTANGLE_M"

    .line 35
    .line 36
    const/4 v2, 0x4

    .line 37
    invoke-direct {v0, v1, v3, v2}, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;-><init>(Ljava/lang/String;II)V

    .line 38
    .line 39
    .line 40
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_M:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 41
    .line 42
    new-instance v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 43
    .line 44
    const-string v1, "RECTANGLE_L"

    .line 45
    .line 46
    const/4 v3, 0x5

    .line 47
    invoke-direct {v0, v1, v2, v3}, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;-><init>(Ljava/lang/String;II)V

    .line 48
    .line 49
    .line 50
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_L:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 51
    .line 52
    invoke-static {}, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->a()[Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->$VALUES:[Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 57
    .line 58
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    sput-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->$ENTRIES:Lkotlin/enums/a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput p3, p0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->typeId:I

    .line 5
    .line 6
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;
    .locals 3

    .line 1
    const/4 v0, 0x5

    new-array v0, v0, [Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_S_WITH_HEADER:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->CIRCLE_WITH_LABEL:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_S:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_M:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->RECTANGLE_L:Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->$VALUES:[Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getTypeId()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_collection/models/SportsCollectionType;->typeId:I

    .line 2
    .line 3
    return v0
.end method
