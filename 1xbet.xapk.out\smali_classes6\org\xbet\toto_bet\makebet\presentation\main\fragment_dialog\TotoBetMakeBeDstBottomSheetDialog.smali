.class public final Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;
.super Lorg/xbet/uikit/components/bottomsheet/DesignSystemBottomSheet;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xbet/uikit/components/bottomsheet/DesignSystemBottomSheet<",
        "LOU0/x;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u001a\u0008\u0001\u0018\u0000 /2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u00010B\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u000f\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0004J\u000f\u0010\u0007\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0007\u0010\u0004J\u000f\u0010\u0008\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\u0004J\u001b\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00050\tH\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u0013\u0010\u000f\u001a\u00020\u000e*\u00020\rH\u0002\u00a2\u0006\u0004\u0008\u000f\u0010\u0010J\u000f\u0010\u0011\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008\u0011\u0010\u0004J\u000f\u0010\u0012\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008\u0012\u0010\u0004J\u0015\u0010\u0015\u001a\u00020\u00052\u0006\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\u0004\u0008\u0015\u0010\u0016R+\u0010\u001f\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00178B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008\u0019\u0010\u001a\u001a\u0004\u0008\u001b\u0010\u001c\"\u0004\u0008\u001d\u0010\u001eR+\u0010#\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00178B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008 \u0010\u001a\u001a\u0004\u0008!\u0010\u001c\"\u0004\u0008\"\u0010\u001eR+\u0010)\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u00138B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008$\u0010%\u001a\u0004\u0008&\u0010\'\"\u0004\u0008(\u0010\u0016R\u001b\u0010.\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008*\u0010+\u001a\u0004\u0008,\u0010-\u00a8\u00061"
    }
    d2 = {
        "Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;",
        "Lorg/xbet/uikit/components/bottomsheet/DesignSystemBottomSheet;",
        "LOU0/x;",
        "<init>",
        "()V",
        "",
        "Y2",
        "W2",
        "V2",
        "Lkotlin/Function1;",
        "",
        "S2",
        "()Lkotlin/jvm/functions/Function1;",
        "Landroidx/fragment/app/Fragment;",
        "LjW0/a;",
        "P2",
        "(Landroidx/fragment/app/Fragment;)LjW0/a;",
        "A2",
        "z2",
        "",
        "show",
        "C",
        "(Z)V",
        "",
        "<set-?>",
        "k0",
        "LeX0/k;",
        "U2",
        "()Ljava/lang/String;",
        "b3",
        "(Ljava/lang/String;)V",
        "totoName",
        "l0",
        "R2",
        "Z2",
        "outcomeCount",
        "m0",
        "LeX0/a;",
        "X2",
        "()Z",
        "a3",
        "isPromoEnable",
        "n0",
        "LRc/c;",
        "Q2",
        "()LOU0/x;",
        "binding",
        "o0",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic b1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final k1:I

.field public static final o0:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final k0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 8

    .line 1
    new-instance v0, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;

    .line 4
    .line 5
    const-string v2, "totoName"

    .line 6
    .line 7
    const-string v3, "getTotoName()Ljava/lang/String;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "outcomeCount"

    .line 20
    .line 21
    const-string v5, "getOutcomeCount()Ljava/lang/String;"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "isPromoEnable"

    .line 33
    .line 34
    const-string v6, "isPromoEnable()Z"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v3

    .line 43
    new-instance v5, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 44
    .line 45
    const-string v6, "binding"

    .line 46
    .line 47
    const-string v7, "getBinding()Lorg/xbet/toto_bet/impl/databinding/TotoBetMakeBetDsBottomsheetBinding;"

    .line 48
    .line 49
    invoke-direct {v5, v1, v6, v7, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 50
    .line 51
    .line 52
    invoke-static {v5}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    const/4 v5, 0x4

    .line 57
    new-array v5, v5, [Lkotlin/reflect/m;

    .line 58
    .line 59
    aput-object v0, v5, v4

    .line 60
    .line 61
    const/4 v0, 0x1

    .line 62
    aput-object v2, v5, v0

    .line 63
    .line 64
    const/4 v0, 0x2

    .line 65
    aput-object v3, v5, v0

    .line 66
    .line 67
    const/4 v0, 0x3

    .line 68
    aput-object v1, v5, v0

    .line 69
    .line 70
    sput-object v5, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 71
    .line 72
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    invoke-direct {v0, v1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 76
    .line 77
    .line 78
    sput-object v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->o0:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$a;

    .line 79
    .line 80
    const/16 v0, 0x8

    .line 81
    .line 82
    sput v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->k1:I

    .line 83
    .line 84
    return-void
.end method

.method public constructor <init>()V
    .locals 5

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit/components/bottomsheet/DesignSystemBottomSheet;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, LeX0/k;

    .line 5
    .line 6
    const-string v1, "TOTO_NAME"

    .line 7
    .line 8
    const/4 v2, 0x0

    .line 9
    const/4 v3, 0x2

    .line 10
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->k0:LeX0/k;

    .line 14
    .line 15
    new-instance v0, LeX0/k;

    .line 16
    .line 17
    const-string v1, "OUTCOME_COUNT"

    .line 18
    .line 19
    invoke-direct {v0, v1, v2, v3, v2}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 20
    .line 21
    .line 22
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->l0:LeX0/k;

    .line 23
    .line 24
    new-instance v0, LeX0/a;

    .line 25
    .line 26
    const-string v1, "IS_PROMO_ENABLE"

    .line 27
    .line 28
    const/4 v4, 0x0

    .line 29
    invoke-direct {v0, v1, v4, v3, v2}, LeX0/a;-><init>(Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 30
    .line 31
    .line 32
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->m0:LeX0/a;

    .line 33
    .line 34
    sget-object v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$binding$2;->INSTANCE:Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$binding$2;

    .line 35
    .line 36
    invoke-static {p0, v0}, LLX0/j;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    iput-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->n0:LRc/c;

    .line 41
    .line 42
    return-void
.end method

.method public static synthetic K2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->T2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic L2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;)Lkotlin/jvm/functions/Function1;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->S2()Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic M2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Z2(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic N2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;Z)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->a3(Z)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic O2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b3(Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final P2(Landroidx/fragment/app/Fragment;)LjW0/a;
    .locals 1

    .line 1
    invoke-virtual {p1}, Landroidx/fragment/app/Fragment;->getParentFragment()Landroidx/fragment/app/Fragment;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    if-eqz p1, :cond_1

    .line 6
    .line 7
    instance-of v0, p1, LjW0/a;

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    check-cast p1, LjW0/a;

    .line 12
    .line 13
    return-object p1

    .line 14
    :cond_0
    invoke-direct {p0, p1}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->P2(Landroidx/fragment/app/Fragment;)LjW0/a;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    return-object p1

    .line 19
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 20
    .line 21
    const-string v0, "MakeBetLoader not found"

    .line 22
    .line 23
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    throw p1
.end method

.method private final R2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->l0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method private final S2()Lkotlin/jvm/functions/Function1;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Integer;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/a;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/a;-><init>(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final T2(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    iget-object p0, p0, LOU0/x;->k:Landroidx/viewpager2/widget/ViewPager2;

    .line 6
    .line 7
    invoke-virtual {p0, p1}, Landroidx/viewpager2/widget/ViewPager2;->setCurrentItem(I)V

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method private final U2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->k0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method private final V2()V
    .locals 7

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LOU0/x;->g:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 6
    .line 7
    new-instance v2, Lu01/a;

    .line 8
    .line 9
    invoke-direct {v2}, Lu01/a;-><init>()V

    .line 10
    .line 11
    .line 12
    sget v0, Lpb/k;->bet_type_simple:I

    .line 13
    .line 14
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-virtual {v2, v0}, Lu01/a;->d(Ljava/lang/CharSequence;)V

    .line 19
    .line 20
    .line 21
    const/4 v5, 0x6

    .line 22
    const/4 v6, 0x0

    .line 23
    const/4 v3, 0x0

    .line 24
    const/4 v4, 0x0

    .line 25
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->h(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lu01/a;IZILjava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    new-instance v2, Lu01/a;

    .line 29
    .line 30
    invoke-direct {v2}, Lu01/a;-><init>()V

    .line 31
    .line 32
    .line 33
    sget v0, Lpb/k;->bet_type_promo:I

    .line 34
    .line 35
    invoke-virtual {p0, v0}, Landroidx/fragment/app/Fragment;->getString(I)Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-virtual {v2, v0}, Lu01/a;->d(Ljava/lang/CharSequence;)V

    .line 40
    .line 41
    .line 42
    invoke-static/range {v1 .. v6}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->h(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lu01/a;IZILjava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    const/4 v0, 0x0

    .line 46
    invoke-virtual {v1, v0}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setSelectedPosition(I)V

    .line 47
    .line 48
    .line 49
    return-void
.end method

.method private final W2()V
    .locals 2

    .line 1
    new-instance v0, LLW0/b;

    .line 2
    .line 3
    invoke-direct {v0}, LLW0/b;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    iget-object v1, v1, LOU0/x;->k:Landroidx/viewpager2/widget/ViewPager2;

    .line 11
    .line 12
    invoke-virtual {v0, v1}, LLW0/b;->c(Landroidx/viewpager2/widget/ViewPager2;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iget-object v0, v0, LOU0/x;->k:Landroidx/viewpager2/widget/ViewPager2;

    .line 20
    .line 21
    const/4 v1, 0x0

    .line 22
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->setUserInputEnabled(Z)V

    .line 23
    .line 24
    .line 25
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iget-object v0, v0, LOU0/x;->k:Landroidx/viewpager2/widget/ViewPager2;

    .line 30
    .line 31
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$b;

    .line 32
    .line 33
    invoke-direct {v1, p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$b;-><init>(Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {v0, v1}, Landroidx/viewpager2/widget/ViewPager2;->h(Landroidx/viewpager2/widget/ViewPager2$i;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method

.method private final X2()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->m0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/a;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Boolean;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    return v0
.end method

.method private final Y2()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, v0, LOU0/x;->i:Landroid/widget/TextView;

    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->U2()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 12
    .line 13
    .line 14
    iget-object v1, v0, LOU0/x;->e:Landroid/widget/TextView;

    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->R2()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 21
    .line 22
    .line 23
    new-instance v1, Ljava/util/ArrayList;

    .line 24
    .line 25
    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 26
    .line 27
    .line 28
    sget-object v2, Lorg/xbet/toto_bet/makebet/domain/model/TypeTotoBetMakeBet;->SIMPLE:Lorg/xbet/toto_bet/makebet/domain/model/TypeTotoBetMakeBet;

    .line 29
    .line 30
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 31
    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->X2()Z

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    if-eqz v2, :cond_0

    .line 38
    .line 39
    sget-object v2, Lorg/xbet/toto_bet/makebet/domain/model/TypeTotoBetMakeBet;->PROMO:Lorg/xbet/toto_bet/makebet/domain/model/TypeTotoBetMakeBet;

    .line 40
    .line 41
    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 42
    .line 43
    .line 44
    :cond_0
    iget-object v2, v0, LOU0/x;->k:Landroidx/viewpager2/widget/ViewPager2;

    .line 45
    .line 46
    invoke-interface {v1}, Ljava/util/Collection;->size()I

    .line 47
    .line 48
    .line 49
    move-result v3

    .line 50
    invoke-virtual {v2, v3}, Landroidx/viewpager2/widget/ViewPager2;->setOffscreenPageLimit(I)V

    .line 51
    .line 52
    .line 53
    iget-object v0, v0, LOU0/x;->k:Landroidx/viewpager2/widget/ViewPager2;

    .line 54
    .line 55
    new-instance v2, LWU0/a;

    .line 56
    .line 57
    invoke-direct {v2, p0, v1}, LWU0/a;-><init>(Landroidx/fragment/app/Fragment;Ljava/util/List;)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v0, v2}, Landroidx/viewpager2/widget/ViewPager2;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 61
    .line 62
    .line 63
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    iget-object v0, v0, LOU0/x;->g:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 68
    .line 69
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->X2()Z

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    if-eqz v1, :cond_1

    .line 74
    .line 75
    const/4 v1, 0x0

    .line 76
    goto :goto_0

    .line 77
    :cond_1
    const/16 v1, 0x8

    .line 78
    .line 79
    :goto_0
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 80
    .line 81
    .line 82
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->X2()Z

    .line 83
    .line 84
    .line 85
    move-result v0

    .line 86
    if-eqz v0, :cond_2

    .line 87
    .line 88
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->V2()V

    .line 89
    .line 90
    .line 91
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->W2()V

    .line 92
    .line 93
    .line 94
    :cond_2
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    iget-object v0, v0, LOU0/x;->g:Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;

    .line 99
    .line 100
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->S2()Lkotlin/jvm/functions/Function1;

    .line 101
    .line 102
    .line 103
    move-result-object v1

    .line 104
    const/4 v2, 0x1

    .line 105
    const/4 v3, 0x0

    .line 106
    invoke-static {v0, v3, v1, v2, v3}, Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;->setOnSegmentSelectedListener$default(Lorg/xbet/uikit/components/segmentedcontrol/SegmentedGroup;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 107
    .line 108
    .line 109
    return-void
.end method

.method private final Z2(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->l0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final a3(Z)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->m0:LeX0/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/a;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Z)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final b3(Ljava/lang/String;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->k0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1}, LeX0/k;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public A2()V
    .locals 2

    .line 1
    invoke-super {p0}, Lorg/xbet/uikit/components/bottomsheet/DesignSystemBottomSheet;->A2()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroidx/fragment/app/l;->getDialog()Landroid/app/Dialog;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-virtual {v0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-eqz v0, :cond_0

    .line 15
    .line 16
    const/4 v1, 0x2

    .line 17
    invoke-virtual {v0, v1}, Landroid/view/Window;->clearFlags(I)V

    .line 18
    .line 19
    .line 20
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit/components/bottomsheet/DesignSystemBottomSheet;->t2()Lcom/google/android/material/bottomsheet/BottomSheetBehavior;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    if-eqz v0, :cond_1

    .line 25
    .line 26
    const/4 v1, 0x3

    .line 27
    invoke-virtual {v0, v1}, Lcom/google/android/material/bottomsheet/BottomSheetBehavior;->setState(I)V

    .line 28
    .line 29
    .line 30
    :cond_1
    invoke-direct {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Y2()V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final C(Z)V
    .locals 1

    .line 1
    xor-int/lit8 v0, p1, 0x1

    .line 2
    .line 3
    invoke-virtual {p0, v0}, Landroidx/fragment/app/l;->setCancelable(Z)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0, p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->P2(Landroidx/fragment/app/Fragment;)LjW0/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-interface {v0, p1}, LjW0/a;->x(Z)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public Q2()LOU0/x;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->n0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x3

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LOU0/x;

    .line 13
    .line 14
    return-object v0
.end method

.method public bridge synthetic s2()LL2/a;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;->Q2()LOU0/x;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public z2()V
    .locals 3

    .line 1
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    .line 2
    .line 3
    const/16 v1, 0x23

    .line 4
    .line 5
    if-ge v0, v1, :cond_0

    .line 6
    .line 7
    goto :goto_1

    .line 8
    :cond_0
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireView()Landroid/view/View;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    instance-of v1, v0, Landroid/view/View;

    .line 17
    .line 18
    if-eqz v1, :cond_1

    .line 19
    .line 20
    check-cast v0, Landroid/view/View;

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_1
    const/4 v0, 0x0

    .line 24
    :goto_0
    if-eqz v0, :cond_2

    .line 25
    .line 26
    new-instance v1, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$c;

    .line 27
    .line 28
    const/4 v2, 0x0

    .line 29
    invoke-direct {v1, v2, p0}, Lorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog$c;-><init>(ZLorg/xbet/toto_bet/makebet/presentation/main/fragment_dialog/TotoBetMakeBeDstBottomSheetDialog;)V

    .line 30
    .line 31
    .line 32
    invoke-static {v0, v1}, Landroidx/core/view/e0;->H0(Landroid/view/View;Landroidx/core/view/K;)V

    .line 33
    .line 34
    .line 35
    :cond_2
    :goto_1
    return-void
.end method
