.class public final LmV0/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LmV0/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LmV0/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LmV0/a$b$a;,
        LmV0/a$b$b;
    }
.end annotation


# instance fields
.field public A:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tirage/domain/usecase/b;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/toto/domain/usecase/g;",
            ">;"
        }
    .end annotation
.end field

.field public C:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/toto/domain/usecase/e;",
            ">;"
        }
    .end annotation
.end field

.field public D:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public E:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoTirageUdfViewModel;",
            ">;"
        }
    .end annotation
.end field

.field public final a:LTZ0/a;

.field public final b:LzX0/k;

.field public final c:LfX/b;

.field public final d:LmV0/a$b;

.field public e:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public f:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public g:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LSX0/a;",
            ">;"
        }
    .end annotation
.end field

.field public h:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public i:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public j:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LjV0/b;",
            ">;"
        }
    .end annotation
.end field

.field public k:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lc8/h;",
            ">;"
        }
    .end annotation
.end field

.field public l:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tirage/data/repository/TotoBetTirageRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public m:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lfk/l;",
            ">;"
        }
    .end annotation
.end field

.field public n:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/tirage/domain/usecase/GetTotoBetTirageUseCase;",
            ">;"
        }
    .end annotation
.end field

.field public o:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lcom/xbet/onexuser/domain/managers/TokenRefresher;",
            ">;"
        }
    .end annotation
.end field

.field public p:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lf8/g;",
            ">;"
        }
    .end annotation
.end field

.field public q:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVV0/d;",
            ">;"
        }
    .end annotation
.end field

.field public r:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVV0/b;",
            ">;"
        }
    .end annotation
.end field

.field public s:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LVV0/a;",
            ">;"
        }
    .end annotation
.end field

.field public t:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/toto/data/repository/TotoBetRepositoryImpl;",
            ">;"
        }
    .end annotation
.end field

.field public u:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/toto/domain/usecase/s;",
            ">;"
        }
    .end annotation
.end field

.field public v:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "Lorg/xbet/toto_bet/toto/domain/usecase/Z;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public x:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LwX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public y:Lorg/xbet/toto_bet/tirage/presentation/viewmodel/f;

.field public z:Ldagger/internal/h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ldagger/internal/h<",
            "LmV0/f;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p0, p0, LmV0/a$b;->d:LmV0/a$b;

    .line 4
    iput-object p3, p0, LmV0/a$b;->a:LTZ0/a;

    move-object/from16 v0, p18

    .line 5
    iput-object v0, p0, LmV0/a$b;->b:LzX0/k;

    .line 6
    iput-object p12, p0, LmV0/a$b;->c:LfX/b;

    .line 7
    invoke-virtual/range {p0 .. p20}, LmV0/a$b;->c(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)V

    .line 8
    invoke-virtual/range {p0 .. p20}, LmV0/a$b;->d(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)V

    return-void
.end method

.method public synthetic constructor <init>(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;LmV0/b;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p20}, LmV0/a$b;-><init>(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)V

    return-void
.end method


# virtual methods
.method public a(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LmV0/a$b;->e(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;)Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public b(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LmV0/a$b;->f(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;)Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public final c(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)V
    .locals 5

    .line 1
    invoke-static {p4}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p3

    .line 5
    iput-object p3, p0, LmV0/a$b;->e:Ldagger/internal/h;

    .line 6
    .line 7
    invoke-static {p5}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 8
    .line 9
    .line 10
    move-result-object p3

    .line 11
    iput-object p3, p0, LmV0/a$b;->f:Ldagger/internal/h;

    .line 12
    .line 13
    invoke-static {p7}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 14
    .line 15
    .line 16
    move-result-object p3

    .line 17
    iput-object p3, p0, LmV0/a$b;->g:Ldagger/internal/h;

    .line 18
    .line 19
    invoke-static {p8}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 20
    .line 21
    .line 22
    move-result-object p3

    .line 23
    iput-object p3, p0, LmV0/a$b;->h:Ldagger/internal/h;

    .line 24
    .line 25
    new-instance p3, LmV0/a$b$a;

    .line 26
    .line 27
    invoke-direct {p3, p1}, LmV0/a$b$a;-><init>(LQW0/c;)V

    .line 28
    .line 29
    .line 30
    iput-object p3, p0, LmV0/a$b;->i:Ldagger/internal/h;

    .line 31
    .line 32
    invoke-static/range {p19 .. p19}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iput-object p1, p0, LmV0/a$b;->j:Ldagger/internal/h;

    .line 37
    .line 38
    invoke-static/range {p17 .. p17}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    iput-object p1, p0, LmV0/a$b;->k:Ldagger/internal/h;

    .line 43
    .line 44
    iget-object p3, p0, LmV0/a$b;->i:Ldagger/internal/h;

    .line 45
    .line 46
    iget-object p4, p0, LmV0/a$b;->j:Ldagger/internal/h;

    .line 47
    .line 48
    invoke-static {p3, p4, p1}, Lorg/xbet/toto_bet/tirage/data/repository/a;->a(LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/tirage/data/repository/a;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    iput-object p1, p0, LmV0/a$b;->l:Ldagger/internal/h;

    .line 53
    .line 54
    new-instance p1, LmV0/a$b$b;

    .line 55
    .line 56
    invoke-direct {p1, p2}, LmV0/a$b$b;-><init>(Lak/a;)V

    .line 57
    .line 58
    .line 59
    iput-object p1, p0, LmV0/a$b;->m:Ldagger/internal/h;

    .line 60
    .line 61
    iget-object p2, p0, LmV0/a$b;->l:Ldagger/internal/h;

    .line 62
    .line 63
    invoke-static {p2, p1}, Lorg/xbet/toto_bet/tirage/domain/usecase/a;->a(LBc/a;LBc/a;)Lorg/xbet/toto_bet/tirage/domain/usecase/a;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    iput-object p1, p0, LmV0/a$b;->n:Ldagger/internal/h;

    .line 68
    .line 69
    invoke-static/range {p11 .. p11}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    iput-object p1, p0, LmV0/a$b;->o:Ldagger/internal/h;

    .line 74
    .line 75
    invoke-static {p6}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    iput-object p1, p0, LmV0/a$b;->p:Ldagger/internal/h;

    .line 80
    .line 81
    invoke-static {p1}, LVV0/e;->a(LBc/a;)LVV0/e;

    .line 82
    .line 83
    .line 84
    move-result-object p1

    .line 85
    iput-object p1, p0, LmV0/a$b;->q:Ldagger/internal/h;

    .line 86
    .line 87
    invoke-static/range {p14 .. p14}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, LmV0/a$b;->r:Ldagger/internal/h;

    .line 92
    .line 93
    invoke-static/range {p16 .. p16}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput-object p1, p0, LmV0/a$b;->s:Ldagger/internal/h;

    .line 98
    .line 99
    iget-object p2, p0, LmV0/a$b;->i:Ldagger/internal/h;

    .line 100
    .line 101
    iget-object p3, p0, LmV0/a$b;->o:Ldagger/internal/h;

    .line 102
    .line 103
    iget-object p4, p0, LmV0/a$b;->q:Ldagger/internal/h;

    .line 104
    .line 105
    iget-object p5, p0, LmV0/a$b;->r:Ldagger/internal/h;

    .line 106
    .line 107
    iget-object v0, p0, LmV0/a$b;->k:Ldagger/internal/h;

    .line 108
    .line 109
    move-object p6, p1

    .line 110
    move-object p7, v0

    .line 111
    invoke-static/range {p2 .. p7}, Lorg/xbet/toto_bet/toto/data/repository/a;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/toto/data/repository/a;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    iput-object p1, p0, LmV0/a$b;->t:Ldagger/internal/h;

    .line 116
    .line 117
    invoke-static {p1}, Lorg/xbet/toto_bet/toto/domain/usecase/t;->a(LBc/a;)Lorg/xbet/toto_bet/toto/domain/usecase/t;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    iput-object p1, p0, LmV0/a$b;->u:Ldagger/internal/h;

    .line 122
    .line 123
    iget-object p1, p0, LmV0/a$b;->t:Ldagger/internal/h;

    .line 124
    .line 125
    invoke-static {p1}, Lorg/xbet/toto_bet/toto/domain/usecase/a0;->a(LBc/a;)Lorg/xbet/toto_bet/toto/domain/usecase/a0;

    .line 126
    .line 127
    .line 128
    move-result-object p1

    .line 129
    iput-object p1, p0, LmV0/a$b;->v:Ldagger/internal/h;

    .line 130
    .line 131
    invoke-static/range {p15 .. p15}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 132
    .line 133
    .line 134
    move-result-object p1

    .line 135
    iput-object p1, p0, LmV0/a$b;->w:Ldagger/internal/h;

    .line 136
    .line 137
    invoke-static {p9}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 138
    .line 139
    .line 140
    move-result-object p1

    .line 141
    iput-object p1, p0, LmV0/a$b;->x:Ldagger/internal/h;

    .line 142
    .line 143
    iget-object p2, p0, LmV0/a$b;->e:Ldagger/internal/h;

    .line 144
    .line 145
    iget-object p3, p0, LmV0/a$b;->f:Ldagger/internal/h;

    .line 146
    .line 147
    iget-object p4, p0, LmV0/a$b;->g:Ldagger/internal/h;

    .line 148
    .line 149
    iget-object p5, p0, LmV0/a$b;->h:Ldagger/internal/h;

    .line 150
    .line 151
    iget-object v0, p0, LmV0/a$b;->i:Ldagger/internal/h;

    .line 152
    .line 153
    iget-object v1, p0, LmV0/a$b;->n:Ldagger/internal/h;

    .line 154
    .line 155
    iget-object v2, p0, LmV0/a$b;->u:Ldagger/internal/h;

    .line 156
    .line 157
    iget-object v3, p0, LmV0/a$b;->v:Ldagger/internal/h;

    .line 158
    .line 159
    iget-object v4, p0, LmV0/a$b;->w:Ldagger/internal/h;

    .line 160
    .line 161
    move-object/from16 p11, p1

    .line 162
    .line 163
    move-object p6, v0

    .line 164
    move-object p7, v1

    .line 165
    move-object p8, v2

    .line 166
    move-object p9, v3

    .line 167
    move-object p10, v4

    .line 168
    invoke-static/range {p2 .. p11}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/f;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/tirage/presentation/viewmodel/f;

    .line 169
    .line 170
    .line 171
    move-result-object p1

    .line 172
    iput-object p1, p0, LmV0/a$b;->y:Lorg/xbet/toto_bet/tirage/presentation/viewmodel/f;

    .line 173
    .line 174
    invoke-static {p1}, LmV0/g;->c(Lorg/xbet/toto_bet/tirage/presentation/viewmodel/f;)Ldagger/internal/h;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    iput-object p1, p0, LmV0/a$b;->z:Ldagger/internal/h;

    .line 179
    .line 180
    iget-object p1, p0, LmV0/a$b;->l:Ldagger/internal/h;

    .line 181
    .line 182
    invoke-static {p1}, Lorg/xbet/toto_bet/tirage/domain/usecase/c;->a(LBc/a;)Lorg/xbet/toto_bet/tirage/domain/usecase/c;

    .line 183
    .line 184
    .line 185
    move-result-object p1

    .line 186
    iput-object p1, p0, LmV0/a$b;->A:Ldagger/internal/h;

    .line 187
    .line 188
    iget-object p1, p0, LmV0/a$b;->t:Ldagger/internal/h;

    .line 189
    .line 190
    invoke-static {p1}, Lorg/xbet/toto_bet/toto/domain/usecase/h;->a(LBc/a;)Lorg/xbet/toto_bet/toto/domain/usecase/h;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    iput-object p1, p0, LmV0/a$b;->B:Ldagger/internal/h;

    .line 195
    .line 196
    iget-object p1, p0, LmV0/a$b;->t:Ldagger/internal/h;

    .line 197
    .line 198
    invoke-static {p1}, Lorg/xbet/toto_bet/toto/domain/usecase/f;->a(LBc/a;)Lorg/xbet/toto_bet/toto/domain/usecase/f;

    .line 199
    .line 200
    .line 201
    move-result-object p1

    .line 202
    iput-object p1, p0, LmV0/a$b;->C:Ldagger/internal/h;

    .line 203
    .line 204
    return-void
.end method

.method public final d(LQW0/c;Lak/a;LTZ0/a;Ljava/lang/String;Ljava/lang/Integer;Lf8/g;LSX0/a;Lorg/xbet/ui_common/utils/internet/a;LwX0/c;Li8/c;Lcom/xbet/onexuser/domain/managers/TokenRefresher;LfX/b;LwX0/a;LVV0/b;LHX0/e;LVV0/a;Lc8/h;LzX0/k;LjV0/b;Lorg/xbet/ui_common/utils/M;)V
    .locals 9

    .line 1
    invoke-static/range {p20 .. p20}, Ldagger/internal/e;->a(Ljava/lang/Object;)Ldagger/internal/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    iput-object p1, p0, LmV0/a$b;->D:Ldagger/internal/h;

    .line 6
    .line 7
    iget-object p2, p0, LmV0/a$b;->g:Ldagger/internal/h;

    .line 8
    .line 9
    iget-object p3, p0, LmV0/a$b;->h:Ldagger/internal/h;

    .line 10
    .line 11
    iget-object v0, p0, LmV0/a$b;->i:Ldagger/internal/h;

    .line 12
    .line 13
    iget-object v1, p0, LmV0/a$b;->n:Ldagger/internal/h;

    .line 14
    .line 15
    iget-object v2, p0, LmV0/a$b;->u:Ldagger/internal/h;

    .line 16
    .line 17
    iget-object v3, p0, LmV0/a$b;->A:Ldagger/internal/h;

    .line 18
    .line 19
    iget-object v4, p0, LmV0/a$b;->v:Ldagger/internal/h;

    .line 20
    .line 21
    iget-object v5, p0, LmV0/a$b;->B:Ldagger/internal/h;

    .line 22
    .line 23
    iget-object v6, p0, LmV0/a$b;->C:Ldagger/internal/h;

    .line 24
    .line 25
    iget-object v7, p0, LmV0/a$b;->w:Ldagger/internal/h;

    .line 26
    .line 27
    iget-object v8, p0, LmV0/a$b;->x:Ldagger/internal/h;

    .line 28
    .line 29
    move-object/from16 p12, p1

    .line 30
    .line 31
    move-object p1, p2

    .line 32
    move-object p2, p3

    .line 33
    move-object p3, v0

    .line 34
    move-object p4, v1

    .line 35
    move-object p5, v2

    .line 36
    move-object p6, v3

    .line 37
    move-object/from16 p7, v4

    .line 38
    .line 39
    move-object/from16 p8, v5

    .line 40
    .line 41
    move-object/from16 p9, v6

    .line 42
    .line 43
    move-object/from16 p10, v7

    .line 44
    .line 45
    move-object/from16 p11, v8

    .line 46
    .line 47
    invoke-static/range {p1 .. p12}, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/s;->a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xbet/toto_bet/tirage/presentation/viewmodel/s;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iput-object p1, p0, LmV0/a$b;->E:Ldagger/internal/h;

    .line 52
    .line 53
    return-void
.end method

.method public final e(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;)Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    iget-object v0, p0, LmV0/a$b;->z:Ldagger/internal/h;

    .line 2
    .line 3
    invoke-interface {v0}, LBc/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LmV0/f;

    .line 8
    .line 9
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/l;->d(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;LmV0/f;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, LmV0/a$b;->a:LTZ0/a;

    .line 13
    .line 14
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/l;->a(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;LTZ0/a;)V

    .line 15
    .line 16
    .line 17
    iget-object v0, p0, LmV0/a$b;->b:LzX0/k;

    .line 18
    .line 19
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/l;->b(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;LzX0/k;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, LmV0/a$b;->c:LfX/b;

    .line 23
    .line 24
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/l;->c(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoBetTirageFragment;LfX/b;)V

    .line 25
    .line 26
    .line 27
    return-object p1
.end method

.method public final f(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;)Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;
    .locals 1
    .annotation build Lcom/google/errorprone/annotations/CanIgnoreReturnValue;
    .end annotation

    .line 1
    invoke-virtual {p0}, LmV0/a$b;->h()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/q;->c(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, LmV0/a$b;->b:LzX0/k;

    .line 9
    .line 10
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/q;->b(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;LzX0/k;)V

    .line 11
    .line 12
    .line 13
    iget-object v0, p0, LmV0/a$b;->a:LTZ0/a;

    .line 14
    .line 15
    invoke-static {p1, v0}, Lorg/xbet/toto_bet/tirage/presentation/fragment/q;->a(Lorg/xbet/toto_bet/tirage/presentation/fragment/TotoTirageComposeFragment;LTZ0/a;)V

    .line 16
    .line 17
    .line 18
    return-object p1
.end method

.method public final g()Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "+",
            "Landroidx/lifecycle/b0;",
            ">;",
            "LBc/a<",
            "Landroidx/lifecycle/b0;",
            ">;>;"
        }
    .end annotation

    .line 1
    const-class v0, Lorg/xbet/toto_bet/tirage/presentation/viewmodel/TotoTirageUdfViewModel;

    .line 2
    .line 3
    iget-object v1, p0, LmV0/a$b;->E:Ldagger/internal/h;

    .line 4
    .line 5
    invoke-static {v0, v1}, Ljava/util/Collections;->singletonMap(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final h()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 2

    .line 1
    new-instance v0, Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    invoke-virtual {p0}, LmV0/a$b;->g()Ljava/util/Map;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-direct {v0, v1}, Lorg/xbet/ui_common/viewmodel/core/l;-><init>(Ljava/util/Map;)V

    .line 8
    .line 9
    .line 10
    return-object v0
.end method
