.class public final synthetic Lorg/xbet/uikit_sport/compose/sport_game_events/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Landroidx/compose/ui/l;

.field public final synthetic d:Lkotlin/jvm/functions/Function0;

.field public final synthetic e:I

.field public final synthetic f:I


# direct methods
.method public synthetic constructor <init>(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->a:Ljava/lang/String;

    iput-object p2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->b:Ljava/lang/String;

    iput-object p3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->c:Landroidx/compose/ui/l;

    iput-object p4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->d:Lkotlin/jvm/functions/Function0;

    iput p5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->e:I

    iput p6, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->f:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->a:Ljava/lang/String;

    iget-object v1, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->c:Landroidx/compose/ui/l;

    iget-object v3, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->d:Lkotlin/jvm/functions/Function0;

    iget v4, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->e:I

    iget v5, p0, Lorg/xbet/uikit_sport/compose/sport_game_events/f;->f:I

    move-object v6, p1

    check-cast v6, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v7

    invoke-static/range {v0 .. v7}, Lorg/xbet/uikit_sport/compose/sport_game_events/p;->c(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/l;Lkotlin/jvm/functions/Function0;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
