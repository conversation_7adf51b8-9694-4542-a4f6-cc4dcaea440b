.class public final Lcom/google/android/gms/internal/fido/zzbt;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static zza(Lcom/google/android/gms/internal/fido/zzbp;)Lcom/google/android/gms/internal/fido/zzbp;
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/fido/zzbr;

    invoke-direct {v0, p0}, Lcom/google/android/gms/internal/fido/zzbr;-><init>(Lcom/google/android/gms/internal/fido/zzbp;)V

    return-object v0
.end method

.method public static zzb(Ljava/lang/Object;)Lcom/google/android/gms/internal/fido/zzbp;
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/fido/zzbs;

    invoke-direct {v0, p0}, Lcom/google/android/gms/internal/fido/zzbs;-><init>(Ljava/lang/Object;)V

    return-object v0
.end method
