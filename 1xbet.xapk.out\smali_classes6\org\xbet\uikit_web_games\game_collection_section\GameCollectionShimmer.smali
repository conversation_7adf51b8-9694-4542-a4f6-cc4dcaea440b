.class public final Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008\u0007\u0018\u00002\u00020\u0001B\'\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\u0008\u0008\u0003\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u0015\u0010\r\u001a\u00020\u000c2\u0006\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\r\u0010\u000f\u001a\u00020\u000c\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0014\u001a\u00020\u00118\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0012\u0010\u0013R\u0014\u0010\u0018\u001a\u00020\u00158\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010\u0017\u00a8\u0006\u0019"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "gamesViewType",
        "",
        "s",
        "(Ljava/lang/String;)V",
        "t",
        "()V",
        "Lk41/c;",
        "a",
        "Lk41/c;",
        "binding",
        "Lo41/d;",
        "b",
        "Lo41/d;",
        "shimmerAdapter",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Lk41/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Lo41/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 7
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p2

    invoke-static {p2, p0}, Lk41/c;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Lk41/c;

    move-result-object p2

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 6
    new-instance p3, Lo41/d;

    invoke-direct {p3}, Lo41/d;-><init>()V

    iput-object p3, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->b:Lo41/d;

    .line 7
    iget-object p2, p2, Lk41/c;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 8
    invoke-virtual {p2, p3}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    const/4 p3, 0x1

    .line 9
    invoke-virtual {p2, p3}, Landroidx/recyclerview/widget/RecyclerView;->setHasFixedSize(Z)V

    .line 10
    new-instance p3, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer$1$1;

    invoke-direct {p3, p1}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer$1$1;-><init>(Landroid/content/Context;)V

    invoke-virtual {p2, p3}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$LayoutManager;)V

    .line 11
    new-instance v0, LR11/c;

    .line 12
    invoke-virtual {p2}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p3, LlZ0/g;->space_8:I

    invoke-virtual {p1, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    const/16 v5, 0xa

    const/4 v6, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    .line 13
    invoke-direct/range {v0 .. v6}, LR11/c;-><init>(IIIZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 14
    invoke-virtual {p2, v0}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final s(Ljava/lang/String;)V
    .locals 4
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-eqz v1, :cond_1

    .line 14
    .line 15
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    move-object v2, v1

    .line 20
    check-cast v2, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 21
    .line 22
    invoke-virtual {v2}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->getConfigType()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v2

    .line 26
    invoke-static {v2, p1}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 27
    .line 28
    .line 29
    move-result v2

    .line 30
    if-eqz v2, :cond_0

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_1
    const/4 v1, 0x0

    .line 34
    :goto_0
    check-cast v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 35
    .line 36
    if-nez v1, :cond_2

    .line 37
    .line 38
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 39
    .line 40
    :cond_2
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 41
    .line 42
    invoke-virtual {p1}, Lk41/c;->getRoot()Landroid/view/View;

    .line 43
    .line 44
    .line 45
    move-result-object p1

    .line 46
    check-cast p1, Landroid/view/ViewGroup;

    .line 47
    .line 48
    invoke-static {p1}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 49
    .line 50
    .line 51
    sget-object p1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer$a;->a:[I

    .line 52
    .line 53
    invoke-virtual {v1}, Ljava/lang/Enum;->ordinal()I

    .line 54
    .line 55
    .line 56
    move-result v0

    .line 57
    aget p1, p1, v0

    .line 58
    .line 59
    const/4 v0, 0x1

    .line 60
    const-string v1, "null cannot be cast to non-null type android.view.ViewGroup.LayoutParams"

    .line 61
    .line 62
    const/16 v2, 0x8

    .line 63
    .line 64
    const/4 v3, 0x0

    .line 65
    if-eq p1, v0, :cond_9

    .line 66
    .line 67
    const/4 v0, 0x2

    .line 68
    if-eq p1, v0, :cond_7

    .line 69
    .line 70
    const/4 v0, 0x3

    .line 71
    if-eq p1, v0, :cond_5

    .line 72
    .line 73
    const/4 v0, 0x4

    .line 74
    if-ne p1, v0, :cond_4

    .line 75
    .line 76
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 77
    .line 78
    iget-object p1, p1, Lk41/c;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 79
    .line 80
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 81
    .line 82
    .line 83
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 84
    .line 85
    iget-object p1, p1, Lk41/c;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 86
    .line 87
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 88
    .line 89
    .line 90
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 91
    .line 92
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 93
    .line 94
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 95
    .line 96
    .line 97
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 98
    .line 99
    invoke-virtual {p1}, Lk41/c;->getRoot()Landroid/view/View;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    if-eqz v0, :cond_3

    .line 108
    .line 109
    check-cast v0, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 110
    .line 111
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 112
    .line 113
    .line 114
    move-result-object v1

    .line 115
    sget v2, LlZ0/g;->medium_horizontal_margin_dynamic:I

    .line 116
    .line 117
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 118
    .line 119
    .line 120
    move-result v1

    .line 121
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 122
    .line 123
    .line 124
    invoke-virtual {v0, v3}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginEnd(I)V

    .line 125
    .line 126
    .line 127
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 128
    .line 129
    .line 130
    return-void

    .line 131
    :cond_3
    new-instance p1, Ljava/lang/NullPointerException;

    .line 132
    .line 133
    const-string v0, "null cannot be cast to non-null type android.view.ViewGroup.MarginLayoutParams"

    .line 134
    .line 135
    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 136
    .line 137
    .line 138
    throw p1

    .line 139
    :cond_4
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 140
    .line 141
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 142
    .line 143
    .line 144
    throw p1

    .line 145
    :cond_5
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 146
    .line 147
    iget-object p1, p1, Lk41/c;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 148
    .line 149
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 150
    .line 151
    .line 152
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 153
    .line 154
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 155
    .line 156
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 157
    .line 158
    .line 159
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 160
    .line 161
    iget-object p1, p1, Lk41/c;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 162
    .line 163
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 164
    .line 165
    .line 166
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 167
    .line 168
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 169
    .line 170
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 171
    .line 172
    .line 173
    move-result-object v0

    .line 174
    if-eqz v0, :cond_6

    .line 175
    .line 176
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 177
    .line 178
    invoke-virtual {v1}, Lk41/c;->getRoot()Landroid/view/View;

    .line 179
    .line 180
    .line 181
    move-result-object v1

    .line 182
    invoke-virtual {v1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    sget v2, LlZ0/g;->size_108:I

    .line 187
    .line 188
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 189
    .line 190
    .line 191
    move-result v1

    .line 192
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 193
    .line 194
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 195
    .line 196
    .line 197
    return-void

    .line 198
    :cond_6
    new-instance p1, Ljava/lang/NullPointerException;

    .line 199
    .line 200
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 201
    .line 202
    .line 203
    throw p1

    .line 204
    :cond_7
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 205
    .line 206
    iget-object p1, p1, Lk41/c;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 207
    .line 208
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 209
    .line 210
    .line 211
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 212
    .line 213
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 214
    .line 215
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 216
    .line 217
    .line 218
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 219
    .line 220
    iget-object p1, p1, Lk41/c;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 221
    .line 222
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 223
    .line 224
    .line 225
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 226
    .line 227
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 228
    .line 229
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 230
    .line 231
    .line 232
    move-result-object v0

    .line 233
    if-eqz v0, :cond_8

    .line 234
    .line 235
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 236
    .line 237
    invoke-virtual {v1}, Lk41/c;->getRoot()Landroid/view/View;

    .line 238
    .line 239
    .line 240
    move-result-object v1

    .line 241
    invoke-virtual {v1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 242
    .line 243
    .line 244
    move-result-object v1

    .line 245
    sget v2, LlZ0/g;->size_108:I

    .line 246
    .line 247
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 248
    .line 249
    .line 250
    move-result v1

    .line 251
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 252
    .line 253
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 254
    .line 255
    .line 256
    return-void

    .line 257
    :cond_8
    new-instance p1, Ljava/lang/NullPointerException;

    .line 258
    .line 259
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 260
    .line 261
    .line 262
    throw p1

    .line 263
    :cond_9
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 264
    .line 265
    iget-object p1, p1, Lk41/c;->d:Lorg/xbet/uikit/components/header/DSHeader;

    .line 266
    .line 267
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 268
    .line 269
    .line 270
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 271
    .line 272
    iget-object p1, p1, Lk41/c;->b:Landroidx/recyclerview/widget/RecyclerView;

    .line 273
    .line 274
    invoke-virtual {p1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 275
    .line 276
    .line 277
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 278
    .line 279
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 280
    .line 281
    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 282
    .line 283
    .line 284
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 285
    .line 286
    iget-object p1, p1, Lk41/c;->c:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 287
    .line 288
    invoke-virtual {p1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 289
    .line 290
    .line 291
    move-result-object v0

    .line 292
    if-eqz v0, :cond_a

    .line 293
    .line 294
    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 295
    .line 296
    invoke-virtual {v1}, Lk41/c;->getRoot()Landroid/view/View;

    .line 297
    .line 298
    .line 299
    move-result-object v1

    .line 300
    invoke-virtual {v1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 301
    .line 302
    .line 303
    move-result-object v1

    .line 304
    sget v2, LlZ0/g;->size_100:I

    .line 305
    .line 306
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 307
    .line 308
    .line 309
    move-result v1

    .line 310
    iput v1, v0, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 311
    .line 312
    invoke-virtual {p1, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 313
    .line 314
    .line 315
    return-void

    .line 316
    :cond_a
    new-instance p1, Ljava/lang/NullPointerException;

    .line 317
    .line 318
    invoke-direct {p1, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 319
    .line 320
    .line 321
    throw p1
.end method

.method public final t()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionShimmer;->a:Lk41/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Lk41/c;->getRoot()Landroid/view/View;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/view/ViewGroup;

    .line 8
    .line 9
    invoke-static {v0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method
