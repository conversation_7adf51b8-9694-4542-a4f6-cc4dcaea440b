.class public final synthetic Lorg/xbet/uikit_sport/eventcard/bottom/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

.field public final synthetic b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_sport/eventcard/bottom/p;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/m;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/m;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    iput p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/m;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/m;->a:Lorg/xbet/uikit_sport/eventcard/bottom/p;

    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/m;->b:Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;

    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/m;->c:I

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/p;->c(Lorg/xbet/uikit_sport/eventcard/bottom/p;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;ILandroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
