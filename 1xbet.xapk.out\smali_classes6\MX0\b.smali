.class public final synthetic LMX0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/css/MarkdownView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/css/MarkdownView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LMX0/b;->a:Lorg/xbet/ui_common/viewcomponents/css/MarkdownView;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LMX0/b;->a:Lorg/xbet/ui_common/viewcomponents/css/MarkdownView;

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, <PERSON>java/lang/Integer;->intValue()I

    move-result p1

    invoke-static {v0, p1}, Lorg/xbet/ui_common/viewcomponents/css/MarkdownView;->z(Lorg/xbet/ui_common/viewcomponents/css/MarkdownView;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
