.class public abstract Lcom/google/android/gms/internal/common/zzp;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static zzb(C)Lcom/google/android/gms/internal/common/zzp;
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/common/zzm;

    invoke-direct {v0, p0}, Lcom/google/android/gms/internal/common/zzm;-><init>(C)V

    return-object v0
.end method


# virtual methods
.method public abstract zza(C)Z
.end method
