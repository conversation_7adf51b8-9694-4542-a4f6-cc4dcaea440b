.class public final LMY0/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0010\u0008\n\u0002\u0008\u0006\n\u0002\u0010\u0007\n\u0002\u0008\u0008\u001a9\u0010\u0005\u001a\u00020\u0000*\u00020\u00002\u0008\u0008\u0002\u0010\u0001\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00002\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a9\u0010\u0008\u001a\u00020\u0000*\u00020\u00002\u0008\u0008\u0002\u0010\u0001\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0002\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0003\u001a\u00020\u00072\u0008\u0008\u0002\u0010\u0004\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\u0008\u0010\t\u001a\u001b\u0010\u000b\u001a\u00020\u0000*\u00020\u00002\u0006\u0010\n\u001a\u00020\u0000H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000c\"\u0018\u0010\u0001\u001a\u00020\u0000*\u00020\u00008@X\u0080\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\r\u0010\u000e\u00a8\u0006\u000f"
    }
    d2 = {
        "",
        "alpha",
        "red",
        "green",
        "blue",
        "b",
        "(IIIII)I",
        "",
        "a",
        "(IFFFF)I",
        "bitShift",
        "e",
        "(II)I",
        "f",
        "(I)I",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(IFFFF)I
    .locals 1

    .line 1
    const/high16 v0, 0x437f0000    # 255.0f

    .line 2
    .line 3
    mul-float p1, p1, v0

    .line 4
    .line 5
    float-to-int p1, p1

    .line 6
    mul-float p2, p2, v0

    .line 7
    .line 8
    float-to-int p2, p2

    .line 9
    mul-float p3, p3, v0

    .line 10
    .line 11
    float-to-int p3, p3

    .line 12
    mul-float p4, p4, v0

    .line 13
    .line 14
    float-to-int p4, p4

    .line 15
    invoke-static {p0, p1, p2, p3, p4}, LMY0/d;->b(IIIII)I

    .line 16
    .line 17
    .line 18
    move-result p0

    .line 19
    return p0
.end method

.method public static final b(IIIII)I
    .locals 0

    .line 1
    shl-int/lit8 p0, p1, 0x18

    .line 2
    .line 3
    shl-int/lit8 p1, p2, 0x10

    .line 4
    .line 5
    or-int/2addr p0, p1

    .line 6
    shl-int/lit8 p1, p3, 0x8

    .line 7
    .line 8
    or-int/2addr p0, p1

    .line 9
    or-int/2addr p0, p4

    .line 10
    return p0
.end method

.method public static synthetic c(IFFFFILjava/lang/Object;)I
    .locals 2

    .line 1
    and-int/lit8 p6, p5, 0x1

    .line 2
    .line 3
    const/high16 v0, 0x437f0000    # 255.0f

    .line 4
    .line 5
    if-eqz p6, :cond_0

    .line 6
    .line 7
    const/16 p1, 0x18

    .line 8
    .line 9
    invoke-static {p0, p1}, LMY0/d;->e(II)I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    int-to-float p1, p1

    .line 14
    div-float/2addr p1, v0

    .line 15
    :cond_0
    and-int/lit8 p6, p5, 0x2

    .line 16
    .line 17
    if-eqz p6, :cond_1

    .line 18
    .line 19
    const/16 p2, 0x10

    .line 20
    .line 21
    invoke-static {p0, p2}, LMY0/d;->e(II)I

    .line 22
    .line 23
    .line 24
    move-result p2

    .line 25
    int-to-float p2, p2

    .line 26
    div-float/2addr p2, v0

    .line 27
    :cond_1
    and-int/lit8 p6, p5, 0x4

    .line 28
    .line 29
    const/16 v1, 0x8

    .line 30
    .line 31
    if-eqz p6, :cond_2

    .line 32
    .line 33
    invoke-static {p0, v1}, LMY0/d;->e(II)I

    .line 34
    .line 35
    .line 36
    move-result p3

    .line 37
    int-to-float p3, p3

    .line 38
    div-float/2addr p3, v0

    .line 39
    :cond_2
    and-int/2addr p5, v1

    .line 40
    if-eqz p5, :cond_3

    .line 41
    .line 42
    const/4 p4, 0x0

    .line 43
    invoke-static {p0, p4}, LMY0/d;->e(II)I

    .line 44
    .line 45
    .line 46
    move-result p4

    .line 47
    int-to-float p4, p4

    .line 48
    div-float/2addr p4, v0

    .line 49
    :cond_3
    invoke-static {p0, p1, p2, p3, p4}, LMY0/d;->a(IFFFF)I

    .line 50
    .line 51
    .line 52
    move-result p0

    .line 53
    return p0
.end method

.method public static synthetic d(IIIIIILjava/lang/Object;)I
    .locals 1

    .line 1
    and-int/lit8 p6, p5, 0x1

    .line 2
    .line 3
    if-eqz p6, :cond_0

    .line 4
    .line 5
    const/16 p1, 0x18

    .line 6
    .line 7
    invoke-static {p0, p1}, LMY0/d;->e(II)I

    .line 8
    .line 9
    .line 10
    move-result p1

    .line 11
    :cond_0
    and-int/lit8 p6, p5, 0x2

    .line 12
    .line 13
    if-eqz p6, :cond_1

    .line 14
    .line 15
    const/16 p2, 0x10

    .line 16
    .line 17
    invoke-static {p0, p2}, LMY0/d;->e(II)I

    .line 18
    .line 19
    .line 20
    move-result p2

    .line 21
    :cond_1
    and-int/lit8 p6, p5, 0x4

    .line 22
    .line 23
    const/16 v0, 0x8

    .line 24
    .line 25
    if-eqz p6, :cond_2

    .line 26
    .line 27
    invoke-static {p0, v0}, LMY0/d;->e(II)I

    .line 28
    .line 29
    .line 30
    move-result p3

    .line 31
    :cond_2
    and-int/2addr p5, v0

    .line 32
    if-eqz p5, :cond_3

    .line 33
    .line 34
    const/4 p4, 0x0

    .line 35
    invoke-static {p0, p4}, LMY0/d;->e(II)I

    .line 36
    .line 37
    .line 38
    move-result p4

    .line 39
    :cond_3
    invoke-static {p0, p1, p2, p3, p4}, LMY0/d;->b(IIIII)I

    .line 40
    .line 41
    .line 42
    move-result p0

    .line 43
    return p0
.end method

.method public static final e(II)I
    .locals 0

    .line 1
    shr-int/2addr p0, p1

    .line 2
    and-int/lit16 p0, p0, 0xff

    .line 3
    .line 4
    return p0
.end method

.method public static final f(I)I
    .locals 1

    .line 1
    const/16 v0, 0x18

    .line 2
    .line 3
    invoke-static {p0, v0}, LMY0/d;->e(II)I

    .line 4
    .line 5
    .line 6
    move-result p0

    .line 7
    return p0
.end method
