.class public interface abstract Lcom/google/android/recaptcha/internal/zza;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract zza(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/google/android/recaptcha/internal/zzp;
        }
    .end annotation
.end method

.method public abstract zzb(JLcom/google/android/recaptcha/internal/zzoe;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .param p3    # Lcom/google/android/recaptcha/internal/zzoe;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/google/android/recaptcha/internal/zzp;
        }
    .end annotation
.end method
