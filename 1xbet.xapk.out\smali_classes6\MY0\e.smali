.class public final LMY0/e;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0007\u001a!\u0010\u0005\u001a\u00020\u0001*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u001a\u001f\u0010\u0008\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u0003H\u0002\u00a2\u0006\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "LIY0/b;",
        "",
        "color",
        "",
        "elevationDp",
        "a",
        "(LIY0/b;IF)I",
        "elevationOverlayColor",
        "b",
        "(IF)I",
        "ui_common_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(LIY0/b;IF)I
    .locals 0
    .param p0    # LIY0/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-interface {p0}, LIY0/b;->i()I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    invoke-static {p0, p2}, LMY0/e;->b(IF)I

    .line 6
    .line 7
    .line 8
    move-result p0

    .line 9
    invoke-static {p1, p0}, LMY0/b;->c(II)I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    return p0
.end method

.method public static final b(IF)I
    .locals 7

    .line 1
    const/high16 v0, 0x3f800000    # 1.0f

    .line 2
    .line 3
    cmpg-float v0, p1, v0

    .line 4
    .line 5
    if-gez v0, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    const/4 v1, 0x0

    .line 9
    goto/16 :goto_0

    .line 10
    .line 11
    :cond_0
    const/high16 v0, 0x40000000    # 2.0f

    .line 12
    .line 13
    cmpg-float v0, p1, v0

    .line 14
    .line 15
    if-gez v0, :cond_1

    .line 16
    .line 17
    const p1, 0x3d4ccccd

    .line 18
    .line 19
    .line 20
    const v1, 0x3d4ccccd

    .line 21
    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_1
    const/high16 v0, 0x40400000    # 3.0f

    .line 25
    .line 26
    cmpg-float v0, p1, v0

    .line 27
    .line 28
    if-gez v0, :cond_2

    .line 29
    .line 30
    const p1, 0x3d8f5c29

    .line 31
    .line 32
    .line 33
    const v1, 0x3d8f5c29

    .line 34
    .line 35
    .line 36
    goto :goto_0

    .line 37
    :cond_2
    const/high16 v0, 0x40800000    # 4.0f

    .line 38
    .line 39
    cmpg-float v0, p1, v0

    .line 40
    .line 41
    if-gez v0, :cond_3

    .line 42
    .line 43
    const p1, 0x3da3d70a

    .line 44
    .line 45
    .line 46
    const v1, 0x3da3d70a

    .line 47
    .line 48
    .line 49
    goto :goto_0

    .line 50
    :cond_3
    const/high16 v0, 0x40c00000    # 6.0f

    .line 51
    .line 52
    cmpg-float v0, p1, v0

    .line 53
    .line 54
    if-gez v0, :cond_4

    .line 55
    .line 56
    const p1, 0x3db851ec

    .line 57
    .line 58
    .line 59
    const v1, 0x3db851ec

    .line 60
    .line 61
    .line 62
    goto :goto_0

    .line 63
    :cond_4
    const/high16 v0, 0x41000000    # 8.0f

    .line 64
    .line 65
    cmpg-float v0, p1, v0

    .line 66
    .line 67
    if-gez v0, :cond_5

    .line 68
    .line 69
    const p1, 0x3de147ae

    .line 70
    .line 71
    .line 72
    const v1, 0x3de147ae

    .line 73
    .line 74
    .line 75
    goto :goto_0

    .line 76
    :cond_5
    const/high16 v0, 0x41400000    # 12.0f

    .line 77
    .line 78
    cmpg-float v0, p1, v0

    .line 79
    .line 80
    if-gez v0, :cond_6

    .line 81
    .line 82
    const p1, 0x3df5c28f

    .line 83
    .line 84
    .line 85
    const v1, 0x3df5c28f

    .line 86
    .line 87
    .line 88
    goto :goto_0

    .line 89
    :cond_6
    const/high16 v0, 0x41800000    # 16.0f

    .line 90
    .line 91
    cmpg-float v0, p1, v0

    .line 92
    .line 93
    if-gez v0, :cond_7

    .line 94
    .line 95
    const p1, 0x3e0f5c29

    .line 96
    .line 97
    .line 98
    const v1, 0x3e0f5c29

    .line 99
    .line 100
    .line 101
    goto :goto_0

    .line 102
    :cond_7
    const/high16 v0, 0x41c00000    # 24.0f

    .line 103
    .line 104
    cmpg-float p1, p1, v0

    .line 105
    .line 106
    if-gez p1, :cond_8

    .line 107
    .line 108
    const p1, 0x3e19999a

    .line 109
    .line 110
    .line 111
    const v1, 0x3e19999a

    .line 112
    .line 113
    .line 114
    goto :goto_0

    .line 115
    :cond_8
    const p1, 0x3e23d70a

    .line 116
    .line 117
    .line 118
    const v1, 0x3e23d70a

    .line 119
    .line 120
    .line 121
    :goto_0
    invoke-static {p0}, LMY0/d;->f(I)I

    .line 122
    .line 123
    .line 124
    move-result p1

    .line 125
    if-nez p1, :cond_9

    .line 126
    .line 127
    const/4 p0, 0x0

    .line 128
    return p0

    .line 129
    :cond_9
    const/16 v5, 0xe

    .line 130
    .line 131
    const/4 v6, 0x0

    .line 132
    const/4 v2, 0x0

    .line 133
    const/4 v3, 0x0

    .line 134
    const/4 v4, 0x0

    .line 135
    move v0, p0

    .line 136
    invoke-static/range {v0 .. v6}, LMY0/d;->c(IFFFFILjava/lang/Object;)I

    .line 137
    .line 138
    .line 139
    move-result p0

    .line 140
    return p0
.end method
