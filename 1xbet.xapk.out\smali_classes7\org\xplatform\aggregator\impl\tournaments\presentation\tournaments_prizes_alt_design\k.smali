.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/k;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/k;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;->p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes_alt_design/TournamentPrizesAltDesignViewModel;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
