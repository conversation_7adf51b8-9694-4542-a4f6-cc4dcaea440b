.class public final synthetic Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LUX0/k;

.field public final synthetic b:LVb1/a;

.field public final synthetic c:Ljava/lang/String;

.field public final synthetic d:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;


# direct methods
.method public synthetic constructor <init>(LUX0/k;LVb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->a:LUX0/k;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->b:LVb1/a;

    iput-object p3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->c:Ljava/lang/String;

    iput-object p4, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->d:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->a:LUX0/k;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->b:LVb1/a;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->c:Ljava/lang/String;

    iget-object v3, p0, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/w;->d:Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, v2, v3, p1}, Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/delegates/PopularGamesCategoryContainerDelegateKt;->b(LUX0/k;LVb1/a;Ljava/lang/String;Lorg/xplatform/aggregator/popular/dashboard/impl/presentation/a;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
