.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;
.super Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\t\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 Y2\u0008\u0012\u0004\u0012\u00020\u00020\u0001:\u0001ZB\u0007\u00a2\u0006\u0004\u0008\u0003\u0010\u0004J\u000f\u0010\u0006\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0006\u0010\u0004J\u0017\u0010\t\u001a\u00020\u00052\u0006\u0010\u0008\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u00052\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u000f\u0010\u0013\u001a\u00020\u0005H\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0004J\u000f\u0010\u0014\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0004J\u000f\u0010\u0016\u001a\u00020\u0015H\u0010\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0019\u001a\u00020\u0018H\u0014\u00a2\u0006\u0004\u0008\u0019\u0010\u001aJ!\u0010\u001f\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u001b2\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0016\u00a2\u0006\u0004\u0008\u001f\u0010 J\u000f\u0010!\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008!\u0010\u0004J\u000f\u0010\"\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008\"\u0010\u0004J\u000f\u0010#\u001a\u00020\u0005H\u0016\u00a2\u0006\u0004\u0008#\u0010\u0004J\u0019\u0010$\u001a\u00020\u00052\u0008\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0014\u00a2\u0006\u0004\u0008$\u0010%J\u000f\u0010&\u001a\u00020\u0005H\u0014\u00a2\u0006\u0004\u0008&\u0010\u0004R\u001b\u0010,\u001a\u00020\'8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008(\u0010)\u001a\u0004\u0008*\u0010+R+\u00105\u001a\u00020-2\u0006\u0010.\u001a\u00020-8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102\"\u0004\u00083\u00104R\"\u0010=\u001a\u0002068\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:\"\u0004\u0008;\u0010<R\u001b\u0010B\u001a\u00020\u00028TX\u0094\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010AR\u001b\u0010G\u001a\u00020C8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008D\u0010?\u001a\u0004\u0008E\u0010FR\u001b\u0010L\u001a\u00020H8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008I\u0010?\u001a\u0004\u0008J\u0010KR\u001a\u0010R\u001a\u00020M8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008N\u0010O\u001a\u0004\u0008P\u0010QR\u001a\u0010X\u001a\u00020S8\u0016X\u0096\u0004\u00a2\u0006\u000c\n\u0004\u0008T\u0010U\u001a\u0004\u0008V\u0010W\u00a8\u0006["
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;",
        "Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;",
        "<init>",
        "()V",
        "",
        "X3",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;",
        "bannersListState",
        "W3",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;)V",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;",
        "event",
        "Z3",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "a4",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "S3",
        "u2",
        "Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "K2",
        "()Lorg/xbet/uikit/components/accountselection/AccountSelection;",
        "Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "N2",
        "()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;",
        "Landroid/view/View;",
        "view",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "onViewCreated",
        "(Landroid/view/View;Landroid/os/Bundle;)V",
        "onResume",
        "onPause",
        "onDestroyView",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "LS91/L;",
        "o0",
        "LRc/c;",
        "P3",
        "()LS91/L;",
        "viewBinding",
        "",
        "<set-?>",
        "b1",
        "LeX0/f;",
        "N3",
        "()J",
        "Y3",
        "(J)V",
        "bannerIdToOpen",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "k1",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "R3",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "v1",
        "Lkotlin/j;",
        "Q3",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;",
        "viewModel",
        "Lbb1/a;",
        "x1",
        "O3",
        "()Lbb1/a;",
        "tournamentsCardAdapter",
        "Lorg/xplatform/aggregator/impl/gifts/f;",
        "y1",
        "M3",
        "()Lorg/xplatform/aggregator/impl/gifts/f;",
        "appBarObserver",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "F1",
        "Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "O2",
        "()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;",
        "searchScreenType",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "H1",
        "Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "M2",
        "()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;",
        "depositScreenType",
        "I1",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final I1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final synthetic P1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final S1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b1:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public k1:Lorg/xbet/ui_common/viewmodel/core/l;

.field public final o0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 6

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentAggregatorTournamentsBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "bannerIdToOpen"

    .line 20
    .line 21
    const-string v5, "getBannerIdToOpen()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    const/4 v3, 0x2

    .line 31
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 32
    .line 33
    aput-object v0, v3, v4

    .line 34
    .line 35
    const/4 v0, 0x1

    .line 36
    aput-object v2, v3, v0

    .line 37
    .line 38
    sput-object v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P1:[Lkotlin/reflect/m;

    .line 39
    .line 40
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$a;

    .line 41
    .line 42
    const/4 v2, 0x0

    .line 43
    invoke-direct {v0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 44
    .line 45
    .line 46
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->I1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$a;

    .line 47
    .line 48
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object v0

    .line 52
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->S1:Ljava/lang/String;

    .line 53
    .line 54
    return-void
.end method

.method public constructor <init>()V
    .locals 7

    .line 1
    sget v0, Lu91/c;->fragment_aggregator_tournaments:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->o0:LRc/c;

    .line 13
    .line 14
    new-instance v1, LeX0/f;

    .line 15
    .line 16
    const/4 v5, 0x2

    .line 17
    const/4 v6, 0x0

    .line 18
    const-string v2, "BANNER_ITEM"

    .line 19
    .line 20
    const-wide/16 v3, 0x0

    .line 21
    .line 22
    invoke-direct/range {v1 .. v6}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 23
    .line 24
    .line 25
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->b1:LeX0/f;

    .line 26
    .line 27
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/a;

    .line 28
    .line 29
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/a;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 30
    .line 31
    .line 32
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$1;

    .line 33
    .line 34
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 35
    .line 36
    .line 37
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 38
    .line 39
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$2;

    .line 40
    .line 41
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 42
    .line 43
    .line 44
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    const-class v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 49
    .line 50
    invoke-static {v3}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 51
    .line 52
    .line 53
    move-result-object v3

    .line 54
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$3;

    .line 55
    .line 56
    invoke-direct {v4, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 57
    .line 58
    .line 59
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$4;

    .line 60
    .line 61
    invoke-direct {v5, v6, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 62
    .line 63
    .line 64
    invoke-static {p0, v3, v4, v5, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->v1:Lkotlin/j;

    .line 69
    .line 70
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/e;

    .line 71
    .line 72
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/e;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 73
    .line 74
    .line 75
    invoke-static {v2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 76
    .line 77
    .line 78
    move-result-object v0

    .line 79
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->x1:Lkotlin/j;

    .line 80
    .line 81
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/f;

    .line 82
    .line 83
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/f;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 84
    .line 85
    .line 86
    invoke-static {v2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 87
    .line 88
    .line 89
    move-result-object v0

    .line 90
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->y1:Lkotlin/j;

    .line 91
    .line 92
    sget-object v0, Lorg/xbet/analytics/domain/scope/search/SearchScreenType;->AGGREGATOR_TOURNAMENTS:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 93
    .line 94
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->F1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 95
    .line 96
    sget-object v0, Lorg/xbet/analytics/domain/scope/DepositCallScreenType;->AggregatorTournaments:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 97
    .line 98
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->H1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 99
    .line 100
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)LS91/L;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->S3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->W3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->X3()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;J)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Y3(J)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->a4(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final H3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 7

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->O3()Lbb1/a;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$appBarObserver$2$1;

    .line 8
    .line 9
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$appBarObserver$2$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/j;

    .line 13
    .line 14
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/j;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 15
    .line 16
    .line 17
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/k;

    .line 18
    .line 19
    invoke-direct {v4, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/k;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 20
    .line 21
    .line 22
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/l;

    .line 23
    .line 24
    invoke-direct {v5, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/l;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 25
    .line 26
    .line 27
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/m;

    .line 28
    .line 29
    invoke-direct {v6, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/m;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 30
    .line 31
    .line 32
    invoke-direct/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/gifts/f;-><init>(Landroidx/recyclerview/widget/RecyclerView$Adapter;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;LOc/n;)V

    .line 33
    .line 34
    .line 35
    return-object v0
.end method

.method public static final I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->X3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final J3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->X3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final K3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->X3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final L3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->X3()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method private final M3()Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->y1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/gifts/f;

    .line 8
    .line 9
    return-object v0
.end method

.method private final N3()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->b1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public static final T3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->d5()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final U3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->e5()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method public static final V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->i5()V

    .line 6
    .line 7
    .line 8
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 9
    .line 10
    return-object p0
.end method

.method private final X3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/L;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b;

    .line 8
    .line 9
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {v0, v1}, Landroid/view/View;->addOnLayoutChangeListener(Landroid/view/View$OnLayoutChangeListener;)V

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method private final Y3(J)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->b1:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1, p1, p2}, LeX0/f;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;J)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method private final a4(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/L;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    sget v1, Lpb/k;->update_again_after:I

    .line 8
    .line 9
    const-wide/16 v2, 0x2710

    .line 10
    .line 11
    invoke-virtual {v0, p1, v1, v2, v3}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->g(Lorg/xbet/uikit/components/lottie_empty/n;IJ)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iget-object p1, p1, LS91/L;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 19
    .line 20
    const/4 v0, 0x0

    .line 21
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 22
    .line 23
    .line 24
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    iget-object p1, p1, LS91/L;->e:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 29
    .line 30
    invoke-virtual {p0, p1, v0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public static final b4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lbb1/a;
    .locals 4

    .line 1
    new-instance v0, Lbb1/a;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/b;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 6
    .line 7
    .line 8
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/c;

    .line 9
    .line 10
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/c;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 11
    .line 12
    .line 13
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/d;

    .line 14
    .line 15
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/d;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 16
    .line 17
    .line 18
    invoke-direct {v0, v1, v2, v3}, Lbb1/a;-><init>(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;)V

    .line 19
    .line 20
    .line 21
    return-object v0
.end method

.method public static final c4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;JZ)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p0, p1, p2, p3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->h5(JZLjava/lang/String;)V

    .line 12
    .line 13
    .line 14
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 15
    .line 16
    return-object p0
.end method

.method public static final d4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;J)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    const/4 v1, 0x0

    .line 12
    invoke-virtual {p0, p1, p2, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->f5(JLjava/lang/String;Z)V

    .line 13
    .line 14
    .line 15
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 16
    .line 17
    return-object p0
.end method

.method public static final e4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    const/4 v0, 0x1

    .line 6
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->X4(Z)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static final f4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->R3()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic l3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->e4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic m3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->T3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic n3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->H3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lorg/xplatform/aggregator/impl/gifts/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic o3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;III)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->L3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;III)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->U3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;J)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->d4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;J)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->f4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lbb1/a;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->b4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lbb1/a;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->K3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;JZ)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->c4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;JZ)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->I3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->J3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;II)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic y3()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->S1:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)Lbb1/a;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->O3()Lbb1/a;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method


# virtual methods
.method public K2()Lorg/xbet/uikit/components/accountselection/AccountSelection;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/L;->b:Lorg/xbet/uikit/components/accountselection/AccountSelection;

    .line 6
    .line 7
    return-object v0
.end method

.method public M2()Lorg/xbet/analytics/domain/scope/DepositCallScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->H1:Lorg/xbet/analytics/domain/scope/DepositCallScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public N2()Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/L;->h:Lorg/xbet/uikit/components/toolbar/base/DSNavigationBarBasic;

    .line 6
    .line 7
    return-object v0
.end method

.method public O2()Lorg/xbet/analytics/domain/scope/search/SearchScreenType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->F1:Lorg/xbet/analytics/domain/scope/search/SearchScreenType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final O3()Lbb1/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->x1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lbb1/a;

    .line 8
    .line 9
    return-object v0
.end method

.method public final P3()LS91/L;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->o0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/L;

    .line 13
    .line 14
    return-object v0
.end method

.method public bridge synthetic Q2()Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorViewModel;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->v1:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final R3()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->k1:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final S3()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/L;->e:Lcom/google/android/material/appbar/CollapsingToolbarLayout;

    .line 6
    .line 7
    const/4 v1, 0x1

    .line 8
    invoke-virtual {p0, v0, v1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->I2(Lcom/google/android/material/appbar/CollapsingToolbarLayout;Z)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    iget-object v0, v0, LS91/L;->g:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 16
    .line 17
    const/16 v1, 0x8

    .line 18
    .line 19
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final W3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;)V
    .locals 6

    .line 1
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;->a()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    check-cast p1, Lv21/e;

    .line 10
    .line 11
    if-eqz p1, :cond_0

    .line 12
    .line 13
    instance-of p1, p1, Lv21/c;

    .line 14
    .line 15
    if-eqz p1, :cond_0

    .line 16
    .line 17
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->N3()J

    .line 18
    .line 19
    .line 20
    move-result-wide v0

    .line 21
    const-wide/16 v2, 0x0

    .line 22
    .line 23
    cmp-long p1, v0, v2

    .line 24
    .line 25
    if-eqz p1, :cond_0

    .line 26
    .line 27
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->N3()J

    .line 32
    .line 33
    .line 34
    move-result-wide v0

    .line 35
    const-class v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;

    .line 36
    .line 37
    invoke-virtual {v4}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    const/4 v5, 0x1

    .line 42
    invoke-virtual {p1, v0, v1, v4, v5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->f5(JLjava/lang/String;Z)V

    .line 43
    .line 44
    .line 45
    invoke-direct {p0, v2, v3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Y3(J)V

    .line 46
    .line 47
    .line 48
    :cond_0
    return-void
.end method

.method public final Z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;)V
    .locals 16

    .line 1
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->J2()LTZ0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 6
    .line 7
    invoke-virtual/range {p1 .. p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;->d()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual/range {p1 .. p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;->b()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v3

    .line 15
    invoke-virtual/range {p1 .. p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;->c()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v4

    .line 19
    invoke-virtual/range {p1 .. p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$b$a;->a()Lorg/xbet/uikit/components/dialog/AlertType;

    .line 20
    .line 21
    .line 22
    move-result-object v12

    .line 23
    const/16 v14, 0xbd8

    .line 24
    .line 25
    const/4 v15, 0x0

    .line 26
    const/4 v5, 0x0

    .line 27
    const/4 v6, 0x0

    .line 28
    const-string v7, "DIALOG_REQUEST_KEY"

    .line 29
    .line 30
    const/4 v8, 0x0

    .line 31
    const/4 v9, 0x0

    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v11, 0x0

    .line 34
    const/4 v13, 0x0

    .line 35
    invoke-direct/range {v1 .. v15}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-virtual {v0, v1, v2}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public onDestroyView()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/L;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 9
    .line 10
    .line 11
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onDestroyView()V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public onPause()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->M3()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->l()V

    .line 6
    .line 7
    .line 8
    invoke-super {p0}, Landroidx/fragment/app/Fragment;->onPause()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public onResume()V
    .locals 1

    .line 1
    invoke-super {p0}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->onResume()V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->M3()Lorg/xplatform/aggregator/impl/gifts/f;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/gifts/f;->k()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->j5()V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V
    .locals 2
    .param p1    # Landroid/view/View;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1, p2}, LXW0/a;->onViewCreated(Landroid/view/View;Landroid/os/Bundle;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->c5()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    const/4 p2, 0x1

    .line 16
    const/4 v0, 0x0

    .line 17
    const/4 v1, 0x0

    .line 18
    invoke-static {p1, v1, p2, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->Y4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;ZILjava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/g;

    .line 22
    .line 23
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/g;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 24
    .line 25
    .line 26
    const-string p2, "DIALOG_REQUEST_KEY"

    .line 27
    .line 28
    invoke-static {p0, p2, p1}, LVZ0/c;->e(Landroidx/fragment/app/Fragment;Ljava/lang/String;Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super/range {p0 .. p1}, Lorg/xplatform/aggregator/impl/core/presentation/BaseAggregatorFragment;->t2(Landroid/os/Bundle;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    iget-object v1, v1, LS91/L;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    invoke-virtual {v1, v2}, Landroidx/recyclerview/widget/RecyclerView;->setItemAnimator(Landroidx/recyclerview/widget/RecyclerView$m;)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->O3()Lbb1/a;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-virtual {v1, v3}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$Adapter;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 24
    .line 25
    .line 26
    move-result-object v3

    .line 27
    sget v4, Lpb/f;->space_8:I

    .line 28
    .line 29
    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelOffset(I)I

    .line 30
    .line 31
    .line 32
    move-result v6

    .line 33
    new-instance v5, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;

    .line 34
    .line 35
    const/16 v15, 0x1ce

    .line 36
    .line 37
    const/16 v16, 0x0

    .line 38
    .line 39
    const/4 v7, 0x0

    .line 40
    const/4 v8, 0x0

    .line 41
    const/4 v9, 0x0

    .line 42
    const/4 v11, 0x1

    .line 43
    const/4 v12, 0x0

    .line 44
    const/4 v13, 0x0

    .line 45
    const/4 v14, 0x0

    .line 46
    move v10, v6

    .line 47
    invoke-direct/range {v5 .. v16}, Lorg/xbet/ui_common/viewcomponents/recycler/decorators/o;-><init>(IIIIIILkotlin/jvm/functions/Function1;Lorg/xbet/ui_common/viewcomponents/recycler/decorators/SpacingItemDecorationBias;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {v1, v5}, Landroidx/recyclerview/widget/RecyclerView;->addItemDecoration(Landroidx/recyclerview/widget/RecyclerView$o;)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    iget-object v1, v1, LS91/L;->d:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 58
    .line 59
    invoke-virtual {v1}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getAuthorizationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/h;

    .line 64
    .line 65
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/h;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 66
    .line 67
    .line 68
    const/4 v4, 0x1

    .line 69
    invoke-static {v1, v2, v3, v4, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 70
    .line 71
    .line 72
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->P3()LS91/L;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    iget-object v1, v1, LS91/L;->d:Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;

    .line 77
    .line 78
    invoke-virtual {v1}, Lorg/xbet/uikit/components/authorizationbuttons/AuthorizationButtons;->getRegistrationButton()Lorg/xbet/uikit/components/buttons/DSButton;

    .line 79
    .line 80
    .line 81
    move-result-object v1

    .line 82
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/i;

    .line 83
    .line 84
    invoke-direct {v3, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/i;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 85
    .line 86
    .line 87
    invoke-static {v1, v2, v3, v4, v2}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 88
    .line 89
    .line 90
    return-void
.end method

.method public u2()V
    .locals 1

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/core/presentation/f;->a(Landroidx/fragment/app/Fragment;)LN91/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0, p0}, LN91/k;->a(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public v2()V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-super {v0}, LXW0/a;->v2()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 7
    .line 8
    .line 9
    move-result-object v1

    .line 10
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->T4()Lkotlinx/coroutines/flow/e;

    .line 11
    .line 12
    .line 13
    move-result-object v3

    .line 14
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$1;

    .line 15
    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lkotlin/coroutines/e;)V

    .line 18
    .line 19
    .line 20
    sget-object v10, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 23
    .line 24
    .line 25
    move-result-object v4

    .line 26
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 27
    .line 28
    .line 29
    move-result-object v11

    .line 30
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 31
    .line 32
    const/4 v7, 0x0

    .line 33
    move-object v5, v10

    .line 34
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/4 v15, 0x3

    .line 38
    const/16 v16, 0x0

    .line 39
    .line 40
    const/4 v12, 0x0

    .line 41
    const/4 v13, 0x0

    .line 42
    move-object v14, v2

    .line 43
    invoke-static/range {v11 .. v16}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->S4()Lkotlinx/coroutines/flow/e;

    .line 51
    .line 52
    .line 53
    move-result-object v8

    .line 54
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$2;

    .line 55
    .line 56
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lkotlin/coroutines/e;)V

    .line 57
    .line 58
    .line 59
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 60
    .line 61
    .line 62
    move-result-object v9

    .line 63
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 68
    .line 69
    move-object v7, v5

    .line 70
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 71
    .line 72
    .line 73
    const/4 v6, 0x3

    .line 74
    const/4 v7, 0x0

    .line 75
    const/4 v3, 0x0

    .line 76
    const/4 v4, 0x0

    .line 77
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->q5()Lkotlinx/coroutines/flow/e;

    .line 85
    .line 86
    .line 87
    move-result-object v8

    .line 88
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$3;

    .line 89
    .line 90
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$3;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lkotlin/coroutines/e;)V

    .line 91
    .line 92
    .line 93
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 94
    .line 95
    .line 96
    move-result-object v9

    .line 97
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 98
    .line 99
    .line 100
    move-result-object v2

    .line 101
    new-instance v5, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;

    .line 102
    .line 103
    move-object v7, v5

    .line 104
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$3;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 105
    .line 106
    .line 107
    const/4 v7, 0x0

    .line 108
    invoke-static/range {v2 .. v7}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 109
    .line 110
    .line 111
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;->Q3()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;

    .line 112
    .line 113
    .line 114
    move-result-object v2

    .line 115
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;->a5()Lkotlinx/coroutines/flow/f0;

    .line 116
    .line 117
    .line 118
    move-result-object v8

    .line 119
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$4;

    .line 120
    .line 121
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$4;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment;Lkotlin/coroutines/e;)V

    .line 122
    .line 123
    .line 124
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 125
    .line 126
    .line 127
    move-result-object v9

    .line 128
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 129
    .line 130
    .line 131
    move-result-object v1

    .line 132
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$4;

    .line 133
    .line 134
    move-object v7, v4

    .line 135
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsFragment$onObserveData$$inlined$observeWithLifecycle$default$4;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 136
    .line 137
    .line 138
    const/4 v5, 0x3

    .line 139
    const/4 v6, 0x0

    .line 140
    const/4 v2, 0x0

    .line 141
    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 142
    .line 143
    .line 144
    return-void
.end method
