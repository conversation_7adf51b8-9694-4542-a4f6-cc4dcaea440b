.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_full_info_alt_design.TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1"
    f = "TournamentsFullInfoAltDesignSharedViewModel.kt"
    l = {
        0x131
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;Leu/i;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Lorg/xplatform/aggregator/impl/core/domain/usecases/d;Lorg/xbet/onexlocalization/f;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xbet/remoteconfig/domain/usecases/i;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;",
        "Lkb1/E;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkb1/F<",
        "+",
        "Lkb1/n;",
        ">;>;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u0008\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0006\u0010\u0007"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;",
        "fullInfoState",
        "Lkb1/E;",
        "error",
        "Lkb1/F;",
        "Lkb1/n;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;Lkb1/E;)Lkb1/F;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field L$3:Ljava/lang/Object;

.field L$4:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    const/4 p1, 0x3

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;

    check-cast p2, Lkb1/E;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;Lkb1/E;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;",
            "Lkb1/E;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkb1/F<",
            "Lkb1/n;",
            ">;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    invoke-direct {v0, v1, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 18

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->label:I

    .line 8
    .line 9
    const/4 v3, 0x1

    .line 10
    if-eqz v2, :cond_1

    .line 11
    .line 12
    if-ne v2, v3, :cond_0

    .line 13
    .line 14
    iget-object v0, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$4:Ljava/lang/Object;

    .line 15
    .line 16
    check-cast v0, Li81/a;

    .line 17
    .line 18
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$3:Ljava/lang/Object;

    .line 19
    .line 20
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 21
    .line 22
    iget-object v3, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$2:Ljava/lang/Object;

    .line 23
    .line 24
    check-cast v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 25
    .line 26
    iget-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 29
    .line 30
    iget-object v5, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    .line 31
    .line 32
    check-cast v5, Lkb1/E;

    .line 33
    .line 34
    :try_start_0
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 35
    .line 36
    .line 37
    move-object v7, v3

    .line 38
    move-object/from16 v17, v4

    .line 39
    .line 40
    move-object/from16 v3, p1

    .line 41
    .line 42
    move-object v4, v0

    .line 43
    :goto_0
    move-object v0, v5

    .line 44
    goto :goto_1

    .line 45
    :catchall_0
    move-exception v0

    .line 46
    goto/16 :goto_4

    .line 47
    .line 48
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 49
    .line 50
    const-string v2, "call to \'resume\' before \'invoke\' with coroutine"

    .line 51
    .line 52
    invoke-direct {v0, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw v0

    .line 56
    :cond_1
    invoke-static/range {p1 .. p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 57
    .line 58
    .line 59
    iget-object v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    .line 60
    .line 61
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c;

    .line 62
    .line 63
    iget-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    .line 64
    .line 65
    move-object v5, v4

    .line 66
    check-cast v5, Lkb1/E;

    .line 67
    .line 68
    iget-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 69
    .line 70
    invoke-static {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->X3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lkb1/F$f;

    .line 71
    .line 72
    .line 73
    move-result-object v6

    .line 74
    iget-object v7, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;

    .line 75
    .line 76
    instance-of v8, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$a;

    .line 77
    .line 78
    if-eqz v8, :cond_8

    .line 79
    .line 80
    :try_start_1
    sget-object v6, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 81
    .line 82
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$a;

    .line 83
    .line 84
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$a;->a()Li81/a;

    .line 85
    .line 86
    .line 87
    move-result-object v2

    .line 88
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->N3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lgk/b;

    .line 89
    .line 90
    .line 91
    move-result-object v6

    .line 92
    invoke-virtual {v2}, Li81/a;->d()Lj81/a;

    .line 93
    .line 94
    .line 95
    move-result-object v8

    .line 96
    invoke-virtual {v8}, Lj81/a;->b()I

    .line 97
    .line 98
    .line 99
    move-result v8

    .line 100
    int-to-long v8, v8

    .line 101
    iput-object v5, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$0:Ljava/lang/Object;

    .line 102
    .line 103
    iput-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$1:Ljava/lang/Object;

    .line 104
    .line 105
    iput-object v7, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$2:Ljava/lang/Object;

    .line 106
    .line 107
    iput-object v4, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$3:Ljava/lang/Object;

    .line 108
    .line 109
    iput-object v2, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->L$4:Ljava/lang/Object;

    .line 110
    .line 111
    iput v3, v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$tournamentMainInfoState$1;->label:I

    .line 112
    .line 113
    invoke-interface {v6, v8, v9, v1}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 117
    if-ne v3, v0, :cond_2

    .line 118
    .line 119
    return-object v0

    .line 120
    :cond_2
    move-object/from16 v17, v4

    .line 121
    .line 122
    move-object v4, v2

    .line 123
    move-object/from16 v2, v17

    .line 124
    .line 125
    goto :goto_0

    .line 126
    :goto_1
    :try_start_2
    check-cast v3, Lbk/a;

    .line 127
    .line 128
    invoke-virtual {v3}, Lbk/a;->o()Ljava/lang/String;

    .line 129
    .line 130
    .line 131
    move-result-object v5

    .line 132
    invoke-virtual {v4}, Li81/a;->m()Ljava/util/List;

    .line 133
    .line 134
    .line 135
    move-result-object v3

    .line 136
    new-instance v6, Ljava/util/ArrayList;

    .line 137
    .line 138
    const/16 v8, 0xa

    .line 139
    .line 140
    invoke-static {v3, v8}, Lkotlin/collections/w;->y(Ljava/lang/Iterable;I)I

    .line 141
    .line 142
    .line 143
    move-result v8

    .line 144
    invoke-direct {v6, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 145
    .line 146
    .line 147
    invoke-interface {v3}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 148
    .line 149
    .line 150
    move-result-object v3

    .line 151
    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 152
    .line 153
    .line 154
    move-result v8

    .line 155
    if-eqz v8, :cond_3

    .line 156
    .line 157
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 158
    .line 159
    .line 160
    move-result-object v8

    .line 161
    check-cast v8, Lorg/xplatform/aggregator/api/model/Game;

    .line 162
    .line 163
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->M3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Ljava/util/Map;

    .line 164
    .line 165
    .line 166
    move-result-object v9

    .line 167
    invoke-virtual {v8}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 168
    .line 169
    .line 170
    move-result-wide v10

    .line 171
    invoke-static {v10, v11}, LHc/a;->f(J)Ljava/lang/Long;

    .line 172
    .line 173
    .line 174
    move-result-object v10

    .line 175
    invoke-interface {v9, v10, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    sget-object v8, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 179
    .line 180
    invoke-interface {v6, v8}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 181
    .line 182
    .line 183
    goto :goto_2

    .line 184
    :catchall_1
    move-exception v0

    .line 185
    move-object/from16 v4, v17

    .line 186
    .line 187
    goto/16 :goto_4

    .line 188
    .line 189
    :cond_3
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LHX0/e;

    .line 190
    .line 191
    .line 192
    move-result-object v6

    .line 193
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->V3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LHX0/e;

    .line 194
    .line 195
    .line 196
    move-result-object v3

    .line 197
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->e4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Z

    .line 198
    .line 199
    .line 200
    move-result v8

    .line 201
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->U3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lek0/o;

    .line 202
    .line 203
    .line 204
    move-result-object v9

    .line 205
    invoke-virtual {v9}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 206
    .line 207
    .line 208
    move-result-object v9

    .line 209
    invoke-static {v4, v3, v8, v9}, Ljb1/r;->h(Li81/a;LHX0/e;ZLorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;)Lkb1/m;

    .line 210
    .line 211
    .line 212
    move-result-object v3

    .line 213
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)I

    .line 214
    .line 215
    .line 216
    move-result v8

    .line 217
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)LO21/b;

    .line 218
    .line 219
    .line 220
    move-result-object v10

    .line 221
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Ljava/lang/String;

    .line 222
    .line 223
    .line 224
    move-result-object v9

    .line 225
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;

    .line 226
    .line 227
    .line 228
    move-result-object v11

    .line 229
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;

    .line 230
    .line 231
    .line 232
    move-result-object v12

    .line 233
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;

    .line 234
    .line 235
    .line 236
    move-result-object v13

    .line 237
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;

    .line 238
    .line 239
    .line 240
    move-result-object v14

    .line 241
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;

    .line 242
    .line 243
    .line 244
    move-result-object v15

    .line 245
    invoke-static {v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->Q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;)Ljava/util/Locale;

    .line 246
    .line 247
    .line 248
    move-result-object v16

    .line 249
    move-object v7, v3

    .line 250
    invoke-static/range {v4 .. v16}, Ljb1/t;->r(Li81/a;Ljava/lang/String;LHX0/e;Lkb1/m;ILjava/lang/String;LO21/b;Lorg/xbet/uikit_aggregator/aggregatortournamenttimer/AggregatorTournamentTimerType;Lorg/xbet/uikit_aggregator/aggregatortournamentprize/style/AggregatorTournamentPrizeStyleConfigType;Lorg/xbet/uikit_aggregator/aggregatorTournamentPrizePool/AggregatorTournamentPrizePoolStyle;Lorg/xbet/uikit_aggregator/aggregatorTournamentProgress/models/AggregatorTournamentProgressDSStyleType;Lorg/xbet/uikit_aggregator/aggregatortournamentrules/models/AggregatorTournamentRulesDSStyleType;Ljava/util/Locale;)Lkb1/n;

    .line 251
    .line 252
    .line 253
    move-result-object v3

    .line 254
    instance-of v4, v0, Lkb1/E$c;

    .line 255
    .line 256
    if-eqz v4, :cond_4

    .line 257
    .line 258
    new-instance v0, Lkb1/F$d;

    .line 259
    .line 260
    invoke-direct {v0, v3}, Lkb1/F$d;-><init>(Ljava/lang/Object;)V

    .line 261
    .line 262
    .line 263
    goto :goto_3

    .line 264
    :cond_4
    instance-of v3, v0, Lkb1/E$b;

    .line 265
    .line 266
    if-eqz v3, :cond_5

    .line 267
    .line 268
    new-instance v0, Lkb1/F$b;

    .line 269
    .line 270
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 271
    .line 272
    .line 273
    move-result-object v2

    .line 274
    invoke-direct {v0, v2}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 275
    .line 276
    .line 277
    goto :goto_3

    .line 278
    :cond_5
    instance-of v0, v0, Lkb1/E$a;

    .line 279
    .line 280
    if-eqz v0, :cond_6

    .line 281
    .line 282
    new-instance v0, Lkb1/F$a;

    .line 283
    .line 284
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 285
    .line 286
    .line 287
    move-result-object v2

    .line 288
    invoke-direct {v0, v2}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 289
    .line 290
    .line 291
    :goto_3
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 292
    .line 293
    .line 294
    move-result-object v0

    .line 295
    move-object/from16 v4, v17

    .line 296
    .line 297
    goto :goto_5

    .line 298
    :cond_6
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    .line 299
    .line 300
    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 301
    .line 302
    .line 303
    throw v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 304
    :goto_4
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$a;

    .line 305
    .line 306
    invoke-static {v0}, Lkotlin/n;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    .line 307
    .line 308
    .line 309
    move-result-object v0

    .line 310
    invoke-static {v0}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    .line 311
    .line 312
    .line 313
    move-result-object v0

    .line 314
    :goto_5
    invoke-static {v0}, Lkotlin/Result;->exceptionOrNull-impl(Ljava/lang/Object;)Ljava/lang/Throwable;

    .line 315
    .line 316
    .line 317
    move-result-object v2

    .line 318
    if-nez v2, :cond_7

    .line 319
    .line 320
    goto :goto_6

    .line 321
    :cond_7
    invoke-static {v4, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->i4(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;Ljava/lang/Throwable;)V

    .line 322
    .line 323
    .line 324
    new-instance v0, Lkb1/F$c;

    .line 325
    .line 326
    invoke-virtual {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 327
    .line 328
    .line 329
    move-result-object v2

    .line 330
    invoke-direct {v0, v2}, Lkb1/F$c;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 331
    .line 332
    .line 333
    :goto_6
    check-cast v0, Lkb1/F;

    .line 334
    .line 335
    return-object v0

    .line 336
    :cond_8
    instance-of v0, v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel$c$b;

    .line 337
    .line 338
    if-eqz v0, :cond_9

    .line 339
    .line 340
    instance-of v2, v5, Lkb1/E$a;

    .line 341
    .line 342
    if-eqz v2, :cond_9

    .line 343
    .line 344
    new-instance v0, Lkb1/F$a;

    .line 345
    .line 346
    invoke-virtual {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 347
    .line 348
    .line 349
    move-result-object v2

    .line 350
    invoke-direct {v0, v2}, Lkb1/F$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 351
    .line 352
    .line 353
    return-object v0

    .line 354
    :cond_9
    if-eqz v0, :cond_a

    .line 355
    .line 356
    instance-of v0, v5, Lkb1/E$b;

    .line 357
    .line 358
    if-eqz v0, :cond_a

    .line 359
    .line 360
    new-instance v0, Lkb1/F$b;

    .line 361
    .line 362
    invoke-virtual {v4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info_alt_design/TournamentsFullInfoAltDesignSharedViewModel;->o4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 363
    .line 364
    .line 365
    move-result-object v2

    .line 366
    invoke-direct {v0, v2}, Lkb1/F$b;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 367
    .line 368
    .line 369
    return-object v0

    .line 370
    :cond_a
    return-object v6
.end method
