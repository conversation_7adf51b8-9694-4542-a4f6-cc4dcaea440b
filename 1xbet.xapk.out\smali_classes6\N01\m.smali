.class public final synthetic LN01/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/m;->a:Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LN01/m;->a:Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;

    invoke-static {v0}, Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;->y(Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
