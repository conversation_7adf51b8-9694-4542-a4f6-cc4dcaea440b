.class public final synthetic LNX0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNX0/b;->a:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LNX0/b;->a:Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;

    invoke-static {v0}, Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;->i(Lorg/xbet/ui_common/viewcomponents/imageview/RoundCornerImageView;)Landroid/graphics/RectF;

    move-result-object v0

    return-object v0
.end method
