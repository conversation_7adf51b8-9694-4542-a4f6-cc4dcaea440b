.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$a;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$b;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 o2\u00020\u0001:\u0003pqrBq\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\'\u0010\"\u001a\u00020!2\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u0017\u0010%\u001a\u00020$2\u0006\u0010\u001f\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u0013\u0010)\u001a\u0008\u0012\u0004\u0012\u00020(0\'\u00a2\u0006\u0004\u0008)\u0010*J\u0013\u0010-\u001a\u0008\u0012\u0004\u0012\u00020,0+\u00a2\u0006\u0004\u0008-\u0010.J\u001d\u00100\u001a\u00020!2\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010/\u001a\u00020$\u00a2\u0006\u0004\u00080\u00101J\r\u00102\u001a\u00020!\u00a2\u0006\u0004\u00082\u00103J%\u00108\u001a\u00020!2\u0006\u00105\u001a\u0002042\u0006\u00107\u001a\u0002062\u0006\u0010 \u001a\u00020\u0010\u00a2\u0006\u0004\u00088\u00109J/\u0010;\u001a\u00020!2\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010:\u001a\u0002062\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010 \u001a\u00020\u0010H\u0002\u00a2\u0006\u0004\u0008;\u0010<R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008=\u0010>R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010@R\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008A\u0010BR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008C\u0010DR\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008E\u0010FR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008G\u0010HR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008I\u0010JR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008K\u0010LR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008M\u0010NR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008O\u0010PR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Q\u0010RR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008U\u0010VR\u0018\u0010Z\u001a\u0004\u0018\u00010W8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008X\u0010YR\u001c\u0010^\u001a\u0008\u0012\u0004\u0012\u00020!0[8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008\\\u0010]R\u0014\u0010b\u001a\u00020_8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010aR\u0014\u0010f\u001a\u00020c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010eR\u001a\u0010j\u001a\u0008\u0012\u0004\u0012\u00020(0g8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008h\u0010iR\u001a\u0010n\u001a\u0008\u0012\u0004\u0012\u00020,0k8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008l\u0010m\u00a8\u0006s"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lw81/c;",
        "getTournamentFullInfoScenario",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LP91/b;",
        "aggregatorNavigator",
        "Lw81/g;",
        "takePartTournamentsScenario",
        "LHX0/e;",
        "resourceManager",
        "LwX0/C;",
        "routerHolder",
        "",
        "tournamentTitle",
        "Lm8/a;",
        "coroutineDispatchers",
        "",
        "tournamentId",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "getRemoteConfigUseCase",
        "Lorg/xbet/analytics/domain/scope/g;",
        "aggregatorTournamentsAnalytics",
        "LnR/d;",
        "aggregatorTournamentFatmanLogger",
        "<init>",
        "(Lw81/c;LSX0/c;Lorg/xbet/ui_common/utils/M;LP91/b;Lw81/g;LHX0/e;LwX0/C;Ljava/lang/String;Lm8/a;JLorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/analytics/domain/scope/g;LnR/d;)V",
        "Lh81/b;",
        "result",
        "screenName",
        "",
        "G3",
        "(JLh81/b;Ljava/lang/String;)V",
        "",
        "J3",
        "(Lh81/b;)Z",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$c;",
        "E3",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$b;",
        "getEvents",
        "()Lkotlinx/coroutines/flow/e;",
        "fromCache",
        "F3",
        "(JZ)V",
        "onBackPressed",
        "()V",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "buttonAction",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "tournamentKind",
        "H3",
        "(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;)V",
        "kind",
        "I3",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V",
        "v1",
        "Lw81/c;",
        "x1",
        "LSX0/c;",
        "y1",
        "Lorg/xbet/ui_common/utils/M;",
        "F1",
        "LP91/b;",
        "H1",
        "Lw81/g;",
        "I1",
        "LHX0/e;",
        "P1",
        "LwX0/C;",
        "S1",
        "Ljava/lang/String;",
        "V1",
        "Lm8/a;",
        "b2",
        "J",
        "v2",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "x2",
        "Lorg/xbet/analytics/domain/scope/g;",
        "y2",
        "LnR/d;",
        "Lkotlinx/coroutines/x0;",
        "F2",
        "Lkotlinx/coroutines/x0;",
        "tournamentFullInfoJob",
        "Lkotlin/Function0;",
        "H2",
        "Lkotlin/jvm/functions/Function0;",
        "backAction",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
        "I2",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;",
        "aggregatorProviderCardCollectionStyle",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;",
        "P2",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;",
        "typeProviders",
        "Lkotlinx/coroutines/flow/V;",
        "S2",
        "Lkotlinx/coroutines/flow/V;",
        "mutableProvidersState",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "V2",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "eventsFlow",
        "X2",
        "c",
        "b",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final X2:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F2:Lkotlinx/coroutines/x0;

.field public final H1:Lw81/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H2:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:J

.field public final v1:Lw81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/analytics/domain/scope/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LnR/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->X2:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$a;

    return-void
.end method

.method public constructor <init>(Lw81/c;LSX0/c;Lorg/xbet/ui_common/utils/M;LP91/b;Lw81/g;LHX0/e;LwX0/C;Ljava/lang/String;Lm8/a;JLorg/xbet/remoteconfig/domain/usecases/i;Lorg/xbet/analytics/domain/scope/g;LnR/d;)V
    .locals 0
    .param p1    # Lw81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lw81/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/analytics/domain/scope/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LnR/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->v1:Lw81/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->x1:LSX0/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->F1:LP91/b;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->H1:Lw81/g;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->I1:LHX0/e;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->P1:LwX0/C;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->S1:Ljava/lang/String;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->V1:Lm8/a;

    .line 21
    .line 22
    iput-wide p10, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->b2:J

    .line 23
    .line 24
    iput-object p12, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->v2:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 25
    .line 26
    iput-object p13, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 27
    .line 28
    iput-object p14, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->y2:LnR/d;

    .line 29
    .line 30
    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/f;

    .line 31
    .line 32
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/f;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)V

    .line 33
    .line 34
    .line 35
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->H2:Lkotlin/jvm/functions/Function0;

    .line 36
    .line 37
    sget-object p1, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;->Companion:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle$a;

    .line 38
    .line 39
    invoke-interface {p12}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 40
    .line 41
    .line 42
    move-result-object p2

    .line 43
    invoke-virtual {p2}, Lek0/o;->w()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object p2

    .line 47
    invoke-virtual {p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle$a;->a(Ljava/lang/String;)Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->I2:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 52
    .line 53
    sget-object p2, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;->Vertical:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 54
    .line 55
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->P2:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;

    .line 56
    .line 57
    new-instance p3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$c$c;

    .line 58
    .line 59
    new-instance p4, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$c;

    .line 60
    .line 61
    new-instance p5, LO21/b;

    .line 62
    .line 63
    invoke-direct {p5, p2, p1}, LO21/b;-><init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionType;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;)V

    .line 64
    .line 65
    .line 66
    const/4 p1, 0x0

    .line 67
    const/4 p2, 0x2

    .line 68
    invoke-direct {p4, p5, p1, p2, p1}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a$c;-><init>(LO21/b;Ljava/lang/Integer;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 69
    .line 70
    .line 71
    invoke-direct {p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$c$c;-><init>(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V

    .line 72
    .line 73
    .line 74
    invoke-static {p3}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 79
    .line 80
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 81
    .line 82
    const/4 p2, 0x1

    .line 83
    sget-object p3, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 84
    .line 85
    invoke-direct {p1, p2, p3}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 86
    .line 87
    .line 88
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->V2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 89
    .line 90
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;JLh81/b;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->G3(JLh81/b;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p5}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->I3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;Lh81/b;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->J3(Lh81/b;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->F1:LP91/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LP91/b;->a()V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method

.method private final G3(JLh81/b;Ljava/lang/String;)V
    .locals 7

    .line 1
    instance-of v3, p3, Lh81/b$a;

    .line 2
    .line 3
    instance-of v0, p3, Lh81/b$b;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    check-cast p3, Lh81/b$b;

    .line 9
    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object p3, v1

    .line 12
    :goto_0
    if-eqz p3, :cond_1

    .line 13
    .line 14
    invoke-virtual {p3}, Lh81/b$b;->a()I

    .line 15
    .line 16
    .line 17
    move-result p3

    .line 18
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :cond_1
    move-object v4, v1

    .line 23
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 24
    .line 25
    const-string v5, "providers_tournament"

    .line 26
    .line 27
    move-wide v1, p1

    .line 28
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/analytics/domain/scope/g;->c(JZLjava/lang/Integer;Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->y2:LnR/d;

    .line 32
    .line 33
    const-string v5, "providers_tournament"

    .line 34
    .line 35
    move-object v6, v4

    .line 36
    move v4, v3

    .line 37
    move-wide v2, v1

    .line 38
    move-object v1, p4

    .line 39
    invoke-interface/range {v0 .. v6}, LnR/d;->i(Ljava/lang/String;JZLjava/lang/String;Ljava/lang/Integer;)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method private final J3(Lh81/b;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lh81/b$c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    instance-of p1, p1, Lh81/b$g;

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    return p1

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    return p1
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)LP91/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->F1:LP91/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->I2:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollectionStyle;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->V2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)LSX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->x1:LSX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->I1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->P1:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Lw81/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->H1:Lw81/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->b2:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->S1:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final E3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->S2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final F3(JZ)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->F2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->v1:Lw81/c;

    .line 11
    .line 12
    invoke-interface {v0, p1, p2, p3}, Lw81/c;->a(JZ)Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$loadData$1;

    .line 17
    .line 18
    invoke-direct {p2, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$loadData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->V1:Lm8/a;

    .line 30
    .line 31
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 32
    .line 33
    .line 34
    move-result-object p3

    .line 35
    invoke-static {p2, p3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 36
    .line 37
    .line 38
    move-result-object p2

    .line 39
    new-instance p3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$loadData$2;

    .line 40
    .line 41
    invoke-direct {p3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$loadData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;Lkotlin/coroutines/e;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p1, p2, p3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->F2:Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    return-void
.end method

.method public final H3(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;)V
    .locals 7
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$onButtonClick$1;

    .line 6
    .line 7
    const/4 v6, 0x0

    .line 8
    move-object v3, p0

    .line 9
    move-object v2, p1

    .line 10
    move-object v4, p2

    .line 11
    move-object v5, p3

    .line 12
    invoke-direct/range {v1 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$onButtonClick$1;-><init>(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 13
    .line 14
    .line 15
    const/4 v4, 0x3

    .line 16
    const/4 v5, 0x0

    .line 17
    move-object v3, v1

    .line 18
    const/4 v1, 0x0

    .line 19
    const/4 v2, 0x0

    .line 20
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final I3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 11

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v8

    .line 5
    new-instance v9, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$onParticipateClick$1;

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v9, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$onParticipateClick$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->V1:Lm8/a;

    .line 13
    .line 14
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 15
    .line 16
    .line 17
    move-result-object v10

    .line 18
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$onParticipateClick$2;

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    move-object v1, p0

    .line 22
    move-wide v2, p1

    .line 23
    move-object v4, p3

    .line 24
    move-object v6, p4

    .line 25
    move-object/from16 v5, p5

    .line 26
    .line 27
    invoke-direct/range {v0 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$onParticipateClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 28
    .line 29
    .line 30
    const/16 v6, 0xa

    .line 31
    .line 32
    const/4 v2, 0x0

    .line 33
    const/4 v4, 0x0

    .line 34
    move-object v5, v0

    .line 35
    move-object v0, v8

    .line 36
    move-object v1, v9

    .line 37
    move-object v3, v10

    .line 38
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final getEvents()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->V2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final onBackPressed()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->H2:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method
