.class public final synthetic Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;->b:Ljava/lang/String;

    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;->c:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;->a:Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;

    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/d;->c:Ljava/lang/String;

    check-cast p1, LvX0/f;

    invoke-static {v0, v1, v2, p1}, Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;->q3(Lorg/xbet/toto_bet/makebet/presentation/simple/viewmodel/SimpleMakeBetViewModel;Ljava/lang/String;Ljava/lang/String;LvX0/f;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
