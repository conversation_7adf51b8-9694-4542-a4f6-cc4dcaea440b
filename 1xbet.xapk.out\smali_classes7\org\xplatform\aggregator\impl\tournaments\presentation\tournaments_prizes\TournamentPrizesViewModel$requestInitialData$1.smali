.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_prizes.TournamentPrizesViewModel$requestInitialData$1"
    f = "TournamentPrizesViewModel.kt"
    l = {
        0x59,
        0x6c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->L3(JJZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Li81/a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Li81/a;",
        "tournamentFullInfo",
        "",
        "<anonymous>",
        "(Li81/a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $stageTournamentID:J

.field synthetic L$0:Ljava/lang/Object;

.field L$1:Ljava/lang/Object;

.field L$2:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;JLkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;",
            "J",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    iput-wide p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->$stageTournamentID:J

    const/4 p1, 0x2

    invoke-direct {p0, p1, p4}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->$stageTournamentID:J

    invoke-direct {v0, v1, v2, v3, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;JLkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public final invoke(Li81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Li81/a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 2
    check-cast p1, Li81/a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->invoke(Li81/a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 14

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_3

    .line 10
    .line 11
    if-eq v1, v3, :cond_1

    .line 12
    .line 13
    if-ne v1, v2, :cond_0

    .line 14
    .line 15
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    goto/16 :goto_5

    .line 19
    .line 20
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 23
    .line 24
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw p1

    .line 28
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$2:Ljava/lang/Object;

    .line 29
    .line 30
    check-cast v1, Ljava/util/List;

    .line 31
    .line 32
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$1:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v4, Lk81/a;

    .line 35
    .line 36
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    .line 37
    .line 38
    check-cast v5, Li81/a;

    .line 39
    .line 40
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    :cond_2
    move-object v8, v1

    .line 44
    goto :goto_0

    .line 45
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    .line 49
    .line 50
    move-object v5, p1

    .line 51
    check-cast v5, Li81/a;

    .line 52
    .line 53
    invoke-virtual {v5}, Li81/a;->e()Lk81/a;

    .line 54
    .line 55
    .line 56
    move-result-object v4

    .line 57
    invoke-virtual {v5}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    invoke-static {v5, p1}, Ljb1/w;->h(Li81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;)Ljava/util/List;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 66
    .line 67
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lgk/b;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    invoke-virtual {v5}, Li81/a;->d()Lj81/a;

    .line 72
    .line 73
    .line 74
    move-result-object v6

    .line 75
    invoke-virtual {v6}, Lj81/a;->b()I

    .line 76
    .line 77
    .line 78
    move-result v6

    .line 79
    int-to-long v6, v6

    .line 80
    iput-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    .line 81
    .line 82
    iput-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$1:Ljava/lang/Object;

    .line 83
    .line 84
    iput-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$2:Ljava/lang/Object;

    .line 85
    .line 86
    iput v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->label:I

    .line 87
    .line 88
    invoke-interface {p1, v6, v7, p0}, Lgk/b;->a(JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    if-ne p1, v0, :cond_2

    .line 93
    .line 94
    goto/16 :goto_4

    .line 95
    .line 96
    :goto_0
    check-cast p1, Lbk/a;

    .line 97
    .line 98
    invoke-virtual {p1}, Lbk/a;->o()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    invoke-virtual {v5}, Li81/a;->i()Lo81/a;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    invoke-virtual {v1}, Lo81/a;->a()Ljava/util/List;

    .line 107
    .line 108
    .line 109
    move-result-object v1

    .line 110
    iget-wide v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->$stageTournamentID:J

    .line 111
    .line 112
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    :cond_4
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 117
    .line 118
    .line 119
    move-result v9

    .line 120
    const/4 v10, 0x0

    .line 121
    if-eqz v9, :cond_5

    .line 122
    .line 123
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 124
    .line 125
    .line 126
    move-result-object v9

    .line 127
    move-object v11, v9

    .line 128
    check-cast v11, Lo81/b;

    .line 129
    .line 130
    invoke-virtual {v11}, Lo81/b;->c()J

    .line 131
    .line 132
    .line 133
    move-result-wide v11

    .line 134
    cmp-long v13, v11, v6

    .line 135
    .line 136
    if-nez v13, :cond_4

    .line 137
    .line 138
    goto :goto_1

    .line 139
    :cond_5
    move-object v9, v10

    .line 140
    :goto_1
    check-cast v9, Lo81/b;

    .line 141
    .line 142
    if-eqz v9, :cond_6

    .line 143
    .line 144
    invoke-virtual {v9}, Lo81/b;->a()Lk81/a;

    .line 145
    .line 146
    .line 147
    move-result-object v4

    .line 148
    :cond_6
    invoke-virtual {v5}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 149
    .line 150
    .line 151
    move-result-object v1

    .line 152
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 153
    .line 154
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)LHX0/e;

    .line 155
    .line 156
    .line 157
    move-result-object v6

    .line 158
    invoke-static {v4, v1, p1, v6}, Ljb1/w;->f(Lk81/a;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;LHX0/e;)Ljava/util/List;

    .line 159
    .line 160
    .line 161
    move-result-object p1

    .line 162
    invoke-static {p1}, Lkotlin/collections/CollectionsKt;->C1(Ljava/util/Collection;)Ljava/util/List;

    .line 163
    .line 164
    .line 165
    move-result-object v7

    .line 166
    if-eqz v9, :cond_7

    .line 167
    .line 168
    invoke-virtual {v9}, Lo81/b;->a()Lk81/a;

    .line 169
    .line 170
    .line 171
    move-result-object p1

    .line 172
    if-eqz p1, :cond_7

    .line 173
    .line 174
    invoke-virtual {p1}, Lk81/a;->b()Ljava/lang/String;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    goto :goto_2

    .line 179
    :cond_7
    move-object p1, v10

    .line 180
    :goto_2
    if-nez p1, :cond_8

    .line 181
    .line 182
    const-string p1, ""

    .line 183
    .line 184
    :cond_8
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 185
    .line 186
    .line 187
    move-result v1

    .line 188
    const/4 v4, 0x0

    .line 189
    if-lez v1, :cond_9

    .line 190
    .line 191
    new-instance v1, Lkb1/z$d;

    .line 192
    .line 193
    invoke-direct {v1, p1}, Lkb1/z$d;-><init>(Ljava/lang/String;)V

    .line 194
    .line 195
    .line 196
    invoke-interface {v7, v4, v1}, Ljava/util/List;->add(ILjava/lang/Object;)V

    .line 197
    .line 198
    .line 199
    :cond_9
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 200
    .line 201
    invoke-static {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)Lkotlinx/coroutines/flow/V;

    .line 202
    .line 203
    .line 204
    move-result-object p1

    .line 205
    move-object v1, v5

    .line 206
    invoke-virtual {v1}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 207
    .line 208
    .line 209
    move-result-object v5

    .line 210
    if-nez v9, :cond_a

    .line 211
    .line 212
    invoke-virtual {v1}, Li81/a;->o()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 213
    .line 214
    .line 215
    move-result-object v6

    .line 216
    sget-object v9, Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;->PROVIDER:Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 217
    .line 218
    if-ne v6, v9, :cond_a

    .line 219
    .line 220
    const/4 v9, 0x1

    .line 221
    goto :goto_3

    .line 222
    :cond_a
    const/4 v9, 0x0

    .line 223
    :goto_3
    invoke-virtual {v1}, Li81/a;->k()Lh81/a;

    .line 224
    .line 225
    .line 226
    move-result-object v3

    .line 227
    invoke-virtual {v1}, Li81/a;->t()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 228
    .line 229
    .line 230
    move-result-object v1

    .line 231
    sget-object v4, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 232
    .line 233
    iget-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;

    .line 234
    .line 235
    invoke-static {v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;->w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel;)LHX0/e;

    .line 236
    .line 237
    .line 238
    move-result-object v6

    .line 239
    invoke-static {v3, v1, v4, v6}, LWa1/c;->b(Lh81/a;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;LHX0/e;)Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 240
    .line 241
    .line 242
    move-result-object v6

    .line 243
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;

    .line 244
    .line 245
    invoke-direct/range {v4 .. v9}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$c$a;-><init>(Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;Ljava/util/List;Ljava/util/List;Z)V

    .line 246
    .line 247
    .line 248
    iput-object v10, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$0:Ljava/lang/Object;

    .line 249
    .line 250
    iput-object v10, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$1:Ljava/lang/Object;

    .line 251
    .line 252
    iput-object v10, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->L$2:Ljava/lang/Object;

    .line 253
    .line 254
    iput v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizesViewModel$requestInitialData$1;->label:I

    .line 255
    .line 256
    invoke-interface {p1, v4, p0}, Lkotlinx/coroutines/flow/U;->emit(Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 257
    .line 258
    .line 259
    move-result-object p1

    .line 260
    if-ne p1, v0, :cond_b

    .line 261
    .line 262
    :goto_4
    return-object v0

    .line 263
    :cond_b
    :goto_5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 264
    .line 265
    return-object p1
.end method
