.class public final Lcom/google/api/ResourceProto;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension<",
            "Lcom/google/protobuf/DescriptorProtos$FieldOptions;",
            "Lcom/google/api/ResourceReference;",
            ">;"
        }
    .end annotation
.end field

.field public static final b:Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension<",
            "Lcom/google/protobuf/DescriptorProtos$FileOptions;",
            "Ljava/util/List<",
            "Lcom/google/api/ResourceDescriptor;",
            ">;>;"
        }
    .end annotation
.end field

.field public static final c:Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension<",
            "Lcom/google/protobuf/DescriptorProtos$MessageOptions;",
            "Lcom/google/api/ResourceDescriptor;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 10

    .line 1
    invoke-static {}, Lcom/google/protobuf/DescriptorProtos$FieldOptions;->a0()Lcom/google/protobuf/DescriptorProtos$FieldOptions;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {}, Lcom/google/api/ResourceReference;->Z()Lcom/google/api/ResourceReference;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-static {}, Lcom/google/api/ResourceReference;->Z()Lcom/google/api/ResourceReference;

    .line 10
    .line 11
    .line 12
    move-result-object v2

    .line 13
    sget-object v5, Lcom/google/protobuf/WireFormat$FieldType;->MESSAGE:Lcom/google/protobuf/WireFormat$FieldType;

    .line 14
    .line 15
    const-class v6, Lcom/google/api/ResourceReference;

    .line 16
    .line 17
    const/4 v3, 0x0

    .line 18
    const/16 v4, 0x41f

    .line 19
    .line 20
    invoke-static/range {v0 .. v6}, Lcom/google/protobuf/GeneratedMessageLite;->T(Lcom/google/protobuf/MessageLite;Ljava/lang/Object;Lcom/google/protobuf/MessageLite;Lcom/google/protobuf/Internal$EnumLiteMap;ILcom/google/protobuf/WireFormat$FieldType;Ljava/lang/Class;)Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    sput-object v0, Lcom/google/api/ResourceProto;->a:Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;

    .line 25
    .line 26
    invoke-static {}, Lcom/google/protobuf/DescriptorProtos$FileOptions;->a0()Lcom/google/protobuf/DescriptorProtos$FileOptions;

    .line 27
    .line 28
    .line 29
    move-result-object v3

    .line 30
    invoke-static {}, Lcom/google/api/ResourceDescriptor;->Z()Lcom/google/api/ResourceDescriptor;

    .line 31
    .line 32
    .line 33
    move-result-object v4

    .line 34
    const/4 v8, 0x0

    .line 35
    const-class v9, Lcom/google/api/ResourceDescriptor;

    .line 36
    .line 37
    move-object v7, v5

    .line 38
    const/4 v5, 0x0

    .line 39
    const/16 v6, 0x41d

    .line 40
    .line 41
    invoke-static/range {v3 .. v9}, Lcom/google/protobuf/GeneratedMessageLite;->S(Lcom/google/protobuf/MessageLite;Lcom/google/protobuf/MessageLite;Lcom/google/protobuf/Internal$EnumLiteMap;ILcom/google/protobuf/WireFormat$FieldType;ZLjava/lang/Class;)Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    move-object v5, v7

    .line 46
    sput-object v0, Lcom/google/api/ResourceProto;->b:Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;

    .line 47
    .line 48
    invoke-static {}, Lcom/google/protobuf/DescriptorProtos$MessageOptions;->a0()Lcom/google/protobuf/DescriptorProtos$MessageOptions;

    .line 49
    .line 50
    .line 51
    move-result-object v3

    .line 52
    invoke-static {}, Lcom/google/api/ResourceDescriptor;->Z()Lcom/google/api/ResourceDescriptor;

    .line 53
    .line 54
    .line 55
    move-result-object v4

    .line 56
    invoke-static {}, Lcom/google/api/ResourceDescriptor;->Z()Lcom/google/api/ResourceDescriptor;

    .line 57
    .line 58
    .line 59
    move-result-object v5

    .line 60
    move-object v8, v7

    .line 61
    const/16 v7, 0x41d

    .line 62
    .line 63
    const-class v9, Lcom/google/api/ResourceDescriptor;

    .line 64
    .line 65
    const/4 v6, 0x0

    .line 66
    invoke-static/range {v3 .. v9}, Lcom/google/protobuf/GeneratedMessageLite;->T(Lcom/google/protobuf/MessageLite;Ljava/lang/Object;Lcom/google/protobuf/MessageLite;Lcom/google/protobuf/Internal$EnumLiteMap;ILcom/google/protobuf/WireFormat$FieldType;Ljava/lang/Class;)Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;

    .line 67
    .line 68
    .line 69
    move-result-object v0

    .line 70
    sput-object v0, Lcom/google/api/ResourceProto;->c:Lcom/google/protobuf/GeneratedMessageLite$GeneratedExtension;

    .line 71
    .line 72
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
