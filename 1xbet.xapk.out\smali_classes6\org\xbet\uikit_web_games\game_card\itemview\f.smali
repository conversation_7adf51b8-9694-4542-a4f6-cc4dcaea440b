.class public final synthetic Lorg/xbet/uikit_web_games/game_card/itemview/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;


# direct methods
.method public synthetic constructor <init>(ZLorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/f;->a:Z

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/f;->b:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/f;->a:Z

    iget-object v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/f;->b:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->c(ZLorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;Landroid/view/View;)V

    return-void
.end method
