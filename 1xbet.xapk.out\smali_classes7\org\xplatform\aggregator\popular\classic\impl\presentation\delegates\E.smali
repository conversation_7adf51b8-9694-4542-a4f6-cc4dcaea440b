.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LCb1/a;

.field public final synthetic b:Ljava/lang/String;

.field public final synthetic c:LB4/a;


# direct methods
.method public synthetic constructor <init>(LCb1/a;Ljava/lang/String;LB4/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;->a:LCb1/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;->b:Ljava/lang/String;

    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;->c:LB4/a;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;->a:LCb1/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;->b:Ljava/lang/String;

    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/E;->c:LB4/a;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, v2, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;->b(LCb1/a;Ljava/lang/String;LB4/a;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
