.class public final Lcom/google/firebase/perf/BuildConfig;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:Ljava/lang/Boolean;

.field public static final b:Ljava/lang/String;

.field public static final c:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Bo<PERSON>an;

    .line 2
    .line 3
    sput-object v0, Lcom/google/firebase/perf/BuildConfig;->a:Ljava/lang/Boolean;

    .line 4
    .line 5
    const-string v0, "21.0.5"

    .line 6
    .line 7
    sput-object v0, Lcom/google/firebase/perf/BuildConfig;->b:Ljava/lang/String;

    .line 8
    .line 9
    const-string v0, "FIREPERF"

    .line 10
    .line 11
    sput-object v0, Lcom/google/firebase/perf/BuildConfig;->c:<PERSON><PERSON><PERSON>/lang/String;

    .line 12
    .line 13
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
