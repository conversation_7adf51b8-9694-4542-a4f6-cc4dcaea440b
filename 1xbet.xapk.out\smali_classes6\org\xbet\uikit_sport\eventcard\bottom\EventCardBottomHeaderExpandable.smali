.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0010\u0007\n\u0002\u0008\u0008\u0008\u0007\u0018\u00002\u00020\u0001B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001d\u0010\u0016\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\r\u0010\u0018\u001a\u00020\u0014\u00a2\u0006\u0004\u0008\u0018\u0010\u0019R\u0014\u0010\u001d\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010\u001cR\u0014\u0010\u001f\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u001eR\u001b\u0010%\u001a\u00020 8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010$R\u0014\u0010\'\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\u001eR\u0014\u0010)\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010\u001eR\u0016\u0010,\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0016\u00100\u001a\u00020-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008.\u0010/R\u0016\u00102\u001a\u00020-8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00081\u0010/R\u0016\u00104\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u0010+\u00a8\u00065"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "",
        "title",
        "",
        "expanded",
        "setHeader",
        "(Ljava/lang/String;Z)V",
        "b",
        "()Z",
        "Landroid/text/TextPaint;",
        "a",
        "Landroid/text/TextPaint;",
        "headerPaint",
        "I",
        "accordionWidth",
        "Lorg/xbet/uikit/components/accordion/Accordion;",
        "c",
        "Lkotlin/j;",
        "getAccordionView",
        "()Lorg/xbet/uikit/components/accordion/Accordion;",
        "accordionView",
        "d",
        "titleMargin",
        "e",
        "spaceBetween",
        "f",
        "Ljava/lang/String;",
        "headerTitle",
        "",
        "g",
        "F",
        "yHeaderPosition",
        "h",
        "xHeaderPosition",
        "i",
        "headerEllipsizedText",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:I

.field public final e:I

.field public f:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:F

.field public h:F

.field public i:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 1
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    new-instance p2, Landroid/text/TextPaint;

    const/4 v0, 0x1

    invoke-direct {p2, v0}, Landroid/text/TextPaint;-><init>(I)V

    .line 5
    sget v0, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->a:Landroid/text/TextPaint;

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_24:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->b:I

    .line 8
    sget-object p2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    new-instance v0, Lorg/xbet/uikit_sport/eventcard/bottom/d;

    invoke-direct {v0, p1, p0}, Lorg/xbet/uikit_sport/eventcard/bottom/d;-><init>(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;)V

    invoke-static {p2, v0}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->c:Lkotlin/j;

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->d:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->e:I

    .line 11
    const-string p1, ""

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->f:Ljava/lang/String;

    .line 12
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->i:Ljava/lang/String;

    const/4 p1, 0x0

    .line 13
    invoke-virtual {p0, p1}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method public static synthetic a(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;)Lorg/xbet/uikit/components/accordion/Accordion;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->c(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;)Lorg/xbet/uikit/components/accordion/Accordion;

    move-result-object p0

    return-object p0
.end method

.method public static final c(Landroid/content/Context;Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;)Lorg/xbet/uikit/components/accordion/Accordion;
    .locals 6

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/accordion/Accordion;

    .line 2
    .line 3
    sget v3, LlZ0/d;->accordionSecondaryStyle:I

    .line 4
    .line 5
    const/4 v4, 0x2

    .line 6
    const/4 v5, 0x0

    .line 7
    const/4 v2, 0x0

    .line 8
    move-object v1, p0

    .line 9
    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit/components/accordion/Accordion;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 10
    .line 11
    .line 12
    new-instance p0, Landroid/widget/FrameLayout$LayoutParams;

    .line 13
    .line 14
    iget v1, p1, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->b:I

    .line 15
    .line 16
    invoke-virtual {p1}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 17
    .line 18
    .line 19
    move-result-object v2

    .line 20
    sget v3, LlZ0/g;->size_18:I

    .line 21
    .line 22
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    invoke-direct {p0, v1, v2}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v0, p0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 30
    .line 31
    .line 32
    const/4 p0, 0x0

    .line 33
    invoke-virtual {v0, p0}, Landroid/view/View;->setClickable(Z)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 37
    .line 38
    .line 39
    return-object v0
.end method

.method private final getAccordionView()Lorg/xbet/uikit/components/accordion/Accordion;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->c:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/components/accordion/Accordion;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final b()Z
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->getAccordionView()Lorg/xbet/uikit/components/accordion/Accordion;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/components/accordion/Accordion;->getExpanded()Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    return v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->onDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->i:Ljava/lang/String;

    .line 5
    .line 6
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-lez v0, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->i:Ljava/lang/String;

    .line 13
    .line 14
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->h:F

    .line 15
    .line 16
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->g:F

    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->a:Landroid/text/TextPaint;

    .line 19
    .line 20
    invoke-virtual {p1, v0, v1, v2, v3}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    return-void
.end method

.method public onMeasure(II)V
    .locals 5

    .line 1
    invoke-super {p0, p1, p2}, Landroid/widget/FrameLayout;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 9
    .line 10
    .line 11
    move-result p2

    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->f:Ljava/lang/String;

    .line 13
    .line 14
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-lez v0, :cond_1

    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->f:Ljava/lang/String;

    .line 21
    .line 22
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->a:Landroid/text/TextPaint;

    .line 23
    .line 24
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->d:I

    .line 25
    .line 26
    const/4 v3, 0x2

    .line 27
    mul-int/lit8 v2, v2, 0x2

    .line 28
    .line 29
    sub-int v2, p1, v2

    .line 30
    .line 31
    iget v4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->e:I

    .line 32
    .line 33
    sub-int/2addr v2, v4

    .line 34
    iget v4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->b:I

    .line 35
    .line 36
    sub-int/2addr v2, v4

    .line 37
    int-to-float v2, v2

    .line 38
    sget-object v4, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 39
    .line 40
    invoke-static {v0, v1, v2, v4}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->i:Ljava/lang/String;

    .line 49
    .line 50
    div-int/2addr p2, v3

    .line 51
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    const/4 v1, 0x1

    .line 56
    if-ne v0, v1, :cond_0

    .line 57
    .line 58
    int-to-float v0, p1

    .line 59
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->a:Landroid/text/TextPaint;

    .line 60
    .line 61
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->i:Ljava/lang/String;

    .line 62
    .line 63
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 64
    .line 65
    .line 66
    move-result v1

    .line 67
    sub-float/2addr v0, v1

    .line 68
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->d:I

    .line 69
    .line 70
    int-to-float v1, v1

    .line 71
    sub-float/2addr v0, v1

    .line 72
    goto :goto_0

    .line 73
    :cond_0
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->d:I

    .line 74
    .line 75
    int-to-float v0, v0

    .line 76
    :goto_0
    iput v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->h:F

    .line 77
    .line 78
    int-to-float p2, p2

    .line 79
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->a:Landroid/text/TextPaint;

    .line 80
    .line 81
    invoke-virtual {v0}, Landroid/graphics/Paint;->descent()F

    .line 82
    .line 83
    .line 84
    move-result v0

    .line 85
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->a:Landroid/text/TextPaint;

    .line 86
    .line 87
    invoke-virtual {v1}, Landroid/graphics/Paint;->ascent()F

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    add-float/2addr v0, v1

    .line 92
    int-to-float v1, v3

    .line 93
    div-float/2addr v0, v1

    .line 94
    sub-float/2addr p2, v0

    .line 95
    iput p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->g:F

    .line 96
    .line 97
    :cond_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->getAccordionView()Lorg/xbet/uikit/components/accordion/Accordion;

    .line 98
    .line 99
    .line 100
    move-result-object p2

    .line 101
    invoke-virtual {p2}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 102
    .line 103
    .line 104
    move-result-object v0

    .line 105
    if-eqz v0, :cond_2

    .line 106
    .line 107
    check-cast v0, Landroid/widget/FrameLayout$LayoutParams;

    .line 108
    .line 109
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->d:I

    .line 110
    .line 111
    sub-int/2addr p1, v1

    .line 112
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->b:I

    .line 113
    .line 114
    sub-int/2addr p1, v1

    .line 115
    invoke-virtual {v0, p1}, Landroid/view/ViewGroup$MarginLayoutParams;->setMarginStart(I)V

    .line 116
    .line 117
    .line 118
    invoke-virtual {p2, v0}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 119
    .line 120
    .line 121
    return-void

    .line 122
    :cond_2
    new-instance p1, Ljava/lang/NullPointerException;

    .line 123
    .line 124
    const-string p2, "null cannot be cast to non-null type android.widget.FrameLayout.LayoutParams"

    .line 125
    .line 126
    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 127
    .line 128
    .line 129
    throw p1
.end method

.method public final setHeader(Ljava/lang/String;Z)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->f:Ljava/lang/String;

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeaderExpandable;->getAccordionView()Lorg/xbet/uikit/components/accordion/Accordion;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    const/4 v0, 0x1

    .line 8
    invoke-virtual {p1, p2, v0}, Lorg/xbet/uikit/components/accordion/Accordion;->setExpanded(ZZ)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
