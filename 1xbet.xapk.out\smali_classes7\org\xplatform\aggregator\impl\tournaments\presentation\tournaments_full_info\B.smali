.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw81/c;",
            ">;"
        }
    .end annotation
.end field

.field public final b:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lgk/b;",
            ">;"
        }
    .end annotation
.end field

.field public final c:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lm8/a;",
            ">;"
        }
    .end annotation
.end field

.field public final d:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;"
        }
    .end annotation
.end field

.field public final e:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw81/g;",
            ">;"
        }
    .end annotation
.end field

.field public final f:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw81/d;",
            ">;"
        }
    .end annotation
.end field

.field public final g:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lw81/e;",
            ">;"
        }
    .end annotation
.end field

.field public final h:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;"
        }
    .end annotation
.end field

.field public final i:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
            ">;"
        }
    .end annotation
.end field

.field public final j:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;"
        }
    .end annotation
.end field

.field public final k:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LSX0/c;",
            ">;"
        }
    .end annotation
.end field

.field public final l:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public final m:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LHX0/e;",
            ">;"
        }
    .end annotation
.end field

.field public final n:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final o:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LwX0/C;",
            ">;"
        }
    .end annotation
.end field

.field public final p:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LP91/b;",
            ">;"
        }
    .end annotation
.end field

.field public final q:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g;",
            ">;"
        }
    .end annotation
.end field

.field public final r:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;"
        }
    .end annotation
.end field

.field public final s:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/a;",
            ">;"
        }
    .end annotation
.end field

.field public final t:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "LnR/d;",
            ">;"
        }
    .end annotation
.end field

.field public final u:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Leu/i;",
            ">;"
        }
    .end annotation
.end field

.field public final v:LBc/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lw81/c;",
            ">;",
            "LBc/a<",
            "Lgk/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lw81/g;",
            ">;",
            "LBc/a<",
            "Lw81/d;",
            ">;",
            "LBc/a<",
            "Lw81/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Long;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "LnR/d;",
            ">;",
            "LBc/a<",
            "Leu/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->a:LBc/a;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->b:LBc/a;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->c:LBc/a;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->d:LBc/a;

    .line 11
    .line 12
    iput-object p5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->e:LBc/a;

    .line 13
    .line 14
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->f:LBc/a;

    .line 15
    .line 16
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->g:LBc/a;

    .line 17
    .line 18
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->h:LBc/a;

    .line 19
    .line 20
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->i:LBc/a;

    .line 21
    .line 22
    iput-object p10, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->j:LBc/a;

    .line 23
    .line 24
    iput-object p11, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->k:LBc/a;

    .line 25
    .line 26
    iput-object p12, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->l:LBc/a;

    .line 27
    .line 28
    iput-object p13, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->m:LBc/a;

    .line 29
    .line 30
    iput-object p14, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->n:LBc/a;

    .line 31
    .line 32
    iput-object p15, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->o:LBc/a;

    .line 33
    .line 34
    move-object/from16 p1, p16

    .line 35
    .line 36
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->p:LBc/a;

    .line 37
    .line 38
    move-object/from16 p1, p17

    .line 39
    .line 40
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->q:LBc/a;

    .line 41
    .line 42
    move-object/from16 p1, p18

    .line 43
    .line 44
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->r:LBc/a;

    .line 45
    .line 46
    move-object/from16 p1, p19

    .line 47
    .line 48
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->s:LBc/a;

    .line 49
    .line 50
    move-object/from16 p1, p20

    .line 51
    .line 52
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->t:LBc/a;

    .line 53
    .line 54
    move-object/from16 p1, p21

    .line 55
    .line 56
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->u:LBc/a;

    .line 57
    .line 58
    move-object/from16 p1, p22

    .line 59
    .line 60
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->v:LBc/a;

    .line 61
    .line 62
    return-void
.end method

.method public static a(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;
    .locals 23
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "LBc/a<",
            "Lw81/c;",
            ">;",
            "LBc/a<",
            "Lgk/b;",
            ">;",
            "LBc/a<",
            "Lm8/a;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/internet/a;",
            ">;",
            "LBc/a<",
            "Lw81/g;",
            ">;",
            "LBc/a<",
            "Lw81/d;",
            ">;",
            "LBc/a<",
            "Lw81/e;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/ui_common/utils/M;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/api/navigation/TournamentsPage;",
            ">;",
            "LBc/a<",
            "Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;",
            ">;",
            "LBc/a<",
            "LSX0/c;",
            ">;",
            "LBc/a<",
            "Ljava/lang/Long;",
            ">;",
            "LBc/a<",
            "LHX0/e;",
            ">;",
            "LBc/a<",
            "Ljava/lang/String;",
            ">;",
            "LBc/a<",
            "LwX0/C;",
            ">;",
            "LBc/a<",
            "LP91/b;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/analytics/domain/scope/g0;",
            ">;",
            "LBc/a<",
            "LnR/a;",
            ">;",
            "LBc/a<",
            "LnR/d;",
            ">;",
            "LBc/a<",
            "Leu/i;",
            ">;",
            "LBc/a<",
            "Lorg/xbet/remoteconfig/domain/usecases/i;",
            ">;)",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;"
        }
    .end annotation

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-object/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v13, p12

    .line 28
    .line 29
    move-object/from16 v14, p13

    .line 30
    .line 31
    move-object/from16 v15, p14

    .line 32
    .line 33
    move-object/from16 v16, p15

    .line 34
    .line 35
    move-object/from16 v17, p16

    .line 36
    .line 37
    move-object/from16 v18, p17

    .line 38
    .line 39
    move-object/from16 v19, p18

    .line 40
    .line 41
    move-object/from16 v20, p19

    .line 42
    .line 43
    move-object/from16 v21, p20

    .line 44
    .line 45
    move-object/from16 v22, p21

    .line 46
    .line 47
    invoke-direct/range {v0 .. v22}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;-><init>(LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;LBc/a;)V

    .line 48
    .line 49
    .line 50
    return-object v0
.end method

.method public static c(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;
    .locals 24

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 2
    .line 3
    move-object/from16 v1, p0

    .line 4
    .line 5
    move-object/from16 v2, p1

    .line 6
    .line 7
    move-object/from16 v3, p2

    .line 8
    .line 9
    move-object/from16 v4, p3

    .line 10
    .line 11
    move-object/from16 v5, p4

    .line 12
    .line 13
    move-object/from16 v6, p5

    .line 14
    .line 15
    move-object/from16 v7, p6

    .line 16
    .line 17
    move-object/from16 v8, p7

    .line 18
    .line 19
    move-object/from16 v9, p8

    .line 20
    .line 21
    move-object/from16 v10, p9

    .line 22
    .line 23
    move-object/from16 v11, p10

    .line 24
    .line 25
    move-wide/from16 v12, p11

    .line 26
    .line 27
    move-object/from16 v14, p13

    .line 28
    .line 29
    move-object/from16 v15, p14

    .line 30
    .line 31
    move-object/from16 v16, p15

    .line 32
    .line 33
    move-object/from16 v17, p16

    .line 34
    .line 35
    move-object/from16 v18, p17

    .line 36
    .line 37
    move-object/from16 v19, p18

    .line 38
    .line 39
    move-object/from16 v20, p19

    .line 40
    .line 41
    move-object/from16 v21, p20

    .line 42
    .line 43
    move-object/from16 v22, p21

    .line 44
    .line 45
    move-object/from16 v23, p22

    .line 46
    .line 47
    invoke-direct/range {v0 .. v23}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;-><init>(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)V

    .line 48
    .line 49
    .line 50
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;
    .locals 25

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->a:LBc/a;

    .line 4
    .line 5
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-object v2, v1

    .line 10
    check-cast v2, Lw81/c;

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->b:LBc/a;

    .line 13
    .line 14
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    move-object v3, v1

    .line 19
    check-cast v3, Lgk/b;

    .line 20
    .line 21
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->c:LBc/a;

    .line 22
    .line 23
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    move-object v4, v1

    .line 28
    check-cast v4, Lm8/a;

    .line 29
    .line 30
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->d:LBc/a;

    .line 31
    .line 32
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    move-object v5, v1

    .line 37
    check-cast v5, Lorg/xbet/ui_common/utils/internet/a;

    .line 38
    .line 39
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->e:LBc/a;

    .line 40
    .line 41
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object v1

    .line 45
    move-object v6, v1

    .line 46
    check-cast v6, Lw81/g;

    .line 47
    .line 48
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->f:LBc/a;

    .line 49
    .line 50
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    move-object v7, v1

    .line 55
    check-cast v7, Lw81/d;

    .line 56
    .line 57
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->g:LBc/a;

    .line 58
    .line 59
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    move-object v8, v1

    .line 64
    check-cast v8, Lw81/e;

    .line 65
    .line 66
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->h:LBc/a;

    .line 67
    .line 68
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    move-object v9, v1

    .line 73
    check-cast v9, Lorg/xbet/ui_common/utils/M;

    .line 74
    .line 75
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->i:LBc/a;

    .line 76
    .line 77
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v1

    .line 81
    move-object v10, v1

    .line 82
    check-cast v10, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 83
    .line 84
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->j:LBc/a;

    .line 85
    .line 86
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v1

    .line 90
    move-object v11, v1

    .line 91
    check-cast v11, Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;

    .line 92
    .line 93
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->k:LBc/a;

    .line 94
    .line 95
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v1

    .line 99
    move-object v12, v1

    .line 100
    check-cast v12, LSX0/c;

    .line 101
    .line 102
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->l:LBc/a;

    .line 103
    .line 104
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 105
    .line 106
    .line 107
    move-result-object v1

    .line 108
    check-cast v1, Ljava/lang/Long;

    .line 109
    .line 110
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    .line 111
    .line 112
    .line 113
    move-result-wide v13

    .line 114
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->m:LBc/a;

    .line 115
    .line 116
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 117
    .line 118
    .line 119
    move-result-object v1

    .line 120
    move-object v15, v1

    .line 121
    check-cast v15, LHX0/e;

    .line 122
    .line 123
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->n:LBc/a;

    .line 124
    .line 125
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    move-object/from16 v16, v1

    .line 130
    .line 131
    check-cast v16, Ljava/lang/String;

    .line 132
    .line 133
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->o:LBc/a;

    .line 134
    .line 135
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object v1

    .line 139
    move-object/from16 v17, v1

    .line 140
    .line 141
    check-cast v17, LwX0/C;

    .line 142
    .line 143
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->p:LBc/a;

    .line 144
    .line 145
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 146
    .line 147
    .line 148
    move-result-object v1

    .line 149
    move-object/from16 v18, v1

    .line 150
    .line 151
    check-cast v18, LP91/b;

    .line 152
    .line 153
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->q:LBc/a;

    .line 154
    .line 155
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 156
    .line 157
    .line 158
    move-result-object v1

    .line 159
    move-object/from16 v19, v1

    .line 160
    .line 161
    check-cast v19, Lorg/xbet/analytics/domain/scope/g;

    .line 162
    .line 163
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->r:LBc/a;

    .line 164
    .line 165
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 166
    .line 167
    .line 168
    move-result-object v1

    .line 169
    move-object/from16 v20, v1

    .line 170
    .line 171
    check-cast v20, Lorg/xbet/analytics/domain/scope/g0;

    .line 172
    .line 173
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->s:LBc/a;

    .line 174
    .line 175
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 176
    .line 177
    .line 178
    move-result-object v1

    .line 179
    move-object/from16 v21, v1

    .line 180
    .line 181
    check-cast v21, LnR/a;

    .line 182
    .line 183
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->t:LBc/a;

    .line 184
    .line 185
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 186
    .line 187
    .line 188
    move-result-object v1

    .line 189
    move-object/from16 v22, v1

    .line 190
    .line 191
    check-cast v22, LnR/d;

    .line 192
    .line 193
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->u:LBc/a;

    .line 194
    .line 195
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 196
    .line 197
    .line 198
    move-result-object v1

    .line 199
    move-object/from16 v23, v1

    .line 200
    .line 201
    check-cast v23, Leu/i;

    .line 202
    .line 203
    iget-object v1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->v:LBc/a;

    .line 204
    .line 205
    invoke-interface {v1}, LBc/a;->get()Ljava/lang/Object;

    .line 206
    .line 207
    .line 208
    move-result-object v1

    .line 209
    move-object/from16 v24, v1

    .line 210
    .line 211
    check-cast v24, Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 212
    .line 213
    invoke-static/range {v2 .. v24}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->c(Lw81/c;Lgk/b;Lm8/a;Lorg/xbet/ui_common/utils/internet/a;Lw81/g;Lw81/d;Lw81/e;Lorg/xbet/ui_common/utils/M;Lorg/xplatform/aggregator/api/navigation/TournamentsPage;Lorg/xplatform/aggregator/impl/core/presentation/OpenGameDelegate;LSX0/c;JLHX0/e;Ljava/lang/String;LwX0/C;LP91/b;Lorg/xbet/analytics/domain/scope/g;Lorg/xbet/analytics/domain/scope/g0;LnR/a;LnR/d;Leu/i;Lorg/xbet/remoteconfig/domain/usecases/i;)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 214
    .line 215
    .line 216
    move-result-object v1

    .line 217
    return-object v1
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/B;->b()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsFullInfoSharedViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
