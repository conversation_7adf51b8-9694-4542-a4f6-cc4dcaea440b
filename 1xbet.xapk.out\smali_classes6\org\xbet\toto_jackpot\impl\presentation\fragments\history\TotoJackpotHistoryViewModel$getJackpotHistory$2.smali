.class final Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_jackpot.impl.presentation.fragments.history.TotoJackpotHistoryViewModel$getJackpotHistory$2"
    f = "TotoJackpotHistoryViewModel.kt"
    l = {
        0x8e
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->T3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $jackpotType:I

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;ILkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;",
            "I",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    iput p2, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->$jackpotType:I

    const/4 p1, 0x2

    invoke-direct {p0, p1, p3}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;

    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    iget v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->$jackpotType:I

    invoke-direct {p1, v0, v1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;ILkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_1

    .line 10
    .line 11
    if-ne v1, v3, :cond_0

    .line 12
    .line 13
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 29
    .line 30
    invoke-static {p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->A3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->$jackpotType:I

    .line 35
    .line 36
    iput v3, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->label:I

    .line 37
    .line 38
    invoke-virtual {p1, v1, v3, v2, p0}, Lorg/xbet/toto_jackpot/impl/domain/scenario/GetJackpotHistoryScenario;->a(IIZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    if-ne p1, v0, :cond_2

    .line 43
    .line 44
    return-object v0

    .line 45
    :cond_2
    :goto_0
    check-cast p1, LuW0/b;

    .line 46
    .line 47
    invoke-virtual {p1}, LuW0/b;->a()Ljava/util/List;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    invoke-virtual {p1}, LuW0/b;->b()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 56
    .line 57
    invoke-static {v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->v3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlinx/coroutines/flow/V;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    :cond_3
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 62
    .line 63
    .line 64
    move-result-object v4

    .line 65
    move-object v5, v4

    .line 66
    check-cast v5, Ljava/lang/Boolean;

    .line 67
    .line 68
    invoke-virtual {v5}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 69
    .line 70
    .line 71
    invoke-static {v3}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 72
    .line 73
    .line 74
    move-result-object v5

    .line 75
    invoke-interface {v1, v4, v5}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 76
    .line 77
    .line 78
    move-result v4

    .line 79
    if-eqz v4, :cond_3

    .line 80
    .line 81
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 82
    .line 83
    invoke-static {v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->F3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlinx/coroutines/flow/V;

    .line 84
    .line 85
    .line 86
    move-result-object v4

    .line 87
    :cond_4
    invoke-interface {v4}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 88
    .line 89
    .line 90
    move-result-object v1

    .line 91
    move-object v3, v1

    .line 92
    check-cast v3, Ljava/lang/Boolean;

    .line 93
    .line 94
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 95
    .line 96
    .line 97
    invoke-static {v2}, LHc/a;->a(Z)Ljava/lang/Boolean;

    .line 98
    .line 99
    .line 100
    move-result-object v3

    .line 101
    invoke-interface {v4, v1, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 102
    .line 103
    .line 104
    move-result v1

    .line 105
    if-eqz v1, :cond_4

    .line 106
    .line 107
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 108
    .line 109
    invoke-static {v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->w3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lkotlinx/coroutines/flow/V;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    :cond_5
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 114
    .line 115
    .line 116
    move-result-object v2

    .line 117
    move-object v3, v2

    .line 118
    check-cast v3, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a;

    .line 119
    .line 120
    sget-object v3, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$c;->a:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$a$c;

    .line 121
    .line 122
    invoke-interface {v1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 123
    .line 124
    .line 125
    move-result v2

    .line 126
    if-eqz v2, :cond_5

    .line 127
    .line 128
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 129
    .line 130
    invoke-static {v1, v0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->K3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/util/List;Ljava/lang/String;)V

    .line 131
    .line 132
    .line 133
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getJackpotHistory$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 134
    .line 135
    invoke-static {v1, v0, p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->J3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Ljava/util/List;Ljava/lang/String;)V

    .line 136
    .line 137
    .line 138
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 139
    .line 140
    return-object p1
.end method
