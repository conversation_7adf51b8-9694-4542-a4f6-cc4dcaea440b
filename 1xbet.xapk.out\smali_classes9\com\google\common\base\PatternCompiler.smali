.class interface abstract Lcom/google/common/base/PatternCompiler;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation build Lcom/google/common/annotations/GwtIncompatible;
.end annotation

.annotation runtime Lcom/google/common/base/ElementTypesAreNonnullByDefault;
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/String;)Lcom/google/common/base/CommonPattern;
    .annotation build Lcom/google/errorprone/annotations/RestrictedApi;
    .end annotation
.end method

.method public abstract b()Z
    .annotation build Lcom/google/errorprone/annotations/RestrictedApi;
    .end annotation
.end method
