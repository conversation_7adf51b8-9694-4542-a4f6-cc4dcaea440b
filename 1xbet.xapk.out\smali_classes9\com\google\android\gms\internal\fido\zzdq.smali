.class final Lcom/google/android/gms/internal/fido/zzdq;
.super Lcom/google/android/gms/internal/fido/zzdr;
.source "SourceFile"


# static fields
.field static final zza:Lcom/google/android/gms/internal/fido/zzdq;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/fido/zzdq;

    invoke-direct {v0}, Lcom/google/android/gms/internal/fido/zzdq;-><init>()V

    sput-object v0, Lcom/google/android/gms/internal/fido/zzdq;->zza:Lcom/google/android/gms/internal/fido/zzdq;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/google/android/gms/internal/fido/zzdr;-><init>()V

    return-void
.end method
