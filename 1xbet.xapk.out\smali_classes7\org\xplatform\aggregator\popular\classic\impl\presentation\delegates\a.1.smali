.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/fragment/app/J;


# instance fields
.field public final synthetic a:LAb1/a;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;


# direct methods
.method public synthetic constructor <init>(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;->a:LAb1/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;->b:Lorg/xplatform/aggregator/api/model/Game;

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;->a:LAb1/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/a;->b:Lorg/xplatform/aggregator/api/model/Game;

    invoke-static {v0, v1, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->b(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method
