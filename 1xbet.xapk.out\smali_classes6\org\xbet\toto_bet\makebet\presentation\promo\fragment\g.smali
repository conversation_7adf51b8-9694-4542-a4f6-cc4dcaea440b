.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->k0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;LAX0/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->l0:LAX0/b;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
