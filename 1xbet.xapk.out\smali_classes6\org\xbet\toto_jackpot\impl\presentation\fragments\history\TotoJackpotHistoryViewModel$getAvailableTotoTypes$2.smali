.class final Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_jackpot.impl.presentation.fragments.history.TotoJackpotHistoryViewModel$getAvailableTotoTypes$2"
    f = "TotoJackpotHistoryViewModel.kt"
    l = {
        0x6d
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->N3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;

    iget-object v0, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;-><init>(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x0

    .line 8
    const/4 v3, 0x1

    .line 9
    if-eqz v1, :cond_1

    .line 10
    .line 11
    if-ne v1, v3, :cond_0

    .line 12
    .line 13
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 14
    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 29
    .line 30
    invoke-static {p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->z3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)LwW0/a;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    iget-object v1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 35
    .line 36
    invoke-static {v1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->C3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 37
    .line 38
    .line 39
    move-result-object v1

    .line 40
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {v1}, Lek0/o;->O1()I

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    iput v3, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->label:I

    .line 49
    .line 50
    invoke-virtual {p1, v1, v2, p0}, LwW0/a;->a(IZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    if-ne p1, v0, :cond_2

    .line 55
    .line 56
    return-object v0

    .line 57
    :cond_2
    :goto_0
    check-cast p1, Ljava/lang/Number;

    .line 58
    .line 59
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 60
    .line 61
    .line 62
    move-result p1

    .line 63
    if-nez p1, :cond_3

    .line 64
    .line 65
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 66
    .line 67
    sget-object v0, LSX0/e$c;->a:LSX0/e$c;

    .line 68
    .line 69
    invoke-static {p1, v0}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->I3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;LSX0/e;)V

    .line 70
    .line 71
    .line 72
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 73
    .line 74
    invoke-static {p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->G3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)LwW0/o;

    .line 75
    .line 76
    .line 77
    move-result-object p1

    .line 78
    invoke-virtual {p1, v2}, LwW0/o;->a(Z)V

    .line 79
    .line 80
    .line 81
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 82
    .line 83
    return-object p1

    .line 84
    :cond_3
    iget-object p1, p0, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel$getAvailableTotoTypes$2;->this$0:Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;

    .line 85
    .line 86
    invoke-static {p1}, Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;->D3(Lorg/xbet/toto_jackpot/impl/presentation/fragments/history/TotoJackpotHistoryViewModel;)V

    .line 87
    .line 88
    .line 89
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p1
.end method
