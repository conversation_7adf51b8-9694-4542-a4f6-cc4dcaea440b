.class public final synthetic LNZ0/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroid/view/View$OnClickListener;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/chips/ChipGroup;

.field public final synthetic b:Lorg/xbet/uikit/components/chips/Chip;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/chips/ChipGroup;Lorg/xbet/uikit/components/chips/Chip;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LNZ0/c;->a:Lorg/xbet/uikit/components/chips/ChipGroup;

    iput-object p2, p0, LNZ0/c;->b:Lorg/xbet/uikit/components/chips/Chip;

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 2

    .line 1
    iget-object v0, p0, LNZ0/c;->a:Lorg/xbet/uikit/components/chips/ChipGroup;

    iget-object v1, p0, LNZ0/c;->b:Lorg/xbet/uikit/components/chips/Chip;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/chips/ChipGroup;->b(Lorg/xbet/uikit/components/chips/ChipGroup;Lorg/xbet/uikit/components/chips/Chip;Landroid/view/View;)V

    return-void
.end method
