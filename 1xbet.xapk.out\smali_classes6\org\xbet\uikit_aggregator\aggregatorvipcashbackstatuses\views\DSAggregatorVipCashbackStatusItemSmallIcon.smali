.class public final Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;
.super Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u0008\n\u0002\u0008\u0018\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008!\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0001\u0018\u0000 v2\u00020\u0001:\u0001;B\u001b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0017\u0010\u000b\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u000b\u0010\u000cJ\u000f\u0010\r\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0013\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0013\u0010\u0012J\u0017\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0012J\u000f\u0010\u0016\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u000eJ\u000f\u0010\u0017\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0017\u0010\u000eJ\u000f\u0010\u0018\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u000eJ\u000f\u0010\u0019\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u0019\u0010\u000eJ\u000f\u0010\u001a\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001a\u0010\u000eJ\u000f\u0010\u001b\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001b\u0010\u000eJ\u000f\u0010\u001c\u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\u001c\u0010\u000eJ\u001f\u0010\u001f\u001a\u00020\n2\u0006\u0010\u001d\u001a\u00020\u000f2\u0006\u0010\u001e\u001a\u00020\u000fH\u0014\u00a2\u0006\u0004\u0008\u001f\u0010 J7\u0010&\u001a\u00020\n2\u0006\u0010!\u001a\u00020\u00082\u0006\u0010\"\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u000f2\u0006\u0010$\u001a\u00020\u000f2\u0006\u0010%\u001a\u00020\u000fH\u0014\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010*\u001a\u00020\n2\u0006\u0010)\u001a\u00020(H\u0016\u00a2\u0006\u0004\u0008*\u0010+J\u0019\u0010.\u001a\u00020\n2\u0008\u0010-\u001a\u0004\u0018\u00010,H\u0016\u00a2\u0006\u0004\u0008.\u0010/J\u0017\u00102\u001a\u00020\n2\u0006\u00101\u001a\u000200H\u0016\u00a2\u0006\u0004\u00082\u00103J\u0017\u00104\u001a\u00020\n2\u0006\u00101\u001a\u000200H\u0016\u00a2\u0006\u0004\u00084\u00103J\u0017\u00105\u001a\u00020\n2\u0006\u00101\u001a\u000200H\u0016\u00a2\u0006\u0004\u00085\u00103J\u0017\u00106\u001a\u00020\n2\u0006\u00101\u001a\u000200H\u0016\u00a2\u0006\u0004\u00086\u00103J\u0017\u00107\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\u0008H\u0016\u00a2\u0006\u0004\u00087\u0010\u000cJ\u001b\u00109\u001a\u00020\n2\n\u0008\u0001\u00108\u001a\u0004\u0018\u00010\u000fH\u0016\u00a2\u0006\u0004\u00089\u0010:J\u000f\u0010;\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008;\u0010\u000eJ\u000f\u0010<\u001a\u00020\nH\u0014\u00a2\u0006\u0004\u0008<\u0010\u000eR\u0014\u0010>\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008<\u0010=R\u0014\u0010@\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010=R\u0014\u0010A\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0017\u0010=R\u0014\u0010B\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010=R\u0014\u0010C\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0019\u0010=R\u0014\u0010D\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001b\u0010=R\u0014\u0010E\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001a\u0010=R\u0014\u0010F\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010=R\u0014\u0010G\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\r\u0010=R\u0014\u0010H\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0016\u0010=R\u0014\u0010I\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010=R\u0014\u0010J\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010=R\u0014\u0010K\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0014\u0010=R\u0014\u0010L\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010=R\u0014\u0010M\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000b\u0010=R\u0014\u0010O\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010=R\u0014\u0010Q\u001a\u00020\u000f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010=R\u0014\u0010U\u001a\u00020R8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008S\u0010TR\u0016\u0010\t\u001a\u00020\u00088\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u0014\u0010[\u001a\u00020X8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Y\u0010ZR\u0014\u0010_\u001a\u00020\\8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008]\u0010^R\u0014\u0010a\u001a\u00020\\8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008`\u0010^R\u0014\u0010c\u001a\u00020\\8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008b\u0010^R\u0014\u0010e\u001a\u00020\\8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008d\u0010^R\u001a\u0010k\u001a\u00020f8\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008g\u0010h\u001a\u0004\u0008i\u0010jR \u0010r\u001a\u0008\u0012\u0004\u0012\u00020m0l8\u0014X\u0094\u0004\u00a2\u0006\u000c\n\u0004\u0008n\u0010o\u001a\u0004\u0008p\u0010qR\u0014\u0010u\u001a\u00020m8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\u0008s\u0010t\u00a8\u0006w"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "isActive",
        "",
        "p",
        "(Z)V",
        "j",
        "()V",
        "",
        "parentMeasuredWidth",
        "o",
        "(I)V",
        "l",
        "n",
        "m",
        "k",
        "d",
        "i",
        "f",
        "h",
        "g",
        "e",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Lj31/a;",
        "data",
        "setData",
        "(Lj31/a;)V",
        "Landroid/graphics/drawable/Drawable;",
        "drawable",
        "setStatusDrawable",
        "(Landroid/graphics/drawable/Drawable;)V",
        "",
        "text",
        "setStatusText",
        "(Ljava/lang/String;)V",
        "setCashbackText",
        "setExperienceText",
        "setCoefText",
        "setIsActive",
        "resId",
        "setStatusDrawableRes",
        "(Ljava/lang/Integer;)V",
        "a",
        "b",
        "I",
        "size10",
        "c",
        "size14",
        "size40",
        "size96",
        "space2",
        "space4",
        "space8",
        "space10",
        "space6",
        "space12",
        "tvStatusLineHeight",
        "tvCashbackLineHeight",
        "colorSecondary",
        "colorBackgroundLight60",
        "colorBackgroundContent",
        "q",
        "colorBackground",
        "r",
        "cardHeight",
        "Landroid/graphics/Rect;",
        "s",
        "Landroid/graphics/Rect;",
        "helperRect",
        "t",
        "Z",
        "Landroid/widget/ImageView;",
        "u",
        "Landroid/widget/ImageView;",
        "ivStatus",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "v",
        "Landroidx/appcompat/widget/AppCompatTextView;",
        "tvStatus",
        "w",
        "tvCashback",
        "x",
        "tvExperience",
        "y",
        "tvCoef",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "z",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "getShimmerView",
        "()Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerView",
        "",
        "Landroid/view/View;",
        "A",
        "Ljava/util/List;",
        "getContentViews",
        "()Ljava/util/List;",
        "contentViews",
        "getView",
        "()Landroid/view/View;",
        "view",
        "B",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final B:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final C:I


# instance fields
.field public final A:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public final l:I

.field public final m:I

.field public final n:I

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:I

.field public final s:Landroid/graphics/Rect;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public t:Z

.field public final u:Landroid/widget/ImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y:Landroidx/appcompat/widget/AppCompatTextView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final z:Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->B:Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->C:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 21
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    move-object/from16 v0, p0

    move-object/from16 v2, p1

    .line 2
    invoke-direct/range {p0 .. p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    sget v3, LlZ0/g;->size_10:I

    invoke-virtual {v1, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v1

    iput v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->b:I

    .line 4
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v3

    sget v4, LlZ0/g;->size_14:I

    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v3

    iput v3, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->c:I

    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->size_40:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v4

    iput v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->d:I

    .line 6
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    sget v6, LlZ0/g;->size_96:I

    invoke-virtual {v5, v6}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v7

    iput v7, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->e:I

    .line 7
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    sget v6, LlZ0/g;->space_2:I

    invoke-virtual {v5, v6}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v5

    iput v5, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->f:I

    .line 8
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v5

    sget v6, LlZ0/g;->space_4:I

    invoke-virtual {v5, v6}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v5

    iput v5, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->g:I

    .line 9
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v6

    sget v8, LlZ0/g;->space_8:I

    invoke-virtual {v6, v8}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v6

    iput v6, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->h:I

    .line 10
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v8

    sget v9, LlZ0/g;->space_10:I

    invoke-virtual {v8, v9}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v8

    iput v8, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->i:I

    .line 11
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v9

    sget v10, LlZ0/g;->space_6:I

    invoke-virtual {v9, v10}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v9

    iput v9, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->j:I

    .line 12
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v10

    sget v11, LlZ0/g;->space_12:I

    invoke-virtual {v10, v11}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v10

    iput v10, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v10

    sget v11, LlZ0/g;->line_height_14:I

    invoke-virtual {v10, v11}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v10

    iput v10, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->l:I

    .line 14
    invoke-virtual {v0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v10

    sget v11, LlZ0/g;->line_height_24:I

    invoke-virtual {v10, v11}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v10

    iput v10, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->m:I

    .line 15
    sget v10, LlZ0/d;->uikitSecondary:I

    const/4 v11, 0x0

    const/4 v12, 0x2

    invoke-static {v2, v10, v11, v12, v11}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v10

    iput v10, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->n:I

    .line 16
    sget v13, LlZ0/d;->uikitBackgroundLight60:I

    invoke-static {v2, v13, v11, v12, v11}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v13

    iput v13, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->o:I

    .line 17
    sget v13, LlZ0/d;->uikitBackgroundContent:I

    invoke-static {v2, v13, v11, v12, v11}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v13

    iput v13, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->p:I

    .line 18
    sget v13, LlZ0/d;->uikitBackground:I

    invoke-static {v2, v13, v11, v12, v11}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v13

    iput v13, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->q:I

    .line 19
    iput v7, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->r:I

    .line 20
    new-instance v13, Landroid/graphics/Rect;

    invoke-direct {v13}, Landroid/graphics/Rect;-><init>()V

    iput-object v13, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 21
    new-instance v13, Landroid/widget/ImageView;

    invoke-direct {v13, v2}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    .line 22
    sget v14, LS11/d;->aggregatorVipCashbackStatusesIvStatus:I

    invoke-virtual {v13, v14}, Landroid/view/View;->setId(I)V

    .line 23
    new-instance v14, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v14, v4, v4}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v13, v14}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 24
    iput-object v13, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 25
    new-instance v14, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v14, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 26
    sget v4, LS11/d;->aggregatorVipCashbackStatusesTvStatus:I

    invoke-virtual {v14, v4}, Landroid/view/View;->setId(I)V

    .line 27
    sget v4, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    invoke-static {v14, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 28
    new-instance v4, Landroid/view/ViewGroup$LayoutParams;

    const/4 v15, -0x2

    invoke-direct {v4, v15, v15}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v14, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    const/16 v4, 0x10

    .line 29
    invoke-virtual {v14, v4}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v12, 0x1

    .line 30
    invoke-virtual {v14, v12}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 31
    sget-object v11, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v14, v11}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v12, 0x3

    .line 32
    invoke-virtual {v14, v12}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 v12, 0x0

    .line 33
    invoke-virtual {v14, v12}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 34
    iput-object v14, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 35
    new-instance v12, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v12, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 36
    sget v4, LS11/d;->aggregatorVipCashbackStatusesTvCashback:I

    invoke-virtual {v12, v4}, Landroid/view/View;->setId(I)V

    .line 37
    sget v4, LlZ0/n;->TextStyle_Title_Medium_M_TextPrimary:I

    invoke-static {v12, v4}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 38
    new-instance v4, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v4, v15, v15}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v12, v4}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    const/16 v4, 0x10

    .line 39
    invoke-virtual {v12, v4}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v4, 0x1

    .line 40
    invoke-virtual {v12, v4}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 41
    invoke-virtual {v12, v11}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v4, 0x3

    .line 42
    invoke-virtual {v12, v4}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 v4, 0x0

    .line 43
    invoke-virtual {v12, v4}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 44
    iput-object v12, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 45
    new-instance v4, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v4, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 46
    sget v15, LS11/d;->aggregatorVipCashbackStatusesTvExperience:I

    invoke-virtual {v4, v15}, Landroid/view/View;->setId(I)V

    .line 47
    sget v15, LlZ0/n;->TextStyle_Caption_Medium_M_Secondary:I

    invoke-static {v4, v15}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 48
    new-instance v15, Landroid/view/ViewGroup$LayoutParams;

    move-object/from16 v19, v12

    .line 49
    invoke-virtual {v4}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v12

    move-object/from16 v20, v14

    sget v14, LlZ0/g;->size_22:I

    invoke-virtual {v12, v14}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v12

    const/4 v14, -0x2

    .line 50
    invoke-direct {v15, v14, v12}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v4, v15}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 51
    invoke-virtual {v4, v8, v5, v6, v5}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 52
    sget v8, LlZ0/h;->rounded_background_full:I

    invoke-static {v2, v8}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v8

    invoke-virtual {v4, v8}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    const/16 v8, 0x10

    .line 53
    invoke-virtual {v4, v8}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v8, 0x1

    .line 54
    invoke-virtual {v4, v8}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 55
    invoke-virtual {v4, v11}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v8, 0x3

    .line 56
    invoke-virtual {v4, v8}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 v8, 0x0

    .line 57
    invoke-virtual {v4, v8}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 58
    sget v12, LlZ0/h;->ic_glyph_increase:I

    .line 59
    invoke-static {v2, v12}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v12

    if-eqz v12, :cond_0

    .line 60
    invoke-virtual {v12, v8, v8, v1, v1}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 61
    invoke-virtual {v4, v9}, Landroid/widget/TextView;->setCompoundDrawablePadding(I)V

    .line 62
    const-string v1, "ds_increase"

    invoke-virtual {v4, v1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 63
    invoke-virtual {v12, v10}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    :goto_0
    const/4 v1, 0x0

    goto :goto_1

    :cond_0
    const/4 v12, 0x0

    goto :goto_0

    .line 64
    :goto_1
    invoke-virtual {v4, v12, v1, v1, v1}, Landroidx/appcompat/widget/AppCompatTextView;->setCompoundDrawablesRelative(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    .line 65
    iput-object v4, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 66
    new-instance v8, Landroidx/appcompat/widget/AppCompatTextView;

    invoke-direct {v8, v2}, Landroidx/appcompat/widget/AppCompatTextView;-><init>(Landroid/content/Context;)V

    .line 67
    sget v1, LS11/d;->aggregatorVipCashbackStatusesTvCoef:I

    invoke-virtual {v8, v1}, Landroid/view/View;->setId(I)V

    .line 68
    sget v1, LlZ0/n;->TextStyle_Caption_Medium_M_Secondary:I

    invoke-static {v8, v1}, Lorg/xbet/uikit/utils/M;->b(Landroid/widget/TextView;I)V

    .line 69
    new-instance v1, Landroid/view/ViewGroup$LayoutParams;

    .line 70
    invoke-virtual {v8}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v9

    sget v12, LlZ0/g;->size_22:I

    invoke-virtual {v9, v12}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v9

    const/4 v14, -0x2

    .line 71
    invoke-direct {v1, v14, v9}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v8, v1}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 72
    invoke-virtual {v8, v6, v5, v6, v5}, Landroid/view/View;->setPaddingRelative(IIII)V

    .line 73
    sget v1, LlZ0/h;->rounded_background_full:I

    invoke-static {v2, v1}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v1

    invoke-virtual {v8, v1}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    const/16 v1, 0x10

    .line 74
    invoke-virtual {v8, v1}, Landroid/widget/TextView;->setGravity(I)V

    const/4 v1, 0x1

    .line 75
    invoke-virtual {v8, v1}, Landroid/widget/TextView;->setMaxLines(I)V

    .line 76
    invoke-virtual {v8, v11}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    const/4 v1, 0x3

    .line 77
    invoke-virtual {v8, v1}, Landroid/view/View;->setLayoutDirection(I)V

    const/4 v1, 0x0

    .line 78
    invoke-virtual {v8, v1}, Landroid/widget/TextView;->setIncludeFontPadding(Z)V

    .line 79
    sget v6, LlZ0/h;->ic_glyph_odds_movement:I

    .line 80
    invoke-static {v2, v6}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v6

    if-eqz v6, :cond_1

    .line 81
    invoke-virtual {v6, v1, v1, v3, v3}, Landroid/graphics/drawable/Drawable;->setBounds(IIII)V

    .line 82
    invoke-virtual {v8, v5}, Landroid/widget/TextView;->setCompoundDrawablePadding(I)V

    .line 83
    const-string v1, "ds_odds_movement"

    invoke-virtual {v8, v1}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 84
    invoke-virtual {v6, v10}, Landroid/graphics/drawable/Drawable;->setTint(I)V

    :goto_2
    const/4 v1, 0x0

    goto :goto_3

    :cond_1
    const/4 v6, 0x0

    goto :goto_2

    .line 85
    :goto_3
    invoke-virtual {v8, v6, v1, v1, v1}, Landroidx/appcompat/widget/AppCompatTextView;->setCompoundDrawablesRelative(Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;Landroid/graphics/drawable/Drawable;)V

    .line 86
    iput-object v8, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 87
    new-instance v1, Lorg/xbet/uikit/components/shimmer/ShimmerView;

    const/4 v5, 0x6

    const/4 v6, 0x0

    const/4 v3, 0x0

    move-object v9, v4

    const/4 v4, 0x0

    invoke-direct/range {v1 .. v6}, Lorg/xbet/uikit/components/shimmer/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 88
    sget v3, LS11/d;->aggregatorVipCashbackStatusesShimmer:I

    invoke-virtual {v1, v3}, Landroid/view/View;->setId(I)V

    .line 89
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    const/4 v4, -0x1

    invoke-direct {v3, v4, v7}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v1, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 90
    new-instance v3, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v3}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 91
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v4

    sget v5, LlZ0/g;->radius_16:I

    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v4

    invoke-virtual {v3, v4}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 92
    sget v4, LlZ0/d;->uikitSecondary20:I

    const/4 v5, 0x0

    const/4 v6, 0x2

    invoke-static {v2, v4, v5, v6, v5}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v2

    invoke-static {v2}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    move-result-object v2

    invoke-virtual {v3, v2}, Landroid/graphics/drawable/GradientDrawable;->setColor(Landroid/content/res/ColorStateList;)V

    .line 93
    invoke-virtual {v1, v3}, Landroid/view/View;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 94
    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->z:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    const/4 v1, 0x5

    .line 95
    new-array v1, v1, [Landroid/view/View;

    const/16 v18, 0x0

    aput-object v13, v1, v18

    const/16 v16, 0x1

    aput-object v20, v1, v16

    aput-object v19, v1, v6

    const/16 v17, 0x3

    aput-object v9, v1, v17

    const/4 v2, 0x4

    aput-object v8, v1, v2

    invoke-static {v1}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    iput-object v1, v0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->A:Ljava/util/List;

    .line 96
    sget v1, LlZ0/h;->rounded_background_16:I

    invoke-virtual {v0, v1}, Landroid/view/View;->setBackgroundResource(I)V

    .line 97
    invoke-virtual {v0, v13}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    move-object/from16 v1, v20

    .line 98
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    move-object/from16 v1, v19

    .line 99
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 100
    invoke-virtual {v0, v9}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 101
    invoke-virtual {v0, v8}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 102
    invoke-virtual {v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 1
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method

.method private final d()V
    .locals 6

    .line 1
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 8
    .line 9
    sub-int/2addr v0, v2

    .line 10
    iget-object v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 11
    .line 12
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredWidth()I

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    sub-int v2, v0, v2

    .line 17
    .line 18
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 19
    .line 20
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 25
    .line 26
    sub-int v4, v0, v4

    .line 27
    .line 28
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 29
    .line 30
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 31
    .line 32
    .line 33
    move-result v0

    .line 34
    iget v5, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 35
    .line 36
    add-int/2addr v5, v0

    .line 37
    move-object v0, p0

    .line 38
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method private final e()V
    .locals 6

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 6
    .line 7
    .line 8
    move-result v4

    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 14
    .line 15
    .line 16
    move-result v5

    .line 17
    const/4 v2, 0x0

    .line 18
    const/4 v3, 0x0

    .line 19
    move-object v0, p0

    .line 20
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method private final f()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 9
    .line 10
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 11
    .line 12
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->f:I

    .line 13
    .line 14
    add-int/2addr v0, v1

    .line 15
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->m:I

    .line 16
    .line 17
    add-int v7, v0, v1

    .line 18
    .line 19
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 22
    .line 23
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    sub-int v5, v7, v0

    .line 28
    .line 29
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 30
    .line 31
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 32
    .line 33
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    add-int v6, v0, v1

    .line 38
    .line 39
    move-object v2, p0

    .line 40
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 41
    .line 42
    .line 43
    return-void
.end method

.method private final i()V
    .locals 6

    .line 1
    iget v2, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 2
    .line 3
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->l:I

    .line 4
    .line 5
    add-int v5, v2, v0

    .line 6
    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 8
    .line 9
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    sub-int v3, v5, v0

    .line 14
    .line 15
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 16
    .line 17
    iget-object v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 18
    .line 19
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredWidth()I

    .line 20
    .line 21
    .line 22
    move-result v4

    .line 23
    add-int/2addr v4, v0

    .line 24
    move-object v0, p0

    .line 25
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method private final j()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->d:I

    .line 4
    .line 5
    const/high16 v2, 0x40000000    # 2.0f

    .line 6
    .line 7
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 8
    .line 9
    .line 10
    move-result v1

    .line 11
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->d:I

    .line 12
    .line 13
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 14
    .line 15
    .line 16
    move-result v2

    .line 17
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 18
    .line 19
    .line 20
    return-void
.end method

.method private final k()V
    .locals 4

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0}, Landroid/view/View;->getWidth()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/high16 v2, 0x40000000    # 2.0f

    .line 10
    .line 11
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    iget v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->e:I

    .line 16
    .line 17
    invoke-static {v3, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 18
    .line 19
    .line 20
    move-result v2

    .line 21
    invoke-virtual {v0, v1, v2}, Landroid/view/View;->measure(II)V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method private final l(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 4
    .line 5
    mul-int/lit8 v1, v1, 0x2

    .line 6
    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->h:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    sub-int/2addr p1, v1

    .line 18
    const/high16 v1, -0x80000000

    .line 19
    .line 20
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    const/4 v1, 0x0

    .line 25
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method private final o(I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 4
    .line 5
    mul-int/lit8 v1, v1, 0x2

    .line 6
    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->h:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    sub-int/2addr p1, v1

    .line 18
    const/high16 v1, -0x80000000

    .line 19
    .line 20
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    const/4 v1, 0x0

    .line 25
    invoke-static {v1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 26
    .line 27
    .line 28
    move-result v1

    .line 29
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method private final p(Z)V
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    iget p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->o:I

    .line 4
    .line 5
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    iget p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->p:I

    .line 11
    .line 12
    invoke-static {p1}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    :goto_0
    invoke-static {p0, p1}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 17
    .line 18
    .line 19
    return-void
.end method


# virtual methods
.method public a()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 3
    .line 4
    .line 5
    invoke-super {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->a()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public b()V
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->t:Z

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->p(Z)V

    .line 4
    .line 5
    .line 6
    invoke-super {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->b()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final g()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 11
    .line 12
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    add-int/2addr v0, v1

    .line 17
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->j:I

    .line 18
    .line 19
    add-int v4, v0, v1

    .line 20
    .line 21
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 22
    .line 23
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 24
    .line 25
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 26
    .line 27
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->i:I

    .line 28
    .line 29
    add-int v5, v0, v1

    .line 30
    .line 31
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    add-int v6, v4, v0

    .line 36
    .line 37
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 38
    .line 39
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 40
    .line 41
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->i:I

    .line 42
    .line 43
    add-int/2addr v0, v1

    .line 44
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 45
    .line 46
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 47
    .line 48
    .line 49
    move-result v1

    .line 50
    add-int v7, v0, v1

    .line 51
    .line 52
    move-object v2, p0

    .line 53
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 54
    .line 55
    .line 56
    return-void
.end method

.method public getContentViews()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->A:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->z:Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 2
    .line 3
    return-object v0
.end method

.method public getView()Landroid/view/View;
    .locals 0
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    return-object p0
.end method

.method public final h()V
    .locals 8

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 4
    .line 5
    invoke-virtual {v0, v1}, Landroid/view/View;->getHitRect(Landroid/graphics/Rect;)V

    .line 6
    .line 7
    .line 8
    iget-object v3, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    iget v4, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 13
    .line 14
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 15
    .line 16
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->i:I

    .line 17
    .line 18
    add-int v5, v0, v1

    .line 19
    .line 20
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredWidth()I

    .line 21
    .line 22
    .line 23
    move-result v0

    .line 24
    add-int v6, v4, v0

    .line 25
    .line 26
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->s:Landroid/graphics/Rect;

    .line 27
    .line 28
    iget v0, v0, Landroid/graphics/Rect;->bottom:I

    .line 29
    .line 30
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->i:I

    .line 31
    .line 32
    add-int/2addr v0, v1

    .line 33
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 34
    .line 35
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredHeight()I

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    add-int v7, v0, v1

    .line 40
    .line 41
    move-object v2, p0

    .line 42
    invoke-static/range {v2 .. v7}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final m(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 4
    .line 5
    mul-int/lit8 v1, v1, 0x2

    .line 6
    .line 7
    sub-int/2addr p1, v1

    .line 8
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->j:I

    .line 9
    .line 10
    sub-int/2addr p1, v1

    .line 11
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 12
    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    sub-int/2addr p1, v1

    .line 18
    const/high16 v1, -0x80000000

    .line 19
    .line 20
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 21
    .line 22
    .line 23
    move-result p1

    .line 24
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 25
    .line 26
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 31
    .line 32
    const/high16 v2, 0x40000000    # 2.0f

    .line 33
    .line 34
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method public final n(I)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    div-int/lit8 p1, p1, 0x2

    .line 4
    .line 5
    iget v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k:I

    .line 6
    .line 7
    sub-int/2addr p1, v1

    .line 8
    const/high16 v1, -0x80000000

    .line 9
    .line 10
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 15
    .line 16
    invoke-virtual {v1}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    iget v1, v1, Landroid/view/ViewGroup$LayoutParams;->height:I

    .line 21
    .line 22
    const/high16 v2, 0x40000000    # 2.0f

    .line 23
    .line 24
    invoke-static {v1, v2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 25
    .line 26
    .line 27
    move-result v1

    .line 28
    invoke-virtual {v0, p1, v1}, Landroid/view/View;->measure(II)V

    .line 29
    .line 30
    .line 31
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    invoke-super/range {p0 .. p5}, Landroid/widget/FrameLayout;->onLayout(ZIIII)V

    .line 2
    .line 3
    .line 4
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->d()V

    .line 5
    .line 6
    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->i()V

    .line 8
    .line 9
    .line 10
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->f()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->h()V

    .line 14
    .line 15
    .line 16
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->g()V

    .line 17
    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->e()V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->j()V

    .line 6
    .line 7
    .line 8
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->o(I)V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->l(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->n(I)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->m(I)V

    .line 18
    .line 19
    .line 20
    invoke-direct {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->k()V

    .line 21
    .line 22
    .line 23
    const/high16 p2, 0x40000000    # 2.0f

    .line 24
    .line 25
    invoke-static {p1, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->r:I

    .line 30
    .line 31
    invoke-static {v0, p2}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 32
    .line 33
    .line 34
    move-result p2

    .line 35
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public setCashbackText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->w:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setCoefText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setData(Lj31/a;)V
    .locals 2
    .param p1    # Lj31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setData(Lj31/a;)V

    .line 2
    .line 3
    .line 4
    instance-of v0, p1, Lj31/c;

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    sget-object v0, Li31/i;->a:Li31/i;

    .line 9
    .line 10
    iget-object v1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 11
    .line 12
    check-cast p1, Lj31/c;

    .line 13
    .line 14
    invoke-virtual {p1}, Lj31/c;->g()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-virtual {v0, v1, p1}, Li31/i;->a(Landroid/widget/ImageView;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    .line 19
    .line 20
    .line 21
    :cond_0
    return-void
.end method

.method public setExperienceText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setIsActive(Z)V
    .locals 1

    .line 1
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->t:Z

    .line 2
    .line 3
    invoke-direct {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->p(Z)V

    .line 4
    .line 5
    .line 6
    if-eqz p1, :cond_0

    .line 7
    .line 8
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 9
    .line 10
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->p:I

    .line 11
    .line 12
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 17
    .line 18
    .line 19
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 20
    .line 21
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->p:I

    .line 22
    .line 23
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 24
    .line 25
    .line 26
    move-result-object v0

    .line 27
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 28
    .line 29
    .line 30
    return-void

    .line 31
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->x:Landroidx/appcompat/widget/AppCompatTextView;

    .line 32
    .line 33
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->q:I

    .line 34
    .line 35
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 40
    .line 41
    .line 42
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->y:Landroidx/appcompat/widget/AppCompatTextView;

    .line 43
    .line 44
    iget v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->q:I

    .line 45
    .line 46
    invoke-static {v0}, Landroid/content/res/ColorStateList;->valueOf(I)Landroid/content/res/ColorStateList;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    invoke-static {p1, v0}, Lorg/xbet/uikit/utils/S;->n(Landroid/view/View;Landroid/content/res/ColorStateList;)V

    .line 51
    .line 52
    .line 53
    return-void
.end method

.method public setStatusDrawable(Landroid/graphics/drawable/Drawable;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setStatusDrawableRes(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    iget-object p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    invoke-virtual {p1, v0}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 7
    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->u:Landroid/widget/ImageView;

    .line 11
    .line 12
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    invoke-virtual {v0, p1}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 17
    .line 18
    .line 19
    return-void
.end method

.method public setStatusText(Ljava/lang/String;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/DSAggregatorVipCashbackStatusItemSmallIcon;->v:Landroidx/appcompat/widget/AppCompatTextView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
