.class public final Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;
.super Landroid/view/View;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0006\n\u0002\u0010\u0007\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u001c\u0008\u0007\u0018\u00002\u00020\u0001B\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0017\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u001d\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J\r\u0010\u0017\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0017\u0010\u0018J/\u0010\u001e\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u00192\u0006\u0010\u001c\u001a\u00020\u00192\u0006\u0010\u001d\u001a\u00020\u0008H\u0002\u00a2\u0006\u0004\u0008\u001e\u0010\u001fR\u0014\u0010\"\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001e\u0010!R\u0014\u0010$\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010!R\u0014\u0010\'\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008%\u0010&R\u0014\u0010)\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008(\u0010&R\u0016\u0010,\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u0016\u0010.\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008-\u0010+R\u0016\u00101\u001a\u00020\u00198\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008/\u00100R\u0016\u00103\u001a\u00020\u00198\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00082\u00100R\u0016\u00105\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u0010+R\u0016\u00107\u001a\u00020\u00198\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00086\u00100R\u0016\u00109\u001a\u00020\u00198\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00088\u00100R\u0016\u0010;\u001a\u00020\u00128\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008:\u0010+\u00a8\u0006<"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;",
        "Landroid/view/View;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "",
        "title",
        "additionalTitle",
        "setHeader",
        "(Ljava/lang/String;Ljava/lang/String;)V",
        "getAdditionalHeaderTitle",
        "()Ljava/lang/String;",
        "",
        "headerTitleWidth",
        "halfWidth",
        "additionalTitleWidth",
        "availableWidth",
        "a",
        "(FFFI)V",
        "Landroid/text/TextPaint;",
        "Landroid/text/TextPaint;",
        "headerPaint",
        "b",
        "additionalHeaderPaint",
        "c",
        "I",
        "titleMargin",
        "d",
        "spaceBetween",
        "e",
        "Ljava/lang/String;",
        "headerTitle",
        "f",
        "additionalHeaderTitle",
        "g",
        "F",
        "yHeaderPosition",
        "h",
        "xHeaderPosition",
        "i",
        "headerEllipsizedText",
        "j",
        "yAdditionHeaderPosition",
        "k",
        "xAdditionalHeaderPosition",
        "l",
        "additionalHeaderEllipsizedText",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Landroid/text/TextPaint;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:I

.field public final d:I

.field public e:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public f:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public g:F

.field public h:F

.field public i:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j:F

.field public k:F

.field public l:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/view/View;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    new-instance p2, Landroid/text/TextPaint;

    const/4 v0, 0x1

    invoke-direct {p2, v0}, Landroid/text/TextPaint;-><init>(I)V

    .line 5
    sget v1, LlZ0/n;->TextStyle_Text_Medium_TextPrimary:I

    invoke-static {p2, p1, v1}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 6
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 7
    new-instance p2, Landroid/text/TextPaint;

    invoke-direct {p2, v0}, Landroid/text/TextPaint;-><init>(I)V

    .line 8
    sget v0, LlZ0/n;->TextStyle_Text_Medium_Secondary:I

    invoke-static {p2, p1, v0}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 9
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    sget p2, LlZ0/g;->space_8:I

    invoke-virtual {p1, p2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p1

    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->d:I

    .line 12
    const-string p1, ""

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 13
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 14
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 15
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method


# virtual methods
.method public final a(FFFI)V
    .locals 1

    .line 1
    cmpl-float v0, p1, p2

    .line 2
    .line 3
    if-lez v0, :cond_0

    .line 4
    .line 5
    cmpl-float v0, p3, p2

    .line 6
    .line 7
    if-lez v0, :cond_0

    .line 8
    .line 9
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 10
    .line 11
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 12
    .line 13
    sget-object p4, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 14
    .line 15
    invoke-static {p1, p3, p2, p4}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 24
    .line 25
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 26
    .line 27
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 28
    .line 29
    invoke-static {p1, p3, p2, p4}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 38
    .line 39
    return-void

    .line 40
    :cond_0
    cmpg-float v0, p1, p2

    .line 41
    .line 42
    if-gez v0, :cond_1

    .line 43
    .line 44
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 45
    .line 46
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 47
    .line 48
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 49
    .line 50
    iget-object p3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 51
    .line 52
    int-to-float p4, p4

    .line 53
    sub-float/2addr p4, p1

    .line 54
    sget-object p1, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 55
    .line 56
    invoke-static {p2, p3, p4, p1}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 65
    .line 66
    return-void

    .line 67
    :cond_1
    cmpg-float p1, p3, p2

    .line 68
    .line 69
    if-gez p1, :cond_2

    .line 70
    .line 71
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 72
    .line 73
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 74
    .line 75
    iget-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 76
    .line 77
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 78
    .line 79
    int-to-float p4, p4

    .line 80
    sub-float/2addr p4, p3

    .line 81
    sget-object p3, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 82
    .line 83
    invoke-static {p1, p2, p4, p3}, Landroid/text/TextUtils;->ellipsize(Ljava/lang/CharSequence;Landroid/text/TextPaint;FLandroid/text/TextUtils$TruncateAt;)Ljava/lang/CharSequence;

    .line 84
    .line 85
    .line 86
    move-result-object p1

    .line 87
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object p1

    .line 91
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 92
    .line 93
    :cond_2
    return-void
.end method

.method public final getAdditionalHeaderTitle()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 4
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/view/View;->onDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 5
    .line 6
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-lez v0, :cond_0

    .line 11
    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 13
    .line 14
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->h:F

    .line 15
    .line 16
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->g:F

    .line 17
    .line 18
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 19
    .line 20
    invoke-virtual {p1, v0, v1, v2, v3}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 21
    .line 22
    .line 23
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 24
    .line 25
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    if-lez v0, :cond_1

    .line 30
    .line 31
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 32
    .line 33
    iget v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->k:F

    .line 34
    .line 35
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->j:F

    .line 36
    .line 37
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 38
    .line 39
    invoke-virtual {p1, v0, v1, v2, v3}, Landroid/graphics/Canvas;->drawText(Ljava/lang/String;FFLandroid/graphics/Paint;)V

    .line 40
    .line 41
    .line 42
    :cond_1
    return-void
.end method

.method public onMeasure(II)V
    .locals 8

    .line 1
    invoke-super {p0, p1, p2}, Landroid/view/View;->onMeasure(II)V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p1

    .line 8
    invoke-static {p2}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 9
    .line 10
    .line 11
    move-result p2

    .line 12
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 13
    .line 14
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-nez v0, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 22
    .line 23
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-nez v0, :cond_1

    .line 28
    .line 29
    :goto_0
    const/4 v0, 0x0

    .line 30
    goto :goto_1

    .line 31
    :cond_1
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->d:I

    .line 32
    .line 33
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 34
    .line 35
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 36
    .line 37
    invoke-virtual {v1, v2}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 42
    .line 43
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 44
    .line 45
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 46
    .line 47
    .line 48
    move-result v2

    .line 49
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 50
    .line 51
    .line 52
    move-result v3

    .line 53
    iget v4, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 54
    .line 55
    mul-int/lit8 v5, v4, 0x2

    .line 56
    .line 57
    sub-int/2addr v3, v5

    .line 58
    sub-int/2addr v3, v0

    .line 59
    add-float v5, v1, v2

    .line 60
    .line 61
    int-to-float v6, v0

    .line 62
    add-float/2addr v5, v6

    .line 63
    const/4 v6, 0x2

    .line 64
    mul-int/lit8 v4, v4, 0x2

    .line 65
    .line 66
    int-to-float v4, v4

    .line 67
    add-float/2addr v5, v4

    .line 68
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 69
    .line 70
    .line 71
    move-result v4

    .line 72
    iget v7, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 73
    .line 74
    sub-int/2addr v4, v7

    .line 75
    div-int/2addr v4, v6

    .line 76
    sub-int/2addr v4, v0

    .line 77
    int-to-float v0, v4

    .line 78
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 79
    .line 80
    .line 81
    move-result v4

    .line 82
    int-to-float v4, v4

    .line 83
    cmpg-float v4, v5, v4

    .line 84
    .line 85
    if-gtz v4, :cond_2

    .line 86
    .line 87
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 88
    .line 89
    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 90
    .line 91
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 92
    .line 93
    iput-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 94
    .line 95
    goto :goto_2

    .line 96
    :cond_2
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 97
    .line 98
    .line 99
    move-result v4

    .line 100
    int-to-float v4, v4

    .line 101
    cmpl-float v4, v5, v4

    .line 102
    .line 103
    if-lez v4, :cond_3

    .line 104
    .line 105
    invoke-virtual {p0, v1, v0, v2, v3}, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a(FFFI)V

    .line 106
    .line 107
    .line 108
    :cond_3
    :goto_2
    div-int/2addr p2, v6

    .line 109
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 110
    .line 111
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 112
    .line 113
    .line 114
    move-result v0

    .line 115
    const/4 v1, 0x1

    .line 116
    if-lez v0, :cond_5

    .line 117
    .line 118
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 119
    .line 120
    .line 121
    move-result v0

    .line 122
    if-ne v0, v1, :cond_4

    .line 123
    .line 124
    int-to-float v0, p1

    .line 125
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 126
    .line 127
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->i:Ljava/lang/String;

    .line 128
    .line 129
    invoke-virtual {v2, v3}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 130
    .line 131
    .line 132
    move-result v2

    .line 133
    sub-float/2addr v0, v2

    .line 134
    iget v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 135
    .line 136
    int-to-float v2, v2

    .line 137
    sub-float/2addr v0, v2

    .line 138
    goto :goto_3

    .line 139
    :cond_4
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 140
    .line 141
    int-to-float v0, v0

    .line 142
    :goto_3
    iput v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->h:F

    .line 143
    .line 144
    int-to-float v0, p2

    .line 145
    iget-object v2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 146
    .line 147
    invoke-virtual {v2}, Landroid/graphics/Paint;->descent()F

    .line 148
    .line 149
    .line 150
    move-result v2

    .line 151
    iget-object v3, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->a:Landroid/text/TextPaint;

    .line 152
    .line 153
    invoke-virtual {v3}, Landroid/graphics/Paint;->ascent()F

    .line 154
    .line 155
    .line 156
    move-result v3

    .line 157
    add-float/2addr v2, v3

    .line 158
    int-to-float v3, v6

    .line 159
    div-float/2addr v2, v3

    .line 160
    sub-float/2addr v0, v2

    .line 161
    iput v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->g:F

    .line 162
    .line 163
    :cond_5
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 164
    .line 165
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 166
    .line 167
    .line 168
    move-result v0

    .line 169
    if-lez v0, :cond_7

    .line 170
    .line 171
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 172
    .line 173
    .line 174
    move-result v0

    .line 175
    if-ne v0, v1, :cond_6

    .line 176
    .line 177
    iget p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 178
    .line 179
    int-to-float p1, p1

    .line 180
    goto :goto_4

    .line 181
    :cond_6
    int-to-float p1, p1

    .line 182
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 183
    .line 184
    iget-object v1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->l:Ljava/lang/String;

    .line 185
    .line 186
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->measureText(Ljava/lang/String;)F

    .line 187
    .line 188
    .line 189
    move-result v0

    .line 190
    sub-float/2addr p1, v0

    .line 191
    iget v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->c:I

    .line 192
    .line 193
    int-to-float v0, v0

    .line 194
    sub-float/2addr p1, v0

    .line 195
    :goto_4
    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->k:F

    .line 196
    .line 197
    int-to-float p1, p2

    .line 198
    iget-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 199
    .line 200
    invoke-virtual {p2}, Landroid/graphics/Paint;->descent()F

    .line 201
    .line 202
    .line 203
    move-result p2

    .line 204
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->b:Landroid/text/TextPaint;

    .line 205
    .line 206
    invoke-virtual {v0}, Landroid/graphics/Paint;->ascent()F

    .line 207
    .line 208
    .line 209
    move-result v0

    .line 210
    add-float/2addr p2, v0

    .line 211
    int-to-float v0, v6

    .line 212
    div-float/2addr p2, v0

    .line 213
    sub-float/2addr p1, p2

    .line 214
    iput p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->j:F

    .line 215
    .line 216
    :cond_7
    return-void
.end method

.method public final setHeader(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->e:Ljava/lang/String;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/uikit_sport/eventcard/bottom/EventCardBottomHeader;->f:Ljava/lang/String;

    .line 4
    .line 5
    return-void
.end method
