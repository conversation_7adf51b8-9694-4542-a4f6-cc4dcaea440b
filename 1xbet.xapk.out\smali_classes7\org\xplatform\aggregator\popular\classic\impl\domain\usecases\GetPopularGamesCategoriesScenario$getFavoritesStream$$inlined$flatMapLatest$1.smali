.class public final Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.domain.usecases.GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1"
    f = "GetPopularGamesCategoriesScenario.kt"
    l = {
        0xbf,
        0xcc,
        0xbd
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->l(ZZ)Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Ljava/util/List<",
        "+",
        "Lorg/xplatform/aggregator/api/model/Game;",
        ">;>;",
        "Ln9/b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0006\u001a\u00020\u0004\"\u0004\u0008\u0000\u0010\u0000\"\u0004\u0008\u0001\u0010\u0001*\u0008\u0012\u0004\u0012\u00028\u00000\u00022\u0006\u0010\u0003\u001a\u00028\u0001H\n\u00a8\u0006\u0005"
    }
    d2 = {
        "R",
        "T",
        "Lkotlinx/coroutines/flow/f;",
        "it",
        "",
        "kotlinx/coroutines/flow/FlowKt__MergeKt$flatMapLatest$1",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field final synthetic $initRequest$inlined:Z

.field final synthetic $isVirtual$inlined:Z

.field private synthetic L$0:Ljava/lang/Object;

.field synthetic L$1:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ZZ)V
    .locals 0

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    iput-boolean p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->$isVirtual$inlined:Z

    iput-boolean p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->$initRequest$inlined:Z

    const/4 p2, 0x3

    invoke-direct {p0, p2, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Ljava/lang/Object;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Ljava/util/List<",
            "+",
            "Lorg/xplatform/aggregator/api/model/Game;",
            ">;>;",
            "Ln9/b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    iget-boolean v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->$isVirtual$inlined:Z

    iget-boolean v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->$initRequest$inlined:Z

    invoke-direct {v0, p3, v1, v2, v3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;-><init>(Lkotlin/coroutines/e;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;ZZ)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    iput-object p2, v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {v0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x1

    .line 9
    const/4 v4, 0x2

    .line 10
    const/4 v5, 0x0

    .line 11
    if-eqz v1, :cond_3

    .line 12
    .line 13
    if-eq v1, v3, :cond_2

    .line 14
    .line 15
    if-eq v1, v4, :cond_1

    .line 16
    .line 17
    if-ne v1, v2, :cond_0

    .line 18
    .line 19
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 20
    .line 21
    .line 22
    goto/16 :goto_4

    .line 23
    .line 24
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 25
    .line 26
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 27
    .line 28
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    throw p1

    .line 32
    :cond_1
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 33
    .line 34
    check-cast v1, Lkotlinx/coroutines/flow/f;

    .line 35
    .line 36
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    goto/16 :goto_1

    .line 40
    .line 41
    :cond_2
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast v1, Ln9/b;

    .line 44
    .line 45
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 46
    .line 47
    check-cast v3, Lkotlinx/coroutines/flow/f;

    .line 48
    .line 49
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 50
    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 54
    .line 55
    .line 56
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 57
    .line 58
    check-cast p1, Lkotlinx/coroutines/flow/f;

    .line 59
    .line 60
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 61
    .line 62
    check-cast v1, Ln9/b;

    .line 63
    .line 64
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 65
    .line 66
    .line 67
    move-result v6

    .line 68
    invoke-virtual {v1}, Ln9/b;->d()Z

    .line 69
    .line 70
    .line 71
    move-result v7

    .line 72
    if-eq v6, v7, :cond_5

    .line 73
    .line 74
    iget-object v6, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 75
    .line 76
    invoke-static {v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->b(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lv81/f;

    .line 77
    .line 78
    .line 79
    move-result-object v6

    .line 80
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 81
    .line 82
    iput-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 83
    .line 84
    iput v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 85
    .line 86
    invoke-interface {v6, p0}, Lv81/f;->a(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v3

    .line 90
    if-ne v3, v0, :cond_4

    .line 91
    .line 92
    goto/16 :goto_3

    .line 93
    .line 94
    :cond_4
    move-object v3, p1

    .line 95
    :goto_0
    move-object p1, v3

    .line 96
    :cond_5
    invoke-virtual {v1}, Ln9/b;->c()Z

    .line 97
    .line 98
    .line 99
    move-result v1

    .line 100
    if-eqz v1, :cond_8

    .line 101
    .line 102
    iget-boolean v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->$isVirtual$inlined:Z

    .line 103
    .line 104
    if-eqz v1, :cond_6

    .line 105
    .line 106
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 107
    .line 108
    invoke-static {v1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->e(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lv81/m;

    .line 109
    .line 110
    .line 111
    move-result-object v1

    .line 112
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 113
    .line 114
    invoke-static {v3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->g(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 115
    .line 116
    .line 117
    move-result-object v3

    .line 118
    invoke-interface {v3}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 119
    .line 120
    .line 121
    move-result-object v3

    .line 122
    invoke-virtual {v3}, Lek0/o;->o()Lek0/a;

    .line 123
    .line 124
    .line 125
    move-result-object v3

    .line 126
    invoke-virtual {v3}, Lek0/a;->c()Z

    .line 127
    .line 128
    .line 129
    move-result v3

    .line 130
    iget-object v4, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 131
    .line 132
    invoke-static {v4}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->h(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Li8/j;

    .line 133
    .line 134
    .line 135
    move-result-object v4

    .line 136
    invoke-interface {v4}, Li8/j;->invoke()Ljava/lang/String;

    .line 137
    .line 138
    .line 139
    move-result-object v4

    .line 140
    invoke-interface {v1, v3, v4}, Lv81/m;->a(ZLjava/lang/String;)Lkotlinx/coroutines/flow/e;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    new-instance v3, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$lambda$8$$inlined$map$1;

    .line 145
    .line 146
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$lambda$8$$inlined$map$1;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 147
    .line 148
    .line 149
    goto :goto_2

    .line 150
    :cond_6
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 151
    .line 152
    invoke-static {v1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->f(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lf81/b;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 157
    .line 158
    invoke-static {v3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->g(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 159
    .line 160
    .line 161
    move-result-object v3

    .line 162
    invoke-interface {v3}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 163
    .line 164
    .line 165
    move-result-object v3

    .line 166
    invoke-virtual {v3}, Lek0/o;->o()Lek0/a;

    .line 167
    .line 168
    .line 169
    move-result-object v3

    .line 170
    invoke-virtual {v3}, Lek0/a;->c()Z

    .line 171
    .line 172
    .line 173
    move-result v3

    .line 174
    iget-object v6, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 175
    .line 176
    invoke-static {v6}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->h(Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;)Li8/j;

    .line 177
    .line 178
    .line 179
    move-result-object v6

    .line 180
    invoke-interface {v6}, Li8/j;->invoke()Ljava/lang/String;

    .line 181
    .line 182
    .line 183
    move-result-object v6

    .line 184
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 185
    .line 186
    iput-object v5, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 187
    .line 188
    iput v4, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 189
    .line 190
    invoke-interface {v1, v3, v6, p0}, Lf81/b;->a(ZLjava/lang/String;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 191
    .line 192
    .line 193
    move-result-object v1

    .line 194
    if-ne v1, v0, :cond_7

    .line 195
    .line 196
    goto :goto_3

    .line 197
    :cond_7
    move-object v8, v1

    .line 198
    move-object v1, p1

    .line 199
    move-object p1, v8

    .line 200
    :goto_1
    check-cast p1, Lkotlinx/coroutines/flow/e;

    .line 201
    .line 202
    iget-boolean v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->$initRequest$inlined:Z

    .line 203
    .line 204
    const/4 v6, 0x0

    .line 205
    invoke-static {p1, v3, v6, v4, v5}, Lcom/xbet/onexcore/utils/flows/ScreenRetryStrategiesExtentionsKt;->g(Lkotlinx/coroutines/flow/e;ZZILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 206
    .line 207
    .line 208
    move-result-object p1

    .line 209
    new-instance v3, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$lambda$8$$inlined$map$2;

    .line 210
    .line 211
    invoke-direct {v3, p1}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$lambda$8$$inlined$map$2;-><init>(Lkotlinx/coroutines/flow/e;)V

    .line 212
    .line 213
    .line 214
    new-instance p1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$1$3;

    .line 215
    .line 216
    invoke-direct {p1, v5}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$1$3;-><init>(Lkotlin/coroutines/e;)V

    .line 217
    .line 218
    .line 219
    invoke-static {v3, p1}, Lkotlinx/coroutines/flow/g;->j(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 220
    .line 221
    .line 222
    move-result-object v3

    .line 223
    move-object p1, v1

    .line 224
    goto :goto_2

    .line 225
    :cond_8
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$1$4;

    .line 226
    .line 227
    invoke-direct {v1, v5}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$1$4;-><init>(Lkotlin/coroutines/e;)V

    .line 228
    .line 229
    .line 230
    invoke-static {v1}, Lkotlinx/coroutines/flow/g;->V(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 231
    .line 232
    .line 233
    move-result-object v3

    .line 234
    :goto_2
    iput-object v5, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$0:Ljava/lang/Object;

    .line 235
    .line 236
    iput-object v5, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->L$1:Ljava/lang/Object;

    .line 237
    .line 238
    iput v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario$getFavoritesStream$$inlined$flatMapLatest$1;->label:I

    .line 239
    .line 240
    invoke-static {p1, v3, p0}, Lkotlinx/coroutines/flow/g;->H(Lkotlinx/coroutines/flow/f;Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 241
    .line 242
    .line 243
    move-result-object p1

    .line 244
    if-ne p1, v0, :cond_9

    .line 245
    .line 246
    :goto_3
    return-object v0

    .line 247
    :cond_9
    :goto_4
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 248
    .line 249
    return-object p1
.end method
