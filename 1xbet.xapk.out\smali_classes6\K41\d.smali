.class public final LK41/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LK41/c;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\u0008H\u0096\u0001\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0010\u0010\u000c\u001a\u00020\u000bH\u0096\u0001\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u0010\u0010\u000f\u001a\u00020\u000eH\u0096\u0001\u00a2\u0006\u0004\u0008\u000f\u0010\u0010R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000c\u0010\u0011R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\t\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "LK41/d;",
        "LK41/c;",
        "LRf0/f;",
        "privatePreferencesWrapper",
        "Lcom/google/gson/Gson;",
        "gson",
        "<init>",
        "(LRf0/f;Lcom/google/gson/Gson;)V",
        "LH41/c;",
        "c",
        "()LH41/c;",
        "LH41/b;",
        "b",
        "()LH41/b;",
        "LH41/a;",
        "a",
        "()LH41/a;",
        "LRf0/f;",
        "Lcom/google/gson/Gson;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:LK41/c;

.field public final b:LRf0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lcom/google/gson/Gson;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LRf0/f;Lcom/google/gson/Gson;)V
    .locals 1
    .param p1    # LRf0/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lcom/google/gson/Gson;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-static {}, LK41/a;->a()LK41/c$a;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-interface {v0, p1, p2}, LK41/c$a;->a(LRf0/f;Lcom/google/gson/Gson;)LK41/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, LK41/d;->a:LK41/c;

    .line 13
    .line 14
    iput-object p1, p0, LK41/d;->b:LRf0/f;

    .line 15
    .line 16
    iput-object p2, p0, LK41/d;->c:Lcom/google/gson/Gson;

    .line 17
    .line 18
    return-void
.end method


# virtual methods
.method public a()LH41/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LK41/d;->a:LK41/c;

    .line 2
    .line 3
    invoke-interface {v0}, LG41/a;->a()LH41/a;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public b()LH41/b;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LK41/d;->a:LK41/c;

    .line 2
    .line 3
    invoke-interface {v0}, LG41/a;->b()LH41/b;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public c()LH41/c;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, LK41/d;->a:LK41/c;

    .line 2
    .line 3
    invoke-interface {v0}, LG41/a;->c()LH41/c;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
