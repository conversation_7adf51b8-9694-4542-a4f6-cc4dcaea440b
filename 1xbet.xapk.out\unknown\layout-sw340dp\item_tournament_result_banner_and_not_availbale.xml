<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/banner"
        style="@style/Widget.AppTheme.CardView.New"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/space_8"
        android:layout_marginVertical="@dimen/space_4"
        app:cardCornerRadius="@dimen/corner_radius_8"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/space_8">

            <ImageView
                android:id="@+id/ivBackground"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="@dimen/size_144"
                android:adjustViewBounds="true"
                android:importantForAccessibility="no"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_tournament_not_participant" />

            <ImageView
                android:id="@+id/ivCup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_4"
                android:importantForAccessibility="no"
                app:layout_constraintBottom_toTopOf="@id/tvTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                app:srcCompat="@drawable/ic_tournament_cup_gold" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_4"
                android:text="@string/tournament_participate"
                android:textAppearance="@style/TextAppearance.AppTheme.Subtitle1.Medium"
                app:layout_constraintBottom_toTopOf="@id/tvSubtitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivCup" />

            <TextView
                android:id="@+id/tvSubtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tournament_compete_win"
                android:textAppearance="@style/TextAppearance.AppTheme.New.Caption"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/tvNoResult"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/space_8"
        android:gravity="center"
        android:paddingBottom="@dimen/space_112"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="@string/tournament_result_not_available"
        android:textAppearance="?attr/textAppearanceBody2New"
        app:layout_constraintTop_toBottomOf="@id/banner" />

</androidx.constraintlayout.widget.ConstraintLayout>