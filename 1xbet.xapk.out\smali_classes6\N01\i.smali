.class public final synthetic LN01/i;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, LN01/i;->a:Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;

    iput-object p2, p0, LN01/i;->b:Lkotlin/jvm/functions/Function0;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, LN01/i;->a:Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;

    iget-object v1, p0, LN01/i;->b:Lkotlin/jvm/functions/Function0;

    check-cast p1, Landroid/view/View;

    invoke-static {v0, v1, p1}, Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;->v(Lorg/xbet/uikit/components/toolbar/base/styles/SubTitleNavigationBar;Lkotlin/jvm/functions/Function0;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
