.class public abstract Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0004\u0008!\u0018\u00002\u00020\u00012\u00020\u0002B\u001b\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0004\u0008\u0007\u0010\u0008J\u0017\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\tH\u0016\u00a2\u0006\u0004\u0008\u000c\u0010\rJ\u000f\u0010\u000e\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u000f\u0010\u0010\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u0010\u0010\u000fR\u001a\u0010\u0015\u001a\u00020\u00118\u0004X\u0084\u0004\u00a2\u0006\u000c\n\u0004\u0008\u000e\u0010\u0012\u001a\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0019\u001a\u00020\u00168$X\u00a4\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u0017\u0010\u0018R\u001a\u0010\u001e\u001a\u0008\u0012\u0004\u0012\u00020\u001b0\u001a8$X\u00a4\u0004\u00a2\u0006\u0006\u001a\u0004\u0008\u001c\u0010\u001d\u00a8\u0006\u001f"
    }
    d2 = {
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;",
        "Landroid/widget/FrameLayout;",
        "Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "Lj31/a;",
        "data",
        "",
        "setData",
        "(Lj31/a;)V",
        "a",
        "()V",
        "b",
        "",
        "Z",
        "c",
        "()Z",
        "isRtl",
        "Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "getShimmerView",
        "()Lorg/xbet/uikit/components/shimmer/ShimmerView;",
        "shimmerView",
        "",
        "Landroid/view/View;",
        "getContentViews",
        "()Ljava/util/List;",
        "contentViews",
        "uikit_aggregator_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 2
    .line 3
    .line 4
    invoke-static {}, LQ0/a;->c()LQ0/a;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    invoke-virtual {p1}, LQ0/a;->h()Z

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    iput-boolean p1, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->a:Z

    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public a()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->getContentViews()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    check-cast v1, Landroid/view/View;

    .line 20
    .line 21
    const/16 v2, 0x8

    .line 22
    .line 23
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    const/4 v1, 0x0

    .line 32
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->a(Landroid/view/ViewGroup;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public b()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->getContentViews()Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-eqz v1, :cond_0

    .line 14
    .line 15
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    check-cast v1, Landroid/view/View;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-virtual {v1, v2}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    const/16 v1, 0x8

    .line 31
    .line 32
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 33
    .line 34
    .line 35
    invoke-static {p0}, Lorg/xbet/uikit/utils/F;->b(Landroid/view/ViewGroup;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final c()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->a:Z

    .line 2
    .line 3
    return v0
.end method

.method public abstract getContentViews()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Landroid/view/View;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract getShimmerView()Lorg/xbet/uikit/components/shimmer/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public abstract synthetic getView()Landroid/view/View;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end method

.method public setCashbackLabelText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->a(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setCashbackText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->b(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setCoefLabelText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->c(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setCoefText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->d(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setData(Lj31/a;)V
    .locals 2
    .param p1    # Lj31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, Lj31/c;

    .line 2
    .line 3
    if-eqz v0, :cond_2

    .line 4
    .line 5
    move-object v0, p1

    .line 6
    check-cast v0, Lj31/c;

    .line 7
    .line 8
    invoke-virtual {v0}, Lj31/c;->g()Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setLevel(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {v0}, Lj31/c;->h()Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    if-eqz v1, :cond_0

    .line 20
    .line 21
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setStatusDrawableRes(Ljava/lang/Integer;)V

    .line 22
    .line 23
    .line 24
    :cond_0
    invoke-virtual {v0}, Lj31/c;->i()Ljava/lang/String;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    if-eqz v1, :cond_1

    .line 29
    .line 30
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setStatusImage(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    :cond_1
    invoke-virtual {v0}, Lj31/c;->j()Ljava/lang/String;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setStatusText(Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {v0}, Lj31/c;->b()Ljava/lang/String;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setCashbackText(Ljava/lang/String;)V

    .line 45
    .line 46
    .line 47
    invoke-virtual {v0}, Lj31/c;->a()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setCashbackLabelText(Ljava/lang/String;)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {v0}, Lj31/c;->f()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object v1

    .line 58
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setExperienceText(Ljava/lang/String;)V

    .line 59
    .line 60
    .line 61
    invoke-virtual {v0}, Lj31/c;->e()Ljava/lang/String;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setExperienceLabelText(Ljava/lang/String;)V

    .line 66
    .line 67
    .line 68
    invoke-virtual {v0}, Lj31/c;->d()Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setCoefText(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {v0}, Lj31/c;->c()Ljava/lang/String;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setCoefLabelText(Ljava/lang/String;)V

    .line 80
    .line 81
    .line 82
    invoke-virtual {v0}, Lj31/c;->k()Z

    .line 83
    .line 84
    .line 85
    move-result v0

    .line 86
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->setIsActive(Z)V

    .line 87
    .line 88
    .line 89
    :cond_2
    instance-of p1, p1, Lj31/b;

    .line 90
    .line 91
    if-eqz p1, :cond_3

    .line 92
    .line 93
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->a()V

    .line 94
    .line 95
    .line 96
    return-void

    .line 97
    :cond_3
    invoke-virtual {p0}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/BaseDSAggregatorVipCashbackStatusItemView;->b()V

    .line 98
    .line 99
    .line 100
    return-void
.end method

.method public setExperienceLabelText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->e(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setExperienceText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->f(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public abstract synthetic setIsActive(Z)V
.end method

.method public setLevel(Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V
    .locals 0
    .param p1    # Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->g(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Lorg/xbet/uikit_aggregator/aggregatorvipcashback/AggregatorVipCashbackLevel;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public abstract synthetic setStatusDrawable(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract synthetic setStatusDrawableRes(Ljava/lang/Integer;)V
.end method

.method public setStatusImage(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->h(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setStatusText(Ljava/lang/String;)V
    .locals 0
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a$a;->i(Lorg/xbet/uikit_aggregator/aggregatorvipcashbackstatuses/views/a;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method
