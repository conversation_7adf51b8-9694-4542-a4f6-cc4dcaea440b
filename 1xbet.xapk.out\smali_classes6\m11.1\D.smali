.class public final synthetic Lm11/D;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lm11/H$h;


# direct methods
.method public synthetic constructor <init>(Lm11/H$h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm11/D;->a:Lm11/H$h;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lm11/D;->a:Lm11/H$h;

    invoke-static {v0}, Lm11/G$a;->d(Lm11/H$h;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
