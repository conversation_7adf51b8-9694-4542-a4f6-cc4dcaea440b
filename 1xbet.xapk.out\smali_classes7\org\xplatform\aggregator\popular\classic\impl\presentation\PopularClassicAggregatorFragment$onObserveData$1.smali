.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.PopularClassicAggregatorFragment$onObserveData$1"
    f = "PopularClassicAggregatorFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
        "viewState",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->invoke(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->O2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 28
    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$c;

    .line 32
    .line 33
    if-eqz v0, :cond_1

    .line 34
    .line 35
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->G2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)V

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$b;

    .line 42
    .line 43
    if-eqz v0, :cond_3

    .line 44
    .line 45
    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$b;

    .line 46
    .line 47
    invoke-virtual {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$b;->a()Ljava/util/List;

    .line 48
    .line 49
    .line 50
    move-result-object v0

    .line 51
    invoke-interface {v0}, Ljava/util/Collection;->isEmpty()Z

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    if-nez v0, :cond_2

    .line 56
    .line 57
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 58
    .line 59
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->G2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)V

    .line 60
    .line 61
    .line 62
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 63
    .line 64
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->E2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;)LNb1/a;

    .line 65
    .line 66
    .line 67
    move-result-object v0

    .line 68
    invoke-virtual {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$b;->a()Ljava/util/List;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    invoke-virtual {v0, p1}, LA4/e;->setItems(Ljava/util/List;)V

    .line 73
    .line 74
    .line 75
    :cond_2
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 76
    .line 77
    return-object p1

    .line 78
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 79
    .line 80
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 81
    .line 82
    .line 83
    throw p1

    .line 84
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 85
    .line 86
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 87
    .line 88
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    throw p1
.end method
