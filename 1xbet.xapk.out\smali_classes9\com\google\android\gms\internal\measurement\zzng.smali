.class public interface abstract Lcom/google/android/gms/internal/measurement/zzng;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Cloneable;
.implements Lcom/google/android/gms/internal/measurement/zzni;


# virtual methods
.method public abstract zzaU([B)Lcom/google/android/gms/internal/measurement/zzng;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/google/android/gms/internal/measurement/zzmm;
        }
    .end annotation
.end method

.method public abstract zzaV([BLcom/google/android/gms/internal/measurement/zzlp;)Lcom/google/android/gms/internal/measurement/zzng;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/google/android/gms/internal/measurement/zzmm;
        }
    .end annotation
.end method

.method public abstract zzbc()Lcom/google/android/gms/internal/measurement/zznh;
.end method
