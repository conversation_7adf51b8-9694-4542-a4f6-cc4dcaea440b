.class public final Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"

# interfaces
.implements Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;
.implements LCb1/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$a;,
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;,
        Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0090\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u000c\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\t\n\u0002\u0008\u0003\n\u0002\u0010\u0000\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008B\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0010 \n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 \u0087\u00022\u00020\u00012\u00020\u00022\u00020\u0003:\u0006\u0088\u0002\u0089\u0002\u008a\u0002B\u00fb\u0001\u0008\u0007\u0012\u0008\u0008\u0001\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u0012\u0006\u0010\u001d\u001a\u00020\u001c\u0012\u0006\u0010\u001f\u001a\u00020\u001e\u0012\u0006\u0010!\u001a\u00020 \u0012\u0006\u0010#\u001a\u00020\"\u0012\u0006\u0010%\u001a\u00020$\u0012\u0006\u0010\'\u001a\u00020&\u0012\u0006\u0010)\u001a\u00020(\u0012\u0006\u0010+\u001a\u00020*\u0012\u0006\u0010-\u001a\u00020,\u0012\u0006\u0010/\u001a\u00020.\u0012\u0006\u00101\u001a\u000200\u0012\u0006\u00103\u001a\u000202\u0012\u0006\u00105\u001a\u000204\u0012\u0006\u00107\u001a\u000206\u0012\u0006\u00109\u001a\u000208\u0012\u0006\u0010;\u001a\u00020:\u0012\u0006\u0010=\u001a\u00020<\u0012\u0006\u0010?\u001a\u00020>\u00a2\u0006\u0004\u0008@\u0010AJ\u000f\u0010C\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008C\u0010DJ\u000f\u0010E\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008E\u0010DJ\u000f\u0010F\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008F\u0010DJ\u001f\u0010K\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0006\u0010J\u001a\u00020IH\u0002\u00a2\u0006\u0004\u0008K\u0010LJ\u001f\u0010O\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0006\u0010N\u001a\u00020MH\u0002\u00a2\u0006\u0004\u0008O\u0010PJ\u0017\u0010R\u001a\u00020B2\u0006\u0010N\u001a\u00020QH\u0002\u00a2\u0006\u0004\u0008R\u0010SJ\u0017\u0010T\u001a\u00020B2\u0006\u0010H\u001a\u00020GH\u0002\u00a2\u0006\u0004\u0008T\u0010UJ\'\u0010Z\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0006\u0010W\u001a\u00020V2\u0006\u0010Y\u001a\u00020XH\u0002\u00a2\u0006\u0004\u0008Z\u0010[J\u000f\u0010\\\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008\\\u0010DJ\u0017\u0010_\u001a\u00020B2\u0006\u0010^\u001a\u00020]H\u0002\u00a2\u0006\u0004\u0008_\u0010`J\u0010\u0010a\u001a\u00020BH\u0082@\u00a2\u0006\u0004\u0008a\u0010bJ\u000f\u0010c\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008c\u0010DJ\u0017\u0010d\u001a\u00020B2\u0006\u0010^\u001a\u00020]H\u0002\u00a2\u0006\u0004\u0008d\u0010`J\u0017\u0010e\u001a\u00020B2\u0006\u0010^\u001a\u00020]H\u0002\u00a2\u0006\u0004\u0008e\u0010`J\u0017\u0010f\u001a\u00020B2\u0006\u0010^\u001a\u00020]H\u0002\u00a2\u0006\u0004\u0008f\u0010`J\u000f\u0010h\u001a\u00020gH\u0002\u00a2\u0006\u0004\u0008h\u0010iJ%\u0010n\u001a\u00020B2\u0006\u0010k\u001a\u00020j2\u000c\u0010m\u001a\u0008\u0012\u0004\u0012\u00020B0lH\u0002\u00a2\u0006\u0004\u0008n\u0010oJ\u0017\u0010p\u001a\u00020B2\u0006\u0010k\u001a\u00020jH\u0002\u00a2\u0006\u0004\u0008p\u0010qJ\u000f\u0010r\u001a\u00020BH\u0002\u00a2\u0006\u0004\u0008r\u0010DJ\u000f\u0010s\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008s\u0010tJ\u001f\u0010v\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0006\u0010u\u001a\u00020XH\u0002\u00a2\u0006\u0004\u0008v\u0010wJ\u001f\u0010x\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0006\u0010u\u001a\u00020XH\u0002\u00a2\u0006\u0004\u0008x\u0010wJ\u0013\u0010z\u001a\u00020\u0004*\u00020yH\u0002\u00a2\u0006\u0004\u0008z\u0010{J\u0013\u0010~\u001a\u0008\u0012\u0004\u0012\u00020}0|\u00a2\u0006\u0004\u0008~\u0010\u007fJ\u0016\u0010\u0081\u0001\u001a\t\u0012\u0005\u0012\u00030\u0080\u00010|\u00a2\u0006\u0005\u0008\u0081\u0001\u0010\u007fJ\u0015\u0010\u0082\u0001\u001a\u0008\u0012\u0004\u0012\u00020G0|\u00a2\u0006\u0005\u0008\u0082\u0001\u0010\u007fJ\u0015\u0010\u0083\u0001\u001a\u0008\u0012\u0004\u0012\u00020y0|\u00a2\u0006\u0005\u0008\u0083\u0001\u0010\u007fJ\u000f\u0010\u0084\u0001\u001a\u00020B\u00a2\u0006\u0005\u0008\u0084\u0001\u0010DJ\u000f\u0010\u0085\u0001\u001a\u00020B\u00a2\u0006\u0005\u0008\u0085\u0001\u0010DJ\u0011\u0010\u0086\u0001\u001a\u00020BH\u0014\u00a2\u0006\u0005\u0008\u0086\u0001\u0010DJ,\u0010\u0089\u0001\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0008\u0010\u0088\u0001\u001a\u00030\u0087\u00012\u0006\u0010Y\u001a\u00020XH\u0016\u00a2\u0006\u0006\u0008\u0089\u0001\u0010\u008a\u0001J$\u0010\u008d\u0001\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0008\u0010\u008c\u0001\u001a\u00030\u008b\u0001H\u0016\u00a2\u0006\u0006\u0008\u008d\u0001\u0010\u008e\u0001J$\u0010\u0091\u0001\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0008\u0010\u0090\u0001\u001a\u00030\u008f\u0001H\u0016\u00a2\u0006\u0006\u0008\u0091\u0001\u0010\u0092\u0001J-\u0010\u0094\u0001\u001a\u00020B2\u0008\u0010\u0088\u0001\u001a\u00030\u0087\u00012\u0007\u0010\u0093\u0001\u001a\u00020\u00042\u0006\u0010Y\u001a\u00020XH\u0016\u00a2\u0006\u0006\u0008\u0094\u0001\u0010\u0095\u0001J\u0011\u0010\u0096\u0001\u001a\u00020BH\u0016\u00a2\u0006\u0005\u0008\u0096\u0001\u0010DJ#\u0010\u0098\u0001\u001a\u00020B2\u0006\u0010H\u001a\u00020G2\u0007\u0010W\u001a\u00030\u0097\u0001H\u0016\u00a2\u0006\u0006\u0008\u0098\u0001\u0010\u0099\u0001J \u0010\u009a\u0001\u001a\u00020B2\u0006\u0010W\u001a\u00020V2\u0006\u0010Y\u001a\u00020X\u00a2\u0006\u0006\u0008\u009a\u0001\u0010\u009b\u0001J*\u0010\u009e\u0001\u001a\u00020B2\u0008\u0010\u009d\u0001\u001a\u00030\u009c\u00012\u0006\u0010W\u001a\u00020V2\u0006\u0010Y\u001a\u00020X\u00a2\u0006\u0006\u0008\u009e\u0001\u0010\u009f\u0001J\u000f\u0010\u00a0\u0001\u001a\u00020B\u00a2\u0006\u0005\u0008\u00a0\u0001\u0010DJ-\u0010\u00a3\u0001\u001a\u00020B2\u0008\u0010\u00a1\u0001\u001a\u00030\u0087\u00012\u0007\u0010\u00a2\u0001\u001a\u00020G2\u0006\u0010\u0005\u001a\u00020\u0004H\u0016\u00a2\u0006\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001R\u0016\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a5\u0001\u0010\u00a6\u0001R\u0016\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a7\u0001\u0010\u00a8\u0001R\u0016\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a9\u0001\u0010\u00aa\u0001R\u0016\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ab\u0001\u0010\u00ac\u0001R\u0016\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ad\u0001\u0010\u00ae\u0001R\u0016\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00af\u0001\u0010\u00b0\u0001R\u0016\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b1\u0001\u0010\u00b2\u0001R\u0016\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001R\u0016\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0098\u0001\u0010\u00b5\u0001R\u0016\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0001\u0010\u00b7\u0001R\u0016\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b8\u0001\u0010\u00b9\u0001R\u0016\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ba\u0001\u0010\u00bb\u0001R\u0016\u0010\u001d\u001a\u00020\u001c8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00bc\u0001\u0010\u00bd\u0001R\u0016\u0010\u001f\u001a\u00020\u001e8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00be\u0001\u0010\u00bf\u0001R\u0016\u0010!\u001a\u00020 8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c0\u0001\u0010\u00c1\u0001R\u0016\u0010#\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c2\u0001\u0010\u00c3\u0001R\u0016\u0010%\u001a\u00020$8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c4\u0001\u0010\u00c5\u0001R\u0016\u0010\'\u001a\u00020&8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c6\u0001\u0010\u00c7\u0001R\u0016\u0010)\u001a\u00020(8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00c8\u0001\u0010\u00c9\u0001R\u0016\u0010+\u001a\u00020*8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ca\u0001\u0010\u00cb\u0001R\u0016\u0010-\u001a\u00020,8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00cc\u0001\u0010\u00cd\u0001R\u0016\u0010/\u001a\u00020.8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ce\u0001\u0010\u00cf\u0001R\u0016\u00101\u001a\u0002008\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d0\u0001\u0010\u00d1\u0001R\u0016\u00103\u001a\u0002028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d2\u0001\u0010\u00d3\u0001R\u0016\u00105\u001a\u0002048\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009e\u0001\u0010\u00d4\u0001R\u0016\u00107\u001a\u0002068\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d5\u0001\u0010\u00d6\u0001R\u0016\u00109\u001a\u0002088\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d7\u0001\u0010\u00d8\u0001R\u0016\u0010;\u001a\u00020:8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00d9\u0001\u0010\u00da\u0001R\u0016\u0010=\u001a\u00020<8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00db\u0001\u0010\u00dc\u0001R\u0016\u0010?\u001a\u00020>8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00dd\u0001\u0010\u00de\u0001R\u001c\u0010\u00e2\u0001\u001a\u0005\u0018\u00010\u00df\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00e0\u0001\u0010\u00e1\u0001R\u001e\u0010\u00e6\u0001\u001a\t\u0012\u0004\u0012\u00020y0\u00e3\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00e4\u0001\u0010\u00e5\u0001R\u001c\u0010\u00e8\u0001\u001a\u0005\u0018\u00010\u00df\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00e7\u0001\u0010\u00e1\u0001R\u001c\u0010\u00ea\u0001\u001a\u0005\u0018\u00010\u00df\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00e9\u0001\u0010\u00e1\u0001R\u001c\u0010\u00ec\u0001\u001a\u0005\u0018\u00010\u00df\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00eb\u0001\u0010\u00e1\u0001R\u001c\u0010\u00ee\u0001\u001a\u0005\u0018\u00010\u00df\u00018\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00ed\u0001\u0010\u00e1\u0001R\u0019\u0010\u00f0\u0001\u001a\u00020\u00048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00ef\u0001\u0010\u00a6\u0001R\u0019\u0010\u00f2\u0001\u001a\u00020\u00048\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0008\n\u0006\u0008\u00f1\u0001\u0010\u00a6\u0001R\u0017\u0010\u00f5\u0001\u001a\u00020X8\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f3\u0001\u0010\u00f4\u0001R\u001e\u0010\u00f9\u0001\u001a\t\u0012\u0004\u0012\u00020M0\u00f6\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00f7\u0001\u0010\u00f8\u0001R&\u0010\u00fd\u0001\u001a\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u00fb\u00010\u00fa\u00010\u00e3\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00fc\u0001\u0010\u00e5\u0001R,\u0010\u0080\u0002\u001a\u0017\u0012\u0012\u0012\u0010\u0012\u000b\u0012\t\u0012\u0004\u0012\u00020M0\u00f6\u00010\u00fe\u00010\u00e3\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00ff\u0001\u0010\u00e5\u0001R-\u0010\u0082\u0002\u001a\u0018\u0012\u0013\u0012\u0011\u0012\u000c\u0012\n\u0012\u0005\u0012\u00030\u008f\u00010\u00f6\u00010\u00fa\u00010\u00e3\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0081\u0002\u0010\u00e5\u0001R\u001e\u0010\u0086\u0002\u001a\t\u0012\u0004\u0012\u00020G0\u0083\u00028\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0084\u0002\u0010\u0085\u0002\u00a8\u0006\u008b\u0002"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/a;",
        "LCb1/a;",
        "",
        "isVirtual",
        "LwX0/c;",
        "router",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "popularClassicAggregatorDelegate",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
        "getPromoEntitiesUseCase",
        "Lv81/g;",
        "getAggregatorGameUseCase",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;",
        "getAggregatorCategoryIdUseCase",
        "Li8/l;",
        "getThemeStreamUseCase",
        "Lfk/k;",
        "getBalancesUseCase",
        "LHX0/e;",
        "resourceManager",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "aggregatorScreenFactory",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "connectionObserver",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "LDg/a;",
        "gamesAnalytics",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "myAggregatorAnalytics",
        "Lfk/m;",
        "getPrimaryBalanceUseCase",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
        "getPopularGamesCategoriesScenario",
        "LSR/a;",
        "popularFatmanLogger",
        "LnR/a;",
        "aggregatorGamesFatmanLogger",
        "Lv81/p;",
        "getPopularCategoriesUseCase",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "remoteConfigUseCase",
        "Lek/f;",
        "updateWithCheckGamesAggregatorScenario",
        "Lm8/a;",
        "coroutineDispatchers",
        "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
        "newsAnalytics",
        "LnR/d;",
        "aggregatorTournamentFatmanLogger",
        "Ldu/e;",
        "isCountryNotDefinedScenario",
        "Luf0/a;",
        "updateInnerTabErrorStateUseCase",
        "Lp9/c;",
        "getAuthorizationStateUseCase",
        "LXa0/i;",
        "setShowPopUpBonusUseCase",
        "Lv81/j;",
        "getCategoriesUseCase",
        "<init>",
        "(ZLwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;Lv81/g;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;Li8/l;Lfk/k;LHX0/e;Lorg/xplatform/aggregator/api/navigation/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;LSR/a;LnR/a;Lv81/p;Lorg/xbet/remoteconfig/domain/usecases/i;Lek/f;Lm8/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LnR/d;Ldu/e;Luf0/a;Lp9/c;LXa0/i;Lv81/j;)V",
        "",
        "n4",
        "()V",
        "S4",
        "Z4",
        "",
        "screenName",
        "Lg81/b;",
        "aggregatorCategoryModel",
        "T4",
        "(Ljava/lang/String;Lg81/b;)V",
        "LQb1/c;",
        "model",
        "U4",
        "(Ljava/lang/String;LQb1/c;)V",
        "LQb1/d;",
        "Y4",
        "(LQb1/d;)V",
        "R4",
        "(Ljava/lang/String;)V",
        "Lorg/xplatform/aggregator/api/model/Game;",
        "game",
        "",
        "subcategoryId",
        "V4",
        "(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V",
        "E4",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;",
        "requestType",
        "c5",
        "(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V",
        "d5",
        "(Lkotlin/coroutines/e;)Ljava/lang/Object;",
        "a5",
        "x4",
        "r4",
        "G4",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "u4",
        "()Lorg/xbet/uikit/components/lottie_empty/n;",
        "",
        "throwable",
        "Lkotlin/Function0;",
        "onErrorLoadingContent",
        "K4",
        "(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V",
        "I4",
        "(Ljava/lang/Throwable;)V",
        "p4",
        "q4",
        "()Z",
        "categoryId",
        "C4",
        "(Ljava/lang/String;I)V",
        "D4",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
        "B4",
        "(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;)Z",
        "Lkotlinx/coroutines/flow/e;",
        "Lq81/d;",
        "v4",
        "()Lkotlinx/coroutines/flow/e;",
        "Lq81/c;",
        "w4",
        "b5",
        "A4",
        "Q4",
        "P4",
        "onCleared",
        "",
        "gameId",
        "k0",
        "(Ljava/lang/String;JI)V",
        "",
        "item",
        "l",
        "(Ljava/lang/String;Ljava/lang/Object;)V",
        "LHZ0/a;",
        "selectedCategoryCard",
        "i",
        "(Ljava/lang/String;LHZ0/a;)V",
        "favorite",
        "e0",
        "(JZI)V",
        "b0",
        "LCb1/b;",
        "V1",
        "(Ljava/lang/String;LCb1/b;)V",
        "o4",
        "(Lorg/xplatform/aggregator/api/model/Game;I)V",
        "Lorg/xbet/balance/model/BalanceModel;",
        "balance",
        "H4",
        "(Lorg/xbet/balance/model/BalanceModel;Lorg/xplatform/aggregator/api/model/Game;I)V",
        "p0",
        "id",
        "title",
        "h0",
        "(JLjava/lang/String;Z)V",
        "v1",
        "Z",
        "x1",
        "LwX0/c;",
        "y1",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;",
        "F1",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
        "H1",
        "Lv81/g;",
        "I1",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;",
        "P1",
        "Li8/l;",
        "S1",
        "Lfk/k;",
        "LHX0/e;",
        "b2",
        "Lorg/xplatform/aggregator/api/navigation/a;",
        "v2",
        "LSX0/c;",
        "x2",
        "Lorg/xbet/ui_common/utils/internet/a;",
        "y2",
        "Lorg/xbet/ui_common/utils/M;",
        "F2",
        "LDg/a;",
        "H2",
        "Lorg/xbet/analytics/domain/scope/g0;",
        "I2",
        "Lfk/m;",
        "P2",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;",
        "S2",
        "LSR/a;",
        "V2",
        "LnR/a;",
        "X2",
        "Lv81/p;",
        "F3",
        "Lorg/xbet/remoteconfig/domain/usecases/i;",
        "H3",
        "Lek/f;",
        "I3",
        "Lm8/a;",
        "S3",
        "Lorg/xbet/analytics/domain/scope/NewsAnalytics;",
        "LnR/d;",
        "X4",
        "Ldu/e;",
        "v5",
        "Luf0/a;",
        "w5",
        "Lp9/c;",
        "x5",
        "LXa0/i;",
        "y5",
        "Lv81/j;",
        "Lkotlinx/coroutines/x0;",
        "z5",
        "Lkotlinx/coroutines/x0;",
        "networkConnectionJob",
        "Lkotlinx/coroutines/flow/V;",
        "A5",
        "Lkotlinx/coroutines/flow/V;",
        "viewState",
        "B5",
        "fetchPromoJob",
        "C5",
        "fetchCategoriesJob",
        "D5",
        "fetchGamesJob",
        "E5",
        "loadDataJob",
        "F5",
        "isCountryBlocking",
        "G5",
        "firstRequest",
        "H5",
        "I",
        "initialGamesStyle",
        "",
        "I5",
        "Ljava/util/List;",
        "categoryGamesShimmersModel",
        "LKb1/a;",
        "LBb1/a;",
        "J5",
        "promoEntities",
        "LKb1/c;",
        "K5",
        "categoryGames",
        "L5",
        "categories",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "M5",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "showFavoritesErrorMutableSharedFlow",
        "N5",
        "b",
        "RequestType",
        "a",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final N5:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final A5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public B5:Lkotlinx/coroutines/x0;

.field public C5:Lkotlinx/coroutines/x0;

.field public D5:Lkotlinx/coroutines/x0;

.field public E5:Lkotlinx/coroutines/x0;

.field public final F1:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F2:LDg/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final F3:Lorg/xbet/remoteconfig/domain/usecases/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public F5:Z

.field public G5:Z

.field public final H1:Lv81/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H2:Lorg/xbet/analytics/domain/scope/g0;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H3:Lek/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H4:LnR/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H5:I

.field public final I1:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I2:Lfk/m;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I3:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I5:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "LQb1/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final J5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LKb1/a<",
            "LBb1/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final K5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LKb1/c<",
            "Ljava/util/List<",
            "LQb1/c;",
            ">;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final L5:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "LKb1/a<",
            "Ljava/util/List<",
            "LHZ0/a;",
            ">;>;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final M5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P1:Li8/l;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:Lfk/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:LSR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S3:Lorg/xbet/analytics/domain/scope/NewsAnalytics;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V2:LnR/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X2:Lv81/p;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final X4:Ldu/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:Lorg/xplatform/aggregator/api/navigation/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Z

.field public final v2:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v5:Luf0/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final w5:Lp9/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LwX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/ui_common/utils/internet/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x5:LXa0/i;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y5:Lv81/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public z5:Lkotlinx/coroutines/x0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->N5:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$a;

    return-void
.end method

.method public constructor <init>(ZLwX0/c;Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;Lv81/g;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;Li8/l;Lfk/k;LHX0/e;Lorg/xplatform/aggregator/api/navigation/a;LSX0/c;Lorg/xbet/ui_common/utils/internet/a;Lorg/xbet/ui_common/utils/M;LDg/a;Lorg/xbet/analytics/domain/scope/g0;Lfk/m;Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;LSR/a;LnR/a;Lv81/p;Lorg/xbet/remoteconfig/domain/usecases/i;Lek/f;Lm8/a;Lorg/xbet/analytics/domain/scope/NewsAnalytics;LnR/d;Ldu/e;Luf0/a;Lp9/c;LXa0/i;Lv81/j;)V
    .locals 11
    .param p2    # LwX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p5    # Lv81/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Li8/l;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lfk/k;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lorg/xplatform/aggregator/api/navigation/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/ui_common/utils/internet/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LDg/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p15    # Lorg/xbet/analytics/domain/scope/g0;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p16    # Lfk/m;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p17    # Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p18    # LSR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p19    # LnR/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p20    # Lv81/p;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p21    # Lorg/xbet/remoteconfig/domain/usecases/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p22    # Lek/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p23    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p24    # Lorg/xbet/analytics/domain/scope/NewsAnalytics;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p25    # LnR/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p26    # Ldu/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p27    # Luf0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p28    # Lp9/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p29    # LXa0/i;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p30    # Lv81/j;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-boolean p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 9
    .line 10
    iput-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F1:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;

    .line 11
    .line 12
    move-object/from16 p2, p5

    .line 13
    .line 14
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H1:Lv81/g;

    .line 15
    .line 16
    move-object/from16 p2, p6

    .line 17
    .line 18
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I1:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;

    .line 19
    .line 20
    move-object/from16 p2, p7

    .line 21
    .line 22
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->P1:Li8/l;

    .line 23
    .line 24
    move-object/from16 p2, p8

    .line 25
    .line 26
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S1:Lfk/k;

    .line 27
    .line 28
    move-object/from16 p2, p9

    .line 29
    .line 30
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V1:LHX0/e;

    .line 31
    .line 32
    move-object/from16 p2, p10

    .line 33
    .line 34
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 35
    .line 36
    move-object/from16 p2, p11

    .line 37
    .line 38
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v2:LSX0/c;

    .line 39
    .line 40
    move-object/from16 p2, p12

    .line 41
    .line 42
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x2:Lorg/xbet/ui_common/utils/internet/a;

    .line 43
    .line 44
    move-object/from16 p2, p13

    .line 45
    .line 46
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y2:Lorg/xbet/ui_common/utils/M;

    .line 47
    .line 48
    move-object/from16 p2, p14

    .line 49
    .line 50
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F2:LDg/a;

    .line 51
    .line 52
    move-object/from16 p2, p15

    .line 53
    .line 54
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H2:Lorg/xbet/analytics/domain/scope/g0;

    .line 55
    .line 56
    move-object/from16 p2, p16

    .line 57
    .line 58
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I2:Lfk/m;

    .line 59
    .line 60
    move-object/from16 p2, p17

    .line 61
    .line 62
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->P2:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 63
    .line 64
    move-object/from16 p2, p18

    .line 65
    .line 66
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S2:LSR/a;

    .line 67
    .line 68
    move-object/from16 p2, p19

    .line 69
    .line 70
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V2:LnR/a;

    .line 71
    .line 72
    move-object/from16 p2, p20

    .line 73
    .line 74
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X2:Lv81/p;

    .line 75
    .line 76
    move-object/from16 p2, p21

    .line 77
    .line 78
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F3:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 79
    .line 80
    move-object/from16 v0, p22

    .line 81
    .line 82
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H3:Lek/f;

    .line 83
    .line 84
    move-object/from16 v0, p23

    .line 85
    .line 86
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 87
    .line 88
    move-object/from16 v0, p24

    .line 89
    .line 90
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S3:Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    .line 91
    .line 92
    move-object/from16 v0, p25

    .line 93
    .line 94
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H4:LnR/d;

    .line 95
    .line 96
    move-object/from16 v0, p26

    .line 97
    .line 98
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X4:Ldu/e;

    .line 99
    .line 100
    move-object/from16 v0, p27

    .line 101
    .line 102
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v5:Luf0/a;

    .line 103
    .line 104
    move-object/from16 v0, p28

    .line 105
    .line 106
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->w5:Lp9/c;

    .line 107
    .line 108
    move-object/from16 v0, p29

    .line 109
    .line 110
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x5:LXa0/i;

    .line 111
    .line 112
    move-object/from16 v0, p30

    .line 113
    .line 114
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y5:Lv81/j;

    .line 115
    .line 116
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$c;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$c;

    .line 117
    .line 118
    invoke-static {v0}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 119
    .line 120
    .line 121
    move-result-object v0

    .line 122
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A5:Lkotlinx/coroutines/flow/V;

    .line 123
    .line 124
    invoke-interface {p2}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 125
    .line 126
    .line 127
    move-result-object p2

    .line 128
    invoke-virtual {p2}, Lek0/o;->m()Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;

    .line 129
    .line 130
    .line 131
    move-result-object p2

    .line 132
    const/4 v0, 0x1

    .line 133
    invoke-static {p2, v0}, Ls81/b;->b(Lorg/xbet/remoteconfig/domain/models/AggregatorGameCardCollectionStyleType;Z)I

    .line 134
    .line 135
    .line 136
    move-result p2

    .line 137
    iput p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H5:I

    .line 138
    .line 139
    if-eqz p1, :cond_0

    .line 140
    .line 141
    const/4 p1, 0x3

    .line 142
    goto :goto_0

    .line 143
    :cond_0
    const/4 p1, 0x4

    .line 144
    :goto_0
    new-instance p2, Ljava/util/ArrayList;

    .line 145
    .line 146
    invoke-direct {p2, p1}, Ljava/util/ArrayList;-><init>(I)V

    .line 147
    .line 148
    .line 149
    const/4 v1, 0x0

    .line 150
    :goto_1
    if-ge v1, p1, :cond_1

    .line 151
    .line 152
    new-instance v2, LQb1/c;

    .line 153
    .line 154
    iget v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H5:I

    .line 155
    .line 156
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 157
    .line 158
    .line 159
    move-result-object v4

    .line 160
    sget-object v5, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 161
    .line 162
    const/4 v6, 0x0

    .line 163
    iget-boolean v7, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 164
    .line 165
    const-wide/16 v8, -0x1

    .line 166
    .line 167
    const-string v10, ""

    .line 168
    .line 169
    move-object p3, v2

    .line 170
    move/from16 p7, v3

    .line 171
    .line 172
    move-object/from16 p8, v4

    .line 173
    .line 174
    move-object/from16 p9, v5

    .line 175
    .line 176
    move/from16 p11, v7

    .line 177
    .line 178
    move-wide p4, v8

    .line 179
    move-object/from16 p6, v10

    .line 180
    .line 181
    const/16 p10, 0x0

    .line 182
    .line 183
    invoke-direct/range {p3 .. p11}, LQb1/c;-><init>(JLjava/lang/String;ILjava/util/List;Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;IZ)V

    .line 184
    .line 185
    .line 186
    invoke-virtual {p2, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 187
    .line 188
    .line 189
    add-int/lit8 v1, v1, 0x1

    .line 190
    .line 191
    goto :goto_1

    .line 192
    :cond_1
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I5:Ljava/util/List;

    .line 193
    .line 194
    sget-object p1, LKb1/a$d;->a:LKb1/a$d;

    .line 195
    .line 196
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 197
    .line 198
    .line 199
    move-result-object v1

    .line 200
    iput-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J5:Lkotlinx/coroutines/flow/V;

    .line 201
    .line 202
    new-instance v1, LKb1/c$d;

    .line 203
    .line 204
    invoke-direct {v1, p2}, LKb1/c$d;-><init>(Ljava/lang/Object;)V

    .line 205
    .line 206
    .line 207
    invoke-static {v1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 208
    .line 209
    .line 210
    move-result-object p2

    .line 211
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 212
    .line 213
    invoke-static {p1}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 214
    .line 215
    .line 216
    move-result-object p1

    .line 217
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 218
    .line 219
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 220
    .line 221
    sget-object p2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 222
    .line 223
    invoke-direct {p1, v0, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 224
    .line 225
    .line 226
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->M5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 227
    .line 228
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->n4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->p4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final C4(Ljava/lang/String;I)V
    .locals 3

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F2:LDg/a;

    .line 6
    .line 7
    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    const-string v2, "popular_new_virtual"

    .line 12
    .line 13
    invoke-virtual {v0, v1, v2}, LDg/a;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S2:LSR/a;

    .line 17
    .line 18
    iget-boolean v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 19
    .line 20
    if-eqz v1, :cond_1

    .line 21
    .line 22
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_VIRTUAL:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_1
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 26
    .line 27
    :goto_0
    invoke-interface {v0, p1, p2, v1}, LSR/a;->c(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 28
    .line 29
    .line 30
    return-void
.end method

.method public static final synthetic D3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final D4(Ljava/lang/String;I)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S2:LSR/a;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 4
    .line 5
    invoke-interface {v0, p1, p2, v1}, LSR/a;->h(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic E3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Ljava/util/List;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I5:Ljava/util/List;

    .line 2
    .line 3
    return-object p0
.end method

.method private final E4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->z5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x2:Lorg/xbet/ui_common/utils/internet/a;

    .line 14
    .line 15
    invoke-interface {v0}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->E(Lkotlinx/coroutines/flow/e;I)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeConnection$1;

    .line 24
    .line 25
    const/4 v2, 0x0

    .line 26
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeConnection$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 27
    .line 28
    .line 29
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 38
    .line 39
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-static {v1, v2}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeConnection$2;

    .line 48
    .line 49
    invoke-direct {v2, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeConnection$2;-><init>(Ljava/lang/Object;)V

    .line 50
    .line 51
    .line 52
    invoke-static {v0, v1, v2}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->z5:Lkotlinx/coroutines/x0;

    .line 57
    .line 58
    return-void
.end method

.method public static final synthetic F3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->G5:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic F4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic G3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lv81/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H1:Lv81/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic H3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lp9/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->w5:Lp9/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic I3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lfk/k;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S1:Lfk/k;

    .line 2
    .line 3
    return-object p0
.end method

.method private final I4(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y2:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/l;

    .line 4
    .line 5
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/l;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final synthetic J3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lv81/j;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y5:Lv81/j;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final J4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 8

    .line 1
    instance-of p3, p2, Ljava/net/SocketTimeoutException;

    .line 2
    .line 3
    if-nez p3, :cond_2

    .line 4
    .line 5
    instance-of p3, p2, Ljava/net/ConnectException;

    .line 6
    .line 7
    if-nez p3, :cond_2

    .line 8
    .line 9
    instance-of p3, p2, Ljava/net/UnknownHostException;

    .line 10
    .line 11
    if-nez p3, :cond_2

    .line 12
    .line 13
    instance-of p3, p2, Lcom/xbet/onexcore/data/model/ServerException;

    .line 14
    .line 15
    if-eqz p3, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    instance-of p2, p2, Lorg/xplatform/aggregator/api/domain/exceptions/FavoritesLimitException;

    .line 19
    .line 20
    if-eqz p2, :cond_1

    .line 21
    .line 22
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onError$1$1;

    .line 27
    .line 28
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onError$1$1;-><init>(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    new-instance v5, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onError$1$2;

    .line 32
    .line 33
    const/4 p1, 0x0

    .line 34
    invoke-direct {v5, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onError$1$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 35
    .line 36
    .line 37
    const/16 v6, 0xe

    .line 38
    .line 39
    const/4 v7, 0x0

    .line 40
    const/4 v2, 0x0

    .line 41
    const/4 v3, 0x0

    .line 42
    const/4 v4, 0x0

    .line 43
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 44
    .line 45
    .line 46
    goto :goto_1

    .line 47
    :cond_1
    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    .line 48
    .line 49
    .line 50
    goto :goto_1

    .line 51
    :cond_2
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A5:Lkotlinx/coroutines/flow/V;

    .line 52
    .line 53
    new-instance p2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 54
    .line 55
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->u4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 56
    .line 57
    .line 58
    move-result-object p0

    .line 59
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 60
    .line 61
    .line 62
    invoke-interface {p1, p2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    :goto_1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 66
    .line 67
    return-object p0
.end method

.method public static final synthetic K3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lv81/p;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X2:Lv81/p;

    .line 2
    .line 3
    return-object p0
.end method

.method private final K4(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Throwable;",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y2:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;

    .line 4
    .line 5
    invoke-direct {v1, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/p;-><init>(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1, v1}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public static final synthetic L3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lfk/m;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I2:Lfk/m;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final L4(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Ljava/lang/Throwable;->printStackTrace()V

    .line 2
    .line 3
    .line 4
    invoke-interface {p1}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 5
    .line 6
    .line 7
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 8
    .line 9
    return-object p0
.end method

.method public static final synthetic M3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F1:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final M4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic N3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)I
    .locals 0

    .line 1
    iget p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H5:I

    .line 2
    .line 3
    return p0
.end method

.method public static final N4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y2:Lorg/xbet/ui_common/utils/M;

    .line 2
    .line 3
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/j;

    .line 4
    .line 5
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/j;-><init>()V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, p1, v0}, Lorg/xbet/ui_common/utils/M;->k(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function2;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static final synthetic O3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->u4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final O4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic P3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)LSX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v2:LSX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic Q3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic R3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method private final R4(Ljava/lang/String;)V
    .locals 13

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V2:LnR/a;

    .line 6
    .line 7
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 8
    .line 9
    invoke-interface {v0, p1, v1}, LnR/a;->m(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 10
    .line 11
    .line 12
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H2:Lorg/xbet/analytics/domain/scope/g0;

    .line 13
    .line 14
    const-string v0, "popular_casino"

    .line 15
    .line 16
    invoke-virtual {p1, v0}, Lorg/xbet/analytics/domain/scope/g0;->K(Ljava/lang/String;)V

    .line 17
    .line 18
    .line 19
    :cond_0
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 20
    .line 21
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 22
    .line 23
    iget-boolean v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 24
    .line 25
    if-eqz v1, :cond_1

    .line 26
    .line 27
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$MyVirtual;

    .line 28
    .line 29
    const/4 v9, 0x7

    .line 30
    const/4 v10, 0x0

    .line 31
    const-wide/16 v3, 0x0

    .line 32
    .line 33
    const-wide/16 v5, 0x0

    .line 34
    .line 35
    const-wide/16 v7, 0x0

    .line 36
    .line 37
    invoke-direct/range {v2 .. v10}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$MyVirtual;-><init>(JJJILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 38
    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    new-instance v3, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$MyAggregator;

    .line 42
    .line 43
    const/16 v11, 0xf

    .line 44
    .line 45
    const/4 v12, 0x0

    .line 46
    const-wide/16 v4, 0x0

    .line 47
    .line 48
    const-wide/16 v6, 0x0

    .line 49
    .line 50
    const-wide/16 v8, 0x0

    .line 51
    .line 52
    const/4 v10, 0x0

    .line 53
    invoke-direct/range {v3 .. v12}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$MyAggregator;-><init>(JJJZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 54
    .line 55
    .line 56
    move-object v2, v3

    .line 57
    :goto_0
    invoke-interface {v0, v1, v2}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {p1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public static final synthetic S3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F3:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 2
    .line 3
    return-object p0
.end method

.method private final S4()V
    .locals 6

    .line 1
    iget-boolean v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H2:Lorg/xbet/analytics/domain/scope/g0;

    .line 6
    .line 7
    const-string v1, "popular_casino"

    .line 8
    .line 9
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/domain/scope/g0;->u(Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S2:LSR/a;

    .line 13
    .line 14
    const-class v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 15
    .line 16
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    sget-object v2, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_CLASSIC_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 21
    .line 22
    invoke-interface {v0, v1, v2}, LSR/a;->j(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 23
    .line 24
    .line 25
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 26
    .line 27
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 28
    .line 29
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;

    .line 30
    .line 31
    const/4 v3, 0x3

    .line 32
    const/4 v4, 0x0

    .line 33
    const/4 v5, 0x0

    .line 34
    invoke-direct {v2, v4, v5, v3, v4}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    invoke-interface {v1, v5, v2}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 38
    .line 39
    .line 40
    move-result-object v1

    .line 41
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 42
    .line 43
    .line 44
    return-void
.end method

.method public static final synthetic T3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method private final T4(Ljava/lang/String;Lg81/b;)V
    .locals 26

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-boolean v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 4
    .line 5
    if-nez v1, :cond_0

    .line 6
    .line 7
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H2:Lorg/xbet/analytics/domain/scope/g0;

    .line 8
    .line 9
    invoke-virtual/range {p2 .. p2}, Lg81/b;->g()J

    .line 10
    .line 11
    .line 12
    move-result-wide v2

    .line 13
    long-to-int v3, v2

    .line 14
    const-string v2, "popular_casino"

    .line 15
    .line 16
    invoke-virtual {v1, v3, v2}, Lorg/xbet/analytics/domain/scope/g0;->t(ILjava/lang/String;)V

    .line 17
    .line 18
    .line 19
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S2:LSR/a;

    .line 20
    .line 21
    const-class v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 22
    .line 23
    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v2

    .line 27
    invoke-virtual/range {p2 .. p2}, Lg81/b;->g()J

    .line 28
    .line 29
    .line 30
    move-result-wide v3

    .line 31
    long-to-int v4, v3

    .line 32
    sget-object v3, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_CLASSIC_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 33
    .line 34
    invoke-interface {v1, v2, v4, v3}, LSR/a;->a(Ljava/lang/String;ILorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 35
    .line 36
    .line 37
    :cond_0
    iget-boolean v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 38
    .line 39
    if-nez v1, :cond_1

    .line 40
    .line 41
    invoke-virtual/range {p2 .. p2}, Lg81/b;->l()J

    .line 42
    .line 43
    .line 44
    move-result-wide v1

    .line 45
    const-wide/16 v3, 0x3

    .line 46
    .line 47
    cmp-long v5, v1, v3

    .line 48
    .line 49
    if-nez v5, :cond_1

    .line 50
    .line 51
    invoke-virtual/range {p2 .. p2}, Lg81/b;->e()J

    .line 52
    .line 53
    .line 54
    move-result-wide v7

    .line 55
    invoke-virtual/range {p2 .. p2}, Lg81/b;->m()J

    .line 56
    .line 57
    .line 58
    move-result-wide v9

    .line 59
    invoke-virtual/range {p2 .. p2}, Lg81/b;->n()Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v16

    .line 63
    invoke-virtual/range {p2 .. p2}, Lg81/b;->j()Z

    .line 64
    .line 65
    .line 66
    move-result v21

    .line 67
    invoke-virtual/range {p2 .. p2}, Lg81/b;->k()Z

    .line 68
    .line 69
    .line 70
    move-result v22

    .line 71
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 72
    .line 73
    .line 74
    move-result-object v25

    .line 75
    new-instance v6, Lorg/xplatform/aggregator/api/model/Game;

    .line 76
    .line 77
    const/16 v23, 0x0

    .line 78
    .line 79
    const/16 v24, 0x0

    .line 80
    .line 81
    const-wide/16 v11, 0x0

    .line 82
    .line 83
    const-wide/16 v13, 0x0

    .line 84
    .line 85
    const-string v15, ""

    .line 86
    .line 87
    const-string v17, ""

    .line 88
    .line 89
    const/16 v18, 0x0

    .line 90
    .line 91
    const/16 v19, 0x0

    .line 92
    .line 93
    const/16 v20, 0x0

    .line 94
    .line 95
    invoke-direct/range {v6 .. v25}, Lorg/xplatform/aggregator/api/model/Game;-><init>(JJJJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZZLjava/util/List;)V

    .line 96
    .line 97
    .line 98
    const/4 v1, 0x0

    .line 99
    move-object/from16 v2, p1

    .line 100
    .line 101
    invoke-direct {v0, v2, v6, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V4(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 102
    .line 103
    .line 104
    return-void

    .line 105
    :cond_1
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 106
    .line 107
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 108
    .line 109
    iget-boolean v3, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 110
    .line 111
    new-instance v4, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;

    .line 112
    .line 113
    new-instance v5, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;

    .line 114
    .line 115
    invoke-virtual/range {p2 .. p2}, Lg81/b;->n()Ljava/lang/String;

    .line 116
    .line 117
    .line 118
    move-result-object v6

    .line 119
    invoke-virtual/range {p2 .. p2}, Lg81/b;->g()J

    .line 120
    .line 121
    .line 122
    move-result-wide v7

    .line 123
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 124
    .line 125
    .line 126
    move-result-object v9

    .line 127
    const/16 v13, 0x18

    .line 128
    .line 129
    const/4 v14, 0x0

    .line 130
    const/4 v10, 0x0

    .line 131
    const-wide/16 v11, 0x0

    .line 132
    .line 133
    invoke-direct/range {v5 .. v14}, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;-><init>(Ljava/lang/String;JLjava/util/List;Ljava/util/List;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 134
    .line 135
    .line 136
    iget-boolean v6, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 137
    .line 138
    invoke-direct {v4, v5, v6}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;Z)V

    .line 139
    .line 140
    .line 141
    invoke-interface {v2, v3, v4}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 142
    .line 143
    .line 144
    move-result-object v2

    .line 145
    invoke-virtual {v1, v2}, LwX0/c;->m(Lq4/q;)V

    .line 146
    .line 147
    .line 148
    return-void
.end method

.method public static final synthetic U3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->M5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic V3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Luf0/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v5:Luf0/a;

    .line 2
    .line 3
    return-object p0
.end method

.method private final V4(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 9

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V2:LnR/a;

    .line 2
    .line 3
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    long-to-int v2, v1

    .line 8
    iget-boolean v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 9
    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_VIRTUAL:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 13
    .line 14
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    sget-object v1, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_NEW_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 20
    .line 21
    invoke-virtual {v1}, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->getValue()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    :goto_0
    invoke-interface {v0, p1, v2, p3, v1}, LnR/a;->d(Ljava/lang/String;IILjava/lang/String;)V

    .line 26
    .line 27
    .line 28
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H2:Lorg/xbet/analytics/domain/scope/g0;

    .line 29
    .line 30
    iget-boolean v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 31
    .line 32
    if-eqz v0, :cond_1

    .line 33
    .line 34
    const-string v0, "popular_new_virtual"

    .line 35
    .line 36
    :goto_1
    move-object v4, v0

    .line 37
    goto :goto_2

    .line 38
    :cond_1
    const-string v0, "popular_casino"

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :goto_2
    int-to-long v5, p3

    .line 42
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/Game;->getId()J

    .line 43
    .line 44
    .line 45
    move-result-wide v7

    .line 46
    invoke-virtual/range {v3 .. v8}, Lorg/xbet/analytics/domain/scope/g0;->X(Ljava/lang/String;JJ)V

    .line 47
    .line 48
    .line 49
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 50
    .line 51
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;

    .line 56
    .line 57
    invoke-direct {v2, p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/i;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 58
    .line 59
    .line 60
    invoke-virtual {v0, p2, p3, v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->u(Lorg/xplatform/aggregator/api/model/Game;ILkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;)V

    .line 61
    .line 62
    .line 63
    return-void
.end method

.method public static final synthetic W3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lek/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H3:Lek/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final W4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    instance-of v0, p4, Lcom/xbet/onexuser/domain/exceptions/UnauthorizedException;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object p4, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 6
    .line 7
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/n;

    .line 8
    .line 9
    invoke-direct {v0, p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/n;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p4, v0}, LwX0/c;->l(Lkotlin/jvm/functions/Function0;)V

    .line 13
    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    invoke-direct {p0, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I4(Ljava/lang/Throwable;)V

    .line 17
    .line 18
    .line 19
    :goto_0
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 20
    .line 21
    return-object p0
.end method

.method public static final synthetic X3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final X4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V4(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic Y3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;)Z
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->B4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic Z3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F5:Z

    .line 2
    .line 3
    return p0
.end method

.method private final Z4()V
    .locals 6

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 4
    .line 5
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Promo;

    .line 6
    .line 7
    new-instance v3, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Tournaments;

    .line 8
    .line 9
    const-wide/16 v4, 0x0

    .line 10
    .line 11
    invoke-direct {v3, v4, v5}, Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen$Tournaments;-><init>(J)V

    .line 12
    .line 13
    .line 14
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Promo;-><init>(Lorg/xplatform/aggregator/api/navigation/PromoTypeToOpen;)V

    .line 15
    .line 16
    .line 17
    const/4 v3, 0x0

    .line 18
    invoke-interface {v1, v3, v2}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    invoke-virtual {v0, v1}, LwX0/c;->m(Lq4/q;)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public static final synthetic a4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Ldu/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X4:Ldu/e;

    .line 2
    .line 3
    return-object p0
.end method

.method private final a5()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    :cond_0
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    move-object v2, v1

    .line 8
    check-cast v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;

    .line 9
    .line 10
    instance-of v2, v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 11
    .line 12
    if-eqz v2, :cond_1

    .line 13
    .line 14
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 15
    .line 16
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->u4()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 17
    .line 18
    .line 19
    move-result-object v3

    .line 20
    invoke-direct {v2, v3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 21
    .line 22
    .line 23
    invoke-interface {v0, v1, v2}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    if-eqz v1, :cond_0

    .line 28
    .line 29
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->INIT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 30
    .line 31
    invoke-virtual {p0, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->c5(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V

    .line 32
    .line 33
    .line 34
    :cond_1
    return-void
.end method

.method public static final synthetic b4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic c4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->E4()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic d4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final d5(Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->P1:Li8/l;

    .line 2
    .line 3
    invoke-interface {v0}, Li8/l;->invoke()Lkotlinx/coroutines/flow/e;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J5:Lkotlinx/coroutines/flow/V;

    .line 8
    .line 9
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 10
    .line 11
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 12
    .line 13
    new-instance v4, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$subscribeData$2;

    .line 14
    .line 15
    const/4 v5, 0x0

    .line 16
    invoke-direct {v4, p0, v5}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$subscribeData$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v0, v1, v2, v3, v4}, Lkotlinx/coroutines/flow/g;->q(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/flow/e;LOc/p;)Lkotlinx/coroutines/flow/e;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$subscribeData$4;

    .line 24
    .line 25
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A5:Lkotlinx/coroutines/flow/V;

    .line 26
    .line 27
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$subscribeData$4;-><init>(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    new-instance v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$c;

    .line 31
    .line 32
    invoke-direct {v2, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$c;-><init>(Lkotlin/reflect/j;)V

    .line 33
    .line 34
    .line 35
    invoke-interface {v0, v2, p1}, Lkotlinx/coroutines/flow/e;->collect(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    if-ne p1, v0, :cond_0

    .line 44
    .line 45
    return-object p1

    .line 46
    :cond_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 47
    .line 48
    return-object p1
.end method

.method public static final synthetic e4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I4(Ljava/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic e5(Lkotlin/reflect/j;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-interface {p0, p1}, Lkotlin/reflect/j;->set(Ljava/lang/Object;)V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final synthetic f4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K4(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic g4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lg81/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->T4(Ljava/lang/String;Lg81/b;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic h4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->a5()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic i4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic j4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->G5:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic k4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->c5(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic l4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->d5(Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final synthetic m4(Lkotlin/reflect/j;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->e5(Lkotlin/reflect/j;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method private final n4()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->z5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    invoke-static {v0, v2, v1, v2}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->E5:Lkotlinx/coroutines/x0;

    .line 11
    .line 12
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->D5:Lkotlinx/coroutines/x0;

    .line 16
    .line 17
    if-eqz v0, :cond_1

    .line 18
    .line 19
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 20
    .line 21
    .line 22
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->C5:Lkotlinx/coroutines/x0;

    .line 23
    .line 24
    if-eqz v0, :cond_2

    .line 25
    .line 26
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 27
    .line 28
    .line 29
    :cond_2
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->B5:Lkotlinx/coroutines/x0;

    .line 30
    .line 31
    if-eqz v0, :cond_3

    .line 32
    .line 33
    invoke-static {v0}, Lcom/xbet/onexcore/utils/ext/a;->a(Lkotlinx/coroutines/x0;)V

    .line 34
    .line 35
    .line 36
    :cond_3
    return-void
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->W4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final p4()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$checkCountryBlocking$1;

    .line 6
    .line 7
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y2:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$checkCountryBlocking$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v5, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$checkCountryBlocking$2;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$checkCountryBlocking$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    const/16 v6, 0xe

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public static synthetic q3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic r3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic s3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->M4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final s4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0, p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K4(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static synthetic t3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->s4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final t4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v0, LKb1/a$b;

    .line 4
    .line 5
    invoke-direct {v0, p1}, LKb1/a$b;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method

.method public static synthetic u3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->z4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method private final u4()Lorg/xbet/uikit/components/lottie_empty/n;
    .locals 12

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v2:LSX0/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;->ERROR:Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;

    .line 4
    .line 5
    iget-boolean v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F5:Z

    .line 6
    .line 7
    if-eqz v2, :cond_0

    .line 8
    .line 9
    sget v2, Lpb/k;->country_blocking:I

    .line 10
    .line 11
    :goto_0
    move v6, v2

    .line 12
    goto :goto_1

    .line 13
    :cond_0
    sget v2, Lpb/k;->data_retrieval_error:I

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :goto_1
    sget v8, Lpb/k;->try_again_text:I

    .line 17
    .line 18
    new-instance v9, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getLottieConfig$1;

    .line 19
    .line 20
    invoke-direct {v9, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getLottieConfig$1;-><init>(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    const/16 v10, 0x5e

    .line 24
    .line 25
    const/4 v11, 0x0

    .line 26
    const/4 v2, 0x0

    .line 27
    const/4 v3, 0x0

    .line 28
    const/4 v4, 0x0

    .line 29
    const/4 v5, 0x0

    .line 30
    const/4 v7, 0x0

    .line 31
    invoke-static/range {v0 .. v11}, LSX0/c$a;->a(LSX0/c;Lorg/xbet/ui_common/viewcomponents/lottie_empty_view/LottieSet;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyStyleType;Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyColorType;IIIIILkotlin/jvm/functions/Function0;ILjava/lang/Object;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    return-object v0
.end method

.method public static synthetic v3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic w3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->N4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic x3(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->O4(Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic y3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->t4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final y4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/o;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/o;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0, p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K4(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;)V

    .line 7
    .line 8
    .line 9
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 10
    .line 11
    return-object p0
.end method

.method public static synthetic z3(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L4(Ljava/lang/Throwable;Lkotlin/jvm/functions/Function0;Ljava/lang/Throwable;Ljava/lang/String;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final z4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;
    .locals 1

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v0, LKb1/a$b;

    .line 4
    .line 5
    invoke-direct {v0, p1}, LKb1/a$b;-><init>(Ljava/lang/Throwable;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {p0, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 12
    .line 13
    return-object p0
.end method


# virtual methods
.method public final A4()Lkotlinx/coroutines/flow/e;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$1;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 7
    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;

    .line 14
    .line 15
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$3;

    .line 23
    .line 24
    invoke-direct {v1, p0, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$3;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 25
    .line 26
    .line 27
    invoke-static {v0, v1}, Lkotlinx/coroutines/flow/g;->h0(Lkotlinx/coroutines/flow/e;LOc/n;)Lkotlinx/coroutines/flow/e;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    return-object v0
.end method

.method public final B4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;)Z
    .locals 0

    .line 1
    instance-of p1, p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 2
    .line 3
    return p1
.end method

.method public final G4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V
    .locals 6

    .line 1
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->INIT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    const/4 v2, 0x1

    .line 5
    if-ne p1, v0, :cond_0

    .line 6
    .line 7
    const/4 v0, 0x1

    .line 8
    goto :goto_0

    .line 9
    :cond_0
    const/4 v0, 0x0

    .line 10
    :goto_0
    iget-boolean v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 11
    .line 12
    const/4 v4, 0x0

    .line 13
    if-eqz v3, :cond_5

    .line 14
    .line 15
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->D5:Lkotlinx/coroutines/x0;

    .line 16
    .line 17
    if-eqz v3, :cond_1

    .line 18
    .line 19
    invoke-interface {v3}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 20
    .line 21
    .line 22
    move-result v3

    .line 23
    if-ne v3, v2, :cond_1

    .line 24
    .line 25
    return-void

    .line 26
    :cond_1
    sget-object v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 27
    .line 28
    if-ne p1, v2, :cond_2

    .line 29
    .line 30
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->q4()Z

    .line 31
    .line 32
    .line 33
    move-result p1

    .line 34
    if-eqz p1, :cond_2

    .line 35
    .line 36
    return-void

    .line 37
    :cond_2
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 38
    .line 39
    :cond_3
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    move-object v3, v2

    .line 44
    check-cast v3, LKb1/c;

    .line 45
    .line 46
    instance-of v5, v3, LKb1/c$c;

    .line 47
    .line 48
    if-nez v5, :cond_4

    .line 49
    .line 50
    new-instance v3, LKb1/c$d;

    .line 51
    .line 52
    iget-object v5, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I5:Ljava/util/List;

    .line 53
    .line 54
    invoke-direct {v3, v5}, LKb1/c$d;-><init>(Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    :cond_4
    invoke-interface {p1, v2, v3}, Lkotlinx/coroutines/flow/V;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v2

    .line 61
    if-eqz v2, :cond_3

    .line 62
    .line 63
    goto :goto_1

    .line 64
    :cond_5
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->D5:Lkotlinx/coroutines/x0;

    .line 65
    .line 66
    if-eqz p1, :cond_6

    .line 67
    .line 68
    invoke-static {p1, v4, v2, v4}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 69
    .line 70
    .line 71
    :cond_6
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 72
    .line 73
    invoke-interface {p1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    instance-of p1, p1, LKb1/c$c;

    .line 78
    .line 79
    if-nez p1, :cond_7

    .line 80
    .line 81
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 82
    .line 83
    new-instance v2, LKb1/c$d;

    .line 84
    .line 85
    iget-object v3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I5:Ljava/util/List;

    .line 86
    .line 87
    invoke-direct {v2, v3}, LKb1/c$d;-><init>(Ljava/lang/Object;)V

    .line 88
    .line 89
    .line 90
    invoke-interface {p1, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 91
    .line 92
    .line 93
    :cond_7
    :goto_1
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->P2:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;

    .line 94
    .line 95
    iget-boolean v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 96
    .line 97
    const/16 v3, 0x8

    .line 98
    .line 99
    invoke-virtual {p1, v2, v3, v0}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/GetPopularGamesCategoriesScenario;->o(ZIZ)Lkotlinx/coroutines/flow/e;

    .line 100
    .line 101
    .line 102
    move-result-object p1

    .line 103
    iget-boolean v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 104
    .line 105
    if-eqz v2, :cond_8

    .line 106
    .line 107
    const/4 v2, 0x2

    .line 108
    invoke-static {p1, v0, v1, v2, v4}, Lcom/xbet/onexcore/utils/flows/ScreenRetryStrategiesExtentionsKt;->g(Lkotlinx/coroutines/flow/e;ZZILjava/lang/Object;)Lkotlinx/coroutines/flow/e;

    .line 109
    .line 110
    .line 111
    move-result-object p1

    .line 112
    goto :goto_2

    .line 113
    :cond_8
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeGames$configuredFlow$1;

    .line 114
    .line 115
    invoke-direct {v0, p0, v4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeGames$configuredFlow$1;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 116
    .line 117
    .line 118
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->j0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 119
    .line 120
    .line 121
    move-result-object p1

    .line 122
    :goto_2
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeGames$2;

    .line 123
    .line 124
    invoke-direct {v0, p0, v4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeGames$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 125
    .line 126
    .line 127
    invoke-static {p1, v0}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 136
    .line 137
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 138
    .line 139
    .line 140
    move-result-object v1

    .line 141
    invoke-static {v0, v1}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeGames$3;

    .line 146
    .line 147
    invoke-direct {v1, p0, v4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$observeGames$3;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 148
    .line 149
    .line 150
    invoke-static {p1, v0, v1}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 151
    .line 152
    .line 153
    move-result-object p1

    .line 154
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->D5:Lkotlinx/coroutines/x0;

    .line 155
    .line 156
    return-void
.end method

.method public final H4(Lorg/xbet/balance/model/BalanceModel;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 2
    .param p1    # Lorg/xbet/balance/model/BalanceModel;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onBalanceChosen$1;

    .line 4
    .line 5
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onBalanceChosen$1;-><init>(Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0, p2, p1, p3, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->q(Lorg/xplatform/aggregator/api/model/Game;Lorg/xbet/balance/model/BalanceModel;ILkotlin/jvm/functions/Function1;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public final P4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x5:LXa0/i;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-interface {v0, v1}, LXa0/i;->a(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final Q4()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x5:LXa0/i;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-interface {v0, v1}, LXa0/i;->a(Z)V

    .line 5
    .line 6
    .line 7
    return-void
.end method

.method public final U4(Ljava/lang/String;LQb1/c;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v1, p1

    .line 4
    .line 5
    iget-object v2, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I1:Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;

    .line 6
    .line 7
    invoke-virtual/range {p2 .. p2}, LQb1/c;->e()Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    invoke-virtual {v2, v3}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;->a(Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;)J

    .line 12
    .line 13
    .line 14
    move-result-wide v2

    .line 15
    iget-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F2:LDg/a;

    .line 16
    .line 17
    invoke-static {v2, v3}, Ljava/lang/String;->valueOf(J)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    const-string v6, "popular_casino"

    .line 22
    .line 23
    invoke-virtual {v4, v5, v6}, LDg/a;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    invoke-virtual/range {p2 .. p2}, LQb1/c;->e()Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 27
    .line 28
    .line 29
    move-result-object v4

    .line 30
    sget-object v5, Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;->NONE:Lorg/xplatform/aggregator/api/model/ShowcaseAggregatorCategory;

    .line 31
    .line 32
    if-eq v4, v5, :cond_0

    .line 33
    .line 34
    long-to-int v4, v2

    .line 35
    invoke-direct {v0, v1, v4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->D4(Ljava/lang/String;I)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    invoke-virtual/range {p2 .. p2}, LQb1/c;->getId()J

    .line 40
    .line 41
    .line 42
    move-result-wide v4

    .line 43
    sget-object v6, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 44
    .line 45
    invoke-virtual {v6}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 46
    .line 47
    .line 48
    move-result-wide v6

    .line 49
    cmp-long v8, v4, v6

    .line 50
    .line 51
    if-eqz v8, :cond_1

    .line 52
    .line 53
    long-to-int v4, v2

    .line 54
    invoke-direct {v0, v1, v4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->C4(Ljava/lang/String;I)V

    .line 55
    .line 56
    .line 57
    :cond_1
    :goto_0
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 58
    .line 59
    iget-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 60
    .line 61
    new-instance v5, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;

    .line 62
    .line 63
    new-instance v6, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;

    .line 64
    .line 65
    invoke-virtual/range {p2 .. p2}, LQb1/c;->getId()J

    .line 66
    .line 67
    .line 68
    move-result-wide v8

    .line 69
    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 70
    .line 71
    .line 72
    move-result-object v2

    .line 73
    invoke-static {v2}, Lkotlin/collections/u;->e(Ljava/lang/Object;)Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object v10

    .line 77
    const/16 v14, 0x19

    .line 78
    .line 79
    const/4 v15, 0x0

    .line 80
    const/4 v7, 0x0

    .line 81
    const/4 v11, 0x0

    .line 82
    const-wide/16 v12, 0x0

    .line 83
    .line 84
    invoke-direct/range {v6 .. v15}, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;-><init>(Ljava/lang/String;JLjava/util/List;Ljava/util/List;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 85
    .line 86
    .line 87
    const/4 v2, 0x2

    .line 88
    const/4 v3, 0x0

    .line 89
    const/4 v7, 0x0

    .line 90
    invoke-direct {v5, v6, v7, v2, v3}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 91
    .line 92
    .line 93
    invoke-interface {v4, v7, v5}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 94
    .line 95
    .line 96
    move-result-object v2

    .line 97
    invoke-virtual {v1, v2}, LwX0/c;->m(Lq4/q;)V

    .line 98
    .line 99
    .line 100
    return-void
.end method

.method public V1(Ljava/lang/String;LCb1/b;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LCb1/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p2}, LPb1/c;->a(LCb1/b;)Lorg/xplatform/aggregator/api/model/Game;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p2}, LCb1/b;->o()Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-static {p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/x;->a(Lorg/xplatform/aggregator/api/model/PartitionType;)I

    .line 10
    .line 11
    .line 12
    move-result p2

    .line 13
    invoke-direct {p0, p1, v0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V4(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final Y4(LQb1/d;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-boolean v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 4
    .line 5
    if-nez v1, :cond_0

    .line 6
    .line 7
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H2:Lorg/xbet/analytics/domain/scope/g0;

    .line 8
    .line 9
    const-string v2, "popular_casino"

    .line 10
    .line 11
    invoke-virtual {v1, v2}, Lorg/xbet/analytics/domain/scope/g0;->y(Ljava/lang/String;)V

    .line 12
    .line 13
    .line 14
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S2:LSR/a;

    .line 15
    .line 16
    const-class v2, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 17
    .line 18
    invoke-virtual {v2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v2

    .line 22
    sget-object v3, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_CLASSIC_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 23
    .line 24
    invoke-interface {v1, v2, v3}, LSR/a;->l(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 25
    .line 26
    .line 27
    :cond_0
    iget-object v4, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 28
    .line 29
    sget-object v1, Lorg/xplatform/aggregator/api/model/PartitionType;->NOT_SET:Lorg/xplatform/aggregator/api/model/PartitionType;

    .line 30
    .line 31
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/PartitionType;->getId()J

    .line 32
    .line 33
    .line 34
    move-result-wide v5

    .line 35
    invoke-virtual/range {p1 .. p1}, LQb1/d;->j()I

    .line 36
    .line 37
    .line 38
    move-result v1

    .line 39
    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 40
    .line 41
    .line 42
    move-result-object v7

    .line 43
    invoke-virtual/range {p1 .. p1}, LQb1/d;->o()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v8

    .line 47
    invoke-virtual/range {p1 .. p1}, LQb1/d;->d()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v12

    .line 51
    invoke-virtual/range {p1 .. p1}, LQb1/d;->f()Ljava/util/List;

    .line 52
    .line 53
    .line 54
    move-result-object v13

    .line 55
    invoke-virtual/range {p1 .. p1}, LQb1/d;->s()Lorg/xplatform/aggregator/api/model/BrandType;

    .line 56
    .line 57
    .line 58
    move-result-object v11

    .line 59
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F3:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 60
    .line 61
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    invoke-virtual {v1}, Lek0/o;->o()Lek0/a;

    .line 66
    .line 67
    .line 68
    move-result-object v1

    .line 69
    invoke-virtual {v1}, Lek0/a;->c()Z

    .line 70
    .line 71
    .line 72
    move-result v14

    .line 73
    iget-object v1, v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F3:Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 74
    .line 75
    invoke-interface {v1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    invoke-virtual {v1}, Lek0/o;->o()Lek0/a;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-virtual {v1}, Lek0/a;->d()Z

    .line 84
    .line 85
    .line 86
    move-result v15

    .line 87
    invoke-virtual/range {p1 .. p1}, LQb1/d;->e()Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object v9

    .line 91
    const/4 v10, 0x0

    .line 92
    invoke-virtual/range {v4 .. v15}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->v(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILorg/xplatform/aggregator/api/model/BrandType;Ljava/lang/String;Ljava/util/List;ZZ)V

    .line 93
    .line 94
    .line 95
    return-void
.end method

.method public b0()V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H4:LnR/d;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    .line 4
    .line 5
    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    sget-object v2, Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;->POPULAR_CLASSIC_AGGREGATOR:Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;

    .line 10
    .line 11
    invoke-interface {v0, v1, v2}, LnR/d;->g(Ljava/lang/String;Lorg/xbet/fatmananalytics/api/domain/models/FatmanScreenType;)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S3:Lorg/xbet/analytics/domain/scope/NewsAnalytics;

    .line 15
    .line 16
    const-string v1, "popular_casino"

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Lorg/xbet/analytics/domain/scope/NewsAnalytics;->d(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->Z4()V

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final b5()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->M5:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c5(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V
    .locals 10

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    instance-of v0, v0, LKb1/a$c;

    .line 8
    .line 9
    if-nez v0, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->r4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    iget-boolean v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->v1:Z

    .line 15
    .line 16
    if-nez v0, :cond_1

    .line 17
    .line 18
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J5:Lkotlinx/coroutines/flow/V;

    .line 19
    .line 20
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    instance-of v0, v0, LKb1/a$c;

    .line 25
    .line 26
    if-nez v0, :cond_1

    .line 27
    .line 28
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V

    .line 29
    .line 30
    .line 31
    :cond_1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->G4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V

    .line 32
    .line 33
    .line 34
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->E5:Lkotlinx/coroutines/x0;

    .line 35
    .line 36
    const/4 v0, 0x0

    .line 37
    if-eqz p1, :cond_2

    .line 38
    .line 39
    const/4 v1, 0x1

    .line 40
    invoke-static {p1, v0, v1, v0}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    :cond_2
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 48
    .line 49
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 50
    .line 51
    .line 52
    move-result-object v5

    .line 53
    new-instance v3, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$startFetchData$1;

    .line 54
    .line 55
    invoke-direct {v3, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$startFetchData$1;-><init>(Ljava/lang/Object;)V

    .line 56
    .line 57
    .line 58
    new-instance v7, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$startFetchData$2;

    .line 59
    .line 60
    invoke-direct {v7, p0, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$startFetchData$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 61
    .line 62
    .line 63
    const/16 v8, 0xa

    .line 64
    .line 65
    const/4 v9, 0x0

    .line 66
    const/4 v4, 0x0

    .line 67
    const/4 v6, 0x0

    .line 68
    invoke-static/range {v2 .. v9}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->E5:Lkotlinx/coroutines/x0;

    .line 73
    .line 74
    return-void
.end method

.method public e0(JZI)V
    .locals 11

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/s;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/s;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v4, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onFavoriteClick$2;

    .line 17
    .line 18
    const/4 v10, 0x0

    .line 19
    move-object v5, p0

    .line 20
    move-wide v6, p1

    .line 21
    move v8, p3

    .line 22
    move v9, p4

    .line 23
    invoke-direct/range {v4 .. v10}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onFavoriteClick$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;JZILkotlin/coroutines/e;)V

    .line 24
    .line 25
    .line 26
    const/16 v6, 0xa

    .line 27
    .line 28
    const/4 v7, 0x0

    .line 29
    const/4 v2, 0x0

    .line 30
    move-object v5, v4

    .line 31
    const/4 v4, 0x0

    .line 32
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public h0(JLjava/lang/String;Z)V
    .locals 14
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    move/from16 v0, p4

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->x1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$a;

    .line 4
    .line 5
    invoke-virtual {v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment$a;->a()Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    move-wide v4, p1

    .line 10
    long-to-int v2, v4

    .line 11
    invoke-direct {p0, v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->C4(Ljava/lang/String;I)V

    .line 12
    .line 13
    .line 14
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x1:LwX0/c;

    .line 15
    .line 16
    iget-object v12, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->b2:Lorg/xplatform/aggregator/api/navigation/a;

    .line 17
    .line 18
    new-instance v13, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;

    .line 19
    .line 20
    new-instance v2, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;

    .line 21
    .line 22
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 23
    .line 24
    .line 25
    move-result-object v6

    .line 26
    const/16 v10, 0x18

    .line 27
    .line 28
    const/4 v11, 0x0

    .line 29
    const/4 v7, 0x0

    .line 30
    const-wide/16 v8, 0x0

    .line 31
    .line 32
    move-object/from16 v3, p3

    .line 33
    .line 34
    invoke-direct/range {v2 .. v11}, Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;-><init>(Ljava/lang/String;JLjava/util/List;Ljava/util/List;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 35
    .line 36
    .line 37
    invoke-direct {v13, v2, v0}, Lorg/xplatform/aggregator/api/navigation/AggregatorTab$Categories;-><init>(Lorg/xplatform/aggregator/api/navigation/AggregatorCategoryItemModel;Z)V

    .line 38
    .line 39
    .line 40
    invoke-interface {v12, v0, v13}, Lorg/xplatform/aggregator/api/navigation/a;->e(ZLorg/xplatform/aggregator/api/navigation/AggregatorTab;)Lq4/q;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {v1, v0}, LwX0/c;->m(Lq4/q;)V

    .line 45
    .line 46
    .line 47
    return-void
.end method

.method public i(Ljava/lang/String;LHZ0/a;)V
    .locals 8
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LHZ0/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 6
    .line 7
    invoke-interface {v1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/r;

    .line 12
    .line 13
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/r;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V

    .line 14
    .line 15
    .line 16
    new-instance v5, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p2, p1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$onOpenCategoryCard$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;LHZ0/a;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public k0(Ljava/lang/String;JI)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->H1:Lv81/g;

    .line 2
    .line 3
    invoke-interface {v0, p2, p3}, Lv81/g;->a(J)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p2

    .line 7
    invoke-static {p2}, Lkotlin/Result;->isSuccess-impl(Ljava/lang/Object;)Z

    .line 8
    .line 9
    .line 10
    move-result p3

    .line 11
    if-eqz p3, :cond_0

    .line 12
    .line 13
    check-cast p2, Lorg/xplatform/aggregator/api/model/Game;

    .line 14
    .line 15
    invoke-direct {p0, p1, p2, p4}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->V4(Ljava/lang/String;Lorg/xplatform/aggregator/api/model/Game;I)V

    .line 16
    .line 17
    .line 18
    :cond_0
    return-void
.end method

.method public l(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Ljava/lang/Object;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p2, LQb1/d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p2, LQb1/d;

    .line 6
    .line 7
    invoke-virtual {p0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->Y4(LQb1/d;)V

    .line 8
    .line 9
    .line 10
    return-void

    .line 11
    :cond_0
    instance-of v0, p2, LQb1/b;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->R4(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_1
    instance-of v0, p2, LQb1/c;

    .line 20
    .line 21
    if-eqz v0, :cond_2

    .line 22
    .line 23
    check-cast p2, LQb1/c;

    .line 24
    .line 25
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->U4(Ljava/lang/String;LQb1/c;)V

    .line 26
    .line 27
    .line 28
    return-void

    .line 29
    :cond_2
    instance-of p1, p2, LHZ0/a;

    .line 30
    .line 31
    if-eqz p1, :cond_3

    .line 32
    .line 33
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S4()V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_3
    instance-of p1, p2, LQb1/a;

    .line 38
    .line 39
    if-eqz p1, :cond_4

    .line 40
    .line 41
    invoke-direct {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S4()V

    .line 42
    .line 43
    .line 44
    :cond_4
    return-void
.end method

.method public final o4(Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 8
    .param p1    # Lorg/xplatform/aggregator/api/model/Game;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$changeBalanceToPrimary$1;

    .line 6
    .line 7
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$changeBalanceToPrimary$1;-><init>(Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 11
    .line 12
    invoke-interface {v2}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 13
    .line 14
    .line 15
    move-result-object v3

    .line 16
    new-instance v5, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$changeBalanceToPrimary$2;

    .line 17
    .line 18
    const/4 v2, 0x0

    .line 19
    invoke-direct {v5, p0, p1, p2, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$changeBalanceToPrimary$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lorg/xplatform/aggregator/api/model/Game;ILkotlin/coroutines/e;)V

    .line 20
    .line 21
    .line 22
    const/16 v6, 0xa

    .line 23
    .line 24
    const/4 v7, 0x0

    .line 25
    const/4 v4, 0x0

    .line 26
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 27
    .line 28
    .line 29
    return-void
.end method

.method public onCleared()V
    .locals 1

    .line 1
    invoke-super {p0}, Lorg/xbet/ui_common/viewmodel/core/b;->onCleared()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 5
    .line 6
    invoke-virtual {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->r()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final p0()V
    .locals 8

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$updateBalance$1;

    .line 6
    .line 7
    iget-object v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y2:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v1, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$updateBalance$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    new-instance v5, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$updateBalance$2;

    .line 13
    .line 14
    const/4 v2, 0x0

    .line 15
    invoke-direct {v5, p0, v2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$updateBalance$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 16
    .line 17
    .line 18
    const/16 v6, 0xe

    .line 19
    .line 20
    const/4 v7, 0x0

    .line 21
    const/4 v3, 0x0

    .line 22
    const/4 v4, 0x0

    .line 23
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 24
    .line 25
    .line 26
    return-void
.end method

.method public final q4()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LKb1/a;

    .line 8
    .line 9
    invoke-static {v0}, LKb1/b;->a(LKb1/a;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->K5:Lkotlinx/coroutines/flow/V;

    .line 16
    .line 17
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, LKb1/c;

    .line 22
    .line 23
    invoke-static {v0}, LKb1/b;->b(LKb1/c;)Z

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    if-eqz v0, :cond_0

    .line 28
    .line 29
    const/4 v0, 0x1

    .line 30
    return v0

    .line 31
    :cond_0
    const/4 v0, 0x0

    .line 32
    return v0
.end method

.method public final r4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->C5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 14
    .line 15
    if-ne p1, v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->q4()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->L5:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    sget-object v2, LKb1/a$d;->a:LKb1/a$d;

    .line 27
    .line 28
    invoke-interface {v0, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->INIT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 36
    .line 37
    if-ne p1, v0, :cond_2

    .line 38
    .line 39
    const/4 v4, 0x1

    .line 40
    goto :goto_0

    .line 41
    :cond_2
    const/4 v1, 0x0

    .line 42
    const/4 v4, 0x0

    .line 43
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 44
    .line 45
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 46
    .line 47
    .line 48
    move-result-object v6

    .line 49
    new-instance v10, Lorg/xplatform/aggregator/popular/classic/impl/presentation/k;

    .line 50
    .line 51
    invoke-direct {v10, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/k;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V

    .line 52
    .line 53
    .line 54
    new-instance v11, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getCategories$2;

    .line 55
    .line 56
    const/4 p1, 0x0

    .line 57
    invoke-direct {v11, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getCategories$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 58
    .line 59
    .line 60
    const/16 v12, 0x18

    .line 61
    .line 62
    const/4 v13, 0x0

    .line 63
    const-string v5, "PopularClassicAggregatorViewModel.getCategories"

    .line 64
    .line 65
    const/4 v7, 0x0

    .line 66
    const-wide/16 v8, 0x0

    .line 67
    .line 68
    invoke-static/range {v3 .. v13}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->B(Lkotlinx/coroutines/N;ZLjava/lang/String;Lkotlin/coroutines/CoroutineContext;IJLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->C5:Lkotlinx/coroutines/x0;

    .line 73
    .line 74
    return-void
.end method

.method public final v4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lq81/d;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->o()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final w4()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lq81/c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y1:Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PopularClassicAggregatorDelegate;->p()Lkotlinx/coroutines/flow/Z;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public final x4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V
    .locals 14

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->B5:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    invoke-interface {v0}, Lkotlinx/coroutines/x0;->isActive()Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-ne v0, v1, :cond_0

    .line 11
    .line 12
    return-void

    .line 13
    :cond_0
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 14
    .line 15
    if-ne p1, v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->q4()Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_1

    .line 22
    .line 23
    return-void

    .line 24
    :cond_1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->J5:Lkotlinx/coroutines/flow/V;

    .line 25
    .line 26
    sget-object v2, LKb1/a$d;->a:LKb1/a$d;

    .line 27
    .line 28
    invoke-interface {v0, v2}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 29
    .line 30
    .line 31
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    sget-object v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->INIT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 36
    .line 37
    if-ne p1, v0, :cond_2

    .line 38
    .line 39
    const/4 v4, 0x1

    .line 40
    goto :goto_0

    .line 41
    :cond_2
    const/4 v1, 0x0

    .line 42
    const/4 v4, 0x0

    .line 43
    :goto_0
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->I3:Lm8/a;

    .line 44
    .line 45
    invoke-interface {p1}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 46
    .line 47
    .line 48
    move-result-object v6

    .line 49
    new-instance v10, Lorg/xplatform/aggregator/popular/classic/impl/presentation/m;

    .line 50
    .line 51
    invoke-direct {v10, p0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/m;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V

    .line 52
    .line 53
    .line 54
    new-instance v11, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;

    .line 55
    .line 56
    const/4 p1, 0x0

    .line 57
    invoke-direct {v11, p0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    .line 58
    .line 59
    .line 60
    const/16 v12, 0x18

    .line 61
    .line 62
    const/4 v13, 0x0

    .line 63
    const-string v5, "PopularClassicAggregatorViewModel.getPromoEntities"

    .line 64
    .line 65
    const/4 v7, 0x0

    .line 66
    const-wide/16 v8, 0x0

    .line 67
    .line 68
    invoke-static/range {v3 .. v13}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->B(Lkotlinx/coroutines/N;ZLjava/lang/String;Lkotlin/coroutines/CoroutineContext;IJLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->B5:Lkotlinx/coroutines/x0;

    .line 73
    .line 74
    return-void
.end method
