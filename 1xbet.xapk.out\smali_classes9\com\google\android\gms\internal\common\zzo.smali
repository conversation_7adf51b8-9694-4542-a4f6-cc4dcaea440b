.class final Lcom/google/android/gms/internal/common/zzo;
.super Lcom/google/android/gms/internal/common/zzn;
.source "SourceFile"


# static fields
.field static final zza:Lcom/google/android/gms/internal/common/zzp;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/google/android/gms/internal/common/zzo;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/google/android/gms/internal/common/zzo;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/google/android/gms/internal/common/zzo;->zza:Lcom/google/android/gms/internal/common/zzp;

    .line 7
    .line 8
    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 1
    const-string v0, "CharMatcher.none()"

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lcom/google/android/gms/internal/common/zzn;-><init>(Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final zza(C)Z
    .locals 0

    const/4 p1, 0x0

    throw p1
.end method
