.class public final synthetic Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/C;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/C;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/C;->a:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;

    invoke-static {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;->z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_full_info/TournamentsGamesFragment;)Landroidx/lifecycle/h0;

    move-result-object v0

    return-object v0
.end method
