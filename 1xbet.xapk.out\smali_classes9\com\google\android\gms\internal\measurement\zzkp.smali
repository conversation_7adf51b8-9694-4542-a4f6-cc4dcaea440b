.class public abstract Lcom/google/android/gms/internal/measurement/zzkp;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Cloneable;
.implements Lcom/google/android/gms/internal/measurement/zznh;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final synthetic clone()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/CloneNotSupportedException;
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    throw v0
.end method

.method public final zzcA()Lcom/google/android/gms/internal/measurement/zzng;
    .locals 1

    const/4 v0, 0x0

    throw v0
.end method

.method public final zzcB(Lcom/google/android/gms/internal/measurement/zzlk;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 p1, 0x0

    throw p1
.end method

.method public final zzcb()Lcom/google/android/gms/internal/measurement/zzld;
    .locals 1

    const/4 v0, 0x0

    throw v0
.end method
