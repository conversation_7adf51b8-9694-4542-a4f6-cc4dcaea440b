.class public final enum Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u000b\u0008\u0086\u0081\u0002\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0019\u0008\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\u0008\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0008\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\n\u0010\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000f\u00a8\u0006\u0010"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;",
        "",
        "cardType",
        "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "configType",
        "",
        "<init>",
        "(Ljava/lang/String;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;Ljava/lang/String;)V",
        "getCardType",
        "()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
        "getConfigType",
        "()Ljava/lang/String;",
        "Circle",
        "SquareS",
        "SquareSNoHeader",
        "Rectangle",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $ENTRIES:Lkotlin/enums/a;

.field private static final synthetic $VALUES:[Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

.field public static final enum Circle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

.field public static final enum Rectangle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

.field public static final enum SquareS:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

.field public static final enum SquareSNoHeader:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;


# instance fields
.field private final cardType:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field private final configType:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 2
    .line 3
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 4
    .line 5
    const-string v2, "smallCircles"

    .line 6
    .line 7
    const-string v3, "Circle"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;-><init>(Ljava/lang/String;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 14
    .line 15
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 16
    .line 17
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->SquareS:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 18
    .line 19
    const-string v2, "smallSquares"

    .line 20
    .line 21
    const-string v3, "SquareS"

    .line 22
    .line 23
    const/4 v4, 0x1

    .line 24
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;-><init>(Ljava/lang/String;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->SquareS:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 28
    .line 29
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 30
    .line 31
    const/4 v2, 0x2

    .line 32
    const-string v3, "smallSquaresNoHeader"

    .line 33
    .line 34
    const-string v4, "SquareSNoHeader"

    .line 35
    .line 36
    invoke-direct {v0, v4, v2, v1, v3}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;-><init>(Ljava/lang/String;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->SquareSNoHeader:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 40
    .line 41
    new-instance v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 42
    .line 43
    sget-object v1, Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 44
    .line 45
    const-string v2, "mediumRectangles"

    .line 46
    .line 47
    const-string v3, "Rectangle"

    .line 48
    .line 49
    const/4 v4, 0x3

    .line 50
    invoke-direct {v0, v3, v4, v1, v2}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;-><init>(Ljava/lang/String;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;Ljava/lang/String;)V

    .line 51
    .line 52
    .line 53
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 54
    .line 55
    invoke-static {}, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->a()[Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 56
    .line 57
    .line 58
    move-result-object v0

    .line 59
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->$VALUES:[Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 60
    .line 61
    invoke-static {v0}, Lkotlin/enums/b;->a([Ljava/lang/Enum;)Lkotlin/enums/a;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    sput-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->$ENTRIES:Lkotlin/enums/a;

    .line 66
    .line 67
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;ILorg/xbet/uikit_web_games/game_collection/GameCollectionType;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 2
    .line 3
    .line 4
    iput-object p3, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->cardType:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 5
    .line 6
    iput-object p4, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->configType:Ljava/lang/String;

    .line 7
    .line 8
    return-void
.end method

.method public static final synthetic a()[Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;
    .locals 3

    .line 1
    const/4 v0, 0x4

    new-array v0, v0, [Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Circle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->SquareS:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->SquareSNoHeader:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->Rectangle:Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static getEntries()Lkotlin/enums/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/enums/a<",
            "Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    sget-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->$ENTRIES:Lkotlin/enums/a;

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;
    .locals 1

    .line 1
    const-class v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    .line 4
    .line 5
    .line 6
    move-result-object p0

    .line 7
    check-cast p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 8
    .line 9
    return-object p0
.end method

.method public static values()[Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;
    .locals 1

    .line 1
    sget-object v0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->$VALUES:[Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, [Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;

    .line 8
    .line 9
    return-object v0
.end method


# virtual methods
.method public final getCardType()Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->cardType:Lorg/xbet/uikit_web_games/game_collection/GameCollectionType;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getConfigType()Ljava/lang/String;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_collection_section/GameCollectionSectionType;->configType:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method
