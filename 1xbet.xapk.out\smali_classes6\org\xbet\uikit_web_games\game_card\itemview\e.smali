.class public final synthetic Lorg/xbet/uikit_web_games/game_card/itemview/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

.field public final synthetic b:I


# direct methods
.method public synthetic constructor <init>(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/e;->a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    iput p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/e;->b:I

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/e;->a:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    iget v1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/e;->b:I

    invoke-static {v0, v1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->a(Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;I)V

    return-void
.end method
