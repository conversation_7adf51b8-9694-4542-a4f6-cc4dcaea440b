.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;
.super LkY0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "LkY0/a<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0007\u0008\u0000\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00020\u0001B=\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u000c\u0010\r\u001a\u0008\u0012\u0004\u0012\u00020\u00020\u000c\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0011\u001a\u00020\u0010H\u0016\u00a2\u0006\u0004\u0008\u0013\u0010\u0014R\u0014\u0010\u0004\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0015\u0010\u0016R\u0014\u0010\u0006\u001a\u00020\u00058\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010\u0017R\u0014\u0010\u0007\u001a\u00020\u00038\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0018\u0010\u0016\u00a8\u0006\u0019"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;",
        "LkY0/a;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
        "",
        "tournamentId",
        "",
        "tournamentTitle",
        "stageTournamentID",
        "Landroidx/fragment/app/FragmentManager;",
        "childFragmentManager",
        "Landroidx/lifecycle/Lifecycle;",
        "lifecycle",
        "",
        "pages",
        "<init>",
        "(JLjava/lang/String;JLandroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V",
        "",
        "position",
        "Landroidx/fragment/app/Fragment;",
        "p",
        "(I)Landroidx/fragment/app/Fragment;",
        "o",
        "J",
        "Ljava/lang/String;",
        "q",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final o:J

.field public final p:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:J


# direct methods
.method public constructor <init>(JLjava/lang/String;JLandroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V
    .locals 0
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Landroidx/fragment/app/FragmentManager;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # Landroidx/lifecycle/Lifecycle;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/lang/String;",
            "J",
            "Landroidx/fragment/app/FragmentManager;",
            "Landroidx/lifecycle/Lifecycle;",
            "Ljava/util/List<",
            "+",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p6, p7, p8}, LkY0/a;-><init>(Landroidx/fragment/app/FragmentManager;Landroidx/lifecycle/Lifecycle;Ljava/util/List;)V

    .line 2
    .line 3
    .line 4
    iput-wide p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->o:J

    .line 5
    .line 6
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->p:Ljava/lang/String;

    .line 7
    .line 8
    iput-wide p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->q:J

    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public p(I)Landroidx/fragment/app/Fragment;
    .locals 8
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0, p1}, LkY0/a;->I(I)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 6
    .line 7
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k$a;->a:[I

    .line 8
    .line 9
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result p1

    .line 13
    aget p1, v0, p1

    .line 14
    .line 15
    const/4 v0, 0x1

    .line 16
    if-eq p1, v0, :cond_1

    .line 17
    .line 18
    const/4 v0, 0x2

    .line 19
    if-ne p1, v0, :cond_0

    .line 20
    .line 21
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->k1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;

    .line 22
    .line 23
    sget-object v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->STAGE:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 24
    .line 25
    iget-wide v3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->o:J

    .line 26
    .line 27
    iget-object v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->p:Ljava/lang/String;

    .line 28
    .line 29
    iget-wide v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->q:J

    .line 30
    .line 31
    invoke-virtual/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;->a(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;JLjava/lang/String;J)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    return-object p1

    .line 36
    :cond_0
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 37
    .line 38
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 39
    .line 40
    .line 41
    throw p1

    .line 42
    :cond_1
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;->k1:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;

    .line 43
    .line 44
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;->MAIN:Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;

    .line 45
    .line 46
    iget-wide v2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->o:J

    .line 47
    .line 48
    iget-object v4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->p:Ljava/lang/String;

    .line 49
    .line 50
    iget-wide v5, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/k;->q:J

    .line 51
    .line 52
    invoke-virtual/range {v0 .. v6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment$a;->a(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/TournamentPrizePageType;JLjava/lang/String;J)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_prizes/TournamentPrizeItemFragment;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    return-object p1
.end method
