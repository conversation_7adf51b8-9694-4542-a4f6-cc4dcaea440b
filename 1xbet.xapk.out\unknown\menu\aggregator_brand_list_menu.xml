<?xml version="1.0" encoding="utf-8"?>
<menu
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/search"
        android:icon="@drawable/ic_search_new"
        android:title="@string/search"
        app:actionViewClass="org.xbet.uikit.components.searchfield.SearchField"
        app:iconTint="?attr/uikitSecondary"
        app:showAsAction="always|collapseActionView" />

    <item
        android:id="@+id/sort"
        android:icon="@drawable/ic_sort_new"
        android:title="@string/sort_by"
        app:iconTint="?attr/uikitSecondary"
        app:showAsAction="always" />
</menu>