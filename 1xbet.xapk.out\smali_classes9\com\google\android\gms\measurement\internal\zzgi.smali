.class public final Lcom/google/android/gms/measurement/internal/zzgi;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final A:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final A0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final B:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final B0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final C:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final C0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final D:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final D0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final E:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final E0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final F:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final F0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final G:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final G0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final H:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final H0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final I:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final I0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final J:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final J0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final K:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final K0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final L:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final L0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final M:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final M0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final N:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final N0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final O:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final O0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final P:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final P0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final Q:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final Q0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final R:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final R0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final S:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final S0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final T:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final T0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final U:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final U0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final V:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final V0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final W:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final W0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final X:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final X0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final Y:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final Y0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final Z:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final Z0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final a:Ljava/util/List;

.field public static final a0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final a1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final b:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final b0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final b1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final c:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final c0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final c1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final d:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final d0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final d1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final e:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final e0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final e1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final f:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final f0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final f1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final g:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final g0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final g1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final h:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final h0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final h1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final i:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final i0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final i1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final j:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final j0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final j1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final k:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final k0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final k1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final l:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final l0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final l1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final m:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final m0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final m1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final n:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final n0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final n1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final o:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final o0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final o1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final p:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final p0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final p1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final q:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final q0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final q1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final r:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final r0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final r1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final s:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final s0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final s1:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final t:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final t0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final u:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final u0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final v:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final v0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final w:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final w0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final x:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final x0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final y:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final y0:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final z:Lcom/google/android/gms/measurement/internal/zzgg;

.field public static final z0:Lcom/google/android/gms/measurement/internal/zzgg;


# direct methods
.method static constructor <clinit>()V
    .locals 14

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-static {v0}, Lj$/util/DesugarCollections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->a:Ljava/util/List;

    .line 11
    .line 12
    new-instance v0, Ljava/util/HashSet;

    .line 13
    .line 14
    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    .line 15
    .line 16
    .line 17
    invoke-static {v0}, Lj$/util/DesugarCollections;->synchronizedSet(Ljava/util/Set;)Ljava/util/Set;

    .line 18
    .line 19
    .line 20
    const-wide/16 v0, 0x2710

    .line 21
    .line 22
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzct;

    .line 27
    .line 28
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzct;-><init>()V

    .line 29
    .line 30
    .line 31
    const-string v2, "measurement.ad_id_cache_time"

    .line 32
    .line 33
    const/4 v3, 0x0

    .line 34
    invoke-static {v2, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->b:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 39
    .line 40
    const-wide/32 v1, 0x36ee80

    .line 41
    .line 42
    .line 43
    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    new-instance v2, Lcom/google/android/gms/measurement/internal/zzdl;

    .line 48
    .line 49
    invoke-direct {v2}, Lcom/google/android/gms/measurement/internal/zzdl;-><init>()V

    .line 50
    .line 51
    .line 52
    const-string v4, "measurement.app_uninstalled_additional_ad_id_cache_time"

    .line 53
    .line 54
    invoke-static {v4, v1, v1, v2, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->c:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 59
    .line 60
    const-wide/32 v4, 0x5265c00

    .line 61
    .line 62
    .line 63
    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzdx;

    .line 68
    .line 69
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzdx;-><init>()V

    .line 70
    .line 71
    .line 72
    const-string v5, "measurement.monitoring.sample_period_millis"

    .line 73
    .line 74
    invoke-static {v5, v2, v2, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 75
    .line 76
    .line 77
    move-result-object v4

    .line 78
    sput-object v4, Lcom/google/android/gms/measurement/internal/zzgi;->d:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 79
    .line 80
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzej;

    .line 81
    .line 82
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzej;-><init>()V

    .line 83
    .line 84
    .line 85
    const-string v5, "measurement.config.cache_time"

    .line 86
    .line 87
    invoke-static {v5, v2, v1, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 88
    .line 89
    .line 90
    move-result-object v4

    .line 91
    sput-object v4, Lcom/google/android/gms/measurement/internal/zzgi;->e:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 92
    .line 93
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzev;

    .line 94
    .line 95
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzev;-><init>()V

    .line 96
    .line 97
    .line 98
    const-string v5, "measurement.config.url_scheme"

    .line 99
    .line 100
    const-string v6, "https"

    .line 101
    .line 102
    invoke-static {v5, v6, v6, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 103
    .line 104
    .line 105
    move-result-object v4

    .line 106
    sput-object v4, Lcom/google/android/gms/measurement/internal/zzgi;->f:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 107
    .line 108
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzfi;

    .line 109
    .line 110
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzfi;-><init>()V

    .line 111
    .line 112
    .line 113
    const-string v5, "measurement.config.url_authority"

    .line 114
    .line 115
    const-string v7, "app-measurement.com"

    .line 116
    .line 117
    invoke-static {v5, v7, v7, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 118
    .line 119
    .line 120
    move-result-object v4

    .line 121
    sput-object v4, Lcom/google/android/gms/measurement/internal/zzgi;->g:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 122
    .line 123
    const/16 v4, 0x64

    .line 124
    .line 125
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 126
    .line 127
    .line 128
    move-result-object v4

    .line 129
    new-instance v5, Lcom/google/android/gms/measurement/internal/zzfu;

    .line 130
    .line 131
    invoke-direct {v5}, Lcom/google/android/gms/measurement/internal/zzfu;-><init>()V

    .line 132
    .line 133
    .line 134
    const-string v7, "measurement.upload.max_bundles"

    .line 135
    .line 136
    invoke-static {v7, v4, v4, v5, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 137
    .line 138
    .line 139
    move-result-object v5

    .line 140
    sput-object v5, Lcom/google/android/gms/measurement/internal/zzgi;->h:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 141
    .line 142
    const/high16 v5, 0x10000

    .line 143
    .line 144
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 145
    .line 146
    .line 147
    move-result-object v5

    .line 148
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzbm;

    .line 149
    .line 150
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzbm;-><init>()V

    .line 151
    .line 152
    .line 153
    const-string v8, "measurement.upload.max_batch_size"

    .line 154
    .line 155
    invoke-static {v8, v5, v5, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 156
    .line 157
    .line 158
    move-result-object v7

    .line 159
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->i:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 160
    .line 161
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzby;

    .line 162
    .line 163
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzby;-><init>()V

    .line 164
    .line 165
    .line 166
    const-string v8, "measurement.upload.max_bundle_size"

    .line 167
    .line 168
    invoke-static {v8, v5, v5, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 169
    .line 170
    .line 171
    move-result-object v5

    .line 172
    sput-object v5, Lcom/google/android/gms/measurement/internal/zzgi;->j:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 173
    .line 174
    const/16 v5, 0x3e8

    .line 175
    .line 176
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 177
    .line 178
    .line 179
    move-result-object v5

    .line 180
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzck;

    .line 181
    .line 182
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzck;-><init>()V

    .line 183
    .line 184
    .line 185
    const-string v8, "measurement.upload.max_events_per_bundle"

    .line 186
    .line 187
    invoke-static {v8, v5, v5, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 188
    .line 189
    .line 190
    move-result-object v7

    .line 191
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->k:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 192
    .line 193
    const v7, 0x186a0

    .line 194
    .line 195
    .line 196
    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 197
    .line 198
    .line 199
    move-result-object v7

    .line 200
    new-instance v8, Lcom/google/android/gms/measurement/internal/zzcl;

    .line 201
    .line 202
    invoke-direct {v8}, Lcom/google/android/gms/measurement/internal/zzcl;-><init>()V

    .line 203
    .line 204
    .line 205
    const-string v9, "measurement.upload.max_events_per_day"

    .line 206
    .line 207
    invoke-static {v9, v7, v7, v8, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 208
    .line 209
    .line 210
    move-result-object v8

    .line 211
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->l:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 212
    .line 213
    new-instance v8, Lcom/google/android/gms/measurement/internal/zzcx;

    .line 214
    .line 215
    invoke-direct {v8}, Lcom/google/android/gms/measurement/internal/zzcx;-><init>()V

    .line 216
    .line 217
    .line 218
    const-string v9, "measurement.upload.max_error_events_per_day"

    .line 219
    .line 220
    invoke-static {v9, v5, v5, v8, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 221
    .line 222
    .line 223
    move-result-object v8

    .line 224
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->m:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 225
    .line 226
    const v8, 0xc350

    .line 227
    .line 228
    .line 229
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 230
    .line 231
    .line 232
    move-result-object v8

    .line 233
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdc;

    .line 234
    .line 235
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdc;-><init>()V

    .line 236
    .line 237
    .line 238
    const-string v10, "measurement.upload.max_public_events_per_day"

    .line 239
    .line 240
    invoke-static {v10, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 241
    .line 242
    .line 243
    move-result-object v8

    .line 244
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->n:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 245
    .line 246
    const/16 v8, 0x2710

    .line 247
    .line 248
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 249
    .line 250
    .line 251
    move-result-object v8

    .line 252
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdd;

    .line 253
    .line 254
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdd;-><init>()V

    .line 255
    .line 256
    .line 257
    const-string v10, "measurement.upload.max_conversions_per_day"

    .line 258
    .line 259
    invoke-static {v10, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 260
    .line 261
    .line 262
    move-result-object v8

    .line 263
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->o:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 264
    .line 265
    const/16 v8, 0xa

    .line 266
    .line 267
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 268
    .line 269
    .line 270
    move-result-object v8

    .line 271
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdf;

    .line 272
    .line 273
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdf;-><init>()V

    .line 274
    .line 275
    .line 276
    const-string v10, "measurement.upload.max_realtime_events_per_day"

    .line 277
    .line 278
    invoke-static {v10, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 279
    .line 280
    .line 281
    move-result-object v9

    .line 282
    sput-object v9, Lcom/google/android/gms/measurement/internal/zzgi;->p:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 283
    .line 284
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdg;

    .line 285
    .line 286
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdg;-><init>()V

    .line 287
    .line 288
    .line 289
    const-string v10, "measurement.store.max_stored_events_per_app"

    .line 290
    .line 291
    invoke-static {v10, v7, v7, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 292
    .line 293
    .line 294
    move-result-object v7

    .line 295
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->q:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 296
    .line 297
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzdh;

    .line 298
    .line 299
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzdh;-><init>()V

    .line 300
    .line 301
    .line 302
    const-string v9, "measurement.upload.url"

    .line 303
    .line 304
    const-string v10, "https://app-measurement.com/a"

    .line 305
    .line 306
    invoke-static {v9, v10, v10, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 307
    .line 308
    .line 309
    move-result-object v7

    .line 310
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->r:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 311
    .line 312
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzdi;

    .line 313
    .line 314
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzdi;-><init>()V

    .line 315
    .line 316
    .line 317
    const-string v9, "measurement.sgtm.google_signal.url"

    .line 318
    .line 319
    const-string v10, "https://app-measurement.com/s/d"

    .line 320
    .line 321
    invoke-static {v9, v10, v10, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 322
    .line 323
    .line 324
    move-result-object v7

    .line 325
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->s:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 326
    .line 327
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzdj;

    .line 328
    .line 329
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzdj;-><init>()V

    .line 330
    .line 331
    .line 332
    const-string v9, "measurement.sgtm.service_upload_apps_list"

    .line 333
    .line 334
    const-string v10, ""

    .line 335
    .line 336
    invoke-static {v9, v10, v10, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 337
    .line 338
    .line 339
    move-result-object v7

    .line 340
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->t:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 341
    .line 342
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzdk;

    .line 343
    .line 344
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzdk;-><init>()V

    .line 345
    .line 346
    .line 347
    const-string v9, "measurement.sgtm.upload.backoff_http_codes"

    .line 348
    .line 349
    const-string v11, "404,429,503,504"

    .line 350
    .line 351
    invoke-static {v9, v11, v11, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 352
    .line 353
    .line 354
    move-result-object v7

    .line 355
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->u:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 356
    .line 357
    const-wide/32 v11, 0x927c0

    .line 358
    .line 359
    .line 360
    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 361
    .line 362
    .line 363
    move-result-object v7

    .line 364
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdm;

    .line 365
    .line 366
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdm;-><init>()V

    .line 367
    .line 368
    .line 369
    const-string v11, "measurement.sgtm.upload.retry_interval"

    .line 370
    .line 371
    invoke-static {v11, v7, v7, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 372
    .line 373
    .line 374
    move-result-object v9

    .line 375
    sput-object v9, Lcom/google/android/gms/measurement/internal/zzgi;->v:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 376
    .line 377
    const-wide/32 v11, 0x1499700

    .line 378
    .line 379
    .line 380
    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 381
    .line 382
    .line 383
    move-result-object v9

    .line 384
    new-instance v11, Lcom/google/android/gms/measurement/internal/zzdn;

    .line 385
    .line 386
    invoke-direct {v11}, Lcom/google/android/gms/measurement/internal/zzdn;-><init>()V

    .line 387
    .line 388
    .line 389
    const-string v12, "measurement.sgtm.upload.retry_max_wait"

    .line 390
    .line 391
    invoke-static {v12, v9, v9, v11, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 392
    .line 393
    .line 394
    move-result-object v11

    .line 395
    sput-object v11, Lcom/google/android/gms/measurement/internal/zzgi;->w:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 396
    .line 397
    const-wide/32 v11, 0x1b7740

    .line 398
    .line 399
    .line 400
    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 401
    .line 402
    .line 403
    move-result-object v11

    .line 404
    new-instance v12, Lcom/google/android/gms/measurement/internal/zzdo;

    .line 405
    .line 406
    invoke-direct {v12}, Lcom/google/android/gms/measurement/internal/zzdo;-><init>()V

    .line 407
    .line 408
    .line 409
    const-string v13, "measurement.sgtm.batch.retry_interval"

    .line 410
    .line 411
    invoke-static {v13, v11, v11, v12, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 412
    .line 413
    .line 414
    move-result-object v12

    .line 415
    sput-object v12, Lcom/google/android/gms/measurement/internal/zzgi;->x:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 416
    .line 417
    new-instance v12, Lcom/google/android/gms/measurement/internal/zzdq;

    .line 418
    .line 419
    invoke-direct {v12}, Lcom/google/android/gms/measurement/internal/zzdq;-><init>()V

    .line 420
    .line 421
    .line 422
    const-string v13, "measurement.sgtm.batch.retry_max_wait"

    .line 423
    .line 424
    invoke-static {v13, v9, v9, v12, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 425
    .line 426
    .line 427
    move-result-object v9

    .line 428
    sput-object v9, Lcom/google/android/gms/measurement/internal/zzgi;->y:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 429
    .line 430
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdr;

    .line 431
    .line 432
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdr;-><init>()V

    .line 433
    .line 434
    .line 435
    const-string v12, "measurement.sgtm.batch.retry_max_count"

    .line 436
    .line 437
    invoke-static {v12, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 438
    .line 439
    .line 440
    move-result-object v8

    .line 441
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->z:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 442
    .line 443
    const/16 v8, 0x1388

    .line 444
    .line 445
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 446
    .line 447
    .line 448
    move-result-object v8

    .line 449
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzds;

    .line 450
    .line 451
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzds;-><init>()V

    .line 452
    .line 453
    .line 454
    const-string v12, "measurement.sgtm.upload.max_queued_batches"

    .line 455
    .line 456
    invoke-static {v12, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 457
    .line 458
    .line 459
    move-result-object v8

    .line 460
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->A:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 461
    .line 462
    const/4 v8, 0x5

    .line 463
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 464
    .line 465
    .line 466
    move-result-object v8

    .line 467
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdt;

    .line 468
    .line 469
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdt;-><init>()V

    .line 470
    .line 471
    .line 472
    const-string v12, "measurement.sgtm.upload.batches_retrieval_limit"

    .line 473
    .line 474
    invoke-static {v12, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 475
    .line 476
    .line 477
    move-result-object v8

    .line 478
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->B:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 479
    .line 480
    const-wide/16 v8, 0x1388

    .line 481
    .line 482
    invoke-static {v8, v9}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 483
    .line 484
    .line 485
    move-result-object v8

    .line 486
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzdu;

    .line 487
    .line 488
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzdu;-><init>()V

    .line 489
    .line 490
    .line 491
    const-string v12, "measurement.sgtm.upload.min_delay_after_startup"

    .line 492
    .line 493
    invoke-static {v12, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 494
    .line 495
    .line 496
    move-result-object v9

    .line 497
    sput-object v9, Lcom/google/android/gms/measurement/internal/zzgi;->C:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 498
    .line 499
    const-wide/16 v12, 0x3e8

    .line 500
    .line 501
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 502
    .line 503
    .line 504
    move-result-object v9

    .line 505
    new-instance v12, Lcom/google/android/gms/measurement/internal/zzdv;

    .line 506
    .line 507
    invoke-direct {v12}, Lcom/google/android/gms/measurement/internal/zzdv;-><init>()V

    .line 508
    .line 509
    .line 510
    const-string v13, "measurement.sgtm.upload.min_delay_after_broadcast"

    .line 511
    .line 512
    invoke-static {v13, v9, v9, v12, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 513
    .line 514
    .line 515
    move-result-object v12

    .line 516
    sput-object v12, Lcom/google/android/gms/measurement/internal/zzgi;->D:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 517
    .line 518
    new-instance v12, Lcom/google/android/gms/measurement/internal/zzdw;

    .line 519
    .line 520
    invoke-direct {v12}, Lcom/google/android/gms/measurement/internal/zzdw;-><init>()V

    .line 521
    .line 522
    .line 523
    const-string v13, "measurement.sgtm.upload.min_delay_after_background"

    .line 524
    .line 525
    invoke-static {v13, v7, v7, v12, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 526
    .line 527
    .line 528
    move-result-object v7

    .line 529
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->E:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 530
    .line 531
    const-wide/32 v12, 0x2932e00

    .line 532
    .line 533
    .line 534
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 535
    .line 536
    .line 537
    move-result-object v7

    .line 538
    new-instance v12, Lcom/google/android/gms/measurement/internal/zzdy;

    .line 539
    .line 540
    invoke-direct {v12}, Lcom/google/android/gms/measurement/internal/zzdy;-><init>()V

    .line 541
    .line 542
    .line 543
    const-string v13, "measurement.upload.backoff_period"

    .line 544
    .line 545
    invoke-static {v13, v7, v7, v12, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 546
    .line 547
    .line 548
    move-result-object v7

    .line 549
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->F:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 550
    .line 551
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzdz;

    .line 552
    .line 553
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzdz;-><init>()V

    .line 554
    .line 555
    .line 556
    const-string v12, "measurement.upload.window_interval"

    .line 557
    .line 558
    invoke-static {v12, v1, v1, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 559
    .line 560
    .line 561
    move-result-object v7

    .line 562
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->G:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 563
    .line 564
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzeb;

    .line 565
    .line 566
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzeb;-><init>()V

    .line 567
    .line 568
    .line 569
    const-string v12, "measurement.upload.interval"

    .line 570
    .line 571
    invoke-static {v12, v1, v1, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 572
    .line 573
    .line 574
    move-result-object v7

    .line 575
    sput-object v7, Lcom/google/android/gms/measurement/internal/zzgi;->H:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 576
    .line 577
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzec;

    .line 578
    .line 579
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzec;-><init>()V

    .line 580
    .line 581
    .line 582
    const-string v12, "measurement.upload.realtime_upload_interval"

    .line 583
    .line 584
    invoke-static {v12, v0, v0, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 585
    .line 586
    .line 587
    move-result-object v0

    .line 588
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->I:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 589
    .line 590
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzed;

    .line 591
    .line 592
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzed;-><init>()V

    .line 593
    .line 594
    .line 595
    const-string v7, "measurement.upload.debug_upload_interval"

    .line 596
    .line 597
    invoke-static {v7, v9, v9, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 598
    .line 599
    .line 600
    move-result-object v0

    .line 601
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->J:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 602
    .line 603
    const-wide/16 v12, 0x1f4

    .line 604
    .line 605
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 606
    .line 607
    .line 608
    move-result-object v0

    .line 609
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzee;

    .line 610
    .line 611
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzee;-><init>()V

    .line 612
    .line 613
    .line 614
    const-string v12, "measurement.upload.minimum_delay"

    .line 615
    .line 616
    invoke-static {v12, v0, v0, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 617
    .line 618
    .line 619
    move-result-object v0

    .line 620
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->K:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 621
    .line 622
    const-wide/32 v12, 0xea60

    .line 623
    .line 624
    .line 625
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 626
    .line 627
    .line 628
    move-result-object v0

    .line 629
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzef;

    .line 630
    .line 631
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzef;-><init>()V

    .line 632
    .line 633
    .line 634
    const-string v12, "measurement.alarm_manager.minimum_interval"

    .line 635
    .line 636
    invoke-static {v12, v0, v0, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 637
    .line 638
    .line 639
    move-result-object v0

    .line 640
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->L:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 641
    .line 642
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzeg;

    .line 643
    .line 644
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzeg;-><init>()V

    .line 645
    .line 646
    .line 647
    const-string v7, "measurement.upload.stale_data_deletion_interval"

    .line 648
    .line 649
    invoke-static {v7, v2, v2, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 650
    .line 651
    .line 652
    move-result-object v0

    .line 653
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->M:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 654
    .line 655
    const-wide/32 v12, 0x240c8400

    .line 656
    .line 657
    .line 658
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 659
    .line 660
    .line 661
    move-result-object v0

    .line 662
    new-instance v2, Lcom/google/android/gms/measurement/internal/zzeh;

    .line 663
    .line 664
    invoke-direct {v2}, Lcom/google/android/gms/measurement/internal/zzeh;-><init>()V

    .line 665
    .line 666
    .line 667
    const-string v7, "measurement.upload.refresh_blacklisted_config_interval"

    .line 668
    .line 669
    invoke-static {v7, v0, v0, v2, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 670
    .line 671
    .line 672
    move-result-object v2

    .line 673
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->N:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 674
    .line 675
    const-wide/16 v12, 0x3a98

    .line 676
    .line 677
    invoke-static {v12, v13}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 678
    .line 679
    .line 680
    move-result-object v2

    .line 681
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzei;

    .line 682
    .line 683
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzei;-><init>()V

    .line 684
    .line 685
    .line 686
    const-string v12, "measurement.upload.initial_upload_delay_time"

    .line 687
    .line 688
    invoke-static {v12, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 689
    .line 690
    .line 691
    move-result-object v2

    .line 692
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->O:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 693
    .line 694
    new-instance v2, Lcom/google/android/gms/measurement/internal/zzek;

    .line 695
    .line 696
    invoke-direct {v2}, Lcom/google/android/gms/measurement/internal/zzek;-><init>()V

    .line 697
    .line 698
    .line 699
    const-string v7, "measurement.upload.retry_time"

    .line 700
    .line 701
    invoke-static {v7, v11, v11, v2, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 702
    .line 703
    .line 704
    move-result-object v2

    .line 705
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->P:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 706
    .line 707
    const/4 v2, 0x6

    .line 708
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 709
    .line 710
    .line 711
    move-result-object v2

    .line 712
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzem;

    .line 713
    .line 714
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzem;-><init>()V

    .line 715
    .line 716
    .line 717
    const-string v11, "measurement.upload.retry_count"

    .line 718
    .line 719
    invoke-static {v11, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 720
    .line 721
    .line 722
    move-result-object v2

    .line 723
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->Q:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 724
    .line 725
    const-wide/32 v11, 0x1ee62800

    .line 726
    .line 727
    .line 728
    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 729
    .line 730
    .line 731
    move-result-object v2

    .line 732
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzen;

    .line 733
    .line 734
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzen;-><init>()V

    .line 735
    .line 736
    .line 737
    const-string v11, "measurement.upload.max_queue_time"

    .line 738
    .line 739
    invoke-static {v11, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 740
    .line 741
    .line 742
    move-result-object v2

    .line 743
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->R:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 744
    .line 745
    const-wide/32 v11, 0x493e0

    .line 746
    .line 747
    .line 748
    invoke-static {v11, v12}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 749
    .line 750
    .line 751
    move-result-object v2

    .line 752
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzeo;

    .line 753
    .line 754
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzeo;-><init>()V

    .line 755
    .line 756
    .line 757
    const-string v11, "measurement.upload.google_signal_max_queue_time"

    .line 758
    .line 759
    invoke-static {v11, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 760
    .line 761
    .line 762
    move-result-object v2

    .line 763
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->S:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 764
    .line 765
    const/4 v2, 0x4

    .line 766
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 767
    .line 768
    .line 769
    move-result-object v2

    .line 770
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzep;

    .line 771
    .line 772
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzep;-><init>()V

    .line 773
    .line 774
    .line 775
    const-string v11, "measurement.lifetimevalue.max_currency_tracked"

    .line 776
    .line 777
    invoke-static {v11, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 778
    .line 779
    .line 780
    move-result-object v2

    .line 781
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->T:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 782
    .line 783
    const/16 v2, 0xc8

    .line 784
    .line 785
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 786
    .line 787
    .line 788
    move-result-object v2

    .line 789
    new-instance v7, Lcom/google/android/gms/measurement/internal/zzeq;

    .line 790
    .line 791
    invoke-direct {v7}, Lcom/google/android/gms/measurement/internal/zzeq;-><init>()V

    .line 792
    .line 793
    .line 794
    const-string v11, "measurement.audience.filter_result_max_count"

    .line 795
    .line 796
    invoke-static {v11, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 797
    .line 798
    .line 799
    move-result-object v2

    .line 800
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->U:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 801
    .line 802
    const-string v2, "measurement.upload.max_public_user_properties"

    .line 803
    .line 804
    const/4 v7, 0x0

    .line 805
    invoke-static {v2, v4, v4, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 806
    .line 807
    .line 808
    move-result-object v2

    .line 809
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->V:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 810
    .line 811
    const/16 v2, 0x7d0

    .line 812
    .line 813
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 814
    .line 815
    .line 816
    move-result-object v2

    .line 817
    const-string v11, "measurement.upload.max_event_name_cardinality"

    .line 818
    .line 819
    invoke-static {v11, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 820
    .line 821
    .line 822
    move-result-object v2

    .line 823
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->W:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 824
    .line 825
    const-string v2, "measurement.upload.max_public_event_params"

    .line 826
    .line 827
    invoke-static {v2, v4, v4, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 828
    .line 829
    .line 830
    move-result-object v2

    .line 831
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->X:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 832
    .line 833
    new-instance v2, Lcom/google/android/gms/measurement/internal/zzer;

    .line 834
    .line 835
    invoke-direct {v2}, Lcom/google/android/gms/measurement/internal/zzer;-><init>()V

    .line 836
    .line 837
    .line 838
    const-string v11, "measurement.service_client.idle_disconnect_millis"

    .line 839
    .line 840
    invoke-static {v11, v8, v8, v2, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 841
    .line 842
    .line 843
    move-result-object v2

    .line 844
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->Y:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 845
    .line 846
    new-instance v2, Lcom/google/android/gms/measurement/internal/zzes;

    .line 847
    .line 848
    invoke-direct {v2}, Lcom/google/android/gms/measurement/internal/zzes;-><init>()V

    .line 849
    .line 850
    .line 851
    const-string v8, "measurement.service_client.reconnect_millis"

    .line 852
    .line 853
    invoke-static {v8, v9, v9, v2, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 854
    .line 855
    .line 856
    move-result-object v2

    .line 857
    sput-object v2, Lcom/google/android/gms/measurement/internal/zzgi;->Z:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 858
    .line 859
    sget-object v2, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 860
    .line 861
    new-instance v8, Lcom/google/android/gms/measurement/internal/zzet;

    .line 862
    .line 863
    invoke-direct {v8}, Lcom/google/android/gms/measurement/internal/zzet;-><init>()V

    .line 864
    .line 865
    .line 866
    const-string v9, "measurement.test.boolean_flag"

    .line 867
    .line 868
    invoke-static {v9, v2, v2, v8, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 869
    .line 870
    .line 871
    move-result-object v8

    .line 872
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->a0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 873
    .line 874
    new-instance v8, Lcom/google/android/gms/measurement/internal/zzeu;

    .line 875
    .line 876
    invoke-direct {v8}, Lcom/google/android/gms/measurement/internal/zzeu;-><init>()V

    .line 877
    .line 878
    .line 879
    const-string v9, "measurement.test.string_flag"

    .line 880
    .line 881
    const-string v11, "---"

    .line 882
    .line 883
    invoke-static {v9, v11, v11, v8, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 884
    .line 885
    .line 886
    move-result-object v8

    .line 887
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->b0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 888
    .line 889
    const-wide/16 v8, -0x1

    .line 890
    .line 891
    invoke-static {v8, v9}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 892
    .line 893
    .line 894
    move-result-object v8

    .line 895
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzex;

    .line 896
    .line 897
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzex;-><init>()V

    .line 898
    .line 899
    .line 900
    const-string v11, "measurement.test.long_flag"

    .line 901
    .line 902
    invoke-static {v11, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 903
    .line 904
    .line 905
    move-result-object v9

    .line 906
    sput-object v9, Lcom/google/android/gms/measurement/internal/zzgi;->c0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 907
    .line 908
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzey;

    .line 909
    .line 910
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzey;-><init>()V

    .line 911
    .line 912
    .line 913
    const-string v11, "measurement.test.cached_long_flag"

    .line 914
    .line 915
    const/4 v12, 0x1

    .line 916
    invoke-static {v11, v8, v8, v9, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 917
    .line 918
    .line 919
    const/4 v8, -0x2

    .line 920
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 921
    .line 922
    .line 923
    move-result-object v8

    .line 924
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzez;

    .line 925
    .line 926
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzez;-><init>()V

    .line 927
    .line 928
    .line 929
    const-string v11, "measurement.test.int_flag"

    .line 930
    .line 931
    invoke-static {v11, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 932
    .line 933
    .line 934
    move-result-object v8

    .line 935
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->d0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 936
    .line 937
    const-wide/high16 v8, -0x3ff8000000000000L    # -3.0

    .line 938
    .line 939
    invoke-static {v8, v9}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    .line 940
    .line 941
    .line 942
    move-result-object v8

    .line 943
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzfa;

    .line 944
    .line 945
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzfa;-><init>()V

    .line 946
    .line 947
    .line 948
    const-string v11, "measurement.test.double_flag"

    .line 949
    .line 950
    invoke-static {v11, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 951
    .line 952
    .line 953
    move-result-object v8

    .line 954
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->e0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 955
    .line 956
    const/16 v8, 0x32

    .line 957
    .line 958
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 959
    .line 960
    .line 961
    move-result-object v8

    .line 962
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzfb;

    .line 963
    .line 964
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzfb;-><init>()V

    .line 965
    .line 966
    .line 967
    const-string v11, "measurement.experiment.max_ids"

    .line 968
    .line 969
    invoke-static {v11, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 970
    .line 971
    .line 972
    move-result-object v8

    .line 973
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->f0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 974
    .line 975
    const/16 v8, 0x1b

    .line 976
    .line 977
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 978
    .line 979
    .line 980
    move-result-object v8

    .line 981
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzfc;

    .line 982
    .line 983
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzfc;-><init>()V

    .line 984
    .line 985
    .line 986
    const-string v11, "measurement.upload.max_item_scoped_custom_parameters"

    .line 987
    .line 988
    invoke-static {v11, v8, v8, v9, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 989
    .line 990
    .line 991
    move-result-object v8

    .line 992
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->g0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 993
    .line 994
    const/16 v8, 0x1f4

    .line 995
    .line 996
    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 997
    .line 998
    .line 999
    move-result-object v8

    .line 1000
    new-instance v9, Lcom/google/android/gms/measurement/internal/zzfd;

    .line 1001
    .line 1002
    invoke-direct {v9}, Lcom/google/android/gms/measurement/internal/zzfd;-><init>()V

    .line 1003
    .line 1004
    .line 1005
    const-string v11, "measurement.upload.max_event_parameter_value_length"

    .line 1006
    .line 1007
    invoke-static {v11, v8, v8, v9, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1008
    .line 1009
    .line 1010
    move-result-object v8

    .line 1011
    sput-object v8, Lcom/google/android/gms/measurement/internal/zzgi;->h0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1012
    .line 1013
    new-instance v8, Lcom/google/android/gms/measurement/internal/zzfe;

    .line 1014
    .line 1015
    invoke-direct {v8}, Lcom/google/android/gms/measurement/internal/zzfe;-><init>()V

    .line 1016
    .line 1017
    .line 1018
    const-string v9, "measurement.max_bundles_per_iteration"

    .line 1019
    .line 1020
    invoke-static {v9, v4, v4, v8, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1021
    .line 1022
    .line 1023
    move-result-object v4

    .line 1024
    sput-object v4, Lcom/google/android/gms/measurement/internal/zzgi;->i0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1025
    .line 1026
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzff;

    .line 1027
    .line 1028
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzff;-><init>()V

    .line 1029
    .line 1030
    .line 1031
    const-string v8, "measurement.sdk.attribution.cache.ttl"

    .line 1032
    .line 1033
    invoke-static {v8, v0, v0, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1034
    .line 1035
    .line 1036
    move-result-object v0

    .line 1037
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->j0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1038
    .line 1039
    const-wide/32 v8, 0x6ddd00

    .line 1040
    .line 1041
    .line 1042
    invoke-static {v8, v9}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 1043
    .line 1044
    .line 1045
    move-result-object v0

    .line 1046
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzfg;

    .line 1047
    .line 1048
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzfg;-><init>()V

    .line 1049
    .line 1050
    .line 1051
    const-string v8, "measurement.redaction.app_instance_id.ttl"

    .line 1052
    .line 1053
    invoke-static {v8, v0, v0, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1054
    .line 1055
    .line 1056
    move-result-object v0

    .line 1057
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->k0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1058
    .line 1059
    const/4 v0, 0x7

    .line 1060
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1061
    .line 1062
    .line 1063
    move-result-object v0

    .line 1064
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzfj;

    .line 1065
    .line 1066
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzfj;-><init>()V

    .line 1067
    .line 1068
    .line 1069
    const-string v8, "measurement.rb.attribution.client.min_ad_services_version"

    .line 1070
    .line 1071
    invoke-static {v8, v0, v0, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1072
    .line 1073
    .line 1074
    move-result-object v0

    .line 1075
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->l0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1076
    .line 1077
    invoke-static {v12}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1078
    .line 1079
    .line 1080
    move-result-object v0

    .line 1081
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzfk;

    .line 1082
    .line 1083
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzfk;-><init>()V

    .line 1084
    .line 1085
    .line 1086
    const-string v8, "measurement.dma_consent.max_daily_dcu_realtime_events"

    .line 1087
    .line 1088
    invoke-static {v8, v0, v0, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1089
    .line 1090
    .line 1091
    move-result-object v0

    .line 1092
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->m0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1093
    .line 1094
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfl;

    .line 1095
    .line 1096
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfl;-><init>()V

    .line 1097
    .line 1098
    .line 1099
    const-string v4, "measurement.rb.attribution.uri_scheme"

    .line 1100
    .line 1101
    invoke-static {v4, v6, v6, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1102
    .line 1103
    .line 1104
    move-result-object v0

    .line 1105
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->n0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1106
    .line 1107
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfm;

    .line 1108
    .line 1109
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfm;-><init>()V

    .line 1110
    .line 1111
    .line 1112
    const-string v4, "measurement.rb.attribution.uri_authority"

    .line 1113
    .line 1114
    const-string v6, "google-analytics.com"

    .line 1115
    .line 1116
    invoke-static {v4, v6, v6, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1117
    .line 1118
    .line 1119
    move-result-object v0

    .line 1120
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->o0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1121
    .line 1122
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfn;

    .line 1123
    .line 1124
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfn;-><init>()V

    .line 1125
    .line 1126
    .line 1127
    const-string v4, "measurement.rb.attribution.uri_path"

    .line 1128
    .line 1129
    const-string v6, "privacy-sandbox/register-app-conversion"

    .line 1130
    .line 1131
    invoke-static {v4, v6, v6, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1132
    .line 1133
    .line 1134
    move-result-object v0

    .line 1135
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->p0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1136
    .line 1137
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfo;

    .line 1138
    .line 1139
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfo;-><init>()V

    .line 1140
    .line 1141
    .line 1142
    const-string v4, "measurement.session.engagement_interval"

    .line 1143
    .line 1144
    invoke-static {v4, v1, v1, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1145
    .line 1146
    .line 1147
    move-result-object v0

    .line 1148
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->q0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1149
    .line 1150
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfp;

    .line 1151
    .line 1152
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfp;-><init>()V

    .line 1153
    .line 1154
    .line 1155
    const-string v1, "measurement.rb.attribution.app_allowlist"

    .line 1156
    .line 1157
    const-string v4, "*"

    .line 1158
    .line 1159
    invoke-static {v1, v4, v4, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1160
    .line 1161
    .line 1162
    move-result-object v0

    .line 1163
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->r0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1164
    .line 1165
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfq;

    .line 1166
    .line 1167
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfq;-><init>()V

    .line 1168
    .line 1169
    .line 1170
    const-string v1, "measurement.rb.attribution.user_properties"

    .line 1171
    .line 1172
    const-string v4, "_npa,npa|_fot,fot"

    .line 1173
    .line 1174
    invoke-static {v1, v4, v4, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1175
    .line 1176
    .line 1177
    move-result-object v0

    .line 1178
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->s0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1179
    .line 1180
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfr;

    .line 1181
    .line 1182
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfr;-><init>()V

    .line 1183
    .line 1184
    .line 1185
    const-string v1, "measurement.rb.attribution.event_params"

    .line 1186
    .line 1187
    const-string v4, "value|currency"

    .line 1188
    .line 1189
    invoke-static {v1, v4, v4, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1190
    .line 1191
    .line 1192
    move-result-object v0

    .line 1193
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->t0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1194
    .line 1195
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzft;

    .line 1196
    .line 1197
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzft;-><init>()V

    .line 1198
    .line 1199
    .line 1200
    const-string v1, "measurement.rb.attribution.query_parameters_to_remove"

    .line 1201
    .line 1202
    invoke-static {v1, v10, v10, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1203
    .line 1204
    .line 1205
    move-result-object v0

    .line 1206
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->u0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1207
    .line 1208
    const-wide/32 v0, 0x337f9800

    .line 1209
    .line 1210
    .line 1211
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 1212
    .line 1213
    .line 1214
    move-result-object v0

    .line 1215
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzfv;

    .line 1216
    .line 1217
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzfv;-><init>()V

    .line 1218
    .line 1219
    .line 1220
    const-string v4, "measurement.rb.attribution.max_queue_time"

    .line 1221
    .line 1222
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1223
    .line 1224
    .line 1225
    move-result-object v0

    .line 1226
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->v0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1227
    .line 1228
    const/16 v0, 0x10

    .line 1229
    .line 1230
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1231
    .line 1232
    .line 1233
    move-result-object v0

    .line 1234
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzfw;

    .line 1235
    .line 1236
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzfw;-><init>()V

    .line 1237
    .line 1238
    .line 1239
    const-string v4, "measurement.rb.attribution.max_retry_delay_seconds"

    .line 1240
    .line 1241
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1242
    .line 1243
    .line 1244
    move-result-object v0

    .line 1245
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->w0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1246
    .line 1247
    const/16 v0, 0x5a

    .line 1248
    .line 1249
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1250
    .line 1251
    .line 1252
    move-result-object v0

    .line 1253
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzfx;

    .line 1254
    .line 1255
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzfx;-><init>()V

    .line 1256
    .line 1257
    .line 1258
    const-string v4, "measurement.rb.attribution.client.min_time_after_boot_seconds"

    .line 1259
    .line 1260
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1261
    .line 1262
    .line 1263
    move-result-object v0

    .line 1264
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->x0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1265
    .line 1266
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1267
    .line 1268
    .line 1269
    move-result-object v0

    .line 1270
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzfy;

    .line 1271
    .line 1272
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzfy;-><init>()V

    .line 1273
    .line 1274
    .line 1275
    const-string v4, "measurement.rb.attribution.max_trigger_uris_queried_at_once"

    .line 1276
    .line 1277
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1278
    .line 1279
    .line 1280
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzfz;

    .line 1281
    .line 1282
    invoke-direct {v0}, Lcom/google/android/gms/measurement/internal/zzfz;-><init>()V

    .line 1283
    .line 1284
    .line 1285
    const-string v1, "measurement.rb.max_trigger_registrations_per_day"

    .line 1286
    .line 1287
    invoke-static {v1, v5, v5, v0, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1288
    .line 1289
    .line 1290
    move-result-object v0

    .line 1291
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->y0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1292
    .line 1293
    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 1294
    .line 1295
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzga;

    .line 1296
    .line 1297
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzga;-><init>()V

    .line 1298
    .line 1299
    .line 1300
    const-string v4, "measurement.config.bundle_for_all_apps_on_backgrounded"

    .line 1301
    .line 1302
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1303
    .line 1304
    .line 1305
    move-result-object v1

    .line 1306
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->z0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1307
    .line 1308
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzgb;

    .line 1309
    .line 1310
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzgb;-><init>()V

    .line 1311
    .line 1312
    .line 1313
    const-string v4, "measurement.config.notify_trigger_uris_on_backgrounded"

    .line 1314
    .line 1315
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1316
    .line 1317
    .line 1318
    move-result-object v1

    .line 1319
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->A0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1320
    .line 1321
    const/16 v1, 0xbb8

    .line 1322
    .line 1323
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1324
    .line 1325
    .line 1326
    move-result-object v1

    .line 1327
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzgc;

    .line 1328
    .line 1329
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzgc;-><init>()V

    .line 1330
    .line 1331
    .line 1332
    const-string v5, "measurement.rb.attribution.notify_app_delay_millis"

    .line 1333
    .line 1334
    invoke-static {v5, v1, v1, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1335
    .line 1336
    .line 1337
    move-result-object v1

    .line 1338
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->B0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1339
    .line 1340
    const-string v1, "measurement.quality.checksum"

    .line 1341
    .line 1342
    invoke-static {v1, v2, v2, v7, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1343
    .line 1344
    .line 1345
    move-result-object v1

    .line 1346
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->C0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1347
    .line 1348
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbk;

    .line 1349
    .line 1350
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbk;-><init>()V

    .line 1351
    .line 1352
    .line 1353
    const-string v4, "measurement.audience.use_bundle_end_timestamp_for_non_sequence_property_filters"

    .line 1354
    .line 1355
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1356
    .line 1357
    .line 1358
    move-result-object v1

    .line 1359
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->D0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1360
    .line 1361
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbl;

    .line 1362
    .line 1363
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbl;-><init>()V

    .line 1364
    .line 1365
    .line 1366
    const-string v4, "measurement.audience.refresh_event_count_filters_timestamp"

    .line 1367
    .line 1368
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1369
    .line 1370
    .line 1371
    move-result-object v1

    .line 1372
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->E0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1373
    .line 1374
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbn;

    .line 1375
    .line 1376
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbn;-><init>()V

    .line 1377
    .line 1378
    .line 1379
    const-string v4, "measurement.audience.use_bundle_timestamp_for_event_count_filters"

    .line 1380
    .line 1381
    invoke-static {v4, v2, v2, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1382
    .line 1383
    .line 1384
    move-result-object v1

    .line 1385
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->F0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1386
    .line 1387
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbo;

    .line 1388
    .line 1389
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbo;-><init>()V

    .line 1390
    .line 1391
    .line 1392
    const-string v4, "measurement.sdk.collection.last_deep_link_referrer_campaign2"

    .line 1393
    .line 1394
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1395
    .line 1396
    .line 1397
    move-result-object v1

    .line 1398
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->G0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1399
    .line 1400
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbp;

    .line 1401
    .line 1402
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbp;-><init>()V

    .line 1403
    .line 1404
    .line 1405
    const-string v4, "measurement.integration.disable_firebase_instance_id"

    .line 1406
    .line 1407
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1408
    .line 1409
    .line 1410
    move-result-object v1

    .line 1411
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->H0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1412
    .line 1413
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbq;

    .line 1414
    .line 1415
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbq;-><init>()V

    .line 1416
    .line 1417
    .line 1418
    const-string v4, "measurement.collection.service.update_with_analytics_fix"

    .line 1419
    .line 1420
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1421
    .line 1422
    .line 1423
    move-result-object v1

    .line 1424
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->I0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1425
    .line 1426
    const v1, 0x31b50

    .line 1427
    .line 1428
    .line 1429
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 1430
    .line 1431
    .line 1432
    move-result-object v1

    .line 1433
    new-instance v4, Lcom/google/android/gms/measurement/internal/zzbr;

    .line 1434
    .line 1435
    invoke-direct {v4}, Lcom/google/android/gms/measurement/internal/zzbr;-><init>()V

    .line 1436
    .line 1437
    .line 1438
    const-string v5, "measurement.service.storage_consent_support_version"

    .line 1439
    .line 1440
    invoke-static {v5, v1, v1, v4, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1441
    .line 1442
    .line 1443
    move-result-object v1

    .line 1444
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->J0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1445
    .line 1446
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbs;

    .line 1447
    .line 1448
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbs;-><init>()V

    .line 1449
    .line 1450
    .line 1451
    const-string v4, "measurement.service.store_null_safelist"

    .line 1452
    .line 1453
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1454
    .line 1455
    .line 1456
    move-result-object v1

    .line 1457
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->K0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1458
    .line 1459
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbt;

    .line 1460
    .line 1461
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbt;-><init>()V

    .line 1462
    .line 1463
    .line 1464
    const-string v4, "measurement.service.store_safelist"

    .line 1465
    .line 1466
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1467
    .line 1468
    .line 1469
    move-result-object v1

    .line 1470
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->L0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1471
    .line 1472
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbv;

    .line 1473
    .line 1474
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbv;-><init>()V

    .line 1475
    .line 1476
    .line 1477
    const-string v4, "measurement.session_stitching_token_enabled"

    .line 1478
    .line 1479
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1480
    .line 1481
    .line 1482
    move-result-object v1

    .line 1483
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->M0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1484
    .line 1485
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbw;

    .line 1486
    .line 1487
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbw;-><init>()V

    .line 1488
    .line 1489
    .line 1490
    const-string v4, "measurement.sgtm.upload_queue"

    .line 1491
    .line 1492
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1493
    .line 1494
    .line 1495
    move-result-object v1

    .line 1496
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->N0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1497
    .line 1498
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbx;

    .line 1499
    .line 1500
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbx;-><init>()V

    .line 1501
    .line 1502
    .line 1503
    const-string v4, "measurement.sgtm.google_signal.enable"

    .line 1504
    .line 1505
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1506
    .line 1507
    .line 1508
    move-result-object v1

    .line 1509
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->O0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1510
    .line 1511
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbz;

    .line 1512
    .line 1513
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbz;-><init>()V

    .line 1514
    .line 1515
    .line 1516
    const-string v4, "measurement.sgtm.upload_on_uninstall"

    .line 1517
    .line 1518
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1519
    .line 1520
    .line 1521
    move-result-object v1

    .line 1522
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->P0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1523
    .line 1524
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzca;

    .line 1525
    .line 1526
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzca;-><init>()V

    .line 1527
    .line 1528
    .line 1529
    const-string v4, "measurement.sgtm.no_proxy.service"

    .line 1530
    .line 1531
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1532
    .line 1533
    .line 1534
    move-result-object v1

    .line 1535
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->Q0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1536
    .line 1537
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcb;

    .line 1538
    .line 1539
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcb;-><init>()V

    .line 1540
    .line 1541
    .line 1542
    const-string v4, "measurement.sgtm.service.batching_on_backgrounded"

    .line 1543
    .line 1544
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1545
    .line 1546
    .line 1547
    move-result-object v1

    .line 1548
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->R0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1549
    .line 1550
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcc;

    .line 1551
    .line 1552
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcc;-><init>()V

    .line 1553
    .line 1554
    .line 1555
    const-string v4, "measurement.sgtm.no_proxy.client2"

    .line 1556
    .line 1557
    invoke-static {v4, v2, v2, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1558
    .line 1559
    .line 1560
    move-result-object v1

    .line 1561
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->S0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1562
    .line 1563
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcd;

    .line 1564
    .line 1565
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcd;-><init>()V

    .line 1566
    .line 1567
    .line 1568
    const-string v4, "measurement.sgtm.client.upload_on_backgrounded.dev"

    .line 1569
    .line 1570
    invoke-static {v4, v2, v2, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1571
    .line 1572
    .line 1573
    move-result-object v1

    .line 1574
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->T0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1575
    .line 1576
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzce;

    .line 1577
    .line 1578
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzce;-><init>()V

    .line 1579
    .line 1580
    .line 1581
    const-string v4, "measurement.sgtm.client.scion_upload_action"

    .line 1582
    .line 1583
    invoke-static {v4, v0, v0, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1584
    .line 1585
    .line 1586
    move-result-object v1

    .line 1587
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->U0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1588
    .line 1589
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcg;

    .line 1590
    .line 1591
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcg;-><init>()V

    .line 1592
    .line 1593
    .line 1594
    const-string v4, "measurement.gmscore_client_telemetry"

    .line 1595
    .line 1596
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1597
    .line 1598
    .line 1599
    move-result-object v1

    .line 1600
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->V0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1601
    .line 1602
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzch;

    .line 1603
    .line 1604
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzch;-><init>()V

    .line 1605
    .line 1606
    .line 1607
    const-string v4, "measurement.rb.attribution.service"

    .line 1608
    .line 1609
    invoke-static {v4, v0, v0, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1610
    .line 1611
    .line 1612
    move-result-object v1

    .line 1613
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->W0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1614
    .line 1615
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzci;

    .line 1616
    .line 1617
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzci;-><init>()V

    .line 1618
    .line 1619
    .line 1620
    const-string v4, "measurement.rb.attribution.client2"

    .line 1621
    .line 1622
    invoke-static {v4, v0, v0, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1623
    .line 1624
    .line 1625
    move-result-object v1

    .line 1626
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->X0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1627
    .line 1628
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcj;

    .line 1629
    .line 1630
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcj;-><init>()V

    .line 1631
    .line 1632
    .line 1633
    const-string v4, "measurement.rb.attribution.uuid_generation"

    .line 1634
    .line 1635
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1636
    .line 1637
    .line 1638
    move-result-object v1

    .line 1639
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->Y0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1640
    .line 1641
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzde;

    .line 1642
    .line 1643
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzde;-><init>()V

    .line 1644
    .line 1645
    .line 1646
    const-string v4, "measurement.rb.attribution.enable_trigger_redaction"

    .line 1647
    .line 1648
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1649
    .line 1650
    .line 1651
    move-result-object v1

    .line 1652
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->Z0:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1653
    .line 1654
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzdp;

    .line 1655
    .line 1656
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzdp;-><init>()V

    .line 1657
    .line 1658
    .line 1659
    const-string v4, "measurement.rb.attribution.followup1.service"

    .line 1660
    .line 1661
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1662
    .line 1663
    .line 1664
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzea;

    .line 1665
    .line 1666
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzea;-><init>()V

    .line 1667
    .line 1668
    .line 1669
    const-string v4, "measurement.rb.attribution.retry_disposition"

    .line 1670
    .line 1671
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1672
    .line 1673
    .line 1674
    move-result-object v1

    .line 1675
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->a1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1676
    .line 1677
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzel;

    .line 1678
    .line 1679
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzel;-><init>()V

    .line 1680
    .line 1681
    .line 1682
    const-string v4, "measurement.client.sessions.enable_fix_background_engagement"

    .line 1683
    .line 1684
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1685
    .line 1686
    .line 1687
    move-result-object v1

    .line 1688
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->b1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1689
    .line 1690
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzew;

    .line 1691
    .line 1692
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzew;-><init>()V

    .line 1693
    .line 1694
    .line 1695
    const-string v4, "measurement.fix_engagement_on_reset_analytics_data"

    .line 1696
    .line 1697
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1698
    .line 1699
    .line 1700
    move-result-object v1

    .line 1701
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->c1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1702
    .line 1703
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzfh;

    .line 1704
    .line 1705
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzfh;-><init>()V

    .line 1706
    .line 1707
    .line 1708
    const-string v4, "measurement.set_default_event_parameters_propagate_clear.service.dev"

    .line 1709
    .line 1710
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1711
    .line 1712
    .line 1713
    move-result-object v1

    .line 1714
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->d1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1715
    .line 1716
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzfs;

    .line 1717
    .line 1718
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzfs;-><init>()V

    .line 1719
    .line 1720
    .line 1721
    const-string v4, "measurement.set_default_event_parameters_propagate_clear.client.dev"

    .line 1722
    .line 1723
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1724
    .line 1725
    .line 1726
    move-result-object v1

    .line 1727
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->e1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1728
    .line 1729
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzgd;

    .line 1730
    .line 1731
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzgd;-><init>()V

    .line 1732
    .line 1733
    .line 1734
    const-string v4, "measurement.set_default_event_parameters.fix_deferred_analytics_collection"

    .line 1735
    .line 1736
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1737
    .line 1738
    .line 1739
    move-result-object v1

    .line 1740
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->f1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1741
    .line 1742
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzbu;

    .line 1743
    .line 1744
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzbu;-><init>()V

    .line 1745
    .line 1746
    .line 1747
    const-string v4, "measurement.chimera.parameter.service"

    .line 1748
    .line 1749
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1750
    .line 1751
    .line 1752
    move-result-object v1

    .line 1753
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->g1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1754
    .line 1755
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcf;

    .line 1756
    .line 1757
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcf;-><init>()V

    .line 1758
    .line 1759
    .line 1760
    const-string v4, "measurement.service.ad_impression.convert_value_to_double"

    .line 1761
    .line 1762
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1763
    .line 1764
    .line 1765
    move-result-object v1

    .line 1766
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->h1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1767
    .line 1768
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcm;

    .line 1769
    .line 1770
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcm;-><init>()V

    .line 1771
    .line 1772
    .line 1773
    const-string v4, "measurement.rb.attribution.service.enable_max_trigger_uris_queried_at_once"

    .line 1774
    .line 1775
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1776
    .line 1777
    .line 1778
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcn;

    .line 1779
    .line 1780
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcn;-><init>()V

    .line 1781
    .line 1782
    .line 1783
    const-string v4, "measurement.remove_conflicting_first_party_apis.dev"

    .line 1784
    .line 1785
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1786
    .line 1787
    .line 1788
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzco;

    .line 1789
    .line 1790
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzco;-><init>()V

    .line 1791
    .line 1792
    .line 1793
    const-string v4, "measurement.rb.attribution.service.trigger_uris_high_priority"

    .line 1794
    .line 1795
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1796
    .line 1797
    .line 1798
    move-result-object v1

    .line 1799
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->i1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1800
    .line 1801
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcp;

    .line 1802
    .line 1803
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcp;-><init>()V

    .line 1804
    .line 1805
    .line 1806
    const-string v4, "measurement.backfill_session_ids.service"

    .line 1807
    .line 1808
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1809
    .line 1810
    .line 1811
    move-result-object v1

    .line 1812
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->j1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1813
    .line 1814
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcq;

    .line 1815
    .line 1816
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcq;-><init>()V

    .line 1817
    .line 1818
    .line 1819
    const-string v4, "measurement.tcf.consent_fix"

    .line 1820
    .line 1821
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1822
    .line 1823
    .line 1824
    move-result-object v1

    .line 1825
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->k1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1826
    .line 1827
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcr;

    .line 1828
    .line 1829
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcr;-><init>()V

    .line 1830
    .line 1831
    .line 1832
    const-string v4, "measurement.experiment.enable_phenotype_experiment_reporting"

    .line 1833
    .line 1834
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1835
    .line 1836
    .line 1837
    move-result-object v1

    .line 1838
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->l1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1839
    .line 1840
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcs;

    .line 1841
    .line 1842
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcs;-><init>()V

    .line 1843
    .line 1844
    .line 1845
    const-string v4, "measurement.set_default_event_parameters.fix_service_request_ordering"

    .line 1846
    .line 1847
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1848
    .line 1849
    .line 1850
    move-result-object v1

    .line 1851
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->m1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1852
    .line 1853
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcu;

    .line 1854
    .line 1855
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcu;-><init>()V

    .line 1856
    .line 1857
    .line 1858
    const-string v4, "measurement.set_default_event_parameters.fix_app_update_logging"

    .line 1859
    .line 1860
    invoke-static {v4, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1861
    .line 1862
    .line 1863
    move-result-object v1

    .line 1864
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->n1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1865
    .line 1866
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcv;

    .line 1867
    .line 1868
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcv;-><init>()V

    .line 1869
    .line 1870
    .line 1871
    const-string v4, "measurement.fix_high_memory.prune_ees_config"

    .line 1872
    .line 1873
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1874
    .line 1875
    .line 1876
    move-result-object v1

    .line 1877
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->o1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1878
    .line 1879
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcw;

    .line 1880
    .line 1881
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcw;-><init>()V

    .line 1882
    .line 1883
    .line 1884
    const-string v4, "measurement.upload_controller.wait_initialization"

    .line 1885
    .line 1886
    invoke-static {v4, v2, v2, v1, v12}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1887
    .line 1888
    .line 1889
    move-result-object v1

    .line 1890
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->p1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1891
    .line 1892
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcy;

    .line 1893
    .line 1894
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcy;-><init>()V

    .line 1895
    .line 1896
    .line 1897
    const-string v4, "measurement.admob_plus_removal.client.dev"

    .line 1898
    .line 1899
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1900
    .line 1901
    .line 1902
    move-result-object v1

    .line 1903
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->q1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1904
    .line 1905
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzcz;

    .line 1906
    .line 1907
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzcz;-><init>()V

    .line 1908
    .line 1909
    .line 1910
    const-string v4, "measurement.admob_plus_removal.service"

    .line 1911
    .line 1912
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1913
    .line 1914
    .line 1915
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzda;

    .line 1916
    .line 1917
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzda;-><init>()V

    .line 1918
    .line 1919
    .line 1920
    const-string v4, "measurement.service.fix_stop_bundling_bug"

    .line 1921
    .line 1922
    invoke-static {v4, v2, v2, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1923
    .line 1924
    .line 1925
    move-result-object v1

    .line 1926
    sput-object v1, Lcom/google/android/gms/measurement/internal/zzgi;->r1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1927
    .line 1928
    new-instance v1, Lcom/google/android/gms/measurement/internal/zzdb;

    .line 1929
    .line 1930
    invoke-direct {v1}, Lcom/google/android/gms/measurement/internal/zzdb;-><init>()V

    .line 1931
    .line 1932
    .line 1933
    const-string v2, "measurement.fix_params_logcat_spam"

    .line 1934
    .line 1935
    invoke-static {v2, v0, v0, v1, v3}, Lcom/google/android/gms/measurement/internal/zzgi;->a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1936
    .line 1937
    .line 1938
    move-result-object v0

    .line 1939
    sput-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->s1:Lcom/google/android/gms/measurement/internal/zzgg;

    .line 1940
    .line 1941
    return-void
.end method

.method public static a(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Z)Lcom/google/android/gms/measurement/internal/zzgg;
    .locals 6
    .annotation build Lcom/google/common/annotations/VisibleForTesting;
    .end annotation

    .line 1
    new-instance v0, Lcom/google/android/gms/measurement/internal/zzgg;

    .line 2
    .line 3
    const/4 v5, 0x0

    .line 4
    move-object v1, p0

    .line 5
    move-object v2, p1

    .line 6
    move-object v3, p2

    .line 7
    move-object v4, p3

    .line 8
    invoke-direct/range {v0 .. v5}, Lcom/google/android/gms/measurement/internal/zzgg;-><init>(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzge;Lcom/google/android/gms/measurement/internal/zzgh;)V

    .line 9
    .line 10
    .line 11
    if-eqz p4, :cond_0

    .line 12
    .line 13
    sget-object p0, Lcom/google/android/gms/measurement/internal/zzgi;->a:Ljava/util/List;

    .line 14
    .line 15
    invoke-interface {p0, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 16
    .line 17
    .line 18
    :cond_0
    return-object v0
.end method

.method public static bridge synthetic b()Ljava/util/List;
    .locals 1

    .line 1
    sget-object v0, Lcom/google/android/gms/measurement/internal/zzgi;->a:Ljava/util/List;

    return-object v0
.end method
