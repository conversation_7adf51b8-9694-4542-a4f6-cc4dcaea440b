.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/fragment/app/J;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;->b:Lorg/xplatform/aggregator/api/model/Game;

    iput p3, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;->c:I

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;->b:Lorg/xplatform/aggregator/api/model/Game;

    iget v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/f;->c:I

    invoke-static {v0, v1, v2, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;->C2(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorFragment;Lorg/xplatform/aggregator/api/model/Game;ILjava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method
