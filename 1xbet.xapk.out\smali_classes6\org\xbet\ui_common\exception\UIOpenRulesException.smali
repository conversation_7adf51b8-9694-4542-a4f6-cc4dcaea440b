.class public final Lorg/xbet/ui_common/exception/UIOpenRulesException;
.super Ljava/lang/Throwable;
.source "SourceFile"

# interfaces
.implements Ld8/b;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0008\n\u0002\u0008\u0007\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\u000f\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0004\u0008\u0005\u0010\u0006R\u0017\u0010\u0004\u001a\u00020\u00038\u0006\u00a2\u0006\u000c\n\u0004\u0008\u0004\u0010\u0007\u001a\u0004\u0008\u0008\u0010\t\u00a8\u0006\n"
    }
    d2 = {
        "Lorg/xbet/ui_common/exception/UIOpenRulesException;",
        "",
        "Ld8/b;",
        "",
        "resId",
        "<init>",
        "(I)V",
        "I",
        "getResId",
        "()I",
        "ui_common_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final $stable:I = 0x8


# instance fields
.field private final resId:I


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Throwable;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lorg/xbet/ui_common/exception/UIOpenRulesException;->resId:I

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final getResId()I
    .locals 1

    .line 1
    iget v0, p0, Lorg/xbet/ui_common/exception/UIOpenRulesException;->resId:I

    .line 2
    .line 3
    return v0
.end method
