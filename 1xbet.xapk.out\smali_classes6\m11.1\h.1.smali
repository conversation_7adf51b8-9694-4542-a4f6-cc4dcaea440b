.class public final synthetic Lm11/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:Lm11/H$c;

.field public final synthetic c:Z

.field public final synthetic d:I

.field public final synthetic e:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;Lm11/H$c;ZII)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lm11/h;->a:Landroidx/compose/ui/l;

    iput-object p2, p0, Lm11/h;->b:Lm11/H$c;

    iput-boolean p3, p0, Lm11/h;->c:Z

    iput p4, p0, Lm11/h;->d:I

    iput p5, p0, Lm11/h;->e:I

    return-void
.end method


# virtual methods
.method public final invoke(Lja<PERSON>/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 7

    .line 1
    iget-object v0, p0, Lm11/h;->a:Landroidx/compose/ui/l;

    iget-object v1, p0, Lm11/h;->b:Lm11/H$c;

    iget-boolean v2, p0, Lm11/h;->c:Z

    iget v3, p0, Lm11/h;->d:I

    iget v4, p0, Lm11/h;->e:I

    move-object v5, p1

    check-cast v5, Landroidx/compose/runtime/j;

    check-cast p2, Ljava/lang/Integer;

    invoke-virtual {p2}, Ljava/lang/Integer;->intValue()I

    move-result v6

    invoke-static/range {v0 .. v6}, Lm11/G;->g(Landroidx/compose/ui/l;Lm11/H$c;ZIILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
