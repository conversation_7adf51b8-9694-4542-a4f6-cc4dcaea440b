.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:LCb1/a;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(LCb1/a;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;->a:LCb1/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;->b:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;->a:LCb1/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/D;->b:Ljava/lang/String;

    check-cast p1, LB4/a;

    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/PromoGameDelegateKt;->c(LCb1/a;Ljava/lang/String;LB4/a;)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
