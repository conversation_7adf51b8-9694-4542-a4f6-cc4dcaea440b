.class final Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xbet.toto_bet.makebet.presentation.promo.viewmodel.PromoMakeBetDsViewModel$onMakeBet$2"
    f = "PromoMakeBetDsViewModel.kt"
    l = {
        0x41,
        0x46,
        0x48
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->F3()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field J$0:J

.field label:I

.field final synthetic this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;


# direct methods
.method public constructor <init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;

    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;-><init>(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 10

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x3

    .line 8
    const/4 v3, 0x2

    .line 9
    const/4 v4, 0x1

    .line 10
    if-eqz v1, :cond_3

    .line 11
    .line 12
    if-eq v1, v4, :cond_2

    .line 13
    .line 14
    if-eq v1, v3, :cond_1

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    iget-wide v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->J$0:J

    .line 19
    .line 20
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 21
    .line 22
    .line 23
    move-wide v2, v0

    .line 24
    goto/16 :goto_3

    .line 25
    .line 26
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 27
    .line 28
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 29
    .line 30
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    throw p1

    .line 34
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 35
    .line 36
    .line 37
    goto :goto_1

    .line 38
    :cond_2
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 39
    .line 40
    .line 41
    goto :goto_0

    .line 42
    :cond_3
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 46
    .line 47
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->t3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/internet/a;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    invoke-interface {p1}, Lorg/xbet/ui_common/utils/internet/a;->b()Lkotlinx/coroutines/flow/e;

    .line 52
    .line 53
    .line 54
    move-result-object p1

    .line 55
    iput v4, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 56
    .line 57
    invoke-static {p1, p0}, Lkotlinx/coroutines/flow/g;->L(Lkotlinx/coroutines/flow/e;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    if-ne p1, v0, :cond_4

    .line 62
    .line 63
    goto :goto_2

    .line 64
    :cond_4
    :goto_0
    check-cast p1, Ljava/lang/Boolean;

    .line 65
    .line 66
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 67
    .line 68
    .line 69
    move-result p1

    .line 70
    if-nez p1, :cond_5

    .line 71
    .line 72
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 73
    .line 74
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->A3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)V

    .line 75
    .line 76
    .line 77
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 78
    .line 79
    return-object p1

    .line 80
    :cond_5
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 81
    .line 82
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->s3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    sget-object v1, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$a;->a:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$a;

    .line 87
    .line 88
    invoke-virtual {p1, v1}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 89
    .line 90
    .line 91
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 92
    .line 93
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->u3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lfk/m;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    iput v3, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 98
    .line 99
    const/4 v1, 0x0

    .line 100
    invoke-static {p1, v1, p0, v4, v1}, Lfk/m$a;->a(Lfk/m;Lorg/xbet/balance/model/BalanceRefreshType;Lkotlin/coroutines/e;ILjava/lang/Object;)Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    if-ne p1, v0, :cond_6

    .line 105
    .line 106
    goto :goto_2

    .line 107
    :cond_6
    :goto_1
    check-cast p1, Lorg/xbet/balance/model/BalanceModel;

    .line 108
    .line 109
    invoke-virtual {p1}, Lorg/xbet/balance/model/BalanceModel;->getId()J

    .line 110
    .line 111
    .line 112
    move-result-wide v3

    .line 113
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 114
    .line 115
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->w3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/l;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 120
    .line 121
    invoke-static {v1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->x3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lkotlinx/coroutines/flow/V;

    .line 122
    .line 123
    .line 124
    move-result-object v1

    .line 125
    invoke-interface {v1}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    check-cast v1, Ljava/lang/String;

    .line 130
    .line 131
    iput-wide v3, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->J$0:J

    .line 132
    .line 133
    iput v2, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->label:I

    .line 134
    .line 135
    invoke-virtual {p1, v1, v3, v4, p0}, Lorg/xbet/toto_bet/makebet/domain/usecase/l;->a(Ljava/lang/String;JLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    if-ne p1, v0, :cond_7

    .line 140
    .line 141
    :goto_2
    return-object v0

    .line 142
    :cond_7
    move-wide v2, v3

    .line 143
    :goto_3
    check-cast p1, LUU0/a;

    .line 144
    .line 145
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 146
    .line 147
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->y3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)LHX0/e;

    .line 148
    .line 149
    .line 150
    move-result-object v4

    .line 151
    invoke-virtual {p1}, LUU0/a;->c()Ljava/lang/String;

    .line 152
    .line 153
    .line 154
    move-result-object v9

    .line 155
    const-string v5, ""

    .line 156
    .line 157
    const-wide/16 v6, 0x0

    .line 158
    .line 159
    const-string v8, ""

    .line 160
    .line 161
    invoke-static/range {v4 .. v9}, Lio/a;->c(LHX0/e;Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    .line 162
    .line 163
    .line 164
    move-result-object v1

    .line 165
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 166
    .line 167
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->s3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 168
    .line 169
    .line 170
    move-result-object v6

    .line 171
    new-instance v0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;

    .line 172
    .line 173
    invoke-virtual {p1}, LUU0/a;->c()Ljava/lang/String;

    .line 174
    .line 175
    .line 176
    move-result-object v4

    .line 177
    iget-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$onMakeBet$2;->this$0:Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;

    .line 178
    .line 179
    invoke-static {p1}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;->v3(Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel;)Lorg/xbet/toto_bet/makebet/domain/usecase/e;

    .line 180
    .line 181
    .line 182
    move-result-object p1

    .line 183
    invoke-virtual {p1}, Lorg/xbet/toto_bet/makebet/domain/usecase/e;->a()LZV0/g;

    .line 184
    .line 185
    .line 186
    move-result-object p1

    .line 187
    invoke-virtual {p1}, LZV0/g;->d()Ljava/lang/String;

    .line 188
    .line 189
    .line 190
    move-result-object v5

    .line 191
    invoke-direct/range {v0 .. v5}, Lorg/xbet/toto_bet/makebet/presentation/promo/viewmodel/PromoMakeBetDsViewModel$a$e;-><init>(Ljava/lang/String;JLjava/lang/String;Ljava/lang/String;)V

    .line 192
    .line 193
    .line 194
    invoke-virtual {v6, v0}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;->j(Ljava/lang/Object;)V

    .line 195
    .line 196
    .line 197
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 198
    .line 199
    return-object p1
.end method
