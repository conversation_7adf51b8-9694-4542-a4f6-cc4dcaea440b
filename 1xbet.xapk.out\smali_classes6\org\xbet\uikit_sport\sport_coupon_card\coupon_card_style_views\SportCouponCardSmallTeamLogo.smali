.class public final Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LU31/a;
.implements LU31/g;
.implements LU31/e;
.implements LU31/d;
.implements LU31/f;
.implements LU31/i;
.implements LU31/c;
.implements LU31/h;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00e0\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\r\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0012\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0016\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u000f\n\u0002\u0010\u0007\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0007\u0018\u0000 \u00bc\u00012\u00020\u00012\u00020\u00022\u00020\u00032\u00020\u00042\u00020\u00052\u00020\u00062\u00020\u00072\u00020\u00082\u00020\t:\u00019B\'\u0008\u0007\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\n\u0008\u0002\u0010\r\u001a\u0004\u0018\u00010\u000c\u0012\u0008\u0008\u0002\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0004\u0008\u0010\u0010\u0011J\u0017\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0012\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u000eH\u0002\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u000f\u0010\u0018\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0018\u0010\u0019J\u0011\u0010\u001b\u001a\u0004\u0018\u00010\u001aH\u0016\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u001f\u0010\u001f\u001a\u00020\u00132\u0006\u0010\u001d\u001a\u00020\u000e2\u0006\u0010\u001e\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\u001f\u0010 J7\u0010\'\u001a\u00020\u00132\u0006\u0010\"\u001a\u00020!2\u0006\u0010#\u001a\u00020\u000e2\u0006\u0010$\u001a\u00020\u000e2\u0006\u0010%\u001a\u00020\u000e2\u0006\u0010&\u001a\u00020\u000eH\u0014\u00a2\u0006\u0004\u0008\'\u0010(J\u0017\u0010+\u001a\u00020\u00132\u0006\u0010*\u001a\u00020)H\u0014\u00a2\u0006\u0004\u0008+\u0010,J\u0017\u0010/\u001a\u00020\u00132\u0006\u0010.\u001a\u00020-H\u0016\u00a2\u0006\u0004\u0008/\u00100J\u0017\u00102\u001a\u00020\u00132\u0006\u00101\u001a\u00020\u000eH\u0016\u00a2\u0006\u0004\u00082\u0010\u0015J%\u00106\u001a\u00020\u00132\u0014\u00105\u001a\u0010\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\u0013\u0018\u000103H\u0016\u00a2\u0006\u0004\u00086\u00107J%\u00108\u001a\u00020\u00132\u0014\u00105\u001a\u0010\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\u0013\u0018\u000103H\u0016\u00a2\u0006\u0004\u00088\u00107J\u000f\u00109\u001a\u00020\u0013H\u0016\u00a2\u0006\u0004\u00089\u0010\u0019J\u0019\u0010<\u001a\u00020\u00132\u0008\u0010;\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008<\u0010=J\u0017\u0010?\u001a\u00020\u00132\u0006\u0010>\u001a\u00020!H\u0016\u00a2\u0006\u0004\u0008?\u0010@J\u0019\u0010B\u001a\u00020\u00132\u0008\u0010A\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008B\u0010=J\u0019\u0010D\u001a\u00020\u00132\u0008\u0010C\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008D\u0010=J!\u0010H\u001a\u00020\u00132\u0008\u0010E\u001a\u0004\u0018\u00010:2\u0006\u0010G\u001a\u00020FH\u0016\u00a2\u0006\u0004\u0008H\u0010IJ\u0019\u0010K\u001a\u00020\u00132\u0008\u0010J\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008K\u0010=J\u0019\u0010M\u001a\u00020\u00132\u0008\u0010L\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008M\u0010=J\u0017\u0010O\u001a\u00020\u00132\u0006\u0010N\u001a\u00020\u000eH\u0016\u00a2\u0006\u0004\u0008O\u0010\u0015J\u0019\u0010Q\u001a\u00020\u00132\u0008\u0010P\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008Q\u0010=J\u0019\u0010S\u001a\u00020\u00132\u0008\u0008\u0001\u0010R\u001a\u00020\u000eH\u0016\u00a2\u0006\u0004\u0008S\u0010\u0015J\u0019\u0010U\u001a\u00020\u00132\u0008\u0010T\u001a\u0004\u0018\u00010:H\u0016\u00a2\u0006\u0004\u0008U\u0010=J\u001b\u0010W\u001a\u00020\u00132\n\u0008\u0001\u0010V\u001a\u0004\u0018\u00010\u000eH\u0016\u00a2\u0006\u0004\u0008W\u0010XJ\u0017\u0010[\u001a\u00020\u00132\u0006\u0010Z\u001a\u00020YH\u0016\u00a2\u0006\u0004\u0008[\u0010\\J\u0017\u0010_\u001a\u00020\u00132\u0006\u0010^\u001a\u00020]H\u0016\u00a2\u0006\u0004\u0008_\u0010`J\u0017\u0010c\u001a\u00020\u00132\u0006\u0010b\u001a\u00020aH\u0016\u00a2\u0006\u0004\u0008c\u0010dJ\u0017\u0010<\u001a\u00020\u00132\u0008\u0008\u0001\u0010;\u001a\u00020\u000e\u00a2\u0006\u0004\u0008<\u0010\u0015J\u0017\u0010B\u001a\u00020\u00132\u0008\u0008\u0001\u0010e\u001a\u00020\u000e\u00a2\u0006\u0004\u0008B\u0010\u0015J\u0017\u0010H\u001a\u00020\u00132\u0008\u0010f\u001a\u0004\u0018\u00010:\u00a2\u0006\u0004\u0008H\u0010=J\u0015\u0010g\u001a\u00020\u00132\u0006\u0010G\u001a\u00020F\u00a2\u0006\u0004\u0008g\u0010hJ\u0017\u0010Q\u001a\u00020\u00132\u0008\u0008\u0001\u0010P\u001a\u00020\u000e\u00a2\u0006\u0004\u0008Q\u0010\u0015J\u0015\u0010S\u001a\u00020\u00132\u0006\u0010R\u001a\u00020\u001a\u00a2\u0006\u0004\u0008S\u0010iJ\u0017\u0010U\u001a\u00020\u00132\u0008\u0008\u0001\u0010T\u001a\u00020\u000e\u00a2\u0006\u0004\u0008U\u0010\u0015J+\u0010m\u001a\u00020\u00132\u0008\u0010j\u001a\u0004\u0018\u00010:2\u0008\u0010k\u001a\u0004\u0018\u00010:2\u0008\u0010l\u001a\u0004\u0018\u00010:\u00a2\u0006\u0004\u0008m\u0010nR\u001b\u0010s\u001a\u00020o8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00089\u0010p\u001a\u0004\u0008q\u0010rR\u0014\u0010u\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008?\u0010tR\u0014\u0010w\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008v\u0010tR\u0014\u0010y\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008x\u0010tR\u0014\u0010{\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008z\u0010tR\u0014\u0010}\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008|\u0010tR\u0014\u0010\u007f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008~\u0010tR\u0016\u0010\u0081\u0001\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0080\u0001\u0010tR\u0016\u0010\u0083\u0001\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0082\u0001\u0010tR\u0016\u0010\u0085\u0001\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0007\n\u0005\u0008\u0084\u0001\u0010tR\u0018\u0010\u0089\u0001\u001a\u00030\u0086\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u0087\u0001\u0010\u0088\u0001R \u0010\u008e\u0001\u001a\u00030\u008a\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008\u008b\u0001\u0010p\u001a\u0006\u0008\u008c\u0001\u0010\u008d\u0001R\u001f\u0010\u0090\u0001\u001a\u00030\u008a\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008\u0018\u0010p\u001a\u0006\u0008\u008f\u0001\u0010\u008d\u0001R \u0010\u0093\u0001\u001a\u00030\u008a\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000f\n\u0005\u0008\u0091\u0001\u0010p\u001a\u0006\u0008\u0092\u0001\u0010\u008d\u0001R\u001f\u0010\u0097\u0001\u001a\u00030\u0094\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008_\u0010p\u001a\u0006\u0008\u0095\u0001\u0010\u0096\u0001R\u001f\u0010\u0099\u0001\u001a\u00030\u0094\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008\u0016\u0010p\u001a\u0006\u0008\u0098\u0001\u0010\u0096\u0001R\u001f\u0010\u009d\u0001\u001a\u00030\u009a\u00018BX\u0082\u0084\u0002\u00a2\u0006\u000e\n\u0004\u0008\u0014\u0010p\u001a\u0006\u0008\u009b\u0001\u0010\u009c\u0001R\u0018\u0010\u00a1\u0001\u001a\u00030\u009e\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u009f\u0001\u0010\u00a0\u0001R\u0018\u0010\u00a5\u0001\u001a\u00030\u00a2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00a3\u0001\u0010\u00a4\u0001R\u0018\u0010\u00a7\u0001\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u00a6\u0001\u0010tR\u0018\u0010\u00a9\u0001\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u00a8\u0001\u0010tR\u0018\u0010\u00ab\u0001\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u00aa\u0001\u0010tR\u0018\u0010\u00ad\u0001\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u00ac\u0001\u0010tR\u0018\u0010\u00af\u0001\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u00ae\u0001\u0010tR\u0018\u0010\u00b1\u0001\u001a\u00020\u000e8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0007\n\u0005\u0008\u00b0\u0001\u0010tR\u0018\u0010\u00b5\u0001\u001a\u00030\u00b2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b3\u0001\u0010\u00b4\u0001R\u0018\u0010\u00b7\u0001\u001a\u00030\u00b2\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b6\u0001\u0010\u00b4\u0001R\u0018\u0010\u00bb\u0001\u001a\u00030\u00b8\u00018\u0002X\u0082\u0004\u00a2\u0006\u0008\n\u0006\u0008\u00b9\u0001\u0010\u00ba\u0001\u00a8\u0006\u00bd\u0001"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;",
        "Landroid/widget/FrameLayout;",
        "LU31/a;",
        "LU31/g;",
        "LU31/e;",
        "LU31/d;",
        "LU31/f;",
        "LU31/i;",
        "LU31/c;",
        "LU31/h;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "width",
        "",
        "q",
        "(I)V",
        "p",
        "()I",
        "m",
        "()V",
        "Landroid/content/res/ColorStateList;",
        "getBackgroundTintList",
        "()Landroid/content/res/ColorStateList;",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "dispatchDraw",
        "(Landroid/graphics/Canvas;)V",
        "LX31/c;",
        "couponCardUiModel",
        "setModel",
        "(LX31/c;)V",
        "res",
        "setSportImage",
        "Lkotlin/Function1;",
        "Landroid/view/View;",
        "listener",
        "setCancelButtonClickListener",
        "(Lkotlin/jvm/functions/Function1;)V",
        "setMoveButtonClickListener",
        "a",
        "",
        "caption",
        "setCaption",
        "(Ljava/lang/CharSequence;)V",
        "live",
        "b",
        "(Z)V",
        "subTitle",
        "setSubTitle",
        "marketHeader",
        "setMarketHeader",
        "coef",
        "Lorg/xbet/uikit/components/market/base/CoefficientState;",
        "coefficientState",
        "setMarketCoefficient",
        "(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "bonusTitle",
        "setCouponBonusTitle",
        "description",
        "setMarketDescription",
        "textStyle",
        "setCouponMarketDescriptionStyle",
        "tag",
        "setTagText",
        "tagColor",
        "setTagColor",
        "error",
        "setError",
        "marketStyle",
        "setMarketStyle",
        "(Ljava/lang/Integer;)V",
        "LX31/a;",
        "scoreUiModel",
        "setScoreUiModel",
        "(LX31/a;)V",
        "",
        "url",
        "o",
        "(Ljava/lang/String;)V",
        "LX31/b;",
        "teamsUiModel",
        "setTeamsUiModel",
        "(LX31/b;)V",
        "subtitle",
        "marketCoefficient",
        "setMarketCoefficientState",
        "(Lorg/xbet/uikit/components/market/base/CoefficientState;)V",
        "(Landroid/content/res/ColorStateList;)V",
        "title",
        "header",
        "coefficient",
        "setMarket",
        "(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V",
        "Lorg/xbet/uikit/utils/e;",
        "Lkotlin/j;",
        "getBackgroundTintHelper",
        "()Lorg/xbet/uikit/utils/e;",
        "backgroundTintHelper",
        "I",
        "paddingHorizontal",
        "c",
        "paddingVertical",
        "d",
        "liveTagEndMargin",
        "e",
        "shimmerHeight",
        "f",
        "topElementsMargin",
        "g",
        "sportIconEndWidthCutSize",
        "h",
        "sportIconTopHeightCutSize",
        "i",
        "sportIconSize",
        "j",
        "emptyMiddleViewsOccupiedHeight",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "k",
        "Lorg/xbet/uikit/components/views/LoadableImageView;",
        "sportImageView",
        "LW31/h;",
        "l",
        "getSubTitleDelegate",
        "()LW31/h;",
        "subTitleDelegate",
        "getCaptionDelegate",
        "captionDelegate",
        "n",
        "getErrorDelegate",
        "errorDelegate",
        "LW31/f;",
        "getWarningTagDelegate",
        "()LW31/f;",
        "warningTagDelegate",
        "getLiveTagDelegate",
        "liveTagDelegate",
        "LW31/e;",
        "getShimmerDelegate",
        "()LW31/e;",
        "shimmerDelegate",
        "LW31/j;",
        "r",
        "LW31/j;",
        "buttonsDelegate",
        "LW31/k;",
        "s",
        "LW31/k;",
        "middleViewsDelegate",
        "t",
        "subTitleOccupiedHeight",
        "u",
        "captionOccupiedHeight",
        "v",
        "warningTagOccupiedHeight",
        "w",
        "middleViewsOccupiedHeight",
        "x",
        "errorOccupiedHeight",
        "y",
        "marketHeight",
        "",
        "z",
        "F",
        "shrinkCoefTextSize",
        "A",
        "usualCoefTextSize",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "B",
        "Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;",
        "marketView",
        "C",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final C:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final D:I


# instance fields
.field public final A:F

.field public final B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final a:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:Lorg/xbet/uikit/components/views/LoadableImageView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final l:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final o:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final p:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final q:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final r:LW31/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final s:LW31/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public t:I

.field public u:I

.field public v:I

.field public w:I

.field public x:I

.field public y:I

.field public final z:F


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->C:Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->D:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 10
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct/range {p0 .. p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    new-instance v0, LV31/y;

    invoke-direct {v0, p0}, LV31/y;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->a:Lkotlin/j;

    .line 7
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->d:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_120:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v0

    float-to-int v0, v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->e:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->space_4:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 12
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_12:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->g:I

    .line 13
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_26:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->h:I

    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v2, LlZ0/g;->size_110:I

    invoke-virtual {v0, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v0

    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->i:I

    .line 15
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    sget v3, LlZ0/g;->space_12:I

    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result v2

    iput v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->j:I

    .line 16
    new-instance v2, Lorg/xbet/uikit/components/views/LoadableImageView;

    const/4 v6, 0x6

    const/4 v7, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    move-object v3, p1

    invoke-direct/range {v2 .. v7}, Lorg/xbet/uikit/components/views/LoadableImageView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 17
    new-instance v3, Landroid/view/ViewGroup$LayoutParams;

    invoke-direct {v3, v0, v0}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v3}, Landroid/view/View;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 18
    sget v0, LlZ0/d;->uikitBackgroundGroupSecondary:I

    const/4 v3, 0x0

    const/4 v4, 0x2

    invoke-static {p1, v0, v3, v4, v3}, Lorg/xbet/uikit/utils/i;->d(Landroid/content/Context;ILandroid/content/res/Resources$Theme;ILjava/lang/Object;)I

    move-result v0

    invoke-virtual {v2, v0}, Landroid/widget/ImageView;->setColorFilter(I)V

    .line 19
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 20
    iput-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->k:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 21
    new-instance v0, LV31/z;

    invoke-direct {v0, p0}, LV31/z;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->l:Lkotlin/j;

    .line 22
    new-instance v0, LV31/A;

    invoke-direct {v0, p0}, LV31/A;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->m:Lkotlin/j;

    .line 23
    new-instance v0, LV31/B;

    invoke-direct {v0, p0}, LV31/B;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->n:Lkotlin/j;

    .line 24
    new-instance v0, LV31/C;

    invoke-direct {v0, p0}, LV31/C;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->o:Lkotlin/j;

    .line 25
    new-instance v0, LV31/D;

    invoke-direct {v0, p0}, LV31/D;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->p:Lkotlin/j;

    .line 26
    new-instance v0, LV31/E;

    invoke-direct {v0, p0}, LV31/E;-><init>(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)V

    invoke-static {v0}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object v0

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->q:Lkotlin/j;

    .line 27
    new-instance v0, LW31/j;

    .line 28
    sget v2, LlZ0/h;->ic_glyph_move_vertical_large:I

    .line 29
    sget v3, LlZ0/h;->ic_glyph_cancel_small:I

    .line 30
    sget v4, LlZ0/g;->size_40:I

    move v5, v4

    move-object v1, p0

    .line 31
    invoke-direct/range {v0 .. v5}, LW31/j;-><init>(Landroid/view/ViewGroup;IIII)V

    .line 32
    invoke-virtual {v0}, LW31/c;->e()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 33
    invoke-virtual {v0}, LW31/c;->c()Landroid/widget/ImageView;

    move-result-object v1

    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 34
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 35
    new-instance v0, LW31/k;

    invoke-direct {v0, p0}, LW31/k;-><init>(Landroid/view/ViewGroup;)V

    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 36
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->text_12:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v8

    iput v8, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->z:F

    .line 37
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    sget v1, LlZ0/g;->text_16:I

    invoke-virtual {v0, v1}, Landroid/content/res/Resources;->getDimension(I)F

    move-result v9

    iput v9, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->A:F

    .line 38
    new-instance v0, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 39
    sget-object v2, Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;->END:Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketBlockedIconPosition(Lorg/xbet/uikit_sport/sport_coupon_card/models/MarketBlockedIconPosition;)V

    .line 40
    sget v2, LlZ0/n;->TextStyle_Caption_Medium_M_Secondary:I

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionTextStyle(I)V

    .line 41
    sget v2, LlZ0/n;->TextStyle_Caption_Bold_L_TextPrimary:I

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setHeaderTextStyle(I)V

    .line 42
    sget v2, LlZ0/n;->TextStyle_Headline_Bold_TextPrimary:I

    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientTextStyle(I)V

    const/4 v2, 0x1

    .line 43
    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionMaxLines(I)V

    const v2, 0x7fffffff

    .line 44
    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setHeaderMaxLines(I)V

    .line 45
    invoke-virtual {v0, v9}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMaxTextSize(F)V

    .line 46
    invoke-virtual {v0, v8}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setCoefficientMinTextSize(F)V

    .line 47
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 48
    sget-object v0, Lm31/g;->SportCouponCardView:[I

    const/4 v2, 0x0

    .line 49
    invoke-virtual {p1, p2, v0, p3, v2}, Landroid/content/Context;->obtainStyledAttributes(Landroid/util/AttributeSet;[III)Landroid/content/res/TypedArray;

    move-result-object v0

    .line 50
    sget v3, Lm31/g;->SportCouponCardView_caption:I

    invoke-virtual {v0, v3}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setCaption(Ljava/lang/CharSequence;)V

    .line 51
    sget v3, Lm31/g;->SportCouponCardView_subtitle:I

    invoke-virtual {v0, v3}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 52
    sget v3, Lm31/g;->SportCouponCardView_tag:I

    invoke-virtual {v0, v3}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p0, v3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setTagText(Ljava/lang/CharSequence;)V

    .line 53
    sget v3, Lm31/g;->SportCouponCardView_tagColor:I

    invoke-static {v0, p1, v3}, Lorg/xbet/uikit/utils/I;->c(Landroid/content/res/TypedArray;Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setTagColor(Landroid/content/res/ColorStateList;)V

    .line 54
    :cond_0
    sget v1, Lm31/g;->SportCouponCardView_error:I

    invoke-virtual {v0, v1}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setError(Ljava/lang/CharSequence;)V

    .line 55
    sget v1, Lm31/g;->SportCouponCardView_couponMarketStyle:I

    invoke-virtual {v0, v1, v2}, Landroid/content/res/TypedArray;->getResourceId(II)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketStyle(Ljava/lang/Integer;)V

    .line 56
    sget v1, Lm31/g;->SportCouponCardView_marketTitle:I

    invoke-virtual {v0, v1}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v1

    .line 57
    sget v3, Lm31/g;->SportCouponCardView_marketHeader:I

    invoke-virtual {v0, v3}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v3

    .line 58
    sget v4, Lm31/g;->SportCouponCardView_marketCoefficient:I

    invoke-virtual {v0, v4}, Landroid/content/res/TypedArray;->getString(I)Ljava/lang/String;

    move-result-object v4

    .line 59
    invoke-virtual {p0, v1, v3, v4}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 60
    sget v1, Lm31/g;->SportCouponCardView_showSkeleton:I

    invoke-virtual {v0, v1, v2}, Landroid/content/res/TypedArray;->getBoolean(IZ)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 61
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->a()V

    .line 62
    :cond_1
    invoke-virtual {v0}, Landroid/content/res/TypedArray;->recycle()V

    .line 63
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    move-result-object v0

    invoke-virtual {v0, p2, p3}, Lorg/xbet/uikit/utils/e;->a(Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->sportCouponCardStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic c(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->n(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->k(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/f;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->l(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method private final getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->a:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xbet/uikit/utils/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getCaptionDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->m:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getErrorDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->n:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getLiveTagDelegate()LW31/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->p:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/f;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getShimmerDelegate()LW31/e;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->q:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/e;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getSubTitleDelegate()LW31/h;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->l:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/h;

    .line 8
    .line 9
    return-object v0
.end method

.method private final getWarningTagDelegate()LW31/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->o:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, LW31/f;

    .line 8
    .line 9
    return-object v0
.end method

.method public static synthetic h(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)Lorg/xbet/uikit/utils/e;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->j(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)Lorg/xbet/uikit/utils/e;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;

    move-result-object p0

    return-object p0
.end method

.method public static final j(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)Lorg/xbet/uikit/utils/e;
    .locals 1

    .line 1
    new-instance v0, Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lorg/xbet/uikit/utils/e;-><init>(Landroid/view/View;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static final k(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Secondary:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const/4 v2, 0x2

    .line 8
    const/4 v3, 0x0

    .line 9
    move-object v1, p0

    .line 10
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public static final l(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;
    .locals 7

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    sget v4, LlZ0/n;->TextStyle_Caption_Regular_L_Warning:I

    .line 4
    .line 5
    const/4 v5, 0x4

    .line 6
    const/4 v6, 0x0

    .line 7
    const v2, 0x7fffffff

    .line 8
    .line 9
    .line 10
    const/4 v3, 0x0

    .line 11
    move-object v1, p0

    .line 12
    invoke-direct/range {v0 .. v6}, LW31/h;-><init>(Landroid/view/View;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final n(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/f;
    .locals 2

    .line 1
    new-instance v0, LW31/f;

    .line 2
    .line 3
    sget v1, LlZ0/n;->Widget_Tag_RectangularS_RedTransparent:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/f;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    const-string v1, "Live"

    .line 9
    .line 10
    invoke-virtual {v0, v1}, LW31/f;->g(Ljava/lang/CharSequence;)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 21
    .line 22
    .line 23
    move-result-object p0

    .line 24
    const/16 v1, 0x8

    .line 25
    .line 26
    invoke-virtual {p0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 27
    .line 28
    .line 29
    return-object v0
.end method

.method private final p()I
    .locals 5

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getSubTitleDelegate()LW31/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/h;->c()I

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 10
    .line 11
    add-int/2addr v0, v1

    .line 12
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getSubTitleDelegate()LW31/h;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v1}, LW31/h;->e()Z

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    const/4 v2, 0x0

    .line 25
    if-nez v1, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    move-object v0, v2

    .line 29
    :goto_0
    const/4 v1, 0x0

    .line 30
    if-eqz v0, :cond_1

    .line 31
    .line 32
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 33
    .line 34
    .line 35
    move-result v0

    .line 36
    goto :goto_1

    .line 37
    :cond_1
    const/4 v0, 0x0

    .line 38
    :goto_1
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t:I

    .line 39
    .line 40
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getCaptionDelegate()LW31/h;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    invoke-virtual {v0}, LW31/h;->c()I

    .line 45
    .line 46
    .line 47
    move-result v0

    .line 48
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 49
    .line 50
    add-int/2addr v0, v3

    .line 51
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getCaptionDelegate()LW31/h;

    .line 56
    .line 57
    .line 58
    move-result-object v3

    .line 59
    invoke-virtual {v3}, LW31/h;->e()Z

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-nez v3, :cond_2

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_2
    move-object v0, v2

    .line 67
    :goto_2
    if-eqz v0, :cond_3

    .line 68
    .line 69
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 70
    .line 71
    .line 72
    move-result v0

    .line 73
    goto :goto_3

    .line 74
    :cond_3
    const/4 v0, 0x0

    .line 75
    :goto_3
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->u:I

    .line 76
    .line 77
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 82
    .line 83
    .line 84
    move-result-object v0

    .line 85
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 86
    .line 87
    .line 88
    move-result v0

    .line 89
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 90
    .line 91
    add-int/2addr v0, v3

    .line 92
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 93
    .line 94
    .line 95
    move-result-object v0

    .line 96
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    .line 97
    .line 98
    .line 99
    move-result-object v3

    .line 100
    invoke-virtual {v3}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 101
    .line 102
    .line 103
    move-result-object v3

    .line 104
    invoke-virtual {v3}, Landroid/view/View;->getVisibility()I

    .line 105
    .line 106
    .line 107
    move-result v3

    .line 108
    if-nez v3, :cond_4

    .line 109
    .line 110
    goto :goto_4

    .line 111
    :cond_4
    move-object v0, v2

    .line 112
    :goto_4
    if-eqz v0, :cond_5

    .line 113
    .line 114
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 115
    .line 116
    .line 117
    move-result v0

    .line 118
    goto :goto_5

    .line 119
    :cond_5
    const/4 v0, 0x0

    .line 120
    :goto_5
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->v:I

    .line 121
    .line 122
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 123
    .line 124
    invoke-virtual {v0}, LW31/k;->a()I

    .line 125
    .line 126
    .line 127
    move-result v0

    .line 128
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 129
    .line 130
    add-int/2addr v0, v3

    .line 131
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 132
    .line 133
    .line 134
    move-result-object v0

    .line 135
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 136
    .line 137
    invoke-virtual {v3}, LW31/k;->a()I

    .line 138
    .line 139
    .line 140
    move-result v3

    .line 141
    if-lez v3, :cond_6

    .line 142
    .line 143
    goto :goto_6

    .line 144
    :cond_6
    move-object v0, v2

    .line 145
    :goto_6
    if-eqz v0, :cond_7

    .line 146
    .line 147
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 148
    .line 149
    .line 150
    move-result v0

    .line 151
    goto :goto_7

    .line 152
    :cond_7
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->j:I

    .line 153
    .line 154
    :goto_7
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->w:I

    .line 155
    .line 156
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getErrorDelegate()LW31/h;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    invoke-virtual {v0}, LW31/h;->c()I

    .line 161
    .line 162
    .line 163
    move-result v0

    .line 164
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 165
    .line 166
    add-int/2addr v0, v3

    .line 167
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 168
    .line 169
    .line 170
    move-result-object v0

    .line 171
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getErrorDelegate()LW31/h;

    .line 172
    .line 173
    .line 174
    move-result-object v3

    .line 175
    invoke-virtual {v3}, LW31/h;->e()Z

    .line 176
    .line 177
    .line 178
    move-result v3

    .line 179
    if-nez v3, :cond_8

    .line 180
    .line 181
    goto :goto_8

    .line 182
    :cond_8
    move-object v0, v2

    .line 183
    :goto_8
    if-eqz v0, :cond_9

    .line 184
    .line 185
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 186
    .line 187
    .line 188
    move-result v0

    .line 189
    goto :goto_9

    .line 190
    :cond_9
    const/4 v0, 0x0

    .line 191
    :goto_9
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->x:I

    .line 192
    .line 193
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 194
    .line 195
    invoke-virtual {v0}, Landroid/view/View;->getMeasuredHeight()I

    .line 196
    .line 197
    .line 198
    move-result v0

    .line 199
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->y:I

    .line 200
    .line 201
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 202
    .line 203
    mul-int/lit8 v3, v3, 0x2

    .line 204
    .line 205
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t:I

    .line 206
    .line 207
    add-int/2addr v3, v4

    .line 208
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->u:I

    .line 209
    .line 210
    add-int/2addr v3, v4

    .line 211
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->v:I

    .line 212
    .line 213
    add-int/2addr v3, v4

    .line 214
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->w:I

    .line 215
    .line 216
    add-int/2addr v3, v4

    .line 217
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->x:I

    .line 218
    .line 219
    add-int/2addr v3, v4

    .line 220
    add-int/2addr v3, v0

    .line 221
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 222
    .line 223
    .line 224
    move-result-object v0

    .line 225
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getShimmerDelegate()LW31/e;

    .line 226
    .line 227
    .line 228
    move-result-object v3

    .line 229
    invoke-virtual {v3}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 230
    .line 231
    .line 232
    move-result-object v3

    .line 233
    invoke-virtual {v3}, Landroid/view/View;->getVisibility()I

    .line 234
    .line 235
    .line 236
    move-result v3

    .line 237
    if-nez v3, :cond_a

    .line 238
    .line 239
    const/4 v1, 0x1

    .line 240
    :cond_a
    if-nez v1, :cond_b

    .line 241
    .line 242
    move-object v2, v0

    .line 243
    :cond_b
    if-eqz v2, :cond_c

    .line 244
    .line 245
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 246
    .line 247
    .line 248
    move-result v0

    .line 249
    goto :goto_a

    .line 250
    :cond_c
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->e:I

    .line 251
    .line 252
    :goto_a
    const/high16 v1, 0x40000000    # 2.0f

    .line 253
    .line 254
    invoke-static {v0, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 255
    .line 256
    .line 257
    move-result v0

    .line 258
    return v0
.end method

.method private final q(I)V
    .locals 4

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 2
    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    add-int/2addr v0, v1

    .line 16
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->d:I

    .line 17
    .line 18
    add-int/2addr v0, v1

    .line 19
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 28
    .line 29
    .line 30
    move-result-object v1

    .line 31
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    const/4 v2, 0x0

    .line 36
    if-nez v1, :cond_0

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    move-object v0, v2

    .line 40
    :goto_0
    if-eqz v0, :cond_1

    .line 41
    .line 42
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    goto :goto_1

    .line 47
    :cond_1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 48
    .line 49
    :goto_1
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 50
    .line 51
    invoke-virtual {v1}, LW31/j;->l()I

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    invoke-virtual {v1}, Ljava/lang/Number;->intValue()I

    .line 60
    .line 61
    .line 62
    move-result v3

    .line 63
    if-lez v3, :cond_2

    .line 64
    .line 65
    move-object v2, v1

    .line 66
    :cond_2
    if-eqz v2, :cond_3

    .line 67
    .line 68
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 69
    .line 70
    .line 71
    move-result v1

    .line 72
    goto :goto_2

    .line 73
    :cond_3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 74
    .line 75
    :goto_2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getSubTitleDelegate()LW31/h;

    .line 76
    .line 77
    .line 78
    move-result-object v2

    .line 79
    sub-int/2addr p1, v0

    .line 80
    sub-int/2addr p1, v1

    .line 81
    invoke-virtual {v2, p1}, LW31/h;->f(I)V

    .line 82
    .line 83
    .line 84
    return-void
.end method

.method public static final r(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/e;
    .locals 2

    .line 1
    new-instance v0, LW31/e;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->e:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/e;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method public static final s(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/h;
    .locals 4

    .line 1
    new-instance v0, LW31/h;

    .line 2
    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    sget v2, LlZ0/g;->space_1:I

    .line 8
    .line 9
    invoke-virtual {v1, v2}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    sget v2, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 14
    .line 15
    const/4 v3, 0x2

    .line 16
    invoke-direct {v0, p0, v3, v1, v2}, LW31/h;-><init>(Landroid/view/View;III)V

    .line 17
    .line 18
    .line 19
    return-object v0
.end method

.method public static final t(Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;)LW31/f;
    .locals 2

    .line 1
    new-instance v0, LW31/f;

    .line 2
    .line 3
    sget v1, LlZ0/n;->Widget_Tag_RectangularS_Red:I

    .line 4
    .line 5
    invoke-direct {v0, p0, v1}, LW31/f;-><init>(Landroid/view/ViewGroup;I)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {p0, v1}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method


# virtual methods
.method public a()V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/e;->d()V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 9
    .line 10
    .line 11
    return-void
.end method

.method public b(Z)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    if-eqz p1, :cond_0

    .line 10
    .line 11
    const/4 p1, 0x0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/16 p1, 0x8

    .line 14
    .line 15
    :goto_0
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 19
    .line 20
    .line 21
    return-void
.end method

.method public dispatchDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-super {p0, p1}, Landroid/widget/FrameLayout;->dispatchDraw(Landroid/graphics/Canvas;)V

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 5
    .line 6
    int-to-float v0, v0

    .line 7
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->d:I

    .line 20
    .line 21
    add-int/2addr v1, v2

    .line 22
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 23
    .line 24
    add-int/2addr v1, v2

    .line 25
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-virtual {v2}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-virtual {v2}, Landroid/view/View;->getVisibility()I

    .line 38
    .line 39
    .line 40
    move-result v2

    .line 41
    if-nez v2, :cond_0

    .line 42
    .line 43
    goto :goto_0

    .line 44
    :cond_0
    const/4 v1, 0x0

    .line 45
    :goto_0
    if-eqz v1, :cond_1

    .line 46
    .line 47
    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    .line 48
    .line 49
    .line 50
    move-result v1

    .line 51
    goto :goto_1

    .line 52
    :cond_1
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 53
    .line 54
    :goto_1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getSubTitleDelegate()LW31/h;

    .line 55
    .line 56
    .line 57
    move-result-object v2

    .line 58
    int-to-float v1, v1

    .line 59
    invoke-virtual {v2, p1, v1, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 60
    .line 61
    .line 62
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t:I

    .line 63
    .line 64
    int-to-float v1, v1

    .line 65
    add-float/2addr v0, v1

    .line 66
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getCaptionDelegate()LW31/h;

    .line 67
    .line 68
    .line 69
    move-result-object v1

    .line 70
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 71
    .line 72
    int-to-float v2, v2

    .line 73
    invoke-virtual {v1, p1, v2, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 74
    .line 75
    .line 76
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->u:I

    .line 77
    .line 78
    int-to-float v1, v1

    .line 79
    add-float/2addr v0, v1

    .line 80
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->v:I

    .line 81
    .line 82
    int-to-float v1, v1

    .line 83
    add-float/2addr v0, v1

    .line 84
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->w:I

    .line 85
    .line 86
    int-to-float v1, v1

    .line 87
    add-float/2addr v0, v1

    .line 88
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getErrorDelegate()LW31/h;

    .line 89
    .line 90
    .line 91
    move-result-object v1

    .line 92
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 93
    .line 94
    int-to-float v2, v2

    .line 95
    invoke-virtual {v1, p1, v2, v0}, LW31/h;->b(Landroid/graphics/Canvas;FF)V

    .line 96
    .line 97
    .line 98
    return-void
.end method

.method public getBackgroundTintList()Landroid/content/res/ColorStateList;
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getBackgroundTintHelper()Lorg/xbet/uikit/utils/e;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lorg/xbet/uikit/utils/e;->b()Landroid/content/res/ColorStateList;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    return-object v0
.end method

.method public final m()V
    .locals 3

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t:I

    .line 4
    .line 5
    add-int/2addr v0, v1

    .line 6
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->u:I

    .line 7
    .line 8
    add-int/2addr v0, v1

    .line 9
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 14
    .line 15
    invoke-virtual {v1, v2, v2, v0}, LW31/f;->b(III)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public o(Ljava/lang/String;)V
    .locals 7
    .param p1    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->k:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    const/16 v5, 0xe

    .line 4
    .line 5
    const/4 v6, 0x0

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x0

    .line 8
    const/4 v4, 0x0

    .line 9
    move-object v1, p1

    .line 10
    invoke-static/range {v0 .. v6}, Lorg/xbet/uikit/components/views/LoadableImageView;->X(Lorg/xbet/uikit/components/views/LoadableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 7

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getShimmerDelegate()LW31/e;

    .line 2
    .line 3
    .line 4
    move-result-object v1

    .line 5
    invoke-virtual {v1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-nez v1, :cond_0

    .line 14
    .line 15
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getShimmerDelegate()LW31/e;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-virtual {v1}, LW31/e;->b()V

    .line 20
    .line 21
    .line 22
    return-void

    .line 23
    :cond_0
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->k:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 24
    .line 25
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 26
    .line 27
    .line 28
    move-result v2

    .line 29
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->i:I

    .line 30
    .line 31
    sub-int/2addr v2, v3

    .line 32
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->g:I

    .line 33
    .line 34
    add-int/2addr v2, v3

    .line 35
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->h:I

    .line 36
    .line 37
    neg-int v3, v3

    .line 38
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 39
    .line 40
    .line 41
    move-result v4

    .line 42
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->g:I

    .line 43
    .line 44
    add-int/2addr v4, v5

    .line 45
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->i:I

    .line 46
    .line 47
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->h:I

    .line 48
    .line 49
    sub-int/2addr v5, v6

    .line 50
    move-object v0, p0

    .line 51
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 59
    .line 60
    sub-int/2addr v1, v2

    .line 61
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 62
    .line 63
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 64
    .line 65
    .line 66
    move-result v2

    .line 67
    sub-int v3, v1, v2

    .line 68
    .line 69
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 70
    .line 71
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 72
    .line 73
    invoke-virtual {v1}, Landroid/view/View;->getMeasuredWidth()I

    .line 74
    .line 75
    .line 76
    move-result v4

    .line 77
    add-int/2addr v4, v2

    .line 78
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 79
    .line 80
    invoke-virtual {v5}, Landroid/view/View;->getMeasuredHeight()I

    .line 81
    .line 82
    .line 83
    move-result v5

    .line 84
    add-int/2addr v5, v3

    .line 85
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 86
    .line 87
    .line 88
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 89
    .line 90
    invoke-virtual {v1}, LW31/c;->c()Landroid/widget/ImageView;

    .line 91
    .line 92
    .line 93
    move-result-object v2

    .line 94
    invoke-virtual {v2}, Landroid/view/View;->getMeasuredHeight()I

    .line 95
    .line 96
    .line 97
    move-result v2

    .line 98
    div-int/lit8 v2, v2, 0x2

    .line 99
    .line 100
    iget-object v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 101
    .line 102
    invoke-virtual {v3}, LW31/c;->e()Landroid/widget/ImageView;

    .line 103
    .line 104
    .line 105
    move-result-object v3

    .line 106
    invoke-virtual {v3}, Landroid/view/View;->getMeasuredHeight()I

    .line 107
    .line 108
    .line 109
    move-result v3

    .line 110
    div-int/lit8 v3, v3, 0x2

    .line 111
    .line 112
    invoke-virtual {v1, v2, v3}, LW31/j;->m(II)V

    .line 113
    .line 114
    .line 115
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->m()V

    .line 116
    .line 117
    .line 118
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 119
    .line 120
    .line 121
    move-result-object v1

    .line 122
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 123
    .line 124
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 125
    .line 126
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t:I

    .line 127
    .line 128
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->f:I

    .line 129
    .line 130
    sub-int/2addr v4, v5

    .line 131
    div-int/lit8 v4, v4, 0x2

    .line 132
    .line 133
    add-int/2addr v3, v4

    .line 134
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 135
    .line 136
    .line 137
    move-result-object v4

    .line 138
    invoke-virtual {v4}, LW31/f;->a()Lorg/xbet/uikit/components/tag/Tag;

    .line 139
    .line 140
    .line 141
    move-result-object v4

    .line 142
    invoke-virtual {v4}, Landroid/view/View;->getMeasuredHeight()I

    .line 143
    .line 144
    .line 145
    move-result v4

    .line 146
    div-int/lit8 v4, v4, 0x2

    .line 147
    .line 148
    sub-int/2addr v3, v4

    .line 149
    invoke-virtual {v1, v2, v2, v3}, LW31/f;->b(III)V

    .line 150
    .line 151
    .line 152
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 153
    .line 154
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->c:I

    .line 155
    .line 156
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->t:I

    .line 157
    .line 158
    add-int/2addr v2, v3

    .line 159
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->u:I

    .line 160
    .line 161
    add-int/2addr v2, v3

    .line 162
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->v:I

    .line 163
    .line 164
    add-int/2addr v2, v3

    .line 165
    invoke-virtual {v1, v2}, LW31/k;->e(I)V

    .line 166
    .line 167
    .line 168
    return-void
.end method

.method public onMeasure(II)V
    .locals 4

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getShimmerDelegate()LW31/e;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    invoke-virtual {v1}, LW31/e;->a()Lorg/xbet/uikit/components/shimmer/ShimmerView;

    .line 10
    .line 11
    .line 12
    move-result-object v1

    .line 13
    invoke-virtual {v1}, Landroid/view/View;->getVisibility()I

    .line 14
    .line 15
    .line 16
    move-result v1

    .line 17
    if-nez v1, :cond_0

    .line 18
    .line 19
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getShimmerDelegate()LW31/e;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-virtual {p2, v0}, LW31/e;->c(I)V

    .line 24
    .line 25
    .line 26
    goto :goto_0

    .line 27
    :cond_0
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 28
    .line 29
    mul-int/lit8 v1, v1, 0x2

    .line 30
    .line 31
    sub-int v1, v0, v1

    .line 32
    .line 33
    iget-object v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 34
    .line 35
    const/high16 v3, 0x40000000    # 2.0f

    .line 36
    .line 37
    invoke-static {v1, v3}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 38
    .line 39
    .line 40
    move-result v1

    .line 41
    invoke-virtual {v2, v1, p2}, Landroid/view/View;->measure(II)V

    .line 42
    .line 43
    .line 44
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 49
    .line 50
    mul-int/lit8 v2, v2, 0x2

    .line 51
    .line 52
    sub-int v2, v0, v2

    .line 53
    .line 54
    invoke-virtual {v1, v2}, LW31/f;->c(I)V

    .line 55
    .line 56
    .line 57
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getLiveTagDelegate()LW31/f;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-virtual {v1, v0}, LW31/f;->c(I)V

    .line 62
    .line 63
    .line 64
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getErrorDelegate()LW31/h;

    .line 65
    .line 66
    .line 67
    move-result-object v1

    .line 68
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 69
    .line 70
    mul-int/lit8 v2, v2, 0x2

    .line 71
    .line 72
    sub-int v2, v0, v2

    .line 73
    .line 74
    invoke-virtual {v1, v2}, LW31/h;->f(I)V

    .line 75
    .line 76
    .line 77
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 78
    .line 79
    invoke-virtual {v1}, LW31/c;->g()V

    .line 80
    .line 81
    .line 82
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->q(I)V

    .line 83
    .line 84
    .line 85
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getCaptionDelegate()LW31/h;

    .line 86
    .line 87
    .line 88
    move-result-object v1

    .line 89
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b:I

    .line 90
    .line 91
    mul-int/lit8 v2, v2, 0x2

    .line 92
    .line 93
    sub-int/2addr v0, v2

    .line 94
    invoke-virtual {v1, v0}, LW31/h;->f(I)V

    .line 95
    .line 96
    .line 97
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 98
    .line 99
    invoke-virtual {v0, p1, p2}, LW31/k;->f(II)V

    .line 100
    .line 101
    .line 102
    :goto_0
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->p()I

    .line 103
    .line 104
    .line 105
    move-result p2

    .line 106
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 107
    .line 108
    .line 109
    return-void
.end method

.method public setCancelButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->h(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setCaption(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setCaption(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setCaption(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getCaptionDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setCouponBonusTitle(Ljava/lang/CharSequence;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setCaption(Ljava/lang/CharSequence;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public setCouponMarketDescriptionStyle(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setDescriptionTextStyle(I)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public final setError(I)V
    .locals 1

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->g(I)V

    return-void
.end method

.method public setError(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getErrorDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public final setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-nez v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p0, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public final setMarketCoefficient(Ljava/lang/CharSequence;)V
    .locals 1

    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setMarketCoefficient(Ljava/lang/CharSequence;Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 0
    .param p2    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketCoefficient(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    return-void
.end method

.method public final setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit/components/market/base/CoefficientState;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setMarketDescription(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketDescription(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMarketHeader(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setMarketStyle(Ljava/lang/Integer;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->B:Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/SportCouponCardMarketView;->setMarketStyle(Ljava/lang/Integer;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setModel(LX31/c;)V
    .locals 3
    .param p1    # LX31/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-virtual {p1}, LX31/c;->j()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->o(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p1}, LX31/c;->a()Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setCaption(Ljava/lang/CharSequence;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p1}, LX31/c;->m()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setTagText(Ljava/lang/CharSequence;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p1}, LX31/c;->l()I

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setTagColor(I)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/c;->k()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setSubTitle(Ljava/lang/CharSequence;)V

    .line 34
    .line 35
    .line 36
    invoke-virtual {p1}, LX31/c;->c()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setError(Ljava/lang/CharSequence;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p1}, LX31/c;->f()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v0

    .line 47
    invoke-virtual {p1}, LX31/c;->g()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    invoke-virtual {p1}, LX31/c;->e()Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v2

    .line 55
    invoke-virtual {p0, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarket(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p1}, LX31/c;->h()I

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 63
    .line 64
    .line 65
    move-result-object v0

    .line 66
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketStyle(Ljava/lang/Integer;)V

    .line 67
    .line 68
    .line 69
    invoke-virtual {p1}, LX31/c;->b()Lorg/xbet/uikit/components/market/base/CoefficientState;

    .line 70
    .line 71
    .line 72
    move-result-object v0

    .line 73
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketCoefficientState(Lorg/xbet/uikit/components/market/base/CoefficientState;)V

    .line 74
    .line 75
    .line 76
    invoke-virtual {p1}, LX31/c;->g()Ljava/lang/String;

    .line 77
    .line 78
    .line 79
    move-result-object v0

    .line 80
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setMarketHeader(Ljava/lang/CharSequence;)V

    .line 81
    .line 82
    .line 83
    invoke-virtual {p1}, LX31/c;->d()Z

    .line 84
    .line 85
    .line 86
    move-result v0

    .line 87
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->b(Z)V

    .line 88
    .line 89
    .line 90
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 91
    .line 92
    invoke-virtual {p1}, LX31/c;->n()LX31/b;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    invoke-virtual {p1}, LX31/c;->i()LX31/a;

    .line 97
    .line 98
    .line 99
    move-result-object p1

    .line 100
    invoke-virtual {v0, v1, p1}, LW31/k;->g(LX31/b;LX31/a;)V

    .line 101
    .line 102
    .line 103
    return-void
.end method

.method public setMoveButtonClickListener(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroid/view/View;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->r:LW31/j;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/c;->j(Lkotlin/jvm/functions/Function1;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public setScoreUiModel(LX31/a;)V
    .locals 1
    .param p1    # LX31/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/k;->j(LX31/a;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 7
    .line 8
    .line 9
    return-void
.end method

.method public setSportImage(I)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->k:Lorg/xbet/uikit/components/views/LoadableImageView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/AppCompatImageView;->setImageResource(I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setSubTitle(I)V
    .locals 1

    .line 3
    invoke-virtual {p0}, Landroid/view/View;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->setSubTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setSubTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getSubTitleDelegate()LW31/h;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/h;->h(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setTagColor(I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->d(I)V

    return-void
.end method

.method public final setTagColor(Landroid/content/res/ColorStateList;)V
    .locals 1
    .param p1    # Landroid/content/res/ColorStateList;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->e(Landroid/content/res/ColorStateList;)V

    return-void
.end method

.method public final setTagText(I)V
    .locals 1

    .line 3
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->f(I)V

    return-void
.end method

.method public setTagText(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->getWarningTagDelegate()LW31/f;

    move-result-object v0

    invoke-virtual {v0, p1}, LW31/f;->g(Ljava/lang/CharSequence;)V

    .line 2
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    return-void
.end method

.method public setTeamsUiModel(LX31/b;)V
    .locals 1
    .param p1    # LX31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/coupon_card_style_views/SportCouponCardSmallTeamLogo;->s:LW31/k;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LW31/k;->k(LX31/b;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
