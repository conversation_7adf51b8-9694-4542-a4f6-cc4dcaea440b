.class final Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tvbet.presentation.fragments.TvBetJackpotTableFragment$onObserveData$1"
    f = "TvBetJackpotTableFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;",
        "state",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->invoke(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_3

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;

    .line 16
    .line 17
    if-eqz v0, :cond_0

    .line 18
    .line 19
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;

    .line 20
    .line 21
    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;

    .line 22
    .line 23
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;->a()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;->c()Ljava/util/List;

    .line 28
    .line 29
    .line 30
    move-result-object v2

    .line 31
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;->d()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$a;->b()Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object p1

    .line 39
    invoke-static {v0, v1, v2, v3, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->C2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/util/List;)V

    .line 40
    .line 41
    .line 42
    goto :goto_0

    .line 43
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;

    .line 44
    .line 45
    if-eqz v0, :cond_1

    .line 46
    .line 47
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;

    .line 48
    .line 49
    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;

    .line 50
    .line 51
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;->b()Z

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$b;->a()Lorg/xbet/uikit/components/lottie/a;

    .line 56
    .line 57
    .line 58
    move-result-object p1

    .line 59
    invoke-static {v0, v1, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->D2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;ZLorg/xbet/uikit/components/lottie/a;)V

    .line 60
    .line 61
    .line 62
    goto :goto_0

    .line 63
    :cond_1
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$c;

    .line 64
    .line 65
    if-eqz v0, :cond_2

    .line 66
    .line 67
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;

    .line 68
    .line 69
    check-cast p1, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$c;

    .line 70
    .line 71
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/TvBetJackpotTableViewModel$a$c;->a()Z

    .line 72
    .line 73
    .line 74
    move-result p1

    .line 75
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;->E2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotTableFragment;Z)V

    .line 76
    .line 77
    .line 78
    :goto_0
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 79
    .line 80
    return-object p1

    .line 81
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 82
    .line 83
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 84
    .line 85
    .line 86
    throw p1

    .line 87
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 88
    .line 89
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 90
    .line 91
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    throw p1
.end method
