.class public final Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lyb/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lyb/b<",
        "Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;",
        ">;"
    }
.end annotation


# direct methods
.method public static a(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;LzX0/k;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->j0:LzX0/k;

    .line 2
    .line 3
    return-void
.end method

.method public static b(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;LAX0/b;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->k0:LAX0/b;

    .line 2
    .line 3
    return-void
.end method

.method public static c(Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;Lorg/xbet/ui_common/viewmodel/core/l;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/promo/fragment/PromoMakeBetDsFragment;->i0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    return-void
.end method
