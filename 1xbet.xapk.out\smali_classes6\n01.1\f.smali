.class public final synthetic Ln01/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lkotlin/jvm/functions/Function0;

.field public final synthetic b:Lkotlin/jvm/functions/Function0;

.field public final synthetic c:Lg01/a;

.field public final synthetic d:Lorg/xbet/uikit/components/navigationbar/views/right/RightNavigationBarButtonsContainer;


# direct methods
.method public synthetic constructor <init>(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lg01/a;Lorg/xbet/uikit/components/navigationbar/views/right/RightNavigationBarButtonsContainer;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln01/f;->a:Lkotlin/jvm/functions/Function0;

    iput-object p2, p0, Ln01/f;->b:L<PERSON><PERSON>/jvm/functions/Function0;

    iput-object p3, p0, Ln01/f;->c:Lg01/a;

    iput-object p4, p0, Ln01/f;->d:Lorg/xbet/uikit/components/navigationbar/views/right/RightNavigationBarButtonsContainer;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 4

    .line 1
    iget-object v0, p0, Ln01/f;->a:Lkotlin/jvm/functions/Function0;

    iget-object v1, p0, Ln01/f;->b:Lkotlin/jvm/functions/Function0;

    iget-object v2, p0, Ln01/f;->c:Lg01/a;

    iget-object v3, p0, Ln01/f;->d:Lorg/xbet/uikit/components/navigationbar/views/right/RightNavigationBarButtonsContainer;

    invoke-static {v0, v1, v2, v3}, Lorg/xbet/uikit/components/navigationbar/views/right/RightNavigationBarButtonsContainer;->b(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lg01/a;Lorg/xbet/uikit/components/navigationbar/views/right/RightNavigationBarButtonsContainer;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
