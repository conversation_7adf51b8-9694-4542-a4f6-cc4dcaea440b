.class public final Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000b\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0000\u0018\u00002\u00020\u0001B\u0019\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J0\u0010\u000e\u001a\u00020\r2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u00082\u0006\u0010\u000b\u001a\u00020\u00082\u0006\u0010\u000c\u001a\u00020\u0008H\u0086B\u00a2\u0006\u0004\u0008\u000e\u0010\u000fR\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u000e\u0010\u0010R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0011\u0010\u0012\u00a8\u0006\u0013"
    }
    d2 = {
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;",
        "",
        "LfX/b;",
        "testRepository",
        "LMb1/a;",
        "popularAggregatorRepository",
        "<init>",
        "(LfX/b;LMb1/a;)V",
        "",
        "fromCache",
        "brandsApi",
        "hasProvidersAggregator",
        "hasAggregatorBrandsFullInfo",
        "LBb1/a;",
        "a",
        "(ZZZZLkotlin/coroutines/e;)Ljava/lang/Object;",
        "LfX/b;",
        "b",
        "LMb1/a;",
        "impl_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:LfX/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:LMb1/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method public constructor <init>(LfX/b;LMb1/a;)V
    .locals 0
    .param p1    # LfX/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LMb1/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;->a:LfX/b;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;->b:LMb1/a;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a(ZZZZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .param p5    # Lkotlin/coroutines/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZZZZ",
            "Lkotlin/coroutines/e<",
            "-",
            "LBb1/a;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;->b:LMb1/a;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;->a:LfX/b;

    .line 4
    .line 5
    invoke-interface {v1}, LfX/b;->B0()Z

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    move v1, p1

    .line 10
    move v3, p2

    .line 11
    move v4, p3

    .line 12
    move v5, p4

    .line 13
    move-object v6, p5

    .line 14
    invoke-interface/range {v0 .. v6}, LMb1/a;->a(ZZZZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    return-object p1
.end method
