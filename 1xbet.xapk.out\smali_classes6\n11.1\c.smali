.class public final synthetic Ln11/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# instance fields
.field public final synthetic a:Landroidx/compose/ui/l;

.field public final synthetic b:I

.field public final synthetic c:I


# direct methods
.method public synthetic constructor <init>(Landroidx/compose/ui/l;II)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln11/c;->a:Landroidx/compose/ui/l;

    iput p2, p0, Ln11/c;->b:I

    iput p3, p0, Ln11/c;->c:I

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    iget-object v0, p0, Ln11/c;->a:Landroidx/compose/ui/l;

    iget v1, p0, Ln11/c;->b:I

    iget v2, p0, Ln11/c;->c:I

    check-cast p1, Landroidx/compose/runtime/j;

    check-cast p2, <PERSON><PERSON><PERSON>/lang/Integer;

    invoke-virtual {p2}, <PERSON><PERSON>va/lang/Integer;->intValue()I

    move-result p2

    invoke-static {v0, v1, v2, p1, p2}, Ln11/d;->c(Landroidx/compose/ui/l;IILandroidx/compose/runtime/j;I)Lkotlin/Unit;

    move-result-object p1

    return-object p1
.end method
