.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements LOc/n;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournaments_list.AggregatorTournamentsViewModel$tournamentsListFlow$1"
    f = "AggregatorTournamentsViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel;-><init>(Lorg/xbet/ui_common/utils/M;LP91/b;Lorg/xbet/ui_common/utils/internet/a;LwX0/a;Lorg/xbet/analytics/domain/scope/g;LSX0/c;LwX0/C;LHX0/e;Lw81/b;Lgk/b;Lw81/g;Lm8/a;Lp9/c;LnR/d;LZb0/c;LYb0/b;Lac0/a;Lcom/xbet/onexuser/domain/usecases/GetProfileUseCase;Lp9/g;Lorg/xbet/onexlocalization/f;Lek/d;LGg/a;Lorg/xbet/analytics/domain/scope/I;LxX0/a;LAR/a;Lfk/s;LZR/a;Lorg/xbet/remoteconfig/domain/usecases/i;Lfk/o;Lgk0/a;Lek/f;Lfk/l;LC81/f;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "LOc/n<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;",
        "Ljava/lang/Boolean;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0001\u001a\u00020\u00002\u0006\u0010\u0003\u001a\u00020\u0002H\n\u00a2\u0006\u0004\u0008\u0005\u0010\u0006"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;",
        "banners",
        "",
        "<unused var>",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;Z)Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I


# direct methods
.method public constructor <init>(Lkotlin/coroutines/e;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x3

    invoke-direct {p0, v0, p1}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;

    check-cast p2, Ljava/lang/Boolean;

    invoke-virtual {p2}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p2

    check-cast p3, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;ZLkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;ZLkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;",
            "Z",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;

    invoke-direct {p2, p3}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;-><init>(Lkotlin/coroutines/e;)V

    iput-object p1, p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;->L$0:Ljava/lang/Object;

    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p2, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 4

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$tournamentsListFlow$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c;

    .line 14
    .line 15
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;

    .line 16
    .line 17
    instance-of v1, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$c;

    .line 18
    .line 19
    instance-of v2, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$a;

    .line 20
    .line 21
    if-eqz v2, :cond_0

    .line 22
    .line 23
    move-object v2, p1

    .line 24
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$a;

    .line 25
    .line 26
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$a;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    instance-of v2, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$b;

    .line 32
    .line 33
    if-eqz v2, :cond_1

    .line 34
    .line 35
    move-object v2, p1

    .line 36
    check-cast v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$b;

    .line 37
    .line 38
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    goto :goto_0

    .line 43
    :cond_1
    const/4 v2, 0x0

    .line 44
    :goto_0
    if-eqz v1, :cond_2

    .line 45
    .line 46
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$c;

    .line 47
    .line 48
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$c;->a()Ljava/util/List;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    goto :goto_1

    .line 53
    :cond_2
    instance-of v3, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$d;

    .line 54
    .line 55
    if-eqz v3, :cond_3

    .line 56
    .line 57
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$d;

    .line 58
    .line 59
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$c$d;->a()Ljava/util/List;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    goto :goto_1

    .line 64
    :cond_3
    invoke-static {}, Lkotlin/collections/v;->n()Ljava/util/List;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    :goto_1
    invoke-direct {v0, v1, v2, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournaments_list/AggregatorTournamentsViewModel$a$d;-><init>(ZLorg/xbet/uikit/components/lottie_empty/n;Ljava/util/List;)V

    .line 69
    .line 70
    .line 71
    return-object v0

    .line 72
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 73
    .line 74
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 75
    .line 76
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    throw p1
.end method
