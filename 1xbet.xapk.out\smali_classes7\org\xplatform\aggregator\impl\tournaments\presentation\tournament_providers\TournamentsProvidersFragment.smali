.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;
.super LXW0/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u000e\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\t\n\u0002\u0008\u0012\u0008\u0000\u0018\u0000 L2\u00020\u0001:\u0001MB\u0007\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u000f\u0010\u0005\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\u0005\u0010\u0003J\u0019\u0010\u0008\u001a\u00020\u00042\u0008\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u0014\u00a2\u0006\u0004\u0008\u0008\u0010\tJ\u000f\u0010\n\u001a\u00020\u0004H\u0014\u00a2\u0006\u0004\u0008\n\u0010\u0003J\u0017\u0010\r\u001a\u00020\u00042\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\r\u0010\u000eJ\u0017\u0010\u0011\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u000fH\u0002\u00a2\u0006\u0004\u0008\u0011\u0010\u0012J\u0017\u0010\u0015\u001a\u00020\u00042\u0006\u0010\u0014\u001a\u00020\u0013H\u0002\u00a2\u0006\u0004\u0008\u0015\u0010\u0016J/\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u00172\u0006\u0010\u001a\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\u001bH\u0002\u00a2\u0006\u0004\u0008\u001d\u0010\u001eJ\u000f\u0010\u001f\u001a\u00020\u0004H\u0002\u00a2\u0006\u0004\u0008\u001f\u0010\u0003R\u001b\u0010%\u001a\u00020 8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008!\u0010\"\u001a\u0004\u0008#\u0010$R\"\u0010-\u001a\u00020&8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008\'\u0010(\u001a\u0004\u0008)\u0010*\"\u0004\u0008+\u0010,R\"\u00105\u001a\u00020.8\u0006@\u0006X\u0087.\u00a2\u0006\u0012\n\u0004\u0008/\u00100\u001a\u0004\u00081\u00102\"\u0004\u00083\u00104R\u001b\u0010;\u001a\u0002068BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u00087\u00108\u001a\u0004\u00089\u0010:R+\u0010D\u001a\u00020<2\u0006\u0010=\u001a\u00020<8B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008>\u0010?\u001a\u0004\u0008@\u0010A\"\u0004\u0008B\u0010CR+\u0010K\u001a\u00020\u00172\u0006\u0010=\u001a\u00020\u00178B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\u0008E\u0010F\u001a\u0004\u0008G\u0010H\"\u0004\u0008I\u0010J\u00a8\u0006N"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;",
        "LXW0/a;",
        "<init>",
        "()V",
        "",
        "u2",
        "Landroid/os/Bundle;",
        "savedInstanceState",
        "t2",
        "(Landroid/os/Bundle;)V",
        "v2",
        "Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;",
        "providersData",
        "R2",
        "(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V",
        "Lorg/xbet/uikit/components/lottie_empty/n;",
        "lottieConfig",
        "T2",
        "(Lorg/xbet/uikit/components/lottie_empty/n;)V",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;",
        "data",
        "P2",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V",
        "",
        "title",
        "description",
        "positiveButtonTitle",
        "Lorg/xbet/uikit/components/dialog/AlertType;",
        "alertType",
        "S2",
        "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V",
        "M2",
        "LS91/j0;",
        "i0",
        "LRc/c;",
        "J2",
        "()LS91/j0;",
        "viewBinding",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "j0",
        "Lorg/xbet/ui_common/viewmodel/core/l;",
        "L2",
        "()Lorg/xbet/ui_common/viewmodel/core/l;",
        "setViewModelFactory",
        "(Lorg/xbet/ui_common/viewmodel/core/l;)V",
        "viewModelFactory",
        "LTZ0/a;",
        "k0",
        "LTZ0/a;",
        "G2",
        "()LTZ0/a;",
        "setActionDialogManager",
        "(LTZ0/a;)V",
        "actionDialogManager",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;",
        "l0",
        "Lkotlin/j;",
        "K2",
        "()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;",
        "viewModel",
        "",
        "<set-?>",
        "m0",
        "LeX0/f;",
        "H2",
        "()J",
        "setTournamentID",
        "(J)V",
        "tournamentID",
        "n0",
        "LeX0/k;",
        "I2",
        "()Ljava/lang/String;",
        "setTournamentTitle",
        "(Ljava/lang/String;)V",
        "tournamentTitle",
        "o0",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final synthetic b1:[Lkotlin/reflect/m;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "[",
            "Lkotlin/reflect/m<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field public static final o0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final i0:LRc/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public j0:Lorg/xbet/ui_common/viewmodel/core/l;

.field public k0:LTZ0/a;

.field public final l0:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final m0:LeX0/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final n0:LeX0/k;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    new-instance v0, Lkotlin/jvm/internal/PropertyReference1Impl;

    .line 2
    .line 3
    const-class v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

    .line 4
    .line 5
    const-string v2, "viewBinding"

    .line 6
    .line 7
    const-string v3, "getViewBinding()Lorg/xplatform/aggregator/impl/databinding/FragmentTournamentsProvidersBinding;"

    .line 8
    .line 9
    const/4 v4, 0x0

    .line 10
    invoke-direct {v0, v1, v2, v3, v4}, Lkotlin/jvm/internal/PropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 11
    .line 12
    .line 13
    invoke-static {v0}, Lkotlin/jvm/internal/y;->k(Lkotlin/jvm/internal/PropertyReference1;)Lkotlin/reflect/o;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    new-instance v2, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 18
    .line 19
    const-string v3, "tournamentID"

    .line 20
    .line 21
    const-string v5, "getTournamentID()J"

    .line 22
    .line 23
    invoke-direct {v2, v1, v3, v5, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 24
    .line 25
    .line 26
    invoke-static {v2}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 27
    .line 28
    .line 29
    move-result-object v2

    .line 30
    new-instance v3, Lkotlin/jvm/internal/MutablePropertyReference1Impl;

    .line 31
    .line 32
    const-string v5, "tournamentTitle"

    .line 33
    .line 34
    const-string v6, "getTournamentTitle()Ljava/lang/String;"

    .line 35
    .line 36
    invoke-direct {v3, v1, v5, v6, v4}, Lkotlin/jvm/internal/MutablePropertyReference1Impl;-><init>(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 37
    .line 38
    .line 39
    invoke-static {v3}, Lkotlin/jvm/internal/y;->f(Lkotlin/jvm/internal/MutablePropertyReference1;)Lkotlin/reflect/k;

    .line 40
    .line 41
    .line 42
    move-result-object v1

    .line 43
    const/4 v3, 0x3

    .line 44
    new-array v3, v3, [Lkotlin/reflect/m;

    .line 45
    .line 46
    aput-object v0, v3, v4

    .line 47
    .line 48
    const/4 v0, 0x1

    .line 49
    aput-object v2, v3, v0

    .line 50
    .line 51
    const/4 v0, 0x2

    .line 52
    aput-object v1, v3, v0

    .line 53
    .line 54
    sput-object v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->b1:[Lkotlin/reflect/m;

    .line 55
    .line 56
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$a;

    .line 57
    .line 58
    const/4 v1, 0x0

    .line 59
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 60
    .line 61
    .line 62
    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->o0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$a;

    .line 63
    .line 64
    return-void
.end method

.method public constructor <init>()V
    .locals 12

    .line 1
    sget v0, Lu91/c;->fragment_tournaments_providers:I

    .line 2
    .line 3
    invoke-direct {p0, v0}, LXW0/a;-><init>(I)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$viewBinding$2;->INSTANCE:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$viewBinding$2;

    .line 7
    .line 8
    invoke-static {p0, v0}, LLX0/j;->d(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;)LRc/c;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->i0:LRc/c;

    .line 13
    .line 14
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/d;

    .line 15
    .line 16
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/d;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)V

    .line 17
    .line 18
    .line 19
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$1;

    .line 20
    .line 21
    invoke-direct {v1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$1;-><init>(Landroidx/fragment/app/Fragment;)V

    .line 22
    .line 23
    .line 24
    sget-object v2, Lkotlin/LazyThreadSafetyMode;->NONE:Lkotlin/LazyThreadSafetyMode;

    .line 25
    .line 26
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$2;

    .line 27
    .line 28
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$2;-><init>(Lkotlin/jvm/functions/Function0;)V

    .line 29
    .line 30
    .line 31
    invoke-static {v2, v3}, Lkotlin/k;->a(Lkotlin/LazyThreadSafetyMode;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 32
    .line 33
    .line 34
    move-result-object v1

    .line 35
    const-class v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 36
    .line 37
    invoke-static {v2}, Lkotlin/jvm/internal/y;->b(Ljava/lang/Class;)Lkotlin/reflect/d;

    .line 38
    .line 39
    .line 40
    move-result-object v2

    .line 41
    new-instance v3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$3;

    .line 42
    .line 43
    invoke-direct {v3, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$3;-><init>(Lkotlin/j;)V

    .line 44
    .line 45
    .line 46
    new-instance v4, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$4;

    .line 47
    .line 48
    const/4 v5, 0x0

    .line 49
    invoke-direct {v4, v5, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$special$$inlined$viewModels$default$4;-><init>(Lkotlin/jvm/functions/Function0;Lkotlin/j;)V

    .line 50
    .line 51
    .line 52
    invoke-static {p0, v2, v3, v4, v0}, Landroidx/fragment/app/FragmentViewModelLazyKt;->c(Landroidx/fragment/app/Fragment;Lkotlin/reflect/d;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->l0:Lkotlin/j;

    .line 57
    .line 58
    new-instance v6, LeX0/f;

    .line 59
    .line 60
    const/4 v10, 0x2

    .line 61
    const/4 v11, 0x0

    .line 62
    const-string v7, "TOURNAMENT_ID"

    .line 63
    .line 64
    const-wide/16 v8, 0x0

    .line 65
    .line 66
    invoke-direct/range {v6 .. v11}, LeX0/f;-><init>(Ljava/lang/String;JILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 67
    .line 68
    .line 69
    iput-object v6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->m0:LeX0/f;

    .line 70
    .line 71
    new-instance v0, LeX0/k;

    .line 72
    .line 73
    const-string v1, "TOURNAMENT_TITLE"

    .line 74
    .line 75
    const/4 v2, 0x2

    .line 76
    invoke-direct {v0, v1, v5, v2, v5}, LeX0/k;-><init>(Ljava/lang/String;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 77
    .line 78
    .line 79
    iput-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->n0:LeX0/k;

    .line 80
    .line 81
    return-void
.end method

.method public static synthetic A2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;Landroid/view/View;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->Q2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;Landroid/view/View;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic B2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->U2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Landroidx/lifecycle/e0$c;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->P2(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic D2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->R2(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->S2(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic F2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->T2(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method private final I2()Ljava/lang/String;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->n0:LeX0/k;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/k;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/String;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    return-object v0
.end method

.method public static final N2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->M2()V

    .line 2
    .line 3
    .line 4
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 5
    .line 6
    return-object p0
.end method

.method public static final O2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->M2()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final Q2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;Landroid/view/View;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->K2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 6
    .line 7
    .line 8
    move-result-object p2

    .line 9
    invoke-virtual {p2}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 10
    .line 11
    .line 12
    move-result-object p2

    .line 13
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->g()Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    const-class v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;

    .line 18
    .line 19
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    invoke-virtual {p0, p2, p1, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->H3(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;)V

    .line 24
    .line 25
    .line 26
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 27
    .line 28
    return-object p0
.end method

.method public static final U2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Landroidx/lifecycle/e0$c;
    .locals 0

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->L2()Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic y2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->N2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic z2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->O2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Landroid/view/View;)V

    return-void
.end method


# virtual methods
.method public final G2()LTZ0/a;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->k0:LTZ0/a;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final H2()J
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->m0:LeX0/f;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x1

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-virtual {v0, p0, v1}, LeX0/f;->b(Landroidx/fragment/app/Fragment;Lkotlin/reflect/m;)Ljava/lang/Long;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    .line 13
    .line 14
    .line 15
    move-result-wide v0

    .line 16
    return-wide v0
.end method

.method public final J2()LS91/j0;
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->i0:LRc/c;

    .line 2
    .line 3
    sget-object v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->b1:[Lkotlin/reflect/m;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    aget-object v1, v1, v2

    .line 7
    .line 8
    invoke-interface {v0, p0, v1}, LRc/c;->getValue(Ljava/lang/Object;Lkotlin/reflect/m;)Ljava/lang/Object;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, LS91/j0;

    .line 13
    .line 14
    return-object v0
.end method

.method public final K2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->l0:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 8
    .line 9
    return-object v0
.end method

.method public final L2()Lorg/xbet/ui_common/viewmodel/core/l;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->j0:Lorg/xbet/ui_common/viewmodel/core/l;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    const/4 v0, 0x0

    .line 7
    return-object v0
.end method

.method public final M2()V
    .locals 3

    .line 1
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Landroidx/fragment/app/FragmentActivity;->getSupportFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    const-string v1, "REQUEST_KEY_CLOSE_OTHER_TOURNAMENTS_FRAGMENTS"

    .line 10
    .line 11
    invoke-static {}, Landroidx/core/os/d;->a()Landroid/os/Bundle;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    invoke-virtual {v0, v1, v2}, Landroidx/fragment/app/FragmentManager;->K1(Ljava/lang/String;Landroid/os/Bundle;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->K2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->onBackPressed()V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public final P2(Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V
    .locals 5

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/j0;->b:Landroid/widget/Button;

    .line 6
    .line 7
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->a()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 16
    .line 17
    .line 18
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iget-object v0, v0, LS91/j0;->c:Landroid/widget/LinearLayout;

    .line 23
    .line 24
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-virtual {v1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 29
    .line 30
    .line 31
    move-result-object v1

    .line 32
    sget-object v2, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 33
    .line 34
    const/4 v3, 0x0

    .line 35
    const/4 v4, 0x1

    .line 36
    if-eq v1, v2, :cond_0

    .line 37
    .line 38
    const/4 v1, 0x1

    .line 39
    goto :goto_0

    .line 40
    :cond_0
    const/4 v1, 0x0

    .line 41
    :goto_0
    if-eqz v1, :cond_1

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_1
    const/16 v3, 0x8

    .line 45
    .line 46
    :goto_1
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    iget-object v0, v0, LS91/j0;->b:Landroid/widget/Button;

    .line 54
    .line 55
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;

    .line 56
    .line 57
    invoke-direct {v1, p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/c;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V

    .line 58
    .line 59
    .line 60
    const/4 p1, 0x0

    .line 61
    invoke-static {v0, p1, v1, v4, p1}, LN11/f;->d(Landroid/view/View;Lorg/xbet/uikit/utils/debounce/Interval;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Landroid/view/View$OnClickListener;

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public final R2(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/j0;->h:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;

    .line 6
    .line 7
    invoke-static {p0}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-virtual {v0, p1, v1}, Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;->setItem(Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;Landroidx/lifecycle/LifecycleCoroutineScope;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final S2(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/xbet/uikit/components/dialog/AlertType;)V
    .locals 16

    .line 1
    invoke-virtual/range {p0 .. p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->G2()LTZ0/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xbet/uikit/components/dialog/DialogFields;

    .line 6
    .line 7
    const/16 v14, 0xbf8

    .line 8
    .line 9
    const/4 v15, 0x0

    .line 10
    const/4 v5, 0x0

    .line 11
    const/4 v6, 0x0

    .line 12
    const/4 v7, 0x0

    .line 13
    const/4 v8, 0x0

    .line 14
    const/4 v9, 0x0

    .line 15
    const/4 v10, 0x0

    .line 16
    const/4 v11, 0x0

    .line 17
    const/4 v13, 0x0

    .line 18
    move-object/from16 v2, p1

    .line 19
    .line 20
    move-object/from16 v3, p2

    .line 21
    .line 22
    move-object/from16 v4, p3

    .line 23
    .line 24
    move-object/from16 v12, p4

    .line 25
    .line 26
    invoke-direct/range {v1 .. v15}, Lorg/xbet/uikit/components/dialog/DialogFields;-><init>(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/String;Ljava/lang/CharSequence;Lorg/xbet/uikit/components/dialog/utils/ActionDialogButtonStyle;Lorg/xbet/uikit/components/dialog/utils/TypeButtonPlacement;ILorg/xbet/uikit/components/dialog/AlertType;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual/range {p0 .. p0}, Landroidx/fragment/app/Fragment;->getChildFragmentManager()Landroidx/fragment/app/FragmentManager;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-virtual {v0, v1, v2}, LTZ0/a;->d(Lorg/xbet/uikit/components/dialog/DialogFields;Landroidx/fragment/app/FragmentManager;)V

    .line 34
    .line 35
    .line 36
    return-void
.end method

.method public final T2(Lorg/xbet/uikit/components/lottie_empty/n;)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget-object v0, v0, LS91/j0;->d:Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Lorg/xbet/uikit/components/lottie_empty/DsLottieEmptyContainer;->e(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 8
    .line 9
    .line 10
    const/4 p1, 0x0

    .line 11
    invoke-virtual {v0, p1}, Landroid/view/View;->setVisibility(I)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    iget-object p1, p1, LS91/j0;->h:Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/AggregatorProviderCardCollection;

    .line 19
    .line 20
    const/16 v0, 0x8

    .line 21
    .line 22
    invoke-virtual {p1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public t2(Landroid/os/Bundle;)V
    .locals 3

    .line 1
    new-instance p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/a;

    .line 2
    .line 3
    invoke-direct {p1, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/a;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p0, p1}, LXW0/d;->e(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function0;)V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->J2()LS91/j0;

    .line 10
    .line 11
    .line 12
    move-result-object p1

    .line 13
    iget-object p1, p1, LS91/j0;->f:Lcom/google/android/material/appbar/MaterialToolbar;

    .line 14
    .line 15
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/b;

    .line 16
    .line 17
    invoke-direct {v0, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/b;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)V

    .line 18
    .line 19
    .line 20
    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->K2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->H2()J

    .line 28
    .line 29
    .line 30
    move-result-wide v0

    .line 31
    const/4 v2, 0x1

    .line 32
    invoke-virtual {p1, v0, v1, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->F3(JZ)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public u2()V
    .locals 6

    .line 1
    sget-object v0, LVa1/u;->a:LVa1/u;

    .line 2
    .line 3
    invoke-virtual {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->H2()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    invoke-direct {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->I2()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v4

    .line 11
    sget-object v3, Lorg/xplatform/aggregator/api/navigation/TournamentsPage;->MAIN:Lorg/xplatform/aggregator/api/navigation/TournamentsPage;

    .line 12
    .line 13
    invoke-virtual {p0}, Landroidx/fragment/app/Fragment;->requireActivity()Landroidx/fragment/app/FragmentActivity;

    .line 14
    .line 15
    .line 16
    move-result-object v5

    .line 17
    invoke-virtual {v5}, Landroid/app/Activity;->getApplication()Landroid/app/Application;

    .line 18
    .line 19
    .line 20
    move-result-object v5

    .line 21
    invoke-virtual/range {v0 .. v5}, LVa1/u;->e(JLorg/xplatform/aggregator/api/navigation/TournamentsPage;Ljava/lang/String;Landroid/app/Application;)LVa1/r;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-interface {v0, p0}, LVa1/r;->d(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;)V

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method public v2()V
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->K2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    invoke-virtual {v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->E3()Lkotlinx/coroutines/flow/f0;

    .line 8
    .line 9
    .line 10
    move-result-object v3

    .line 11
    new-instance v6, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$1;

    .line 12
    .line 13
    const/4 v1, 0x0

    .line 14
    invoke-direct {v6, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lkotlin/coroutines/e;)V

    .line 15
    .line 16
    .line 17
    sget-object v5, Landroidx/lifecycle/Lifecycle$State;->STARTED:Landroidx/lifecycle/Lifecycle$State;

    .line 18
    .line 19
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 20
    .line 21
    .line 22
    move-result-object v4

    .line 23
    invoke-static {v4}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 24
    .line 25
    .line 26
    move-result-object v8

    .line 27
    new-instance v2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$$inlined$observeWithLifecycle$default$1;

    .line 28
    .line 29
    const/4 v7, 0x0

    .line 30
    invoke-direct/range {v2 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$$inlined$observeWithLifecycle$default$1;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/4 v11, 0x3

    .line 34
    const/4 v12, 0x0

    .line 35
    move-object v7, v8

    .line 36
    const/4 v8, 0x0

    .line 37
    const/4 v9, 0x0

    .line 38
    move-object v10, v2

    .line 39
    invoke-static/range {v7 .. v12}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 40
    .line 41
    .line 42
    invoke-virtual {v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;->K2()Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    invoke-virtual {v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersViewModel;->getEvents()Lkotlinx/coroutines/flow/e;

    .line 47
    .line 48
    .line 49
    move-result-object v8

    .line 50
    new-instance v11, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$2;

    .line 51
    .line 52
    invoke-direct {v11, v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment;Lkotlin/coroutines/e;)V

    .line 53
    .line 54
    .line 55
    invoke-static {v0}, Lorg/xbet/ui_common/utils/w;->a(Landroidx/fragment/app/Fragment;)Landroidx/lifecycle/w;

    .line 56
    .line 57
    .line 58
    move-result-object v9

    .line 59
    invoke-static {v9}, Landroidx/lifecycle/x;->a(Landroidx/lifecycle/w;)Landroidx/lifecycle/LifecycleCoroutineScope;

    .line 60
    .line 61
    .line 62
    move-result-object v1

    .line 63
    new-instance v15, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$$inlined$observeWithLifecycle$default$2;

    .line 64
    .line 65
    move-object v10, v5

    .line 66
    move-object v7, v15

    .line 67
    invoke-direct/range {v7 .. v12}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers/TournamentsProvidersFragment$onObserveData$$inlined$observeWithLifecycle$default$2;-><init>(Lkotlinx/coroutines/flow/e;Landroidx/lifecycle/w;Landroidx/lifecycle/Lifecycle$State;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)V

    .line 68
    .line 69
    .line 70
    const/16 v16, 0x3

    .line 71
    .line 72
    const/16 v17, 0x0

    .line 73
    .line 74
    const/4 v13, 0x0

    .line 75
    const/4 v14, 0x0

    .line 76
    move-object v12, v1

    .line 77
    invoke-static/range {v12 .. v17}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 78
    .line 79
    .line 80
    return-void
.end method
