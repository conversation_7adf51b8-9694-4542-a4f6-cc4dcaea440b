.class public final synthetic Lcom/google/android/gms/internal/fido/zzbq;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/google/android/gms/internal/fido/zzbp;


# static fields
.field public static final synthetic zza:Lcom/google/android/gms/internal/fido/zzbq;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/fido/zzbq;

    invoke-direct {v0}, Lcom/google/android/gms/internal/fido/zzbq;-><init>()V

    sput-object v0, Lcom/google/android/gms/internal/fido/zzbq;->zza:Lcom/google/android/gms/internal/fido/zzbq;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final zza()Ljava/lang/Object;
    .locals 1

    const/4 v0, 0x0

    throw v0
.end method
