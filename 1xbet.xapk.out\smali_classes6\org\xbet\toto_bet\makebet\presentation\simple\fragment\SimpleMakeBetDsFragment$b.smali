.class public final Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->i3(LZU0/a$f;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0008\n\u0002\u0010\u0002\n\u0002\u0008\u0003\u0010\u0003\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0001\u0010\u0002"
    }
    d2 = {
        "",
        "run",
        "()V",
        "<anonymous>"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field public final synthetic a:Landroid/view/View;

.field public final synthetic b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

.field public final synthetic c:LZU0/a$f;


# direct methods
.method public constructor <init>(Landroid/view/View;Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;LZU0/a$f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;->a:Landroid/view/View;

    .line 2
    .line 3
    iput-object p2, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    .line 4
    .line 5
    iput-object p3, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;->c:LZU0/a$f;

    .line 6
    .line 7
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    .line 2
    .line 3
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->E2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)LOU0/h;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v0, v0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 8
    .line 9
    iget-object v1, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;->c:LZU0/a$f;

    .line 10
    .line 11
    invoke-virtual {v1}, LZU0/a$f;->a()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    const/4 v2, 0x0

    .line 16
    const/4 v3, 0x2

    .line 17
    invoke-static {v0, v1, v2, v3, v2}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setText$default(Lorg/xbet/ui_common/viewcomponents/views/BetInputView;Ljava/lang/String;Ljava/lang/Integer;ILjava/lang/Object;)V

    .line 18
    .line 19
    .line 20
    iget-object v0, p0, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment$b;->b:Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;

    .line 21
    .line 22
    invoke-static {v0}, Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;->E2(Lorg/xbet/toto_bet/makebet/presentation/simple/fragment/SimpleMakeBetDsFragment;)LOU0/h;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    iget-object v0, v0, LOU0/h;->f:Lorg/xbet/ui_common/viewcomponents/views/BetInputView;

    .line 27
    .line 28
    const/4 v1, 0x1

    .line 29
    invoke-virtual {v0, v1}, Lorg/xbet/ui_common/viewcomponents/views/BetInputView;->setMakeBetButtonEnabled(Z)V

    .line 30
    .line 31
    .line 32
    return-void
.end method
