.class final synthetic Lcom/google/android/gms/common/api/zab;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# static fields
.field public static final synthetic a:Lcom/google/android/gms/common/api/zab;


# direct methods
.method public static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/common/api/zab;

    invoke-direct {v0}, Lcom/google/android/gms/common/api/zab;-><init>()V

    sput-object v0, Lcom/google/android/gms/common/api/zab;->a:Lcom/google/android/gms/common/api/zab;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final synthetic run()V
    .locals 0

    return-void
.end method
