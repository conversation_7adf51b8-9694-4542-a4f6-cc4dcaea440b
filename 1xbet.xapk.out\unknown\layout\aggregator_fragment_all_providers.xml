<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/providersRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <org.xbet.uikit.components.toolbar.base.DSNavigationBarBasic
        android:id="@+id/navigationBarAggregator"
        style="@style/Widgets.BasicNavigationBar.Title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="@dimen/elevation_4"
        app:title="@string/providers"
        app:showSeparator="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <org.xbet.uikit_aggregator.aggregatorprovidercardcollection.AggregatorProviderCardCollection
        android:id="@+id/vAggregatorProviderCardCollection"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingHorizontal="@dimen/medium_horizontal_margin_dynamic"
        android:paddingTop="@dimen/space_8"
        android:paddingBottom="@dimen/space_76"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigationBarAggregator" />

    <org.xbet.uikit.components.lottie_empty.DsLottieEmptyContainer
        android:id="@+id/lottieEmptyView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/navigationBarAggregator"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
