.class final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.impl.tournaments.presentation.tournament_providers_alt_design.TournamentsProvidersAltDesignFragment$onObserveData$1"
    f = "TournamentsProvidersAltDesignFragment.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;->v2()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0001\u001a\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;",
        "providersState",
        "",
        "<anonymous>",
        "(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field synthetic L$0:Ljava/lang/Object;

.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;

    iget-object v1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;

    invoke-direct {v0, v1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Lkotlin/coroutines/e;)V

    iput-object p1, v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_4

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->L$0:Ljava/lang/Object;

    .line 12
    .line 13
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c;

    .line 14
    .line 15
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$c;

    .line 16
    .line 17
    const/4 v1, 0x1

    .line 18
    if-eqz v0, :cond_0

    .line 19
    .line 20
    iget-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;

    .line 21
    .line 22
    invoke-static {p1, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;->I2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Z)V

    .line 23
    .line 24
    .line 25
    goto :goto_1

    .line 26
    :cond_0
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$a;

    .line 27
    .line 28
    if-eqz v0, :cond_2

    .line 29
    .line 30
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$a;

    .line 31
    .line 32
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$a;->a()Lkb1/a;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;

    .line 37
    .line 38
    invoke-virtual {p1}, Lkb1/a;->a()Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-static {v0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;->D2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;)V

    .line 43
    .line 44
    .line 45
    invoke-virtual {p1}, Lkb1/a;->b()Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-static {v0, v2}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;->E2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Lorg/xbet/uikit_aggregator/aggregatorprovidercardcollection/a;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p1}, Lkb1/a;->a()Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;

    .line 53
    .line 54
    .line 55
    move-result-object p1

    .line 56
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/models/ContainerUiModel;->a()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;

    .line 57
    .line 58
    .line 59
    move-result-object p1

    .line 60
    invoke-virtual {p1}, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonModel;->b()Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    sget-object v2, Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;->None:Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;

    .line 65
    .line 66
    if-eq p1, v2, :cond_1

    .line 67
    .line 68
    goto :goto_0

    .line 69
    :cond_1
    const/4 v1, 0x0

    .line 70
    :goto_0
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;->C2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Z)V

    .line 71
    .line 72
    .line 73
    goto :goto_1

    .line 74
    :cond_2
    instance-of v0, p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$b;

    .line 75
    .line 76
    if-eqz v0, :cond_3

    .line 77
    .line 78
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment$onObserveData$1;->this$0:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;

    .line 79
    .line 80
    check-cast p1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$b;

    .line 81
    .line 82
    invoke-virtual {p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignViewModel$c$b;->a()Lorg/xbet/uikit/components/lottie_empty/n;

    .line 83
    .line 84
    .line 85
    move-result-object p1

    .line 86
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;->J2(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_providers_alt_design/TournamentsProvidersAltDesignFragment;Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 87
    .line 88
    .line 89
    :goto_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 90
    .line 91
    return-object p1

    .line 92
    :cond_3
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 93
    .line 94
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 95
    .line 96
    .line 97
    throw p1

    .line 98
    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 99
    .line 100
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 101
    .line 102
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 103
    .line 104
    .line 105
    throw p1
.end method
