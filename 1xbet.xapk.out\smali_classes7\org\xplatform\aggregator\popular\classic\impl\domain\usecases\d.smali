.class public final Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ldagger/internal/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ldagger/internal/d<",
        "Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d$a;->a()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public static c()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;
    .locals 1

    .line 1
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;

    .line 2
    .line 3
    invoke-direct {v0}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public b()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;
    .locals 1

    .line 1
    invoke-static {}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;->c()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/d;->b()Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
