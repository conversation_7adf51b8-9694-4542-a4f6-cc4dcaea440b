.class public final Lcom/google/android/gms/internal/ads_identifier/zzk;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field private static volatile zza:Lcom/google/android/gms/internal/ads_identifier/zzj;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/ads_identifier/zzi;

    invoke-direct {v0}, Lcom/google/android/gms/internal/ads_identifier/zzi;-><init>()V

    sput-object v0, Lcom/google/android/gms/internal/ads_identifier/zzk;->zza:Lcom/google/android/gms/internal/ads_identifier/zzj;

    return-void
.end method

.method public static zza()V
    .locals 0
    .annotation build Landroid/annotation/TargetApi;
        value = 0x1c
    .end annotation

    return-void
.end method

.method public static zzb(I)V
    .locals 0

    return-void
.end method
