.class public final Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType$a;",
        "",
        "<init>",
        "()V",
        "",
        "type",
        "Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;",
        "a",
        "(I)Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(I)Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    const/4 v0, 0x1

    .line 2
    if-eq p1, v0, :cond_1

    .line 3
    .line 4
    const/4 v0, 0x2

    .line 5
    if-eq p1, v0, :cond_0

    .line 6
    .line 7
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;->NO_SPECIFIC_SCROLL:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 8
    .line 9
    return-object p1

    .line 10
    :cond_0
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;->SINGLE:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 11
    .line 12
    return-object p1

    .line 13
    :cond_1
    sget-object p1, Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;->MULTI:Lorg/xbet/uikit_sport/sport_collection/models/SportCollectionSelectionType;

    .line 14
    .line 15
    return-object p1
.end method
