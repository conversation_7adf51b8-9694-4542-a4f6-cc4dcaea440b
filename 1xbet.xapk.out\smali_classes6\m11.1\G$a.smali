.class public final Lm11/G$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Landroidx/compose/ui/input/pointer/PointerInputEventHandler;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm11/G;->S(Landroidx/compose/ui/l;Lm11/H$h;ZLandroidx/compose/runtime/j;II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Lm11/H$h;


# direct methods
.method public constructor <init>(ZLm11/H$h;)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lm11/G$a;->a:Z

    .line 2
    .line 3
    iput-object p2, p0, Lm11/G$a;->b:Lm11/H$h;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public static synthetic a(Lm11/H$h;Landroidx/compose/ui/input/pointer/A;Lb0/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lm11/G$a;->h(Lm11/H$h;Landroidx/compose/ui/input/pointer/A;Lb0/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lm11/H$h;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lm11/G$a;->g(Lm11/H$h;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Lm11/H$h;Lb0/f;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lm11/G$a;->e(Lm11/H$h;Lb0/f;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(Lm11/H$h;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lm11/G$a;->f(Lm11/H$h;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final e(Lm11/H$h;Lb0/f;)Lkotlin/Unit;
    .locals 3

    .line 1
    invoke-virtual {p0}, Lm11/H$h;->b()Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    new-instance v0, Lm11/d;

    .line 6
    .line 7
    invoke-virtual {p1}, Lb0/f;->t()J

    .line 8
    .line 9
    .line 10
    move-result-wide v1

    .line 11
    const/4 p1, 0x0

    .line 12
    invoke-direct {v0, v1, v2, p1}, Lm11/d;-><init>(JLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 13
    .line 14
    .line 15
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 19
    .line 20
    return-object p0
.end method

.method public static final f(Lm11/H$h;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lm11/H$h;->b()Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    sget-object v0, Lm11/c;->a:Lm11/c;

    .line 6
    .line 7
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final g(Lm11/H$h;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lm11/H$h;->b()Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    sget-object v0, Lm11/b;->a:Lm11/b;

    .line 6
    .line 7
    invoke-interface {p0, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 11
    .line 12
    return-object p0
.end method

.method public static final h(Lm11/H$h;Landroidx/compose/ui/input/pointer/A;Lb0/f;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroidx/compose/ui/input/pointer/A;->a()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Lm11/H$h;->b()Lkotlin/jvm/functions/Function1;

    .line 5
    .line 6
    .line 7
    move-result-object p0

    .line 8
    new-instance p1, Lm11/a;

    .line 9
    .line 10
    invoke-virtual {p2}, Lb0/f;->t()J

    .line 11
    .line 12
    .line 13
    move-result-wide v0

    .line 14
    const/4 p2, 0x0

    .line 15
    invoke-direct {p1, v0, v1, p2}, Lm11/a;-><init>(JLkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 16
    .line 17
    .line 18
    invoke-interface {p0, p1}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 22
    .line 23
    return-object p0
.end method


# virtual methods
.method public final invoke(Landroidx/compose/ui/input/pointer/I;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/input/pointer/I;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    iget-boolean v0, p0, Lm11/G$a;->a:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 6
    .line 7
    return-object p1

    .line 8
    :cond_0
    iget-object v0, p0, Lm11/G$a;->b:Lm11/H$h;

    .line 9
    .line 10
    new-instance v2, Lm11/C;

    .line 11
    .line 12
    invoke-direct {v2, v0}, Lm11/C;-><init>(Lm11/H$h;)V

    .line 13
    .line 14
    .line 15
    iget-object v0, p0, Lm11/G$a;->b:Lm11/H$h;

    .line 16
    .line 17
    new-instance v3, Lm11/D;

    .line 18
    .line 19
    invoke-direct {v3, v0}, Lm11/D;-><init>(Lm11/H$h;)V

    .line 20
    .line 21
    .line 22
    iget-object v0, p0, Lm11/G$a;->b:Lm11/H$h;

    .line 23
    .line 24
    new-instance v4, Lm11/E;

    .line 25
    .line 26
    invoke-direct {v4, v0}, Lm11/E;-><init>(Lm11/H$h;)V

    .line 27
    .line 28
    .line 29
    iget-object v0, p0, Lm11/G$a;->b:Lm11/H$h;

    .line 30
    .line 31
    new-instance v5, Lm11/F;

    .line 32
    .line 33
    invoke-direct {v5, v0}, Lm11/F;-><init>(Lm11/H$h;)V

    .line 34
    .line 35
    .line 36
    move-object v1, p1

    .line 37
    move-object v6, p2

    .line 38
    invoke-static/range {v1 .. v6}, Landroidx/compose/foundation/gestures/DragGestureDetectorKt;->n(Landroidx/compose/ui/input/pointer/I;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/e;)Ljava/lang/Object;

    .line 39
    .line 40
    .line 41
    move-result-object p1

    .line 42
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 43
    .line 44
    .line 45
    move-result-object p2

    .line 46
    if-ne p1, p2, :cond_1

    .line 47
    .line 48
    return-object p1

    .line 49
    :cond_1
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 50
    .line 51
    return-object p1
.end method
