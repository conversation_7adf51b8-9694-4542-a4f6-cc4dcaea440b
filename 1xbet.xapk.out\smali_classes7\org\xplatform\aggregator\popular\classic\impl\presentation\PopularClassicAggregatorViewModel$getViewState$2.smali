.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.PopularClassicAggregatorViewModel$getViewState$2"
    f = "PopularClassicAggregatorViewModel.kt"
    l = {}
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->A4()Lkotlinx/coroutines/flow/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/flow/f<",
        "-",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
        ">;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0003\u001a\u00020\u0002*\u0008\u0012\u0004\u0012\u00020\u00010\u0000H\n\u00a2\u0006\u0004\u0008\u0003\u0010\u0004"
    }
    d2 = {
        "Lkotlinx/coroutines/flow/f;",
        "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/flow/f;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/flow/f;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/flow/f;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/flow/f<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;",
            ">;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    iget v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->label:I

    .line 5
    .line 6
    if-nez v0, :cond_2

    .line 7
    .line 8
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 12
    .line 13
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->F3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    if-nez p1, :cond_0

    .line 18
    .line 19
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->INIT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 20
    .line 21
    goto :goto_0

    .line 22
    :cond_0
    sget-object p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;->REOPEN_FRAGMENT:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;

    .line 23
    .line 24
    :goto_0
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 25
    .line 26
    const/4 v1, 0x1

    .line 27
    invoke-static {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->j4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Z)V

    .line 28
    .line 29
    .line 30
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 31
    .line 32
    invoke-static {v0, p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->k4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V

    .line 33
    .line 34
    .line 35
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 36
    .line 37
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->B3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V

    .line 38
    .line 39
    .line 40
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 41
    .line 42
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 43
    .line 44
    .line 45
    move-result-object v0

    .line 46
    invoke-interface {v0}, Lkotlinx/coroutines/flow/V;->getValue()Ljava/lang/Object;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    check-cast v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;

    .line 51
    .line 52
    invoke-static {p1, v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->Y3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b;)Z

    .line 53
    .line 54
    .line 55
    move-result p1

    .line 56
    if-eqz p1, :cond_1

    .line 57
    .line 58
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 59
    .line 60
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->X3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    new-instance v0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;

    .line 65
    .line 66
    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 67
    .line 68
    invoke-static {v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->O3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/uikit/components/lottie_empty/n;

    .line 69
    .line 70
    .line 71
    move-result-object v1

    .line 72
    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$b$a;-><init>(Lorg/xbet/uikit/components/lottie_empty/n;)V

    .line 73
    .line 74
    .line 75
    invoke-interface {p1, v0}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 76
    .line 77
    .line 78
    :cond_1
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getViewState$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 79
    .line 80
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->c4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)V

    .line 81
    .line 82
    .line 83
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 84
    .line 85
    return-object p1

    .line 86
    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 87
    .line 88
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 89
    .line 90
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 91
    .line 92
    .line 93
    throw p1
.end method
