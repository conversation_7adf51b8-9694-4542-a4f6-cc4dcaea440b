.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Ld41/b;",
        "clickListener",
        "LA4/c;",
        "",
        "Le41/a;",
        "g",
        "(Ld41/b;)LA4/c;",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/N;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;->h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/N;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;->l(Ld41/b;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic c(Ld41/b;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;->i(Ld41/b;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic d(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;->m(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic e(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;->j(Ld41/b;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic f(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt;->k(Ld41/b;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static final g(Ld41/b;)LA4/c;
    .locals 4
    .param p0    # Ld41/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ld41/b;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "Le41/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lc41/s;

    .line 2
    .line 3
    invoke-direct {v0}, Lc41/s;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lc41/t;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lc41/t;-><init>(Ld41/b;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt$sportFeedsCellSportSmallViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt$sportFeedsCellSportSmallViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt$sportFeedsCellSportSmallViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/sport/DsSportFeedsCellSportSmallViewHolderKt$sportFeedsCellSportSmallViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final h(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/N;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC31/N;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/N;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final i(Ld41/b;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LC31/N;

    .line 6
    .line 7
    iget-object v0, v0, LC31/N;->e:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;

    .line 8
    .line 9
    new-instance v1, Lc41/u;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, Lc41/u;-><init>(Ld41/b;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, LC31/N;

    .line 22
    .line 23
    iget-object v0, v0, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 24
    .line 25
    new-instance v1, Lc41/v;

    .line 26
    .line 27
    invoke-direct {v1, p0, p1}, Lc41/v;-><init>(Ld41/b;LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionClickListener(Landroid/view/View$OnClickListener;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, LC31/N;

    .line 38
    .line 39
    iget-object v0, v0, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 40
    .line 41
    new-instance v1, Lc41/w;

    .line 42
    .line 43
    invoke-direct {v1, p0, p1}, Lc41/w;-><init>(Ld41/b;LB4/a;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckBoxClickListener(Landroid/view/View$OnClickListener;)V

    .line 47
    .line 48
    .line 49
    new-instance p0, Lc41/x;

    .line 50
    .line 51
    invoke-direct {p0, p1}, Lc41/x;-><init>(LB4/a;)V

    .line 52
    .line 53
    .line 54
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 55
    .line 56
    .line 57
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 58
    .line 59
    return-object p0
.end method

.method public static final j(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lg41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/b;->b(Lg41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final k(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lg41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/b;->a(Lg41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final l(Ld41/b;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lg41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/b;->c(Lg41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final m(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LC31/N;

    .line 6
    .line 7
    iget-object p1, p1, LC31/N;->e:Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lg41/a;

    .line 14
    .line 15
    invoke-virtual {v0}, Lg41/a;->g()Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    const/4 v1, 0x1

    .line 20
    invoke-virtual {p1, v0, v1}, Lorg/xbet/uikit_sport/sport_feeds_cell/sport/DsSportFeedsCellSportSmall;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellSportType;Z)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    check-cast p1, LC31/N;

    .line 28
    .line 29
    iget-object p1, p1, LC31/N;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 30
    .line 31
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    check-cast v0, Lg41/a;

    .line 36
    .line 37
    invoke-virtual {v0}, Lg41/a;->i()I

    .line 38
    .line 39
    .line 40
    move-result v0

    .line 41
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIcon(I)V

    .line 42
    .line 43
    .line 44
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    check-cast p1, LC31/N;

    .line 49
    .line 50
    iget-object p1, p1, LC31/N;->b:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 51
    .line 52
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    check-cast v0, Lg41/a;

    .line 57
    .line 58
    invoke-virtual {v0}, Lg41/a;->j()Ljava/lang/Integer;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 63
    .line 64
    .line 65
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 66
    .line 67
    .line 68
    move-result-object p1

    .line 69
    check-cast p1, LC31/N;

    .line 70
    .line 71
    iget-object p1, p1, LC31/N;->c:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 72
    .line 73
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 74
    .line 75
    .line 76
    move-result-object v0

    .line 77
    check-cast v0, Lg41/a;

    .line 78
    .line 79
    invoke-virtual {v0}, Lg41/a;->l()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v0

    .line 83
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleText(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 87
    .line 88
    .line 89
    move-result-object p1

    .line 90
    check-cast p1, LC31/N;

    .line 91
    .line 92
    iget-object p1, p1, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 93
    .line 94
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    move-result-object v0

    .line 98
    check-cast v0, Lg41/a;

    .line 99
    .line 100
    invoke-virtual {v0}, Lg41/a;->f()Z

    .line 101
    .line 102
    .line 103
    move-result v0

    .line 104
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckboxChecked(Z)V

    .line 105
    .line 106
    .line 107
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 108
    .line 109
    .line 110
    move-result-object p1

    .line 111
    check-cast p1, LC31/N;

    .line 112
    .line 113
    iget-object p1, p1, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 114
    .line 115
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 116
    .line 117
    .line 118
    move-result-object v0

    .line 119
    check-cast v0, Lg41/a;

    .line 120
    .line 121
    invoke-virtual {v0}, Lg41/a;->h()I

    .line 122
    .line 123
    .line 124
    move-result v0

    .line 125
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 126
    .line 127
    .line 128
    move-result-object v0

    .line 129
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 130
    .line 131
    .line 132
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    check-cast p1, LC31/N;

    .line 137
    .line 138
    iget-object p1, p1, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 139
    .line 140
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 141
    .line 142
    .line 143
    move-result-object v0

    .line 144
    check-cast v0, Lg41/a;

    .line 145
    .line 146
    invoke-virtual {v0}, Lg41/a;->d()Z

    .line 147
    .line 148
    .line 149
    move-result v0

    .line 150
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionExpanded(Z)V

    .line 151
    .line 152
    .line 153
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 154
    .line 155
    .line 156
    move-result-object p1

    .line 157
    check-cast p1, LC31/N;

    .line 158
    .line 159
    iget-object p1, p1, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 160
    .line 161
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 162
    .line 163
    .line 164
    move-result-object v0

    .line 165
    check-cast v0, Lg41/a;

    .line 166
    .line 167
    invoke-virtual {v0}, Lg41/a;->e()Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 168
    .line 169
    .line 170
    move-result-object v0

    .line 171
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCustomBadgeType(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    .line 172
    .line 173
    .line 174
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 175
    .line 176
    .line 177
    move-result-object p0

    .line 178
    check-cast p0, LC31/N;

    .line 179
    .line 180
    iget-object p0, p0, LC31/N;->d:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 181
    .line 182
    const/4 p1, 0x0

    .line 183
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setTagVisible(Z)V

    .line 184
    .line 185
    .line 186
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 187
    .line 188
    return-object p0
.end method
