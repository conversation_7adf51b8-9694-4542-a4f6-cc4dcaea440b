.class public final Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;
.super Landroid/widget/FrameLayout;
.source "SourceFile"

# interfaces
.implements LY31/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u000b\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u0019\n\u0002\u0018\u0002\n\u0002\u0008\u0006\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0010\u001a\u00020\r2\u0006\u0010\u000c\u001a\u00020\u000bH\u0002\u00a2\u0006\u0004\u0008\u0010\u0010\u000fJ\u001f\u0010\u0013\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u0013\u0010\u0014J7\u0010\u001b\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u00072\u0006\u0010\u001a\u001a\u00020\u0007H\u0014\u00a2\u0006\u0004\u0008\u001b\u0010\u001cJ\u0017\u0010\u001f\u001a\u00020\r2\u0006\u0010\u001e\u001a\u00020\u001dH\u0014\u00a2\u0006\u0004\u0008\u001f\u0010 J\u0017\u0010#\u001a\u00020\r2\u0006\u0010\"\u001a\u00020!H\u0016\u00a2\u0006\u0004\u0008#\u0010$J\u000f\u0010&\u001a\u00020%H\u0002\u00a2\u0006\u0004\u0008&\u0010\'J\u0017\u0010*\u001a\u00020\r2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008*\u0010+J\u0017\u0010,\u001a\u00020\r2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008,\u0010+J\u0017\u0010-\u001a\u00020\r2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008-\u0010+J\u0017\u0010.\u001a\u00020\r2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008.\u0010+J\u001b\u0010/\u001a\u00020\r*\u00020%2\u0006\u0010)\u001a\u00020(H\u0002\u00a2\u0006\u0004\u0008/\u00100J\u000f\u00101\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u00081\u00102J\u000f\u00103\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u00083\u00102J\u000f\u00104\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u00084\u00102J\u000f\u00105\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u00085\u00102J\u0017\u00107\u001a\u00020\r2\u0006\u00106\u001a\u00020\u0007H\u0002\u00a2\u0006\u0004\u00087\u00108J\u000f\u00109\u001a\u00020\rH\u0002\u00a2\u0006\u0004\u00089\u00102J+\u0010=\u001a\u00020\r2\u0006\u0010:\u001a\u00020\u00072\u0008\u0010;\u001a\u0004\u0018\u00010%2\u0008\u0010<\u001a\u0004\u0018\u00010%H\u0002\u00a2\u0006\u0004\u0008=\u0010>J1\u0010D\u001a\u00020\r2\u0006\u0010\u001e\u001a\u00020\u001d2\u0008\u0010@\u001a\u0004\u0018\u00010?2\u0006\u0010B\u001a\u00020A2\u0006\u0010C\u001a\u00020AH\u0002\u00a2\u0006\u0004\u0008D\u0010EJ\u0015\u0010F\u001a\u00020\u0007*\u0004\u0018\u00010?H\u0002\u00a2\u0006\u0004\u0008F\u0010GJ\u0015\u0010H\u001a\u00020\u0007*\u0004\u0018\u00010?H\u0002\u00a2\u0006\u0004\u0008H\u0010GR\u0014\u0010L\u001a\u00020I8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0018\u0010N\u001a\u0004\u0018\u00010%8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00084\u0010MR\u0018\u0010O\u001a\u0004\u0018\u00010%8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00085\u0010MR\u0018\u0010P\u001a\u0004\u0018\u00010%8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00081\u0010MR\u0018\u0010Q\u001a\u0004\u0018\u00010%8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00083\u0010MR\u0014\u0010S\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010RR\u0014\u0010T\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010RR\u0014\u0010U\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010RR\u0014\u0010V\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010RR\u0014\u0010W\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008.\u0010RR\u0014\u0010X\u001a\u00020\u00078\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010RR\u0018\u0010Z\u001a\u0004\u0018\u00010?8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00087\u0010YR\u0018\u0010[\u001a\u0004\u0018\u00010?8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008*\u0010YR\u0016\u0010]\u001a\u00020(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008,\u0010\\R\u0016\u0010^\u001a\u00020(8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008=\u0010\\R\u0016\u0010_\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008/\u0010RR\u0016\u0010`\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u00089\u0010RR\u0016\u0010b\u001a\u00020\u00078\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008a\u0010RR\u001b\u0010h\u001a\u00020c8BX\u0082\u0084\u0002\u00a2\u0006\u000c\n\u0004\u0008d\u0010e\u001a\u0004\u0008f\u0010g\u00a8\u0006i"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;",
        "Landroid/widget/FrameLayout;",
        "LY31/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "LX31/e;",
        "teamLogosUiModel",
        "",
        "setTopTeamLogos",
        "(LX31/e;)V",
        "setBottomTeamLogos",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "left",
        "top",
        "right",
        "bottom",
        "onLayout",
        "(ZIIII)V",
        "Landroid/graphics/Canvas;",
        "canvas",
        "onDraw",
        "(Landroid/graphics/Canvas;)V",
        "LX31/b;",
        "teamsUiModel",
        "setTeamsUiModel",
        "(LX31/b;)V",
        "Lorg/xbet/uikit/components/teamlogo/TeamLogo;",
        "k",
        "()Lorg/xbet/uikit/components/teamlogo/TeamLogo;",
        "",
        "teamLogoUrl",
        "m",
        "(Ljava/lang/String;)V",
        "n",
        "i",
        "j",
        "p",
        "(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V",
        "d",
        "()V",
        "e",
        "b",
        "c",
        "width",
        "l",
        "(I)V",
        "q",
        "teamRowVerticalCenter",
        "firstTeamLogo",
        "secondTeamLogo",
        "o",
        "(ILorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/teamlogo/TeamLogo;)V",
        "Landroid/text/StaticLayout;",
        "textStaticLayout",
        "",
        "positionX",
        "positionY",
        "f",
        "(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V",
        "g",
        "(Landroid/text/StaticLayout;)I",
        "h",
        "Landroid/view/ContextThemeWrapper;",
        "a",
        "Landroid/view/ContextThemeWrapper;",
        "teamLogoContextThemeWrapper",
        "Lorg/xbet/uikit/components/teamlogo/TeamLogo;",
        "topFirstTeamLogo",
        "topSecondTeamLogo",
        "bottomFirstTeamLogo",
        "bottomSecondTeamLogo",
        "I",
        "teamLogosHorizontalBetweenMargin",
        "teamLogosAndNamesBetweenMargin",
        "teamLogoSize",
        "teamRowHeight",
        "totalHeight",
        "topTeamRowVerticalCenter",
        "Landroid/text/StaticLayout;",
        "firstTeamStaticLayout",
        "secondTeamStaticLayout",
        "Ljava/lang/String;",
        "firstTeamName",
        "secondTeamName",
        "teamNameStartPosition",
        "firstTeamNameTopPosition",
        "r",
        "teamLogosWidth",
        "Landroid/text/TextPaint;",
        "s",
        "Lkotlin/j;",
        "getTextTextPaint",
        "()Landroid/text/TextPaint;",
        "textTextPaint",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field public final a:Landroid/view/ContextThemeWrapper;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

.field public c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

.field public d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

.field public e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:I

.field public l:Landroid/text/StaticLayout;

.field public m:Landroid/text/StaticLayout;

.field public n:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public o:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public p:I

.field public q:I

.field public r:I

.field public final s:Lkotlin/j;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 4
    invoke-direct {p0, p1, p2, p3}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 5
    new-instance p2, Landroid/view/ContextThemeWrapper;

    .line 6
    sget p3, LlZ0/n;->Widget_TeamLogo_Size24:I

    .line 7
    invoke-direct {p2, p1, p3}, Landroid/view/ContextThemeWrapper;-><init>(Landroid/content/Context;I)V

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->a:Landroid/view/ContextThemeWrapper;

    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_2:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->f:I

    .line 9
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_8:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->g:I

    .line 10
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->size_24:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 11
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget p3, LlZ0/g;->space_32:I

    invoke-virtual {p2, p3}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->i:I

    mul-int/lit8 p3, p2, 0x2

    .line 12
    iput p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->j:I

    .line 13
    div-int/lit8 p2, p2, 0x2

    iput p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->k:I

    .line 14
    const-string p2, ""

    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->n:Ljava/lang/String;

    .line 15
    iput-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->o:Ljava/lang/String;

    .line 16
    new-instance p2, LY31/b;

    invoke-direct {p2, p1}, LY31/b;-><init>(Landroid/content/Context;)V

    invoke-static {p2}, Lkotlin/k;->b(Lkotlin/jvm/functions/Function0;)Lkotlin/j;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->s:Lkotlin/j;

    const/4 p1, 0x0

    .line 17
    invoke-virtual {p0, p1}, Landroid/view/View;->setWillNotDraw(Z)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    const/4 p3, 0x0

    .line 3
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method

.method public static synthetic a(Landroid/content/Context;)Landroid/text/TextPaint;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->r(Landroid/content/Context;)Landroid/text/TextPaint;

    move-result-object p0

    return-object p0
.end method

.method private final getTextTextPaint()Landroid/text/TextPaint;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->s:Lkotlin/j;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/j;->getValue()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/text/TextPaint;

    .line 8
    .line 9
    return-object v0
.end method

.method public static final r(Landroid/content/Context;)Landroid/text/TextPaint;
    .locals 2

    .line 1
    new-instance v0, Landroid/text/TextPaint;

    .line 2
    .line 3
    invoke-direct {v0}, Landroid/text/TextPaint;-><init>()V

    .line 4
    .line 5
    .line 6
    const/4 v1, 0x1

    .line 7
    invoke-virtual {v0, v1}, Landroid/graphics/Paint;->setAntiAlias(Z)V

    .line 8
    .line 9
    .line 10
    sget v1, LlZ0/n;->TextStyle_Caption_Medium_L_TextPrimary:I

    .line 11
    .line 12
    invoke-static {v0, p0, v1}, Lorg/xbet/uikit/utils/D;->b(Landroid/text/TextPaint;Landroid/content/Context;I)V

    .line 13
    .line 14
    .line 15
    return-object v0
.end method

.method private final setBottomTeamLogos(LX31/e;)V
    .locals 1

    .line 1
    sget-object v0, LX31/e$a;->a:LX31/e$a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c()V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    instance-of v0, p1, LX31/e$b;

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    check-cast p1, LX31/e$b;

    .line 21
    .line 22
    invoke-virtual {p1}, LX31/e$b;->a()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->i(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/e$b;->b()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->j(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_1
    instance-of v0, p1, LX31/e$c;

    .line 38
    .line 39
    if-eqz v0, :cond_2

    .line 40
    .line 41
    check-cast p1, LX31/e$c;

    .line 42
    .line 43
    invoke-virtual {p1}, LX31/e$c;->a()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->i(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c()V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 55
    .line 56
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 57
    .line 58
    .line 59
    throw p1
.end method

.method private final setTopTeamLogos(LX31/e;)V
    .locals 1

    .line 1
    sget-object v0, LX31/e$a;->a:LX31/e$a;

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->e(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e()V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :cond_0
    instance-of v0, p1, LX31/e$b;

    .line 17
    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    check-cast p1, LX31/e$b;

    .line 21
    .line 22
    invoke-virtual {p1}, LX31/e$b;->a()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    invoke-virtual {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->m(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {p1}, LX31/e$b;->b()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->n(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    return-void

    .line 37
    :cond_1
    instance-of v0, p1, LX31/e$c;

    .line 38
    .line 39
    if-eqz v0, :cond_2

    .line 40
    .line 41
    check-cast p1, LX31/e$c;

    .line 42
    .line 43
    invoke-virtual {p1}, LX31/e$c;->a()Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->m(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e()V

    .line 51
    .line 52
    .line 53
    return-void

    .line 54
    :cond_2
    new-instance p1, Lkotlin/NoWhenBranchMatchedException;

    .line 55
    .line 56
    invoke-direct {p1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    .line 57
    .line 58
    .line 59
    throw p1
.end method


# virtual methods
.method public final b()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final c()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final d()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final e()V
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-virtual {v0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 12
    .line 13
    .line 14
    :cond_0
    const/4 v0, 0x0

    .line 15
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    :cond_1
    return-void
.end method

.method public final f(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V
    .locals 2

    .line 1
    invoke-virtual {p0}, Landroid/view/View;->getLayoutDirection()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    if-ne v0, v1, :cond_0

    .line 7
    .line 8
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h(Landroid/text/StaticLayout;)I

    .line 13
    .line 14
    .line 15
    move-result v1

    .line 16
    sub-int/2addr v0, v1

    .line 17
    int-to-float v0, v0

    .line 18
    sub-float p3, v0, p3

    .line 19
    .line 20
    :cond_0
    invoke-virtual {p1}, Landroid/graphics/Canvas;->save()I

    .line 21
    .line 22
    .line 23
    invoke-virtual {p1, p3, p4}, Landroid/graphics/Canvas;->translate(FF)V

    .line 24
    .line 25
    .line 26
    if-eqz p2, :cond_1

    .line 27
    .line 28
    invoke-virtual {p2, p1}, Landroid/text/Layout;->draw(Landroid/graphics/Canvas;)V

    .line 29
    .line 30
    .line 31
    :cond_1
    invoke-virtual {p1}, Landroid/graphics/Canvas;->restore()V

    .line 32
    .line 33
    .line 34
    return-void
.end method

.method public final g(Landroid/text/StaticLayout;)I
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/text/Layout;->getHeight()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    return p1
.end method

.method public final h(Landroid/text/StaticLayout;)I
    .locals 0

    .line 1
    if-eqz p1, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/text/Layout;->getWidth()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    return p1
.end method

.method public final i(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->k()Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public final j(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->k()Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public final k()Lorg/xbet/uikit/components/teamlogo/TeamLogo;
    .locals 4

    .line 1
    new-instance v0, Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->a:Landroid/view/ContextThemeWrapper;

    .line 4
    .line 5
    const/4 v2, 0x0

    .line 6
    const/4 v3, 0x2

    .line 7
    invoke-direct {v0, v1, v2, v3, v2}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method

.method public final l(I)V
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget-object v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->n:Ljava/lang/String;

    .line 4
    .line 5
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    const/4 v2, 0x0

    .line 10
    if-lez v1, :cond_0

    .line 11
    .line 12
    iget-object v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->n:Ljava/lang/String;

    .line 13
    .line 14
    invoke-static {v1}, Lorg/xbet/uikit/utils/g;->a(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 15
    .line 16
    .line 17
    move-result-object v3

    .line 18
    invoke-direct {v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->getTextTextPaint()Landroid/text/TextPaint;

    .line 19
    .line 20
    .line 21
    move-result-object v4

    .line 22
    sget-object v12, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 23
    .line 24
    const/16 v15, 0xdf0

    .line 25
    .line 26
    const/16 v16, 0x0

    .line 27
    .line 28
    const/4 v6, 0x2

    .line 29
    const/4 v7, 0x0

    .line 30
    const/4 v8, 0x0

    .line 31
    const/4 v9, 0x0

    .line 32
    const/4 v10, 0x0

    .line 33
    const/4 v11, 0x0

    .line 34
    const/4 v13, 0x0

    .line 35
    const/4 v14, 0x0

    .line 36
    move/from16 v5, p1

    .line 37
    .line 38
    invoke-static/range {v3 .. v16}, Lorg/xbet/uikit/utils/H;->d(Ljava/lang/CharSequence;Landroid/text/TextPaint;IIIIFFZLandroid/text/TextUtils$TruncateAt;ILandroid/text/Layout$Alignment;ILjava/lang/Object;)Landroid/text/StaticLayout;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    goto :goto_0

    .line 43
    :cond_0
    move-object v1, v2

    .line 44
    :goto_0
    iput-object v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->l:Landroid/text/StaticLayout;

    .line 45
    .line 46
    iget-object v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->o:Ljava/lang/String;

    .line 47
    .line 48
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 49
    .line 50
    .line 51
    move-result v1

    .line 52
    if-lez v1, :cond_1

    .line 53
    .line 54
    iget-object v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->o:Ljava/lang/String;

    .line 55
    .line 56
    invoke-static {v1}, Lorg/xbet/uikit/utils/g;->a(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    .line 57
    .line 58
    .line 59
    move-result-object v3

    .line 60
    invoke-direct {v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->getTextTextPaint()Landroid/text/TextPaint;

    .line 61
    .line 62
    .line 63
    move-result-object v4

    .line 64
    sget-object v12, Landroid/text/TextUtils$TruncateAt;->END:Landroid/text/TextUtils$TruncateAt;

    .line 65
    .line 66
    const/16 v15, 0xdf0

    .line 67
    .line 68
    const/16 v16, 0x0

    .line 69
    .line 70
    const/4 v6, 0x2

    .line 71
    const/4 v7, 0x0

    .line 72
    const/4 v8, 0x0

    .line 73
    const/4 v9, 0x0

    .line 74
    const/4 v10, 0x0

    .line 75
    const/4 v11, 0x0

    .line 76
    const/4 v13, 0x0

    .line 77
    const/4 v14, 0x0

    .line 78
    move/from16 v5, p1

    .line 79
    .line 80
    invoke-static/range {v3 .. v16}, Lorg/xbet/uikit/utils/H;->d(Ljava/lang/CharSequence;Landroid/text/TextPaint;IIIIFFZLandroid/text/TextUtils$TruncateAt;ILandroid/text/Layout$Alignment;ILjava/lang/Object;)Landroid/text/StaticLayout;

    .line 81
    .line 82
    .line 83
    move-result-object v2

    .line 84
    :cond_1
    iput-object v2, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->m:Landroid/text/StaticLayout;

    .line 85
    .line 86
    iget v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->r:I

    .line 87
    .line 88
    iput v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p:I

    .line 89
    .line 90
    iget v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->i:I

    .line 91
    .line 92
    div-int/lit8 v1, v1, 0x2

    .line 93
    .line 94
    iget-object v2, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->l:Landroid/text/StaticLayout;

    .line 95
    .line 96
    invoke-virtual {v0, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->g(Landroid/text/StaticLayout;)I

    .line 97
    .line 98
    .line 99
    move-result v2

    .line 100
    div-int/lit8 v2, v2, 0x2

    .line 101
    .line 102
    sub-int/2addr v1, v2

    .line 103
    iput v1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->q:I

    .line 104
    .line 105
    return-void
.end method

.method public final m(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->k()Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public final n(Ljava/lang/String;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->k()Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 10
    .line 11
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 12
    .line 13
    if-eqz v0, :cond_1

    .line 14
    .line 15
    invoke-virtual {p0, v0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    :cond_1
    return-void
.end method

.method public final o(ILorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/teamlogo/TeamLogo;)V
    .locals 11

    .line 1
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 2
    .line 3
    div-int/lit8 v0, v4, 0x2

    .line 4
    .line 5
    sub-int v3, p1, v0

    .line 6
    .line 7
    if-eqz p2, :cond_0

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    add-int v5, v3, v4

    .line 11
    .line 12
    move-object v0, p0

    .line 13
    move-object v1, p2

    .line 14
    invoke-static/range {v0 .. v5}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 15
    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    move-object v0, p0

    .line 19
    :goto_0
    if-eqz p3, :cond_1

    .line 20
    .line 21
    iget p1, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 22
    .line 23
    iget p2, v0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->f:I

    .line 24
    .line 25
    add-int v7, p1, p2

    .line 26
    .line 27
    add-int v9, v7, p1

    .line 28
    .line 29
    add-int v10, v3, p1

    .line 30
    .line 31
    move-object v6, p3

    .line 32
    move-object v5, v0

    .line 33
    move v8, v3

    .line 34
    invoke-static/range {v5 .. v10}, Lorg/xbet/uikit/utils/S;->i(Landroid/view/ViewGroup;Landroid/view/View;IIII)V

    .line 35
    .line 36
    .line 37
    :cond_1
    return-void
.end method

.method public onDraw(Landroid/graphics/Canvas;)V
    .locals 3
    .param p1    # Landroid/graphics/Canvas;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->l:Landroid/text/StaticLayout;

    .line 2
    .line 3
    iget v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p:I

    .line 4
    .line 5
    int-to-float v1, v1

    .line 6
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->q:I

    .line 7
    .line 8
    int-to-float v2, v2

    .line 9
    invoke-virtual {p0, p1, v0, v1, v2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->f(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V

    .line 10
    .line 11
    .line 12
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->i:I

    .line 13
    .line 14
    div-int/lit8 v1, v0, 0x2

    .line 15
    .line 16
    add-int/2addr v0, v1

    .line 17
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->m:Landroid/text/StaticLayout;

    .line 18
    .line 19
    invoke-virtual {p0, v1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->g(Landroid/text/StaticLayout;)I

    .line 20
    .line 21
    .line 22
    move-result v1

    .line 23
    div-int/lit8 v1, v1, 0x2

    .line 24
    .line 25
    sub-int/2addr v0, v1

    .line 26
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->m:Landroid/text/StaticLayout;

    .line 27
    .line 28
    iget v2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->p:I

    .line 29
    .line 30
    int-to-float v2, v2

    .line 31
    int-to-float v0, v0

    .line 32
    invoke-virtual {p0, p1, v1, v2, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->f(Landroid/graphics/Canvas;Landroid/text/StaticLayout;FF)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->k:I

    .line 2
    .line 3
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 4
    .line 5
    iget-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 6
    .line 7
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->o(ILorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/teamlogo/TeamLogo;)V

    .line 8
    .line 9
    .line 10
    iget p1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->i:I

    .line 11
    .line 12
    div-int/lit8 p2, p1, 0x2

    .line 13
    .line 14
    add-int/2addr p1, p2

    .line 15
    iget-object p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 16
    .line 17
    iget-object p3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 18
    .line 19
    invoke-virtual {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->o(ILorg/xbet/uikit/components/teamlogo/TeamLogo;Lorg/xbet/uikit/components/teamlogo/TeamLogo;)V

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public onMeasure(II)V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->q()V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 5
    .line 6
    .line 7
    move-result p2

    .line 8
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->r:I

    .line 9
    .line 10
    sub-int/2addr p2, v0

    .line 11
    invoke-virtual {p0, p2}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->l(I)V

    .line 12
    .line 13
    .line 14
    iget p2, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->j:I

    .line 15
    .line 16
    const/high16 v0, 0x40000000    # 2.0f

    .line 17
    .line 18
    invoke-static {p2, v0}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 19
    .line 20
    .line 21
    move-result p2

    .line 22
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 23
    .line 24
    .line 25
    return-void
.end method

.method public final p(Lorg/xbet/uikit/components/teamlogo/TeamLogo;Ljava/lang/String;)V
    .locals 8

    .line 1
    invoke-interface {p2}, Ljava/lang/CharSequence;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-lez v0, :cond_0

    .line 6
    .line 7
    const/16 v6, 0xe

    .line 8
    .line 9
    const/4 v7, 0x0

    .line 10
    const/4 v3, 0x0

    .line 11
    const/4 v4, 0x0

    .line 12
    const/4 v5, 0x0

    .line 13
    move-object v1, p1

    .line 14
    move-object v2, p2

    .line 15
    invoke-static/range {v1 .. v7}, Lorg/xbet/uikit/components/views/LoadableShapeableImageView;->P(Lorg/xbet/uikit/components/views/LoadableShapeableImageView;Ljava/lang/String;Landroid/graphics/drawable/Drawable;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V

    .line 16
    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    move-object v1, p1

    .line 20
    invoke-virtual {v1}, Landroid/view/View;->getContext()Landroid/content/Context;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    sget p2, LlZ0/h;->ic_glyph_placeholder_player_secondary60:I

    .line 25
    .line 26
    invoke-static {p1, p2}, LF0/b;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    invoke-virtual {v1, p1}, Lorg/xbet/uikit/components/teamlogo/TeamLogo;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 31
    .line 32
    .line 33
    return-void
.end method

.method public final q()V
    .locals 7

    .line 1
    iget v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 2
    .line 3
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget-object v1, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v0, v2

    .line 14
    :goto_0
    const/4 v1, 0x0

    .line 15
    if-eqz v0, :cond_1

    .line 16
    .line 17
    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    goto :goto_1

    .line 22
    :cond_1
    const/4 v0, 0x0

    .line 23
    :goto_1
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 24
    .line 25
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->f:I

    .line 26
    .line 27
    add-int/2addr v3, v4

    .line 28
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 29
    .line 30
    .line 31
    move-result-object v3

    .line 32
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 33
    .line 34
    if-eqz v4, :cond_2

    .line 35
    .line 36
    goto :goto_2

    .line 37
    :cond_2
    move-object v3, v2

    .line 38
    :goto_2
    if-eqz v3, :cond_3

    .line 39
    .line 40
    invoke-virtual {v3}, Ljava/lang/Integer;->intValue()I

    .line 41
    .line 42
    .line 43
    move-result v3

    .line 44
    goto :goto_3

    .line 45
    :cond_3
    const/4 v3, 0x0

    .line 46
    :goto_3
    iget v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 47
    .line 48
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 49
    .line 50
    .line 51
    move-result-object v4

    .line 52
    iget-object v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 53
    .line 54
    if-eqz v5, :cond_4

    .line 55
    .line 56
    goto :goto_4

    .line 57
    :cond_4
    move-object v4, v2

    .line 58
    :goto_4
    if-eqz v4, :cond_5

    .line 59
    .line 60
    invoke-virtual {v4}, Ljava/lang/Integer;->intValue()I

    .line 61
    .line 62
    .line 63
    move-result v4

    .line 64
    goto :goto_5

    .line 65
    :cond_5
    const/4 v4, 0x0

    .line 66
    :goto_5
    iget v5, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->h:I

    .line 67
    .line 68
    iget v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->f:I

    .line 69
    .line 70
    add-int/2addr v5, v6

    .line 71
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 72
    .line 73
    .line 74
    move-result-object v5

    .line 75
    iget-object v6, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 76
    .line 77
    if-eqz v6, :cond_6

    .line 78
    .line 79
    goto :goto_6

    .line 80
    :cond_6
    move-object v5, v2

    .line 81
    :goto_6
    if-eqz v5, :cond_7

    .line 82
    .line 83
    invoke-virtual {v5}, Ljava/lang/Integer;->intValue()I

    .line 84
    .line 85
    .line 86
    move-result v5

    .line 87
    goto :goto_7

    .line 88
    :cond_7
    const/4 v5, 0x0

    .line 89
    :goto_7
    add-int/2addr v0, v3

    .line 90
    add-int/2addr v4, v5

    .line 91
    invoke-static {v0, v4}, Ljava/lang/Math;->max(II)I

    .line 92
    .line 93
    .line 94
    move-result v0

    .line 95
    iget v3, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->g:I

    .line 96
    .line 97
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 98
    .line 99
    .line 100
    move-result-object v3

    .line 101
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->b:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 102
    .line 103
    if-nez v4, :cond_8

    .line 104
    .line 105
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->c:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 106
    .line 107
    if-nez v4, :cond_8

    .line 108
    .line 109
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->d:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 110
    .line 111
    if-nez v4, :cond_8

    .line 112
    .line 113
    iget-object v4, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->e:Lorg/xbet/uikit/components/teamlogo/TeamLogo;

    .line 114
    .line 115
    if-eqz v4, :cond_9

    .line 116
    .line 117
    :cond_8
    move-object v2, v3

    .line 118
    :cond_9
    if-eqz v2, :cond_a

    .line 119
    .line 120
    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    .line 121
    .line 122
    .line 123
    move-result v1

    .line 124
    :cond_a
    add-int/2addr v0, v1

    .line 125
    iput v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->r:I

    .line 126
    .line 127
    return-void
.end method

.method public setTeamsUiModel(LX31/b;)V
    .locals 1
    .param p1    # LX31/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    instance-of v0, p1, LX31/b$c;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    check-cast p1, LX31/b$c;

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    const/4 p1, 0x0

    .line 9
    :goto_0
    if-nez p1, :cond_1

    .line 10
    .line 11
    return-void

    .line 12
    :cond_1
    invoke-virtual {p1}, LX31/b$c;->b()Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->n:Ljava/lang/String;

    .line 17
    .line 18
    invoke-virtual {p1}, LX31/b$c;->d()Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    iput-object v0, p0, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->o:Ljava/lang/String;

    .line 23
    .line 24
    invoke-virtual {p1}, LX31/b$c;->a()LX31/e;

    .line 25
    .line 26
    .line 27
    move-result-object v0

    .line 28
    invoke-direct {p0, v0}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->setTopTeamLogos(LX31/e;)V

    .line 29
    .line 30
    .line 31
    invoke-virtual {p1}, LX31/b$c;->c()LX31/e;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    invoke-direct {p0, p1}, Lorg/xbet/uikit_sport/sport_coupon_card/teams_middle_views/LeftSideTeamsView;->setBottomTeamLogos(LX31/e;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {p0}, Landroid/view/View;->requestLayout()V

    .line 39
    .line 40
    .line 41
    return-void
.end method
