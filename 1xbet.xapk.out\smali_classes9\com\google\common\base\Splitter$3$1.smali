.class Lcom/google/common/base/Splitter$3$1;
.super Lcom/google/common/base/Splitter$SplittingIterator;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/google/common/base/Splitter$3;->b(Lcom/google/common/base/Splitter;Ljava/lang/CharSequence;)Lcom/google/common/base/Splitter$SplittingIterator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic h:Lcom/google/common/base/CommonMatcher;

.field public final synthetic i:Lcom/google/common/base/Splitter$3;


# direct methods
.method public constructor <init>(Lcom/google/common/base/Splitter$3;Lcom/google/common/base/Splitter;Ljava/lang/CharSequence;Lcom/google/common/base/CommonMatcher;)V
    .locals 0

    .line 1
    iput-object p4, p0, Lcom/google/common/base/Splitter$3$1;->h:Lcom/google/common/base/CommonMatcher;

    .line 2
    .line 3
    iput-object p1, p0, Lcom/google/common/base/Splitter$3$1;->i:Lcom/google/common/base/Splitter$3;

    .line 4
    .line 5
    invoke-direct {p0, p2, p3}, Lcom/google/common/base/Splitter$SplittingIterator;-><init>(Lcom/google/common/base/Splitter;Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public f(I)I
    .locals 0

    .line 1
    iget-object p1, p0, Lcom/google/common/base/Splitter$3$1;->h:Lcom/google/common/base/CommonMatcher;

    .line 2
    .line 3
    invoke-virtual {p1}, Lcom/google/common/base/CommonMatcher;->a()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public g(I)I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/google/common/base/Splitter$3$1;->h:Lcom/google/common/base/CommonMatcher;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lcom/google/common/base/CommonMatcher;->c(I)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    if-eqz p1, :cond_0

    .line 8
    .line 9
    iget-object p1, p0, Lcom/google/common/base/Splitter$3$1;->h:Lcom/google/common/base/CommonMatcher;

    .line 10
    .line 11
    invoke-virtual {p1}, Lcom/google/common/base/CommonMatcher;->e()I

    .line 12
    .line 13
    .line 14
    move-result p1

    .line 15
    return p1

    .line 16
    :cond_0
    const/4 p1, -0x1

    .line 17
    return p1
.end method
