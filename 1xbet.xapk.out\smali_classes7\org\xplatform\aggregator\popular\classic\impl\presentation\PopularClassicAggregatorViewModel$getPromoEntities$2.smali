.class final Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;
.super Lkotlin/coroutines/jvm/internal/SuspendLambda;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation runtime LHc/d;
    c = "org.xplatform.aggregator.popular.classic.impl.presentation.PopularClassicAggregatorViewModel$getPromoEntities$2"
    f = "PopularClassicAggregatorViewModel.kt"
    l = {
        0x24c
    }
    m = "invokeSuspend"
.end annotation

.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->x4(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$RequestType;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/coroutines/jvm/internal/SuspendLambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lkotlinx/coroutines/N;",
        "Lkotlin/coroutines/e<",
        "-",
        "Lkotlin/Unit;",
        ">;",
        "Ljava/lang/Object;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\n\u00a2\u0006\u0004\u0008\u0002\u0010\u0003"
    }
    d2 = {
        "Lkotlinx/coroutines/N;",
        "",
        "<anonymous>",
        "(Lkotlinx/coroutines/N;)V"
    }
    k = 0x3
    mv = {
        0x2,
        0x1,
        0x0
    }
.end annotation


# instance fields
.field label:I

.field final synthetic this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;


# direct methods
.method public constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    const/4 p1, 0x2

    invoke-direct {p0, p1, p2}, Lkotlin/coroutines/jvm/internal/SuspendLambda;-><init>(ILkotlin/coroutines/e;)V

    return-void
.end method


# virtual methods
.method public final create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Lkotlin/coroutines/e<",
            "*>;)",
            "Lkotlin/coroutines/e<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    new-instance p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;

    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    invoke-direct {p1, v0, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;-><init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Lkotlin/coroutines/e;)V

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lkotlinx/coroutines/N;

    check-cast p2, Lkotlin/coroutines/e;

    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invoke(Lkotlinx/coroutines/N;Lkotlin/coroutines/e;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlinx/coroutines/N;",
            "Lkotlin/coroutines/e<",
            "-",
            "Lkotlin/Unit;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 2
    invoke-virtual {p0, p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->create(Ljava/lang/Object;Lkotlin/coroutines/e;)Lkotlin/coroutines/e;

    move-result-object p1

    check-cast p1, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;

    sget-object p2, Lkotlin/Unit;->a:Lkotlin/Unit;

    invoke-virtual {p1, p2}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 9

    .line 1
    invoke-static {}, Lkotlin/coroutines/intrinsics/a;->g()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->label:I

    .line 6
    .line 7
    const/4 v2, 0x1

    .line 8
    if-eqz v1, :cond_1

    .line 9
    .line 10
    if-ne v1, v2, :cond_0

    .line 11
    .line 12
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    move-object v8, p0

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 18
    .line 19
    const-string v0, "call to \'resume\' before \'invoke\' with coroutine"

    .line 20
    .line 21
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 22
    .line 23
    .line 24
    throw p1

    .line 25
    :cond_1
    invoke-static {p1}, Lkotlin/n;->b(Ljava/lang/Object;)V

    .line 26
    .line 27
    .line 28
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 29
    .line 30
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->M3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;

    .line 31
    .line 32
    .line 33
    move-result-object v3

    .line 34
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 35
    .line 36
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-interface {p1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    invoke-virtual {p1}, Lek0/o;->o()Lek0/a;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-virtual {p1}, Lek0/a;->c()Z

    .line 49
    .line 50
    .line 51
    move-result v5

    .line 52
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 53
    .line 54
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-interface {p1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 59
    .line 60
    .line 61
    move-result-object p1

    .line 62
    invoke-virtual {p1}, Lek0/o;->o()Lek0/a;

    .line 63
    .line 64
    .line 65
    move-result-object p1

    .line 66
    invoke-virtual {p1}, Lek0/a;->k()Z

    .line 67
    .line 68
    .line 69
    move-result v6

    .line 70
    iget-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 71
    .line 72
    invoke-static {p1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->S3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lorg/xbet/remoteconfig/domain/usecases/i;

    .line 73
    .line 74
    .line 75
    move-result-object p1

    .line 76
    invoke-interface {p1}, Lorg/xbet/remoteconfig/domain/usecases/i;->invoke()Lek0/o;

    .line 77
    .line 78
    .line 79
    move-result-object p1

    .line 80
    invoke-virtual {p1}, Lek0/o;->o()Lek0/a;

    .line 81
    .line 82
    .line 83
    move-result-object p1

    .line 84
    invoke-virtual {p1}, Lek0/a;->d()Z

    .line 85
    .line 86
    .line 87
    move-result v7

    .line 88
    iput v2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->label:I

    .line 89
    .line 90
    const/4 v4, 0x1

    .line 91
    move-object v8, p0

    .line 92
    invoke-virtual/range {v3 .. v8}, Lorg/xplatform/aggregator/popular/classic/impl/domain/usecases/f;->a(ZZZZLkotlin/coroutines/e;)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object p1

    .line 96
    if-ne p1, v0, :cond_2

    .line 97
    .line 98
    return-object v0

    .line 99
    :cond_2
    :goto_0
    iget-object v0, v8, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel$getPromoEntities$2;->this$0:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    .line 100
    .line 101
    check-cast p1, LBb1/a;

    .line 102
    .line 103
    invoke-static {v0}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->R3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;)Lkotlinx/coroutines/flow/V;

    .line 104
    .line 105
    .line 106
    move-result-object v0

    .line 107
    invoke-virtual {p1}, LBb1/a;->b()LBb1/c;

    .line 108
    .line 109
    .line 110
    move-result-object v1

    .line 111
    invoke-virtual {v1}, LBb1/c;->a()Z

    .line 112
    .line 113
    .line 114
    move-result v1

    .line 115
    if-nez v1, :cond_3

    .line 116
    .line 117
    invoke-virtual {p1}, LBb1/a;->c()LBb1/b;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    invoke-virtual {v1}, LBb1/b;->b()Z

    .line 122
    .line 123
    .line 124
    move-result v1

    .line 125
    if-nez v1, :cond_3

    .line 126
    .line 127
    invoke-virtual {p1}, LBb1/a;->a()LBb1/b;

    .line 128
    .line 129
    .line 130
    move-result-object v1

    .line 131
    invoke-virtual {v1}, LBb1/b;->b()Z

    .line 132
    .line 133
    .line 134
    move-result v1

    .line 135
    if-nez v1, :cond_3

    .line 136
    .line 137
    sget-object p1, LKb1/a$a;->a:LKb1/a$a;

    .line 138
    .line 139
    goto :goto_1

    .line 140
    :cond_3
    new-instance v1, LKb1/a$c;

    .line 141
    .line 142
    invoke-direct {v1, p1}, LKb1/a$c;-><init>(Ljava/lang/Object;)V

    .line 143
    .line 144
    .line 145
    move-object p1, v1

    .line 146
    :goto_1
    invoke-interface {v0, p1}, Lkotlinx/coroutines/flow/V;->setValue(Ljava/lang/Object;)V

    .line 147
    .line 148
    .line 149
    sget-object p1, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 150
    .line 151
    return-object p1
.end method
