.class public final Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u0008\u0086\u0003\u0018\u00002\u00020\u0001B\t\u0008\u0002\u00a2\u0006\u0004\u0008\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\u0004\u0008\u0007\u0010\u0008\u00a8\u0006\t"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType$a;",
        "",
        "<init>",
        "()V",
        "",
        "value",
        "Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;",
        "a",
        "(I)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(I)Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;
    .locals 3
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-static {}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;->getEntries()Lkotlin/enums/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-eqz v1, :cond_1

    .line 14
    .line 15
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    move-object v2, v1

    .line 20
    check-cast v2, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;

    .line 21
    .line 22
    invoke-virtual {v2}, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;->getValue()I

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-ne v2, p1, :cond_0

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_1
    const/4 v1, 0x0

    .line 30
    :goto_0
    check-cast v1, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;

    .line 31
    .line 32
    if-nez v1, :cond_2

    .line 33
    .line 34
    sget-object p1, Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;->LEFT:Lorg/xbet/uikit_sport/victoryindiacator/SportVictoryIndicator$IndicatorType;

    .line 35
    .line 36
    return-object p1

    .line 37
    :cond_2
    return-object v1
.end method
