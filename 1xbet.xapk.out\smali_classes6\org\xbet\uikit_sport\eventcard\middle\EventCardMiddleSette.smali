.class public final Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;
.super Landroidx/constraintlayout/widget/ConstraintLayout;
.source "SourceFile"

# interfaces
.implements Lorg/xbet/uikit_sport/eventcard/middle/a;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0002\u0008\u0003\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\n\u0008\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\'\u0008\u0007\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\u0008\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\u0007\u00a2\u0006\u0004\u0008\t\u0010\nJ\u0017\u0010\u000e\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u000e\u0010\u000fJ\u0017\u0010\u0010\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0010\u0010\u000fJ\u0017\u0010\u0011\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0011\u0010\u000fJ\u0015\u0010\u0014\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u0017\u0010\u0016\u001a\u00020\r2\u0008\u0010\u000c\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\u0008\u0016\u0010\u000fJ\u001b\u0010\u001a\u001a\u00020\r2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u0017\u00a2\u0006\u0004\u0008\u001a\u0010\u001bJ\u001b\u0010\u001c\u001a\u00020\r2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u0017\u00a2\u0006\u0004\u0008\u001c\u0010\u001bJ\u001b\u0010\u001d\u001a\u00020\r2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u0017\u00a2\u0006\u0004\u0008\u001d\u0010\u001bJ\u001b\u0010\u001e\u001a\u00020\r2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u0017\u00a2\u0006\u0004\u0008\u001e\u0010\u001bJ\r\u0010 \u001a\u00020\u001f\u00a2\u0006\u0004\u0008 \u0010!J+\u0010$\u001a\u00020\r2\u000c\u0010\u0019\u001a\u0008\u0012\u0004\u0012\u00020\u00180\u00172\u000c\u0010#\u001a\u0008\u0012\u0004\u0012\u00020\"0\u0017H\u0002\u00a2\u0006\u0004\u0008$\u0010%R\u0014\u0010(\u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008&\u0010\'R\u001f\u0010,\u001a\r\u0012\t\u0012\u00070\"\u00a2\u0006\u0002\u0008)0\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008*\u0010+R\u001f\u0010.\u001a\r\u0012\t\u0012\u00070\"\u00a2\u0006\u0002\u0008)0\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008-\u0010+R\u001f\u00100\u001a\r\u0012\t\u0012\u00070\"\u00a2\u0006\u0002\u0008)0\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008/\u0010+R\u001f\u00102\u001a\r\u0012\t\u0012\u00070\"\u00a2\u0006\u0002\u0008)0\u00178\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u00081\u0010+\u00a8\u00063"
    }
    d2 = {
        "Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;",
        "Landroidx/constraintlayout/widget/ConstraintLayout;",
        "Lorg/xbet/uikit_sport/eventcard/middle/a;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "",
        "defStyleAttr",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;I)V",
        "",
        "text",
        "",
        "setPlayerName",
        "(Ljava/lang/CharSequence;)V",
        "setDealerName",
        "setInformation",
        "Lorg/xbet/uikit_sport/score/a;",
        "scoreModel",
        "setScoreModel",
        "(Lorg/xbet/uikit_sport/score/a;)V",
        "setWinInformation",
        "",
        "LJ11/K;",
        "cards",
        "setPlayer1RowCards",
        "(Ljava/util/List;)V",
        "setPlayer2RowCards",
        "setDealer1RowCards",
        "setDealer2RowCards",
        "LC31/A;",
        "getBinding",
        "()LC31/A;",
        "Landroid/widget/ImageView;",
        "imageViews",
        "s",
        "(Ljava/util/List;Ljava/util/List;)V",
        "a",
        "LC31/A;",
        "binding",
        "Lkotlin/jvm/internal/EnhancedNullability;",
        "b",
        "Ljava/util/List;",
        "player1RowCardViews",
        "c",
        "player2RowCardViews",
        "d",
        "dealer1RowCardViews",
        "e",
        "dealer2RowCardViews",
        "uikit_sport_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# instance fields
.field public final a:LC31/A;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Landroid/widget/ImageView;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v4, 0x6

    const/4 v5, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 6
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 2
    const/4 v4, 0x4

    const/4 v5, 0x0

    const/4 v3, 0x0

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    invoke-direct/range {v0 .. v5}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 10
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 5
    invoke-direct {p0, p1, p2, p3}, Landroidx/constraintlayout/widget/ConstraintLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    .line 6
    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    invoke-static {p1, p0}, LC31/A;->b(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/A;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    const/4 p2, 0x0

    .line 7
    invoke-virtual {p0, p2}, Landroid/view/View;->setLayoutDirection(I)V

    .line 8
    iget-object p3, p1, LC31/A;->n:Landroid/widget/ImageView;

    .line 9
    iget-object v0, p1, LC31/A;->o:Landroid/widget/ImageView;

    .line 10
    iget-object v1, p1, LC31/A;->p:Landroid/widget/ImageView;

    .line 11
    iget-object v2, p1, LC31/A;->q:Landroid/widget/ImageView;

    .line 12
    iget-object v3, p1, LC31/A;->r:Landroid/widget/ImageView;

    const/4 v4, 0x5

    new-array v5, v4, [Landroid/widget/ImageView;

    aput-object p3, v5, p2

    const/4 p3, 0x1

    aput-object v0, v5, p3

    const/4 v0, 0x2

    aput-object v1, v5, v0

    const/4 v1, 0x3

    aput-object v2, v5, v1

    const/4 v2, 0x4

    aput-object v3, v5, v2

    .line 13
    invoke-static {v5}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    iput-object v3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->b:Ljava/util/List;

    .line 14
    iget-object v3, p1, LC31/A;->s:Landroid/widget/ImageView;

    .line 15
    iget-object v5, p1, LC31/A;->t:Landroid/widget/ImageView;

    .line 16
    iget-object v6, p1, LC31/A;->u:Landroid/widget/ImageView;

    .line 17
    iget-object v7, p1, LC31/A;->v:Landroid/widget/ImageView;

    .line 18
    iget-object v8, p1, LC31/A;->w:Landroid/widget/ImageView;

    new-array v9, v4, [Landroid/widget/ImageView;

    aput-object v3, v9, p2

    aput-object v5, v9, p3

    aput-object v6, v9, v0

    aput-object v7, v9, v1

    aput-object v8, v9, v2

    .line 19
    invoke-static {v9}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    iput-object v3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->c:Ljava/util/List;

    .line 20
    iget-object v3, p1, LC31/A;->b:Landroid/widget/ImageView;

    .line 21
    iget-object v5, p1, LC31/A;->c:Landroid/widget/ImageView;

    .line 22
    iget-object v6, p1, LC31/A;->d:Landroid/widget/ImageView;

    .line 23
    iget-object v7, p1, LC31/A;->e:Landroid/widget/ImageView;

    .line 24
    iget-object v8, p1, LC31/A;->f:Landroid/widget/ImageView;

    new-array v9, v4, [Landroid/widget/ImageView;

    aput-object v3, v9, p2

    aput-object v5, v9, p3

    aput-object v6, v9, v0

    aput-object v7, v9, v1

    aput-object v8, v9, v2

    .line 25
    invoke-static {v9}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v3

    iput-object v3, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->d:Ljava/util/List;

    .line 26
    iget-object v3, p1, LC31/A;->g:Landroid/widget/ImageView;

    .line 27
    iget-object v5, p1, LC31/A;->h:Landroid/widget/ImageView;

    .line 28
    iget-object v6, p1, LC31/A;->i:Landroid/widget/ImageView;

    .line 29
    iget-object v7, p1, LC31/A;->j:Landroid/widget/ImageView;

    .line 30
    iget-object p1, p1, LC31/A;->k:Landroid/widget/ImageView;

    new-array v4, v4, [Landroid/widget/ImageView;

    aput-object v3, v4, p2

    aput-object v5, v4, p3

    aput-object v6, v4, v0

    aput-object v7, v4, v1

    aput-object p1, v4, v2

    .line 31
    invoke-static {v4}, Lkotlin/collections/v;->q([Ljava/lang/Object;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->e:Ljava/util/List;

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p5, p4, 0x2

    if-eqz p5, :cond_0

    const/4 p2, 0x0

    :cond_0
    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_1

    .line 3
    sget p3, Lm31/b;->eventCardMiddleSetteStyle:I

    .line 4
    :cond_1
    invoke-direct {p0, p1, p2, p3}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    return-void
.end method


# virtual methods
.method public final getBinding()LC31/A;
    .locals 1
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s(Ljava/util/List;Ljava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;",
            "Ljava/util/List<",
            "+",
            "Landroid/widget/ImageView;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p2}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 2
    .line 3
    .line 4
    move-result-object p2

    .line 5
    const/4 v0, 0x0

    .line 6
    const/4 v1, 0x0

    .line 7
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    if-eqz v2, :cond_4

    .line 12
    .line 13
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object v2

    .line 17
    add-int/lit8 v3, v1, 0x1

    .line 18
    .line 19
    if-gez v1, :cond_0

    .line 20
    .line 21
    invoke-static {}, Lkotlin/collections/v;->x()V

    .line 22
    .line 23
    .line 24
    :cond_0
    check-cast v2, Landroid/widget/ImageView;

    .line 25
    .line 26
    invoke-static {p1}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 27
    .line 28
    .line 29
    move-result v4

    .line 30
    if-gt v1, v4, :cond_1

    .line 31
    .line 32
    invoke-interface {p1, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v4

    .line 36
    check-cast v4, LJ11/K;

    .line 37
    .line 38
    invoke-virtual {v4}, LJ11/K;->a()I

    .line 39
    .line 40
    .line 41
    move-result v4

    .line 42
    invoke-virtual {v2, v4}, Landroid/widget/ImageView;->setImageResource(I)V

    .line 43
    .line 44
    .line 45
    :cond_1
    invoke-static {p1}, Lkotlin/collections/v;->p(Ljava/util/List;)I

    .line 46
    .line 47
    .line 48
    move-result v4

    .line 49
    if-gt v1, v4, :cond_2

    .line 50
    .line 51
    const/4 v1, 0x1

    .line 52
    goto :goto_1

    .line 53
    :cond_2
    const/4 v1, 0x0

    .line 54
    :goto_1
    if-eqz v1, :cond_3

    .line 55
    .line 56
    const/4 v1, 0x0

    .line 57
    goto :goto_2

    .line 58
    :cond_3
    const/16 v1, 0x8

    .line 59
    .line 60
    :goto_2
    invoke-virtual {v2, v1}, Landroid/view/View;->setVisibility(I)V

    .line 61
    .line 62
    .line 63
    move v1, v3

    .line 64
    goto :goto_0

    .line 65
    :cond_4
    return-void
.end method

.method public final setDealer1RowCards(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->d:Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->s(Ljava/util/List;Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setDealer2RowCards(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->e:Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->s(Ljava/util/List;Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setDealerName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 2
    .line 3
    iget-object v0, v0, LC31/A;->l:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setInformation(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 2
    .line 3
    iget-object v0, v0, LC31/A;->m:Landroid/widget/TextView;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz p1, :cond_1

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 9
    .line 10
    .line 11
    move-result v2

    .line 12
    if-nez v2, :cond_0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v2, 0x0

    .line 16
    goto :goto_1

    .line 17
    :cond_1
    :goto_0
    const/4 v2, 0x1

    .line 18
    :goto_1
    if-eqz v2, :cond_2

    .line 19
    .line 20
    const/16 v1, 0x8

    .line 21
    .line 22
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 26
    .line 27
    iget-object v0, v0, LC31/A;->m:Landroid/widget/TextView;

    .line 28
    .line 29
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method

.method public final setPlayer1RowCards(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->b:Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->s(Ljava/util/List;Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setPlayer2RowCards(Ljava/util/List;)V
    .locals 1
    .param p1    # Ljava/util/List;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "+",
            "LJ11/K;",
            ">;)V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->c:Ljava/util/List;

    .line 2
    .line 3
    invoke-virtual {p0, p1, v0}, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->s(Ljava/util/List;Ljava/util/List;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setPlayerName(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 2
    .line 3
    iget-object v0, v0, LC31/A;->x:Landroid/widget/TextView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setScoreModel(Lorg/xbet/uikit_sport/score/a;)V
    .locals 1
    .param p1    # Lorg/xbet/uikit_sport/score/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 2
    .line 3
    iget-object v0, v0, LC31/A;->y:Lorg/xbet/uikit_sport/score/SportScore;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_sport/score/SportScore;->setScore(Lorg/xbet/uikit_sport/score/a;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setWinInformation(Ljava/lang/CharSequence;)V
    .locals 3

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 2
    .line 3
    iget-object v0, v0, LC31/A;->z:Landroid/widget/TextView;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz p1, :cond_1

    .line 7
    .line 8
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 9
    .line 10
    .line 11
    move-result v2

    .line 12
    if-nez v2, :cond_0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v2, 0x0

    .line 16
    goto :goto_1

    .line 17
    :cond_1
    :goto_0
    const/4 v2, 0x1

    .line 18
    :goto_1
    if-eqz v2, :cond_2

    .line 19
    .line 20
    const/16 v1, 0x8

    .line 21
    .line 22
    :cond_2
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 23
    .line 24
    .line 25
    iget-object v0, p0, Lorg/xbet/uikit_sport/eventcard/middle/EventCardMiddleSette;->a:LC31/A;

    .line 26
    .line 27
    iget-object v0, v0, LC31/A;->z:Landroid/widget/TextView;

    .line 28
    .line 29
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 30
    .line 31
    .line 32
    return-void
.end method
