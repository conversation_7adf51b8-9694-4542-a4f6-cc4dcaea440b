.class public final Lm31/d;
.super Ljava/lang/Object;


# static fields
.field public static ActionIcon:I = 0x7f0a0002

.field public static BackgroundIcon:I = 0x7f0a0005

.field public static ChampionshipIcon:I = 0x7f0a0008

.field public static Icon:I = 0x7f0a000b

.field public static accordion:I = 0x7f0a0046

.field public static accordionClickableSpace:I = 0x7f0a0047

.field public static actionIcon:I = 0x7f0a0064

.field public static altInfo:I = 0x7f0a00ee

.field public static badgeWithCounter:I = 0x7f0a0164

.field public static badgeWithCounterWithAccordion:I = 0x7f0a0165

.field public static badgeWithCounterWithListCheckbox:I = 0x7f0a0166

.field public static bankerFifthCard:I = 0x7f0a0180

.field public static bankerFirstCard:I = 0x7f0a0181

.field public static bankerFourthCard:I = 0x7f0a0182

.field public static bankerName:I = 0x7f0a0183

.field public static bankerSecondCard:I = 0x7f0a0184

.field public static bankerThirdCard:I = 0x7f0a0185

.field public static barrier:I = 0x7f0a0196

.field public static botFifthGameScore:I = 0x7f0a022e

.field public static botFirstGameScore:I = 0x7f0a022f

.field public static botFirstLogo:I = 0x7f0a0230

.field public static botFirstTeamLogo:I = 0x7f0a0231

.field public static botFourthGameScore:I = 0x7f0a0232

.field public static botGameIndicator:I = 0x7f0a0233

.field public static botGameScore:I = 0x7f0a0234

.field public static botIndicator:I = 0x7f0a0236

.field public static botLogo:I = 0x7f0a0237

.field public static botResultScore:I = 0x7f0a0238

.field public static botResultSeparator:I = 0x7f0a0239

.field public static botScore:I = 0x7f0a023a

.field public static botSecondGameScore:I = 0x7f0a023b

.field public static botSecondLogo:I = 0x7f0a023c

.field public static botSecondTeamLogo:I = 0x7f0a023d

.field public static botSeekScore:I = 0x7f0a023e

.field public static botSetScore:I = 0x7f0a023f

.field public static botSetSeparator:I = 0x7f0a0240

.field public static botTeamName:I = 0x7f0a0241

.field public static botThirdGameScore:I = 0x7f0a0242

.field public static botVictoryIndicator:I = 0x7f0a0243

.field public static bottomInfo:I = 0x7f0a025e

.field public static cancel:I = 0x7f0a035d

.field public static caption:I = 0x7f0a0363

.field public static cellChampionshipLarge:I = 0x7f0a03a3

.field public static cellChampionshipMedium:I = 0x7f0a03a8

.field public static cellChampionshipMediumClear:I = 0x7f0a03a9

.field public static cellChampionshipSmall:I = 0x7f0a03b2

.field public static cellLeft:I = 0x7f0a03bb

.field public static cellMiddle:I = 0x7f0a03c0

.field public static cellRight:I = 0x7f0a03c5

.field public static cellRoot:I = 0x7f0a03ce

.field public static cellSportLarge:I = 0x7f0a03d2

.field public static cellSportMedium:I = 0x7f0a03d3

.field public static cellSportMediumClear:I = 0x7f0a03d4

.field public static cellSportSmall:I = 0x7f0a03d5

.field public static chevron:I = 0x7f0a043f

.field public static clSportCollectionItemContainer:I = 0x7f0a04ca

.field public static clear:I = 0x7f0a04ee

.field public static columnsGroup:I = 0x7f0a0547

.field public static combinationBarrier:I = 0x7f0a054a

.field public static counter:I = 0x7f0a05a0

.field public static counterWithAccordion:I = 0x7f0a05a4

.field public static counterWithChevron:I = 0x7f0a05a5

.field public static counterWithListCheckbox:I = 0x7f0a05a6

.field public static customBadgeNew:I = 0x7f0a05e3

.field public static customBadgePopular:I = 0x7f0a05e4

.field public static dealer1Row1Card:I = 0x7f0a061a

.field public static dealer1Row2Card:I = 0x7f0a061b

.field public static dealer1Row3Card:I = 0x7f0a061c

.field public static dealer1Row4Card:I = 0x7f0a061d

.field public static dealer1Row5Card:I = 0x7f0a061e

.field public static dealer2Row1Card:I = 0x7f0a061f

.field public static dealer2Row2Card:I = 0x7f0a0620

.field public static dealer2Row3Card:I = 0x7f0a0621

.field public static dealer2Row4Card:I = 0x7f0a0622

.field public static dealer2Row5Card:I = 0x7f0a0623

.field public static dealerFifthCard:I = 0x7f0a0625

.field public static dealerFirstCard:I = 0x7f0a0626

.field public static dealerFourthCard:I = 0x7f0a0627

.field public static dealerName:I = 0x7f0a0628

.field public static dealerSecondCard:I = 0x7f0a0629

.field public static dealerThirdCard:I = 0x7f0a062a

.field public static dragAndDrop:I = 0x7f0a0694

.field public static dragAndDropWithListCheckBox:I = 0x7f0a0695

.field public static emptyEventCard:I = 0x7f0a06ea

.field public static endTag:I = 0x7f0a0726

.field public static error:I = 0x7f0a073b

.field public static fadeGradient:I = 0x7f0a07b3

.field public static favoriteButton:I = 0x7f0a07b9

.field public static fifthGameTitle:I = 0x7f0a07c9

.field public static firstBarrier:I = 0x7f0a07eb

.field public static firstBlockedMarket:I = 0x7f0a07ee

.field public static firstColumnBotScore:I = 0x7f0a07f1

.field public static firstColumnTopScore:I = 0x7f0a07f3

.field public static firstFormula:I = 0x7f0a07f6

.field public static firstFormulaTitle:I = 0x7f0a07f7

.field public static firstGameTitle:I = 0x7f0a07f9

.field public static firstLine:I = 0x7f0a07fd

.field public static firstMarket:I = 0x7f0a07ff

.field public static firstPlayerBottomFirstScore:I = 0x7f0a0803

.field public static firstPlayerBottomIcon:I = 0x7f0a0804

.field public static firstPlayerBottomResult:I = 0x7f0a0805

.field public static firstPlayerBottomSecondScore:I = 0x7f0a0806

.field public static firstPlayerBottomSeparator:I = 0x7f0a0807

.field public static firstPlayerCombination:I = 0x7f0a0809

.field public static firstPlayerFirstAnswer:I = 0x7f0a080d

.field public static firstPlayerFirstAnswerTitle:I = 0x7f0a080e

.field public static firstPlayerFirstCard:I = 0x7f0a080f

.field public static firstPlayerName:I = 0x7f0a0817

.field public static firstPlayerSecondAnswer:I = 0x7f0a081c

.field public static firstPlayerSecondAnswerTitle:I = 0x7f0a081d

.field public static firstPlayerSecondCard:I = 0x7f0a081e

.field public static firstPlayerThirdAnswer:I = 0x7f0a0822

.field public static firstPlayerThirdAnswerTitle:I = 0x7f0a0823

.field public static firstPlayerThirdCard:I = 0x7f0a0824

.field public static firstPlayerTopFirstScore:I = 0x7f0a082b

.field public static firstPlayerTopIcon:I = 0x7f0a082c

.field public static firstPlayerTopResult:I = 0x7f0a082d

.field public static firstPlayerTopSecondScore:I = 0x7f0a082e

.field public static firstPlayerTopSeparator:I = 0x7f0a082f

.field public static firstTeamCounter:I = 0x7f0a083c

.field public static firstTeamLogo:I = 0x7f0a0840

.field public static firstTeamLogosBarrier:I = 0x7f0a0841

.field public static firstTeamName:I = 0x7f0a0842

.field public static firstVictoryIndicator:I = 0x7f0a0849

.field public static flEndTag:I = 0x7f0a087c

.field public static flShimmerContainer:I = 0x7f0a089f

.field public static flStartTag:I = 0x7f0a08a1

.field public static fourthGameTitle:I = 0x7f0a08d3

.field public static gameText:I = 0x7f0a0929

.field public static guideline:I = 0x7f0a0a05

.field public static guideline_horizontal:I = 0x7f0a0a3c

.field public static guideline_vertical:I = 0x7f0a0a5a

.field public static header:I = 0x7f0a0a67

.field public static horizontalSeparator:I = 0x7f0a0aa8

.field public static icon:I = 0x7f0a0ac8

.field public static image:I = 0x7f0a0ae2

.field public static info:I = 0x7f0a0b92

.field public static infoBarrier:I = 0x7f0a0b93

.field public static infoList:I = 0x7f0a0b9b

.field public static infoText:I = 0x7f0a0b9f

.field public static infoTitle:I = 0x7f0a0ba1

.field public static information:I = 0x7f0a0ba6

.field public static ivIcon:I = 0x7f0a0c83

.field public static label:I = 0x7f0a0dc4

.field public static left:I = 0x7f0a0de4

.field public static leftIndicator:I = 0x7f0a0de8

.field public static leftSeeding:I = 0x7f0a0dea

.field public static leftTitle:I = 0x7f0a0dee

.field public static listCheckbox:I = 0x7f0a0e49

.field public static liveInfo:I = 0x7f0a0e54

.field public static liveTag:I = 0x7f0a0e55

.field public static market:I = 0x7f0a0f11

.field public static marketGroupsList:I = 0x7f0a0f16

.field public static marketLineList:I = 0x7f0a0f17

.field public static move:I = 0x7f0a0f84

.field public static multiSelection:I = 0x7f0a0fa3

.field public static noneSpecificScroll:I = 0x7f0a0fda

.field public static notificationsButton:I = 0x7f0a0fe6

.field public static overlay:I = 0x7f0a1043

.field public static player1Row1Card:I = 0x7f0a10a7

.field public static player1Row2Card:I = 0x7f0a10a8

.field public static player1Row3Card:I = 0x7f0a10a9

.field public static player1Row4Card:I = 0x7f0a10aa

.field public static player1Row5Card:I = 0x7f0a10ab

.field public static player2Row1Card:I = 0x7f0a10ac

.field public static player2Row2Card:I = 0x7f0a10ad

.field public static player2Row3Card:I = 0x7f0a10ae

.field public static player2Row4Card:I = 0x7f0a10af

.field public static player2Row5Card:I = 0x7f0a10b0

.field public static playerEighthCard:I = 0x7f0a10b7

.field public static playerFifthCard:I = 0x7f0a10b8

.field public static playerFirstCard:I = 0x7f0a10b9

.field public static playerFourthCard:I = 0x7f0a10ba

.field public static playerName:I = 0x7f0a10c2

.field public static playerSecondCard:I = 0x7f0a10c6

.field public static playerSeventhCard:I = 0x7f0a10c7

.field public static playerSixthCard:I = 0x7f0a10c8

.field public static playerThirdCard:I = 0x7f0a10d0

.field public static primary:I = 0x7f0a1104

.field public static promotionTitleTextView:I = 0x7f0a114f

.field public static promotionValueTextView:I = 0x7f0a1150

.field public static red:I = 0x7f0a11bd

.field public static redCard:I = 0x7f0a11be

.field public static resultText:I = 0x7f0a11f9

.field public static resultTitle:I = 0x7f0a11fa

.field public static right:I = 0x7f0a1202

.field public static rightIndicator:I = 0x7f0a1207

.field public static rightSeeding:I = 0x7f0a120a

.field public static rightTitle:I = 0x7f0a120e

.field public static round_0:I = 0x7f0a1235

.field public static round_16:I = 0x7f0a1237

.field public static round_8:I = 0x7f0a1238

.field public static round_full:I = 0x7f0a1239

.field public static rounded:I = 0x7f0a123a

.field public static score:I = 0x7f0a12f4

.field public static scoreGroup:I = 0x7f0a12f7

.field public static scores:I = 0x7f0a12fb

.field public static secondBarrier:I = 0x7f0a1323

.field public static secondBlockedMarket:I = 0x7f0a1326

.field public static secondColumnBotScore:I = 0x7f0a132a

.field public static secondColumnTopScore:I = 0x7f0a132b

.field public static secondFormula:I = 0x7f0a132e

.field public static secondFormulaTitle:I = 0x7f0a132f

.field public static secondGameTitle:I = 0x7f0a1331

.field public static secondLine:I = 0x7f0a1334

.field public static secondMarket:I = 0x7f0a1338

.field public static secondPlayerBottomFirstScore:I = 0x7f0a133c

.field public static secondPlayerBottomIcon:I = 0x7f0a133d

.field public static secondPlayerBottomResult:I = 0x7f0a133e

.field public static secondPlayerBottomSecondScore:I = 0x7f0a133f

.field public static secondPlayerBottomSeparator:I = 0x7f0a1340

.field public static secondPlayerCombination:I = 0x7f0a1342

.field public static secondPlayerFirstAnswer:I = 0x7f0a1346

.field public static secondPlayerFirstAnswerTitle:I = 0x7f0a1347

.field public static secondPlayerFirstCard:I = 0x7f0a1348

.field public static secondPlayerName:I = 0x7f0a1350

.field public static secondPlayerSecondAnswer:I = 0x7f0a1355

.field public static secondPlayerSecondAnswerTitle:I = 0x7f0a1356

.field public static secondPlayerSecondCard:I = 0x7f0a1357

.field public static secondPlayerThirdAnswer:I = 0x7f0a135b

.field public static secondPlayerThirdAnswerTitle:I = 0x7f0a135c

.field public static secondPlayerThirdCard:I = 0x7f0a135d

.field public static secondPlayerTopFirstScore:I = 0x7f0a1364

.field public static secondPlayerTopIcon:I = 0x7f0a1365

.field public static secondPlayerTopResult:I = 0x7f0a1366

.field public static secondPlayerTopSecondScore:I = 0x7f0a1367

.field public static secondPlayerTopSeparator:I = 0x7f0a1368

.field public static secondTeamCounter:I = 0x7f0a1374

.field public static secondTeamLogo:I = 0x7f0a1378

.field public static secondTeamLogosBarrier:I = 0x7f0a1379

.field public static secondTeamName:I = 0x7f0a137a

.field public static secondVictoryIndicator:I = 0x7f0a1382

.field public static secondary:I = 0x7f0a138e

.field public static secondaryAccordion:I = 0x7f0a138f

.field public static seekScore:I = 0x7f0a139e

.field public static setText:I = 0x7f0a13db

.field public static shimmer:I = 0x7f0a1400

.field public static singleSelection:I = 0x7f0a14a5

.field public static sportCollectionItem:I = 0x7f0a15ad

.field public static startTag:I = 0x7f0a15ed

.field public static streamButton:I = 0x7f0a161e

.field public static stroke:I = 0x7f0a1621

.field public static subtitle:I = 0x7f0a163b

.field public static tag:I = 0x7f0a168e

.field public static tagWithCounterWithActionIcon:I = 0x7f0a1699

.field public static teamLogo:I = 0x7f0a16dc

.field public static teamName:I = 0x7f0a16de

.field public static teamNameEndBarrier:I = 0x7f0a16df

.field public static thirdBlockedMarket:I = 0x7f0a1798

.field public static thirdColumnBotScore:I = 0x7f0a179b

.field public static thirdColumnTopScore:I = 0x7f0a179c

.field public static thirdGameTitle:I = 0x7f0a179f

.field public static thirdLine:I = 0x7f0a17a1

.field public static thirdMarket:I = 0x7f0a17a3

.field public static timeLeft:I = 0x7f0a17e8

.field public static timer:I = 0x7f0a17f3

.field public static title:I = 0x7f0a1808

.field public static topBarrier:I = 0x7f0a1857

.field public static topFifthGameScore:I = 0x7f0a1863

.field public static topFirstGameScore:I = 0x7f0a1864

.field public static topFirstLogo:I = 0x7f0a1865

.field public static topFirstTeamLogo:I = 0x7f0a1866

.field public static topFourthGameScore:I = 0x7f0a1867

.field public static topGameIndicator:I = 0x7f0a1868

.field public static topGameScore:I = 0x7f0a1869

.field public static topIndicator:I = 0x7f0a186d

.field public static topLogo:I = 0x7f0a1871

.field public static topResultScore:I = 0x7f0a1878

.field public static topResultSeparator:I = 0x7f0a1879

.field public static topScore:I = 0x7f0a187a

.field public static topSecondGameScore:I = 0x7f0a187c

.field public static topSecondLogo:I = 0x7f0a187d

.field public static topSecondTeamLogo:I = 0x7f0a187e

.field public static topSeekScore:I = 0x7f0a187f

.field public static topSetScore:I = 0x7f0a1881

.field public static topSetSeparator:I = 0x7f0a1882

.field public static topTeamName:I = 0x7f0a1889

.field public static topThirdGameScore:I = 0x7f0a188a

.field public static topVictoryIndicator:I = 0x7f0a1896

.field public static tvTitle:I = 0x7f0a1cac

.field public static uikitCategoryCardLabel:I = 0x7f0a1ddd

.field public static uikitCategoryCardPicture:I = 0x7f0a1dde

.field public static verticalBarrier:I = 0x7f0a1f00

.field public static verticalSeparator:I = 0x7f0a1f01

.field public static white:I = 0x7f0a1fe9

.field public static winInformation:I = 0x7f0a1ff3

.field public static zoneButton:I = 0x7f0a2025


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
