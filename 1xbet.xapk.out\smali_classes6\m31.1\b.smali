.class public final Lm31/b;
.super Ljava/lang/Object;


# static fields
.field public static actionIcon:I = 0x7f04002a

.field public static actionIconPicture:I = 0x7f04002b

.field public static altInfoText:I = 0x7f040062

.field public static altInfoTextColor:I = 0x7f040063

.field public static amount:I = 0x7f040066

.field public static buttonAllTitle:I = 0x7f040109

.field public static buttonFilterBackground:I = 0x7f040111

.field public static buttonFilterTitle:I = 0x7f040112

.field public static caption:I = 0x7f040126

.field public static couponMarketStyle:I = 0x7f040252

.field public static error:I = 0x7f040306

.field public static eventCardBasicStyle:I = 0x7f040314

.field public static eventCardBetConstructorStyle:I = 0x7f040315

.field public static eventCardChampionshipStyle:I = 0x7f040316

.field public static eventCardCompactStyle:I = 0x7f040317

.field public static eventCardEmptyStyle:I = 0x7f040318

.field public static eventCardHeaderButtonStyle:I = 0x7f040319

.field public static eventCardHeaderStyle:I = 0x7f04031a

.field public static eventCardInfoChampionshipStyle:I = 0x7f04031b

.field public static eventCardInfoFavoritesStyle:I = 0x7f04031c

.field public static eventCardInfoHistoryStyle:I = 0x7f04031d

.field public static eventCardInfoLineStyle:I = 0x7f04031e

.field public static eventCardInfoLiveAltStyle:I = 0x7f04031f

.field public static eventCardInfoLiveStyle:I = 0x7f040320

.field public static eventCardInfoSeedingStyle:I = 0x7f040321

.field public static eventCardMiddleBaccaratStyle:I = 0x7f040322

.field public static eventCardMiddleChampionshipStyle:I = 0x7f040323

.field public static eventCardMiddleCricketStyle:I = 0x7f040324

.field public static eventCardMiddleCyberPokerStyle:I = 0x7f040325

.field public static eventCardMiddleDiceStyle:I = 0x7f040326

.field public static eventCardMiddleFightingStyle:I = 0x7f040327

.field public static eventCardMiddleScoreStyle:I = 0x7f040328

.field public static eventCardMiddleSetteStyle:I = 0x7f040329

.field public static eventCardMiddleStyle:I = 0x7f04032a

.field public static eventCardMiddleWinningFormulaStyle:I = 0x7f04032b

.field public static eventCardPromotionsStyle:I = 0x7f04032c

.field public static eventCardResultsCyberStyle:I = 0x7f04032d

.field public static eventCardResultsFavouritesStyle:I = 0x7f04032e

.field public static eventCardResultsHistoryStyle:I = 0x7f04032f

.field public static eventCardResultsLiveStyle:I = 0x7f040330

.field public static eventCardStatisticsStyle:I = 0x7f040331

.field public static eventCardSyntheticsStyle:I = 0x7f040332

.field public static firstInfoText:I = 0x7f04036f

.field public static hasBorder:I = 0x7f0403cb

.field public static headerShimmerCornerRadius:I = 0x7f0403d0

.field public static headerShimmerHeight:I = 0x7f0403d1

.field public static headerShimmerWidth:I = 0x7f0403d2

.field public static headerTitle:I = 0x7f0403d5

.field public static icon:I = 0x7f0403fd

.field public static iconAllResId:I = 0x7f0403fe

.field public static iconBackground:I = 0x7f0403ff

.field public static iconBackgroundHeight:I = 0x7f040400

.field public static iconBackgroundWidth:I = 0x7f040401

.field public static iconColor:I = 0x7f040402

.field public static iconFilterColor:I = 0x7f040404

.field public static iconFilterResId:I = 0x7f040405

.field public static iconHorizontalPadding:I = 0x7f040407

.field public static iconShimmerCornerRadius:I = 0x7f04040b

.field public static iconShimmerHeight:I = 0x7f04040c

.field public static iconShimmerWidth:I = 0x7f04040d

.field public static iconSize:I = 0x7f04040e

.field public static iconTopMargin:I = 0x7f040413

.field public static iconVerticalPadding:I = 0x7f040414

.field public static infoText:I = 0x7f040431

.field public static infoTextColor:I = 0x7f040432

.field public static itemBackground:I = 0x7f040450

.field public static itemHeight:I = 0x7f040452

.field public static itemWidth:I = 0x7f04046e

.field public static leftProgress:I = 0x7f0404e2

.field public static leftSeedingText:I = 0x7f0404e3

.field public static leftTitle:I = 0x7f0404e5

.field public static marketCoefficient:I = 0x7f040532

.field public static marketHeader:I = 0x7f040533

.field public static marketTitle:I = 0x7f040537

.field public static maxViewItemTextSize:I = 0x7f040581

.field public static minTitleHeight:I = 0x7f040597

.field public static minViewItemTextSize:I = 0x7f04059a

.field public static placeholderIcon:I = 0x7f040657

.field public static placeholderPicture:I = 0x7f040658

.field public static regularSportIndicator:I = 0x7f0406c0

.field public static regularSportIndicatorCompact:I = 0x7f0406c1

.field public static rightProgress:I = 0x7f0406d4

.field public static rightSeedingText:I = 0x7f0406d5

.field public static rightTitle:I = 0x7f0406d7

.field public static secondInfoText:I = 0x7f040706

.field public static showSkeleton:I = 0x7f040777

.field public static sportCellBackgroundTint:I = 0x7f040844

.field public static sportCellLeftCounterNumber:I = 0x7f040845

.field public static sportCellLeftIcon:I = 0x7f040846

.field public static sportCellLeftIconSize:I = 0x7f040847

.field public static sportCellLeftIconTint:I = 0x7f040848

.field public static sportCellLeftImageBackground:I = 0x7f040849

.field public static sportCellLeftImageBackgroundAvailable:I = 0x7f04084a

.field public static sportCellLeftImageBackgroundTint:I = 0x7f04084b

.field public static sportCellLeftStyle:I = 0x7f04084c

.field public static sportCellMaxWidth:I = 0x7f04084d

.field public static sportCellMiddleStyle:I = 0x7f04084e

.field public static sportCellMiddleSubtitleAvailable:I = 0x7f04084f

.field public static sportCellMiddleSubtitleText:I = 0x7f040850

.field public static sportCellMiddleTitleMaxLines:I = 0x7f040851

.field public static sportCellMiddleTitleText:I = 0x7f040852

.field public static sportCellMiddleTitleTextStyle:I = 0x7f040853

.field public static sportCellRightAccordionExpanded:I = 0x7f040854

.field public static sportCellRightAccordionStyle:I = 0x7f040855

.field public static sportCellRightActionIcon:I = 0x7f040856

.field public static sportCellRightActionIconTint:I = 0x7f040857

.field public static sportCellRightCounterNumber:I = 0x7f040858

.field public static sportCellRightCounterStyle:I = 0x7f040859

.field public static sportCellRightCustomBadgeStyle:I = 0x7f04085a

.field public static sportCellRightLabelText:I = 0x7f04085b

.field public static sportCellRightLabelTextStyle:I = 0x7f04085c

.field public static sportCellRightListCheckboxChecked:I = 0x7f04085d

.field public static sportCellRightListCheckboxStyle:I = 0x7f04085e

.field public static sportCellRightStyle:I = 0x7f04085f

.field public static sportCellRightStyleType:I = 0x7f040860

.field public static sportCellRightTagVisible:I = 0x7f040861

.field public static sportCellRoundCorners:I = 0x7f040862

.field public static sportCellRoundedBottom:I = 0x7f040863

.field public static sportCellRoundedTop:I = 0x7f040864

.field public static sportCellSeparatorAvailable:I = 0x7f040865

.field public static sportCellSeparatorEndPadding:I = 0x7f040866

.field public static sportCellSeparatorStartPadding:I = 0x7f040867

.field public static sportCellStyle:I = 0x7f040868

.field public static sportCollectionSpecificScrollType:I = 0x7f040869

.field public static sportCouponCardStyle:I = 0x7f04086a

.field public static subtitle:I = 0x7f0408af

.field public static tag:I = 0x7f0408f7

.field public static tagColor:I = 0x7f0408f8

.field public static thirdInfoText:I = 0x7f040993

.field public static title:I = 0x7f0409cf

.field public static titleBottomMargin:I = 0x7f0409d0

.field public static titleColor:I = 0x7f0409d4

.field public static titleHorizontalMargin:I = 0x7f0409d6

.field public static titleShimmerCornerRadius:I = 0x7f0409e2

.field public static titleShimmerHeight:I = 0x7f0409e3

.field public static titleShimmerWidth:I = 0x7f0409e4

.field public static titleText:I = 0x7f0409e6

.field public static typeSportIndicator:I = 0x7f040a2f

.field public static winColorSportIndicator:I = 0x7f040bba

.field public static winColorSportIndicatorCompact:I = 0x7f040bbb

.field public static winSportIndicator:I = 0x7f040bbd

.field public static winSportIndicatorCompact:I = 0x7f040bbe


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
