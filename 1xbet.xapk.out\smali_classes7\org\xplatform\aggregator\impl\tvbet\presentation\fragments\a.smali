.class public final synthetic Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/a;->a:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/a;->a:Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-static {v0, p1}, Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;->y2(Lorg/xplatform/aggregator/impl/tvbet/presentation/fragments/TvBetJackpotFragment;I)Ljava/lang/CharSequence;

    move-result-object p1

    return-object p1
.end method
