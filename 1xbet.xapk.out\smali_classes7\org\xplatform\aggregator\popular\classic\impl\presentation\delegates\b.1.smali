.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:LAb1/a;

.field public final synthetic b:Lorg/xplatform/aggregator/api/model/Game;


# direct methods
.method public synthetic constructor <init>(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;->a:LAb1/a;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;->b:Lorg/xplatform/aggregator/api/model/Game;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;->a:LAb1/a;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/b;->b:Lorg/xplatform/aggregator/api/model/Game;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/delegates/c;->a(LAb1/a;Lorg/xplatform/aggregator/api/model/Game;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
