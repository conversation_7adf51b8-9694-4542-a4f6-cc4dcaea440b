.class interface abstract Lcom/google/android/gms/internal/measurement/zzns;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract zza(Ljava/lang/Object;)I
.end method

.method public abstract zzb(Ljava/lang/Object;)I
.end method

.method public abstract zze()Ljava/lang/Object;
.end method

.method public abstract zzf(Ljava/lang/Object;)V
.end method

.method public abstract zzg(Ljava/lang/Object;Ljava/lang/Object;)V
.end method

.method public abstract zzh(Ljava/lang/Object;[BIILcom/google/android/gms/internal/measurement/zzks;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract zzi(<PERSON>java/lang/Object;Lcom/google/android/gms/internal/measurement/zzor;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract zzj(Ljava/lang/Object;Ljava/lang/Object;)Z
.end method

.method public abstract zzk(Ljava/lang/Object;)Z
.end method
