.class public final Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;
.super Lorg/xbet/ui_common/viewmodel/core/b;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$a;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$b;,
        Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u00c2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008&\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\u0008\u0000\u0018\u0000 m2\u00020\u0001:\u0003nopBq\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0008\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\u000c\u0012\u0006\u0010\u000f\u001a\u00020\u000e\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0013\u001a\u00020\u0012\u0012\u0006\u0010\u0015\u001a\u00020\u0014\u0012\u0006\u0010\u0017\u001a\u00020\u0016\u0012\u0006\u0010\u0019\u001a\u00020\u0018\u0012\u0006\u0010\u001b\u001a\u00020\u001a\u00a2\u0006\u0004\u0008\u001c\u0010\u001dJ\'\u0010\"\u001a\u00020!2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\u001f\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008\"\u0010#J\u0017\u0010%\u001a\u00020$2\u0006\u0010\u001f\u001a\u00020\u001eH\u0002\u00a2\u0006\u0004\u0008%\u0010&J\u0013\u0010)\u001a\u0008\u0012\u0004\u0012\u00020(0\'\u00a2\u0006\u0004\u0008)\u0010*J\u0013\u0010-\u001a\u0008\u0012\u0004\u0012\u00020,0+\u00a2\u0006\u0004\u0008-\u0010.J-\u00104\u001a\u00020!2\u0006\u00100\u001a\u00020/2\u0006\u00102\u001a\u0002012\u0006\u00103\u001a\u00020\n2\u0006\u0010 \u001a\u00020\n\u00a2\u0006\u0004\u00084\u00105J\r\u00106\u001a\u00020!\u00a2\u0006\u0004\u00086\u00107J%\u00109\u001a\u00020!2\u0006\u0010\t\u001a\u00020\u00082\u0006\u00108\u001a\u00020$2\u0006\u00103\u001a\u00020\n\u00a2\u0006\u0004\u00089\u0010:J7\u0010<\u001a\u00020!2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010;\u001a\u0002012\u0006\u0010\u000b\u001a\u00020\n2\u0006\u00103\u001a\u00020\n2\u0006\u0010 \u001a\u00020\nH\u0002\u00a2\u0006\u0004\u0008<\u0010=R\u0014\u0010\u0003\u001a\u00020\u00028\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008>\u0010?R\u0014\u0010\u0005\u001a\u00020\u00048\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008@\u0010AR\u0014\u0010\u0007\u001a\u00020\u00068\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008B\u0010CR\u0014\u0010\t\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008D\u0010ER\u0014\u0010\u000b\u001a\u00020\n8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008F\u0010GR\u0014\u0010\r\u001a\u00020\u000c8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008H\u0010IR\u0014\u0010\u000f\u001a\u00020\u000e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008J\u0010KR\u0014\u0010\u0011\u001a\u00020\u00108\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008L\u0010MR\u0014\u0010\u0013\u001a\u00020\u00128\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008N\u0010OR\u0014\u0010\u0015\u001a\u00020\u00148\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008P\u0010QR\u0014\u0010\u0017\u001a\u00020\u00168\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008R\u0010SR\u0014\u0010\u0019\u001a\u00020\u00188\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008T\u0010UR\u0014\u0010\u001b\u001a\u00020\u001a8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008V\u0010WR\u001a\u0010\\\u001a\u0008\u0012\u0004\u0012\u00020Y0X8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008Z\u0010[R\u001c\u0010`\u001a\u0008\u0012\u0004\u0012\u00020!0]8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008^\u0010_R\u0018\u0010d\u001a\u0004\u0018\u00010a8\u0002@\u0002X\u0082\u000e\u00a2\u0006\u0006\n\u0004\u0008b\u0010cR\u001a\u0010h\u001a\u0008\u0012\u0004\u0012\u00020(0e8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008f\u0010gR\u001a\u0010l\u001a\u0008\u0012\u0004\u0012\u00020,0i8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008j\u0010k\u00a8\u0006q"
    }
    d2 = {
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;",
        "Lorg/xbet/ui_common/viewmodel/core/b;",
        "Lw81/c;",
        "getTournamentFullInfoScenario",
        "LSX0/c;",
        "lottieEmptyConfigurator",
        "Lorg/xbet/ui_common/utils/M;",
        "errorHandler",
        "",
        "tournamentId",
        "",
        "tournamentTitle",
        "LHX0/e;",
        "resourceManager",
        "Lw81/g;",
        "takePartTournamentsScenario",
        "LwX0/C;",
        "routerHolder",
        "Lm8/a;",
        "coroutineDispatchers",
        "LP91/b;",
        "aggregatorNavigator",
        "Lorg/xbet/onexlocalization/f;",
        "getLocaleUseCase",
        "Lorg/xbet/analytics/domain/scope/g;",
        "aggregatorTournamentsAnalytics",
        "LnR/d;",
        "aggregatorTournamentFatmanLogger",
        "<init>",
        "(Lw81/c;LSX0/c;Lorg/xbet/ui_common/utils/M;JLjava/lang/String;LHX0/e;Lw81/g;LwX0/C;Lm8/a;LP91/b;Lorg/xbet/onexlocalization/f;Lorg/xbet/analytics/domain/scope/g;LnR/d;)V",
        "Lh81/b;",
        "result",
        "screenName",
        "",
        "G3",
        "(JLh81/b;Ljava/lang/String;)V",
        "",
        "K3",
        "(Lh81/b;)Z",
        "Lkotlinx/coroutines/flow/f0;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;",
        "F3",
        "()Lkotlinx/coroutines/flow/f0;",
        "Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$b;",
        "E3",
        "()Lkotlinx/coroutines/flow/e;",
        "Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;",
        "buttonAction",
        "Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;",
        "tournamentKind",
        "typeStage",
        "H3",
        "(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V",
        "onBackPressed",
        "()V",
        "fromCache",
        "J3",
        "(JZLjava/lang/String;)V",
        "kind",
        "I3",
        "(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        "v1",
        "Lw81/c;",
        "x1",
        "LSX0/c;",
        "y1",
        "Lorg/xbet/ui_common/utils/M;",
        "F1",
        "J",
        "H1",
        "Ljava/lang/String;",
        "I1",
        "LHX0/e;",
        "P1",
        "Lw81/g;",
        "S1",
        "LwX0/C;",
        "V1",
        "Lm8/a;",
        "b2",
        "LP91/b;",
        "v2",
        "Lorg/xbet/onexlocalization/f;",
        "x2",
        "Lorg/xbet/analytics/domain/scope/g;",
        "y2",
        "LnR/d;",
        "",
        "Lkb1/C;",
        "F2",
        "Ljava/util/List;",
        "stageShimmers",
        "Lkotlin/Function0;",
        "H2",
        "Lkotlin/jvm/functions/Function0;",
        "backAction",
        "Lkotlinx/coroutines/x0;",
        "I2",
        "Lkotlinx/coroutines/x0;",
        "tournamentFullInfoJob",
        "Lkotlinx/coroutines/flow/V;",
        "P2",
        "Lkotlinx/coroutines/flow/V;",
        "mutableState",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "S2",
        "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;",
        "eventFlow",
        "V2",
        "c",
        "b",
        "a",
        "impl_aggregator_implRelease"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final V2:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# instance fields
.field public final F1:J

.field public final F2:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lkb1/C;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final H1:Ljava/lang/String;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public H2:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final I1:LHX0/e;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public I2:Lkotlinx/coroutines/x0;

.field public final P1:Lw81/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final P2:Lkotlinx/coroutines/flow/V;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlinx/coroutines/flow/V<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S1:LwX0/C;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final S2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final V1:Lm8/a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final b2:LP91/b;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v1:Lw81/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final v2:Lorg/xbet/onexlocalization/f;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x1:LSX0/c;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final x2:Lorg/xbet/analytics/domain/scope/g;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y1:Lorg/xbet/ui_common/utils/M;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final y2:LnR/d;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->V2:Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$a;

    return-void
.end method

.method public constructor <init>(Lw81/c;LSX0/c;Lorg/xbet/ui_common/utils/M;JLjava/lang/String;LHX0/e;Lw81/g;LwX0/C;Lm8/a;LP91/b;Lorg/xbet/onexlocalization/f;Lorg/xbet/analytics/domain/scope/g;LnR/d;)V
    .locals 0
    .param p1    # Lw81/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # LSX0/c;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Lorg/xbet/ui_common/utils/M;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p6    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p7    # LHX0/e;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p8    # Lw81/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p9    # LwX0/C;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p10    # Lm8/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p11    # LP91/b;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p12    # Lorg/xbet/onexlocalization/f;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p13    # Lorg/xbet/analytics/domain/scope/g;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p14    # LnR/d;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-direct {p0}, Lorg/xbet/ui_common/viewmodel/core/b;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->v1:Lw81/c;

    .line 5
    .line 6
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->x1:LSX0/c;

    .line 7
    .line 8
    iput-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 9
    .line 10
    iput-wide p4, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->F1:J

    .line 11
    .line 12
    iput-object p6, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->H1:Ljava/lang/String;

    .line 13
    .line 14
    iput-object p7, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->I1:LHX0/e;

    .line 15
    .line 16
    iput-object p8, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->P1:Lw81/g;

    .line 17
    .line 18
    iput-object p9, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->S1:LwX0/C;

    .line 19
    .line 20
    iput-object p10, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->V1:Lm8/a;

    .line 21
    .line 22
    iput-object p11, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->b2:LP91/b;

    .line 23
    .line 24
    iput-object p12, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->v2:Lorg/xbet/onexlocalization/f;

    .line 25
    .line 26
    iput-object p13, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 27
    .line 28
    iput-object p14, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->y2:LnR/d;

    .line 29
    .line 30
    invoke-static {}, Lkotlin/collections/u;->c()Ljava/util/List;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    const/4 p2, 0x0

    .line 35
    :goto_0
    const/16 p3, 0xa

    .line 36
    .line 37
    if-ge p2, p3, :cond_0

    .line 38
    .line 39
    new-instance p3, Lkb1/C;

    .line 40
    .line 41
    invoke-direct {p3}, Lkb1/C;-><init>()V

    .line 42
    .line 43
    .line 44
    invoke-interface {p1, p3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 45
    .line 46
    .line 47
    add-int/lit8 p2, p2, 0x1

    .line 48
    .line 49
    goto :goto_0

    .line 50
    :cond_0
    invoke-static {p1}, Lkotlin/collections/u;->a(Ljava/util/List;)Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->F2:Ljava/util/List;

    .line 55
    .line 56
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/g;

    .line 57
    .line 58
    invoke-direct {p2, p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/g;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)V

    .line 59
    .line 60
    .line 61
    iput-object p2, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->H2:Lkotlin/jvm/functions/Function0;

    .line 62
    .line 63
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$c;

    .line 64
    .line 65
    invoke-direct {p2, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c$c;-><init>(Ljava/util/List;)V

    .line 66
    .line 67
    .line 68
    invoke-static {p2}, Lkotlinx/coroutines/flow/g0;->a(Ljava/lang/Object;)Lkotlinx/coroutines/flow/V;

    .line 69
    .line 70
    .line 71
    move-result-object p1

    .line 72
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 73
    .line 74
    new-instance p1, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 75
    .line 76
    sget-object p2, Lkotlinx/coroutines/channels/BufferOverflow;->DROP_OLDEST:Lkotlinx/coroutines/channels/BufferOverflow;

    .line 77
    .line 78
    const/4 p3, 0x1

    .line 79
    invoke-direct {p1, p3, p2}, Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;-><init>(ILkotlinx/coroutines/channels/BufferOverflow;)V

    .line 80
    .line 81
    .line 82
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->S2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 83
    .line 84
    return-void
.end method

.method public static final synthetic A3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;JLh81/b;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3, p4}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->G3(JLh81/b;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic B3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-virtual/range {p0 .. p6}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->I3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic C3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;Lh81/b;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->K3(Lh81/b;)Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->b2:LP91/b;

    .line 2
    .line 3
    invoke-virtual {p0}, LP91/b;->a()V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 7
    .line 8
    return-object p0
.end method

.method private final G3(JLh81/b;Ljava/lang/String;)V
    .locals 7

    .line 1
    instance-of v3, p3, Lh81/b$a;

    .line 2
    .line 3
    instance-of v0, p3, Lh81/b$b;

    .line 4
    .line 5
    const/4 v1, 0x0

    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    check-cast p3, Lh81/b$b;

    .line 9
    .line 10
    goto :goto_0

    .line 11
    :cond_0
    move-object p3, v1

    .line 12
    :goto_0
    if-eqz p3, :cond_1

    .line 13
    .line 14
    invoke-virtual {p3}, Lh81/b$b;->a()I

    .line 15
    .line 16
    .line 17
    move-result p3

    .line 18
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    :cond_1
    move-object v4, v1

    .line 23
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->x2:Lorg/xbet/analytics/domain/scope/g;

    .line 24
    .line 25
    const-string v5, "stages_tournament"

    .line 26
    .line 27
    move-wide v1, p1

    .line 28
    invoke-virtual/range {v0 .. v5}, Lorg/xbet/analytics/domain/scope/g;->c(JZLjava/lang/Integer;Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->y2:LnR/d;

    .line 32
    .line 33
    const-string v5, "stages_tournament"

    .line 34
    .line 35
    move-object v6, v4

    .line 36
    move v4, v3

    .line 37
    move-wide v2, v1

    .line 38
    move-object v1, p4

    .line 39
    invoke-interface/range {v0 .. v6}, LnR/d;->i(Ljava/lang/String;JZLjava/lang/String;Ljava/lang/Integer;)V

    .line 40
    .line 41
    .line 42
    return-void
.end method

.method private final K3(Lh81/b;)Z
    .locals 1

    .line 1
    instance-of v0, p1, Lh81/b$c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    instance-of p1, p1, Lh81/b$g;

    .line 6
    .line 7
    if-nez p1, :cond_0

    .line 8
    .line 9
    const/4 p1, 0x1

    .line 10
    return p1

    .line 11
    :cond_0
    const/4 p1, 0x0

    .line 12
    return p1
.end method

.method public static synthetic p3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->D3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic q3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)LP91/b;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->b2:LP91/b;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic r3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->S2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic s3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lorg/xbet/onexlocalization/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->v2:Lorg/xbet/onexlocalization/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic t3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)LSX0/c;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->x1:LSX0/c;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic u3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lkotlinx/coroutines/flow/V;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic v3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)LHX0/e;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->I1:LHX0/e;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic w3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)LwX0/C;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->S1:LwX0/C;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic x3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Lw81/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->P1:Lw81/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic y3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)J
    .locals 2

    .line 1
    iget-wide v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->F1:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public static final synthetic z3(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->H1:Ljava/lang/String;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public final E3()Lkotlinx/coroutines/flow/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/e<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$b;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->S2:Lorg/xbet/ui_common/utils/flows/OneExecuteActionFlow;

    .line 2
    .line 3
    return-object v0
.end method

.method public final F3()Lkotlinx/coroutines/flow/f0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlinx/coroutines/flow/f0<",
            "Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$c;",
            ">;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->P2:Lkotlinx/coroutines/flow/V;

    .line 2
    .line 3
    return-object v0
.end method

.method public final H3(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;)V
    .locals 8
    .param p1    # Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p2    # Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p3    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$onButtonClick$1;

    .line 6
    .line 7
    const/4 v7, 0x0

    .line 8
    move-object v3, p0

    .line 9
    move-object v2, p1

    .line 10
    move-object v4, p2

    .line 11
    move-object v5, p3

    .line 12
    move-object v6, p4

    .line 13
    invoke-direct/range {v1 .. v7}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$onButtonClick$1;-><init>(Lorg/xplatform/aggregator/api/model/tournaments/UserActionButtonType;Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;Lorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 14
    .line 15
    .line 16
    const/4 v4, 0x3

    .line 17
    const/4 v5, 0x0

    .line 18
    move-object v3, v1

    .line 19
    const/4 v1, 0x0

    .line 20
    const/4 v2, 0x0

    .line 21
    invoke-static/range {v0 .. v5}, Lkotlinx/coroutines/h;->d(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 22
    .line 23
    .line 24
    return-void
.end method

.method public final I3(JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 12

    .line 1
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 2
    .line 3
    .line 4
    move-result-object v9

    .line 5
    new-instance v10, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$onParticipateClick$1;

    .line 6
    .line 7
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->y1:Lorg/xbet/ui_common/utils/M;

    .line 8
    .line 9
    invoke-direct {v10, v0}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$onParticipateClick$1;-><init>(Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->V1:Lm8/a;

    .line 13
    .line 14
    invoke-interface {v0}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 15
    .line 16
    .line 17
    move-result-object v11

    .line 18
    new-instance v0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$onParticipateClick$2;

    .line 19
    .line 20
    const/4 v8, 0x0

    .line 21
    move-object v1, p0

    .line 22
    move-wide v2, p1

    .line 23
    move-object v4, p3

    .line 24
    move-object/from16 v6, p4

    .line 25
    .line 26
    move-object/from16 v7, p5

    .line 27
    .line 28
    move-object/from16 v5, p6

    .line 29
    .line 30
    invoke-direct/range {v0 .. v8}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$onParticipateClick$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;JLorg/xplatform/aggregator/api/model/tournaments/TournamentKind;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 31
    .line 32
    .line 33
    const/16 v6, 0xa

    .line 34
    .line 35
    const/4 v7, 0x0

    .line 36
    const/4 v2, 0x0

    .line 37
    const/4 v4, 0x0

    .line 38
    move-object v5, v0

    .line 39
    move-object v0, v9

    .line 40
    move-object v1, v10

    .line 41
    move-object v3, v11

    .line 42
    invoke-static/range {v0 .. v7}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->z(Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/x0;

    .line 43
    .line 44
    .line 45
    return-void
.end method

.method public final J3(JZLjava/lang/String;)V
    .locals 3
    .param p4    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->I2:Lkotlinx/coroutines/x0;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    const/4 v2, 0x1

    .line 7
    invoke-static {v0, v1, v2, v1}, Lkotlinx/coroutines/x0$a;->a(Lkotlinx/coroutines/x0;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->v1:Lw81/c;

    .line 11
    .line 12
    invoke-interface {v0, p1, p2, p3}, Lw81/c;->a(JZ)Lkotlinx/coroutines/flow/e;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    new-instance p2, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;

    .line 17
    .line 18
    invoke-direct {p2, p0, p4, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$1;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;Ljava/lang/String;Lkotlin/coroutines/e;)V

    .line 19
    .line 20
    .line 21
    invoke-static {p1, p2}, Lkotlinx/coroutines/flow/g;->i0(Lkotlinx/coroutines/flow/e;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/e;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    invoke-static {p0}, Landroidx/lifecycle/c0;->a(Landroidx/lifecycle/b0;)Lkotlinx/coroutines/N;

    .line 26
    .line 27
    .line 28
    move-result-object p2

    .line 29
    iget-object p3, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->V1:Lm8/a;

    .line 30
    .line 31
    invoke-interface {p3}, Lm8/a;->b()Lkotlinx/coroutines/J;

    .line 32
    .line 33
    .line 34
    move-result-object p3

    .line 35
    invoke-static {p2, p3}, Lkotlinx/coroutines/O;->i(Lkotlinx/coroutines/N;Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/N;

    .line 36
    .line 37
    .line 38
    move-result-object p2

    .line 39
    new-instance p3, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$2;

    .line 40
    .line 41
    invoke-direct {p3, p0, v1}, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel$requestInitialData$2;-><init>(Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;Lkotlin/coroutines/e;)V

    .line 42
    .line 43
    .line 44
    invoke-static {p1, p2, p3}, Lorg/xbet/ui_common/utils/CoroutinesExtensionKt;->v(Lkotlinx/coroutines/flow/e;Lkotlinx/coroutines/N;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/x0;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    iput-object p1, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->I2:Lkotlinx/coroutines/x0;

    .line 49
    .line 50
    return-void
.end method

.method public final onBackPressed()V
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/impl/tournaments/presentation/tournament_stages_alt_design/TournamentStagesAltDesignViewModel;->H2:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {v0}, Lkotlin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method
