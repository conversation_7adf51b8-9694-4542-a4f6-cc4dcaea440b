.class public final Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;
.super Landroid/widget/FrameLayout;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard$a;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000V\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0008\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000b\n\u0002\u0008\t\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\t\u0008\u0007\u0018\u0000 72\u00020\u0001:\u0001\u001cB\u001d\u0008\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\n\u0008\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u00a2\u0006\u0004\u0008\u0006\u0010\u0007J\u001f\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\t\u001a\u00020\u00082\u0006\u0010\n\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u000c\u0010\rJ7\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00082\u0006\u0010\u0011\u001a\u00020\u00082\u0006\u0010\u0012\u001a\u00020\u00082\u0006\u0010\u0013\u001a\u00020\u0008H\u0014\u00a2\u0006\u0004\u0008\u0014\u0010\u0015J\u000f\u0010\u0016\u001a\u00020\u000bH\u0014\u00a2\u0006\u0004\u0008\u0016\u0010\u0017J\u0015\u0010\u001a\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u0018\u00a2\u0006\u0004\u0008\u001a\u0010\u001bR\u0014\u0010\u001e\u001a\u00020\u00088\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u001c\u0010\u001dR\u0014\u0010!\u001a\u00020\u001f8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008\u0013\u0010 R\u0014\u0010%\u001a\u00020\"8\u0002X\u0082\u0004\u00a2\u0006\u0006\n\u0004\u0008#\u0010$RF\u0010.\u001a\u0010\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000b\u0018\u00010&2\u0014\u0010\'\u001a\u0010\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000b\u0018\u00010&8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u0008(\u0010)\u001a\u0004\u0008*\u0010+\"\u0004\u0008,\u0010-R:\u00106\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010/2\u000e\u0010\'\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010/8\u0006@FX\u0086\u000e\u00a2\u0006\u0012\n\u0004\u00080\u00101\u001a\u0004\u00082\u00103\"\u0004\u00084\u00105\u00a8\u00068"
    }
    d2 = {
        "Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;",
        "Landroid/widget/FrameLayout;",
        "Landroid/content/Context;",
        "context",
        "Landroid/util/AttributeSet;",
        "attrs",
        "<init>",
        "(Landroid/content/Context;Landroid/util/AttributeSet;)V",
        "",
        "widthMeasureSpec",
        "heightMeasureSpec",
        "",
        "onMeasure",
        "(II)V",
        "",
        "changed",
        "l",
        "t",
        "r",
        "b",
        "onLayout",
        "(ZIIII)V",
        "onAttachedToWindow",
        "()V",
        "Ll41/a;",
        "type",
        "setActionIcon",
        "(Ll41/a;)V",
        "a",
        "I",
        "bottomHeight",
        "Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;",
        "Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;",
        "shimmerView",
        "Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;",
        "c",
        "Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;",
        "contentView",
        "Lkotlin/Function1;",
        "value",
        "d",
        "Lkotlin/jvm/functions/Function1;",
        "getOnFavoriteChanged",
        "()Lkotlin/jvm/functions/Function1;",
        "setOnFavoriteChanged",
        "(Lkotlin/jvm/functions/Function1;)V",
        "onFavoriteChanged",
        "Lkotlin/Function0;",
        "e",
        "Lkotlin/jvm/functions/Function0;",
        "getOnMenuClick",
        "()Lkotlin/jvm/functions/Function0;",
        "setOnMenuClick",
        "(Lkotlin/jvm/functions/Function0;)V",
        "onMenuClick",
        "f",
        "uikit_web_games_release"
    }
    k = 0x1
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation

.annotation runtime LlZ0/a;
.end annotation


# static fields
.field public static final f:Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard$a;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public static final g:I


# instance fields
.field public final a:I

.field public final b:Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public final c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation
.end field

.field public d:Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field

.field public e:Lkotlin/jvm/functions/Function0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard$a;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->f:Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard$a;

    const/16 v0, 0x8

    sput v0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->g:I

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p0, p1, v0, v1, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 3
    .param p1    # Landroid/content/Context;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 3
    invoke-direct {p0, p1, p2}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getResources()Landroid/content/res/Resources;

    move-result-object p2

    sget v0, LlZ0/g;->size_52:I

    invoke-virtual {p2, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p2

    iput p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->a:I

    .line 5
    new-instance p2, Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;

    const/4 v0, 0x0

    const/4 v1, 0x2

    invoke-direct {p2, p1, v0, v1, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object p2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->b:Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;

    .line 6
    new-instance v2, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    invoke-direct {v2, p1, v0, v1, v0}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    iput-object v2, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    const/4 p1, 0x0

    .line 7
    invoke-virtual {p0, p1}, Landroid/view/ViewGroup;->setClipChildren(Z)V

    .line 8
    invoke-virtual {p0, p1}, Landroid/view/View;->setWillNotDraw(Z)V

    .line 9
    invoke-virtual {p0, p2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 10
    invoke-virtual {p0, v2}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    .line 2
    :cond_0
    invoke-direct {p0, p1, p2}, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    return-void
.end method


# virtual methods
.method public final getOnFavoriteChanged()Lkotlin/jvm/functions/Function1;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function1<",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->d:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    return-object v0
.end method

.method public final getOnMenuClick()Lkotlin/jvm/functions/Function0;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    return-object v0
.end method

.method public onAttachedToWindow()V
    .locals 2

    .line 1
    invoke-super {p0}, Landroid/widget/FrameLayout;->onAttachedToWindow()V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    instance-of v1, v0, Landroid/view/ViewGroup;

    .line 9
    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    check-cast v0, Landroid/view/ViewGroup;

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v0, 0x0

    .line 16
    :goto_0
    if-eqz v0, :cond_1

    .line 17
    .line 18
    const/4 v1, 0x0

    .line 19
    invoke-virtual {v0, v1}, Landroid/view/ViewGroup;->setClipChildren(Z)V

    .line 20
    .line 21
    .line 22
    :cond_1
    return-void
.end method

.method public onLayout(ZIIII)V
    .locals 0

    .line 1
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 2
    .line 3
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    const/4 p2, 0x0

    .line 8
    const/16 p3, 0x8

    .line 9
    .line 10
    if-eq p1, p3, :cond_0

    .line 11
    .line 12
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 13
    .line 14
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 15
    .line 16
    .line 17
    move-result p4

    .line 18
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 19
    .line 20
    .line 21
    move-result p5

    .line 22
    invoke-virtual {p1, p2, p2, p4, p5}, Landroid/view/View;->layout(IIII)V

    .line 23
    .line 24
    .line 25
    :cond_0
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->b:Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;

    .line 26
    .line 27
    invoke-virtual {p1}, Landroid/view/View;->getVisibility()I

    .line 28
    .line 29
    .line 30
    move-result p1

    .line 31
    if-eq p1, p3, :cond_1

    .line 32
    .line 33
    iget-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->b:Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;

    .line 34
    .line 35
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredWidth()I

    .line 36
    .line 37
    .line 38
    move-result p3

    .line 39
    invoke-virtual {p0}, Landroid/view/View;->getMeasuredHeight()I

    .line 40
    .line 41
    .line 42
    move-result p4

    .line 43
    invoke-virtual {p1, p2, p2, p3, p4}, Landroid/view/View;->layout(IIII)V

    .line 44
    .line 45
    .line 46
    :cond_1
    return-void
.end method

.method public onMeasure(II)V
    .locals 5

    .line 1
    invoke-static {p1}, Landroid/view/View$MeasureSpec;->getSize(I)I

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    int-to-float p2, p1

    .line 6
    const v0, 0x3f2aaaab

    .line 7
    .line 8
    .line 9
    mul-float p2, p2, v0

    .line 10
    .line 11
    float-to-int p2, p2

    .line 12
    iget v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->a:I

    .line 13
    .line 14
    add-int/2addr p2, v0

    .line 15
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 16
    .line 17
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    const/high16 v1, 0x40000000    # 2.0f

    .line 22
    .line 23
    const/16 v2, 0x8

    .line 24
    .line 25
    if-eq v0, v2, :cond_0

    .line 26
    .line 27
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 28
    .line 29
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 30
    .line 31
    .line 32
    move-result v3

    .line 33
    invoke-static {p2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 34
    .line 35
    .line 36
    move-result v4

    .line 37
    invoke-virtual {v0, v3, v4}, Landroid/view/View;->measure(II)V

    .line 38
    .line 39
    .line 40
    :cond_0
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->b:Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;

    .line 41
    .line 42
    invoke-virtual {v0}, Landroid/view/View;->getVisibility()I

    .line 43
    .line 44
    .line 45
    move-result v0

    .line 46
    if-eq v0, v2, :cond_1

    .line 47
    .line 48
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->b:Lorg/xbet/uikit_web_games/game_card/itemview/ShimmerView;

    .line 49
    .line 50
    invoke-static {p1, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 51
    .line 52
    .line 53
    move-result v2

    .line 54
    invoke-static {p2, v1}, Landroid/view/View$MeasureSpec;->makeMeasureSpec(II)I

    .line 55
    .line 56
    .line 57
    move-result v1

    .line 58
    invoke-virtual {v0, v2, v1}, Landroid/view/View;->measure(II)V

    .line 59
    .line 60
    .line 61
    :cond_1
    invoke-virtual {p0, p1, p2}, Landroid/view/View;->setMeasuredDimension(II)V

    .line 62
    .line 63
    .line 64
    return-void
.end method

.method public final setActionIcon(Ll41/a;)V
    .locals 1
    .param p1    # Ll41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param

    .line 1
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setActionIcon(Ll41/a;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public final setOnFavoriteChanged(Lkotlin/jvm/functions/Function1;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Ljava/lang/Boolean;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->d:Lkotlin/jvm/functions/Function1;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setOnFavoriteChanged(Lkotlin/jvm/functions/Function1;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public final setOnMenuClick(Lkotlin/jvm/functions/Function0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function0<",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->e:Lkotlin/jvm/functions/Function0;

    .line 2
    .line 3
    iget-object v0, p0, Lorg/xbet/uikit_web_games/game_card/itemview/DSGameCard;->c:Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;

    .line 4
    .line 5
    invoke-virtual {v0, p1}, Lorg/xbet/uikit_web_games/game_card/itemview/GameCardContentView;->setOnMenuClick(Lkotlin/jvm/functions/Function0;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
