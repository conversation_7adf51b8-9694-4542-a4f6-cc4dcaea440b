.class public final Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime L<PERSON>lin/Metadata;
    d1 = {
        "\u0000\u0016\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a!\u0010\u0005\u001a\u000e\u0012\n\u0012\u0008\u0012\u0004\u0012\u00020\u00040\u00030\u00022\u0006\u0010\u0001\u001a\u00020\u0000\u00a2\u0006\u0004\u0008\u0005\u0010\u0006\u00a8\u0006\u0007"
    }
    d2 = {
        "Ld41/a;",
        "clickListener",
        "LA4/c;",
        "",
        "Le41/a;",
        "h",
        "(Ld41/a;)LA4/c;",
        "uikit_sport_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static synthetic a(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->n(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic b(Ld41/a;LB4/a;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->j(Ld41/a;LB4/a;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic c(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->m(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic d(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->l(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static synthetic e(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/G;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/G;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic f(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->o(LB4/a;Ljava/util/List;)Lkotlin/Unit;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic g(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt;->k(Ld41/a;LB4/a;Landroid/view/View;)V

    return-void
.end method

.method public static final h(Ld41/a;)LA4/c;
    .locals 4
    .param p0    # Ld41/a;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ld41/a;",
            ")",
            "LA4/c<",
            "Ljava/util/List<",
            "Le41/a;",
            ">;>;"
        }
    .end annotation

    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    new-instance v0, Lb41/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lb41/a;-><init>()V

    .line 4
    .line 5
    .line 6
    new-instance v1, Lb41/b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lb41/b;-><init>(Ld41/a;)V

    .line 9
    .line 10
    .line 11
    new-instance p0, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt$sportFeedsCellChampionshipLargeViewHolder$$inlined$adapterDelegateViewBinding$default$1;

    .line 12
    .line 13
    invoke-direct {p0}, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt$sportFeedsCellChampionshipLargeViewHolder$$inlined$adapterDelegateViewBinding$default$1;-><init>()V

    .line 14
    .line 15
    .line 16
    sget-object v2, Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt$sportFeedsCellChampionshipLargeViewHolder$$inlined$adapterDelegateViewBinding$default$2;->INSTANCE:Lorg/xbet/uikit_sport/sport_feeds_cell/adapter/delegates/championship/DsSportFeedsCellChampionshipLargeViewHolderKt$sportFeedsCellChampionshipLargeViewHolder$$inlined$adapterDelegateViewBinding$default$2;

    .line 17
    .line 18
    new-instance v3, LB4/b;

    .line 19
    .line 20
    invoke-direct {v3, v0, p0, v1, v2}, LB4/b;-><init>(Lkotlin/jvm/functions/Function2;LOc/n;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V

    .line 21
    .line 22
    .line 23
    return-object v3
.end method

.method public static final i(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)LC31/G;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {p0, p1, v0}, LC31/G;->c(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Z)LC31/G;

    .line 3
    .line 4
    .line 5
    move-result-object p0

    .line 6
    return-object p0
.end method

.method public static final j(Ld41/a;LB4/a;)Lkotlin/Unit;
    .locals 2

    .line 1
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, LC31/G;

    .line 6
    .line 7
    iget-object v0, v0, LC31/G;->b:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipLarge;

    .line 8
    .line 9
    new-instance v1, Lb41/c;

    .line 10
    .line 11
    invoke-direct {v1, p0, p1}, Lb41/c;-><init>(Ld41/a;LB4/a;)V

    .line 12
    .line 13
    .line 14
    invoke-virtual {v0, v1}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 15
    .line 16
    .line 17
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    check-cast v0, LC31/G;

    .line 22
    .line 23
    iget-object v0, v0, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 24
    .line 25
    new-instance v1, Lb41/d;

    .line 26
    .line 27
    invoke-direct {v1, p0, p1}, Lb41/d;-><init>(Ld41/a;LB4/a;)V

    .line 28
    .line 29
    .line 30
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionClickListener(Landroid/view/View$OnClickListener;)V

    .line 31
    .line 32
    .line 33
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 34
    .line 35
    .line 36
    move-result-object v0

    .line 37
    check-cast v0, LC31/G;

    .line 38
    .line 39
    iget-object v0, v0, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 40
    .line 41
    new-instance v1, Lb41/e;

    .line 42
    .line 43
    invoke-direct {v1, p0, p1}, Lb41/e;-><init>(Ld41/a;LB4/a;)V

    .line 44
    .line 45
    .line 46
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckBoxClickListener(Landroid/view/View$OnClickListener;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {p1}, LB4/a;->e()LL2/a;

    .line 50
    .line 51
    .line 52
    move-result-object v0

    .line 53
    check-cast v0, LC31/G;

    .line 54
    .line 55
    iget-object v0, v0, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 56
    .line 57
    new-instance v1, Lb41/f;

    .line 58
    .line 59
    invoke-direct {v1, p0, p1}, Lb41/f;-><init>(Ld41/a;LB4/a;)V

    .line 60
    .line 61
    .line 62
    invoke-virtual {v0, v1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIconClickListener(Landroid/view/View$OnClickListener;)V

    .line 63
    .line 64
    .line 65
    new-instance p0, Lb41/g;

    .line 66
    .line 67
    invoke-direct {p0, p1}, Lb41/g;-><init>(LB4/a;)V

    .line 68
    .line 69
    .line 70
    invoke-virtual {p1, p0}, LB4/a;->d(Lkotlin/jvm/functions/Function1;)V

    .line 71
    .line 72
    .line 73
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 74
    .line 75
    return-object p0
.end method

.method public static final k(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->c(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final l(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->a(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final m(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->d(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final n(Ld41/a;LB4/a;Landroid/view/View;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, LB4/a;->i()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, Lf41/a;

    .line 6
    .line 7
    invoke-interface {p0, p1}, Ld41/a;->b(Lf41/a;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method

.method public static final o(LB4/a;Ljava/util/List;)Lkotlin/Unit;
    .locals 1

    .line 1
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    check-cast p1, LC31/G;

    .line 6
    .line 7
    iget-object p1, p1, LC31/G;->b:Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipLarge;

    .line 8
    .line 9
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    check-cast v0, Lf41/a;

    .line 14
    .line 15
    invoke-virtual {v0}, Lf41/a;->h()Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_feeds_cell/championship/DsSportFeedsCellChampionshipLarge;->setComponentStyle(Lorg/xbet/uikit_sport/sport_feeds_cell/DsSportFeedsCellChampionShipType;)V

    .line 20
    .line 21
    .line 22
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    check-cast p1, LC31/G;

    .line 27
    .line 28
    iget-object p1, p1, LC31/G;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 29
    .line 30
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    check-cast v0, Lf41/a;

    .line 35
    .line 36
    invoke-virtual {v0}, Lf41/a;->j()I

    .line 37
    .line 38
    .line 39
    move-result v0

    .line 40
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIcon(I)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    check-cast p1, LC31/G;

    .line 48
    .line 49
    iget-object p1, p1, LC31/G;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 50
    .line 51
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 52
    .line 53
    .line 54
    move-result-object v0

    .line 55
    check-cast v0, Lf41/a;

    .line 56
    .line 57
    invoke-virtual {v0}, Lf41/a;->k()Ljava/lang/Integer;

    .line 58
    .line 59
    .line 60
    move-result-object v0

    .line 61
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 62
    .line 63
    .line 64
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 65
    .line 66
    .line 67
    move-result-object p1

    .line 68
    check-cast p1, LC31/G;

    .line 69
    .line 70
    iget-object p1, p1, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 71
    .line 72
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 73
    .line 74
    .line 75
    move-result-object v0

    .line 76
    check-cast v0, Lf41/a;

    .line 77
    .line 78
    invoke-virtual {v0}, Lf41/a;->e()Ljava/lang/Integer;

    .line 79
    .line 80
    .line 81
    move-result-object v0

    .line 82
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setActionIconTintByColorAttr(Ljava/lang/Integer;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    check-cast p1, LC31/G;

    .line 90
    .line 91
    iget-object p1, p1, LC31/G;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 92
    .line 93
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 94
    .line 95
    .line 96
    move-result-object v0

    .line 97
    check-cast v0, Lf41/a;

    .line 98
    .line 99
    invoke-virtual {v0}, Lf41/a;->f()Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;

    .line 100
    .line 101
    .line 102
    move-result-object v0

    .line 103
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setBadge(Lorg/xbet/uikit_sport/sport_cell/DsSportCellBadgeType;)V

    .line 104
    .line 105
    .line 106
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    check-cast p1, LC31/G;

    .line 111
    .line 112
    iget-object p1, p1, LC31/G;->c:Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;

    .line 113
    .line 114
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    check-cast v0, Lf41/a;

    .line 119
    .line 120
    invoke-virtual {v0}, Lf41/a;->l()Ljava/lang/Integer;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/left/SportCellLeftView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 125
    .line 126
    .line 127
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 128
    .line 129
    .line 130
    move-result-object p1

    .line 131
    check-cast p1, LC31/G;

    .line 132
    .line 133
    iget-object p1, p1, LC31/G;->d:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 134
    .line 135
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 136
    .line 137
    .line 138
    move-result-object v0

    .line 139
    check-cast v0, Lf41/a;

    .line 140
    .line 141
    invoke-virtual {v0}, Lf41/a;->o()Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v0

    .line 145
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setTitleText(Ljava/lang/String;)V

    .line 146
    .line 147
    .line 148
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 149
    .line 150
    .line 151
    move-result-object p1

    .line 152
    check-cast p1, LC31/G;

    .line 153
    .line 154
    iget-object p1, p1, LC31/G;->d:Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;

    .line 155
    .line 156
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 157
    .line 158
    .line 159
    move-result-object v0

    .line 160
    check-cast v0, Lf41/a;

    .line 161
    .line 162
    invoke-virtual {v0}, Lf41/a;->n()Ljava/lang/String;

    .line 163
    .line 164
    .line 165
    move-result-object v0

    .line 166
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/middle/SportCellMiddleView;->setSubtitleText(Ljava/lang/String;)V

    .line 167
    .line 168
    .line 169
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 170
    .line 171
    .line 172
    move-result-object p1

    .line 173
    check-cast p1, LC31/G;

    .line 174
    .line 175
    iget-object p1, p1, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 176
    .line 177
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 178
    .line 179
    .line 180
    move-result-object v0

    .line 181
    check-cast v0, Lf41/a;

    .line 182
    .line 183
    invoke-virtual {v0}, Lf41/a;->g()Z

    .line 184
    .line 185
    .line 186
    move-result v0

    .line 187
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setListCheckboxChecked(Z)V

    .line 188
    .line 189
    .line 190
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 191
    .line 192
    .line 193
    move-result-object p1

    .line 194
    check-cast p1, LC31/G;

    .line 195
    .line 196
    iget-object p1, p1, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 197
    .line 198
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 199
    .line 200
    .line 201
    move-result-object v0

    .line 202
    check-cast v0, Lf41/a;

    .line 203
    .line 204
    invoke-virtual {v0}, Lf41/a;->m()Ljava/lang/Integer;

    .line 205
    .line 206
    .line 207
    move-result-object v0

    .line 208
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setCounterNumber(Ljava/lang/Integer;)V

    .line 209
    .line 210
    .line 211
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 212
    .line 213
    .line 214
    move-result-object p1

    .line 215
    check-cast p1, LC31/G;

    .line 216
    .line 217
    iget-object p1, p1, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 218
    .line 219
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 220
    .line 221
    .line 222
    move-result-object v0

    .line 223
    check-cast v0, Lf41/a;

    .line 224
    .line 225
    invoke-virtual {v0}, Lf41/a;->d()Z

    .line 226
    .line 227
    .line 228
    move-result v0

    .line 229
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionExpanded(Z)V

    .line 230
    .line 231
    .line 232
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 233
    .line 234
    .line 235
    move-result-object p1

    .line 236
    check-cast p1, LC31/G;

    .line 237
    .line 238
    iget-object p1, p1, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 239
    .line 240
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 241
    .line 242
    .line 243
    move-result-object v0

    .line 244
    check-cast v0, Lf41/a;

    .line 245
    .line 246
    invoke-virtual {v0}, Lf41/a;->d()Z

    .line 247
    .line 248
    .line 249
    move-result v0

    .line 250
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setAccordionExpanded(Z)V

    .line 251
    .line 252
    .line 253
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 254
    .line 255
    .line 256
    move-result-object p1

    .line 257
    check-cast p1, LC31/G;

    .line 258
    .line 259
    iget-object p1, p1, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 260
    .line 261
    invoke-virtual {p0}, LB4/a;->i()Ljava/lang/Object;

    .line 262
    .line 263
    .line 264
    move-result-object v0

    .line 265
    check-cast v0, Lf41/a;

    .line 266
    .line 267
    invoke-virtual {v0}, Lf41/a;->i()Z

    .line 268
    .line 269
    .line 270
    move-result v0

    .line 271
    invoke-virtual {p1, v0}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setFavoriteIcon(Z)V

    .line 272
    .line 273
    .line 274
    invoke-virtual {p0}, LB4/a;->e()LL2/a;

    .line 275
    .line 276
    .line 277
    move-result-object p0

    .line 278
    check-cast p0, LC31/G;

    .line 279
    .line 280
    iget-object p0, p0, LC31/G;->e:Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;

    .line 281
    .line 282
    const/4 p1, 0x0

    .line 283
    invoke-virtual {p0, p1}, Lorg/xbet/uikit_sport/sport_cell/right/SportCellRightView;->setTagVisible(Z)V

    .line 284
    .line 285
    .line 286
    sget-object p0, Lkotlin/Unit;->a:Lkotlin/Unit;

    .line 287
    .line 288
    return-object p0
.end method
