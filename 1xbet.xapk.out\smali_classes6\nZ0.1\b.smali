.class public final LnZ0/b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0003\u001a\u0013\u0010\u0002\u001a\u00020\u0001*\u00020\u0000H\u0000\u00a2\u0006\u0004\u0008\u0002\u0010\u0003\u00a8\u0006\u0004"
    }
    d2 = {
        "",
        "Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;",
        "a",
        "(Ljava/lang/String;)Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;",
        "uikit_release"
    }
    k = 0x2
    mv = {
        0x2,
        0x1,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static final a(Ljava/lang/String;)Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;
    .locals 1
    .param p0    # Ljava/lang/String;
        .annotation build Lorg/jetbrains/annotations/NotNull;
        .end annotation
    .end param
    .annotation build Lorg/jetbrains/annotations/NotNull;
    .end annotation

    .line 1
    invoke-virtual {p0}, Ljava/lang/String;->hashCode()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    sparse-switch v0, :sswitch_data_0

    .line 6
    .line 7
    .line 8
    goto :goto_0

    .line 9
    :sswitch_0
    const-string v0, "quinary"

    .line 10
    .line 11
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 12
    .line 13
    .line 14
    move-result p0

    .line 15
    if-nez p0, :cond_0

    .line 16
    .line 17
    goto :goto_0

    .line 18
    :cond_0
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->QUINARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 19
    .line 20
    return-object p0

    .line 21
    :sswitch_1
    const-string v0, "primary"

    .line 22
    .line 23
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 24
    .line 25
    .line 26
    move-result p0

    .line 27
    if-eqz p0, :cond_4

    .line 28
    .line 29
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->PRIMARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 30
    .line 31
    return-object p0

    .line 32
    :sswitch_2
    const-string v0, "secondary"

    .line 33
    .line 34
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 35
    .line 36
    .line 37
    move-result p0

    .line 38
    if-nez p0, :cond_1

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->SECONDARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 42
    .line 43
    return-object p0

    .line 44
    :sswitch_3
    const-string v0, "senary"

    .line 45
    .line 46
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 47
    .line 48
    .line 49
    move-result p0

    .line 50
    if-nez p0, :cond_2

    .line 51
    .line 52
    goto :goto_0

    .line 53
    :cond_2
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->SENARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 54
    .line 55
    return-object p0

    .line 56
    :sswitch_4
    const-string v0, "tertiary"

    .line 57
    .line 58
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    move-result p0

    .line 62
    if-nez p0, :cond_3

    .line 63
    .line 64
    goto :goto_0

    .line 65
    :cond_3
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->TERTIARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 66
    .line 67
    return-object p0

    .line 68
    :sswitch_5
    const-string v0, "quaternary"

    .line 69
    .line 70
    invoke-virtual {p0, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 71
    .line 72
    .line 73
    move-result p0

    .line 74
    if-nez p0, :cond_5

    .line 75
    .line 76
    :cond_4
    :goto_0
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->PRIMARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 77
    .line 78
    return-object p0

    .line 79
    :cond_5
    sget-object p0, Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;->QUATERNARY_STYLE:Lorg/xbet/uikit/components/accountcontrol/AccountControlStyleConfigType;

    .line 80
    .line 81
    return-object p0

    .line 82
    nop

    .line 83
    :sswitch_data_0
    .sparse-switch
        -0x60d61282 -> :sswitch_5
        -0x4605f7ae -> :sswitch_4
        -0x35ffef74 -> :sswitch_3
        -0x30bb8e8c -> :sswitch_2
        -0x12c2f1fe -> :sswitch_1
        0x2742477f -> :sswitch_0
    .end sparse-switch
.end method
