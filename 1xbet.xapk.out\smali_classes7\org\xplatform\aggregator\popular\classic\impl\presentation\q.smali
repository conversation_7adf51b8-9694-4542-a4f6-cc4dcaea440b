.class public final synthetic Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# instance fields
.field public final synthetic a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

.field public final synthetic b:Ljava/lang/Throwable;


# direct methods
.method public synthetic constructor <init>(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    iput-object p2, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;->b:Ljava/lang/Throwable;

    return-void
.end method


# virtual methods
.method public final invoke()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;->a:Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;

    iget-object v1, p0, Lorg/xplatform/aggregator/popular/classic/impl/presentation/q;->b:Ljava/lang/Throwable;

    invoke-static {v0, v1}, Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;->y3(Lorg/xplatform/aggregator/popular/classic/impl/presentation/PopularClassicAggregatorViewModel;Ljava/lang/Throwable;)Lkotlin/Unit;

    move-result-object v0

    return-object v0
.end method
